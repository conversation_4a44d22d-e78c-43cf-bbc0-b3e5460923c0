name: Lint and Test Charts

on:
  pull_request:
    branches:
      - develop
    paths:
      - '.github/workflows/lint-helm-charts.yml'
      - 'charts/**'
jobs:
  lint-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up Helm
        uses: azure/setup-helm@v4.2.0
      - name: Ensure documentation is updated
        uses: docker://jnorwood/helm-docs:v1.14.2
      - name: Set up chart-testing
        uses: helm/chart-testing-action@v2.6.1
      - name: Run chart-testing (list-changed)
        id: list-changed
        run: |
          changed=$(ct list-changed --target-branch ${{ github.event.repository.default_branch }})
          if [[ -n "$changed" ]]; then
            echo "changed=true" >> "$GITHUB_OUTPUT"
          fi
      - name: Run chart-testing
        if: steps.list-changed.outputs.changed == 'true'
        run: ct lint --target-branch ${{ github.event.repository.default_branch }} --validate-maintainers=false
