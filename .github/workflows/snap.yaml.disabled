name: Publish Snap

# turn off edge snap builds temporarily and make it manual

# on:
#  push:
#    branches:
#      - develop

on: workflow_dispatch

jobs:
  jobs:
    name: Job Check
    runs-on: ubuntu-22.04
    if: "!contains(github.event.head_commit.message, '[skip ci]')"
    steps:
      - name: Cancel Previous Runs
        uses: styfle/cancel-workflow-action@0.12.1
        with:
          access_token: ${{ secrets.GITHUB_TOKEN }}

  build-snap:
    name: Build Snap Package (${{ matrix.architecture }})
    needs: jobs
    runs-on: ubuntu-22.04
    strategy:
      fail-fast: false
      matrix:
        architecture:
          - amd64
          - arm64
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
      - name: Prepare
        id: prepare
        run: |
          git fetch --prune --unshallow --tags
          if [[ $GITHUB_REF == refs/tags/* || $GITHUB_REF == refs/heads/master ]]; then
            echo "RELEASE=stable" >> $GITHUB_OUTPUT
          else
            echo "RELEASE=edge" >> $GITHUB_OUTPUT
          fi
      - name: Set Up QEMU
        uses: docker/setup-qemu-action@v3
      - name: Configure Git
        run: git config --add safe.directory /data/parts/jellyseerr/src
      - name: Build Snap Package
        uses: diddlesnaps/snapcraft-multiarch-action@v1
        id: build
        with:
          architecture: ${{ matrix.architecture }}
      - name: Upload Snap Package
        uses: actions/upload-artifact@v4
        with:
          name: jellyseerr-snap-package-${{ matrix.architecture }}
          path: ${{ steps.build.outputs.snap }}
      - name: Review Snap Package
        uses: diddlesnaps/snapcraft-review-tools-action@v1
        with:
          snap: ${{ steps.build.outputs.snap }}
      - name: Publish Snap Package
        uses: snapcore/action-publish@v1
        env:
          SNAPCRAFT_STORE_CREDENTIALS: ${{ secrets.SNAP_LOGIN }}
        with:
          snap: ${{ steps.build.outputs.snap }}
          release: ${{ steps.prepare.outputs.RELEASE }}

  discord:
    name: Send Discord Notification
    needs: build-snap
    if: always() && !contains(github.event.head_commit.message, '[skip ci]')
    runs-on: ubuntu-22.04
    steps:
      - name: Get Build Job Status
        uses: technote-space/workflow-conclusion-action@v3
      - name: Combine Job Status
        id: status
        run: |
          failures=(neutral, skipped, timed_out, action_required)
          if [[ ${array[@]} =~ $WORKFLOW_CONCLUSION ]]; then
            echo "status=failure" >> $GITHUB_OUTPUT
          else
            echo "status=$WORKFLOW_CONCLUSION" >> $GITHUB_OUTPUT
          fi
      - name: Post Status to Discord
        uses: sarisia/actions-status-discord@v1
        with:
          webhook: ${{ secrets.DISCORD_WEBHOOK }}
          status: ${{ steps.status.outputs.status }}
          title: ${{ github.workflow }}
          nofail: true
