name: Cypress Tests

on:
  pull_request:
    branches:
      - '*'
  push:
    branches:
      - develop

jobs:
  cypress-run:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22
      - name: Pnpm Setup
        uses: pnpm/action-setup@v4
        with:
          version: 9
      - name: Cypress run
        uses: cypress-io/github-action@v6
        with:
          build: pnpm cypress:build
          start: pnpm start
          wait-on: 'http://localhost:5055'
          record: true
        env:
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          WITH_MIGRATIONS: true
          # Fix test titles in cypress dashboard
          COMMIT_INFO_MESSAGE: ${{github.event.pull_request.title}}
          COMMIT_INFO_SHA: ${{github.event.pull_request.head.sha}}
      - name: Upload video files
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: cypress-videos
          path: |
            cypress/videos
            cypress/screenshots
