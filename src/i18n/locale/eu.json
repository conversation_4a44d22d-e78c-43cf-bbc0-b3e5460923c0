{"components.Blacklist.blacklistedby": "{date} {user} erabiltzailearengatik", "components.Blacklist.blacklistsettings": "Zerrenda beltzaren ezarpenak", "components.Blacklist.mediaName": "<PERSON><PERSON><PERSON>", "components.Blacklist.mediaTmdbId": "tmdb Id", "components.Blacklist.mediaType": "<PERSON><PERSON>", "components.CollectionDetails.numberofmovies": "{count} film", "components.CollectionDetails.overview": "Ikuspegi orokorra", "component.BlacklistBlock.blacklistdate": "Zerrenda beltzeko data", "component.BlacklistBlock.blacklistedby": "Zerrenda beltzera honengatik gehituta", "components.AirDateBadge.airedrelative": "{relativeTime} i<PERSON><PERSON>", "components.AirDateBadge.airsrelative": "{relativeTime} i<PERSON><PERSON>en", "components.Discover.CreateSlider.addSlider": "Gehitu irristaria", "components.Discover.CreateSlider.addfail": "Irristari berria sortzeak huts egin du.", "components.Discover.DiscoverSliderEdit.remove": "<PERSON><PERSON>", "components.Discover.FilterSlideover.genres": "Generoak", "components.Discover.FilterSlideover.keywords": "<PERSON>ak<PERSON><PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.runtime": "Ira<PERSON>na", "components.IssueDetails.comments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.issuetype": "<PERSON><PERSON>", "components.IssueDetails.leavecomment": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.IssueList.IssueItem.issuestatus": "Egoera", "components.IssueList.IssueItem.issuetype": "<PERSON><PERSON>", "components.IssueList.IssueItem.opened": "<PERSON><PERSON><PERSON>", "components.IssueList.IssueItem.unknownissuetype": "Ezezaguna", "components.IssueModal.issueAudio": "Audio", "components.IssueModal.issueOther": "Bestelakoa", "components.IssueModal.issueVideo": "Bideoa", "components.Layout.Sidebar.blacklist": "Zerrenda beltza", "components.Layout.Sidebar.browsemovies": "Filmak", "components.Layout.Sidebar.dashboard": "Aurkitu", "components.Layout.Sidebar.issues": "Intzidentziak", "components.Layout.Sidebar.requests": "Eskaerak", "components.Layout.Sidebar.settings": "Ezarpenak", "components.Layout.Sidebar.users": "Erabiltzaileak", "components.Layout.UserDropdown.myprofile": "Profila", "components.Layout.UserDropdown.requests": "Eskaerak", "components.Layout.UserDropdown.settings": "Ezarpenak", "components.Login.initialsigningin": "Konektatzen…", "components.Login.password": "<PERSON><PERSON><PERSON><PERSON>", "components.Login.port": "<PERSON><PERSON>", "components.Login.save": "<PERSON><PERSON><PERSON><PERSON>", "components.Login.saving": "<PERSON><PERSON><PERSON><PERSON>…", "components.ManageSlideOver.manageModalAdvanced": "Aurreratua", "components.ManageSlideOver.manageModalMedia": "Multimedia", "components.ManageSlideOver.manageModalRequests": "Eskaerak", "components.ManageSlideOver.movie": "filma", "components.MovieDetails.budget": "Aurrekontua", "components.MovieDetails.cast": "Aktoreak", "components.MovieDetails.recommendations": "Gomendioak", "components.MovieDetails.revenue": "<PERSON><PERSON><PERSON><PERSON>", "components.PermissionEdit.autoapprove": "Auto-onarpena", "components.PermissionEdit.request": "Eskaera", "components.PersonDetails.crewmember": "Taldea", "components.QuotaSelector.unlimited": "Mugagabea", "components.RequestList.RequestItem.profileName": "Profila", "components.RequestList.RequestItem.requesteddate": "Eskatuta", "components.RequestModal.AdvancedRequester.advancedoptions": "Aurreratuak", "components.ResetPassword.password": "<PERSON><PERSON><PERSON><PERSON>", "components.Selector.canceled": "<PERSON><PERSON><PERSON>", "components.Selector.ended": "<PERSON><PERSON><PERSON><PERSON>", "components.Selector.planned": "Planifikatuta", "components.Settings.RadarrModal.port": "<PERSON><PERSON>", "components.Settings.RadarrModal.released": "Kaleratuta", "components.Settings.RadarrModal.tags": "Etiketak", "components.Settings.SettingsAbout.Releases.latestversion": "<PERSON><PERSON><PERSON> berts<PERSON>", "components.Settings.SettingsAbout.Releases.releases": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.about": "<PERSON><PERSON> b<PERSON>z", "components.Settings.SettingsAbout.documentation": "Dokumentazioa", "components.Settings.SettingsAbout.preferredmethod": "Nahiagokoa", "components.Settings.SettingsAbout.version": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachehits": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachemisses": "Hu<PERSON>egiteak", "components.Settings.SettingsJobsCache.command": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobs": "Atazak", "components.Settings.SettingsJobsCache.jobtype": "<PERSON><PERSON>", "components.Settings.SettingsLogs.filterDebug": "Arazketa", "components.Settings.SettingsLogs.filterError": "Errorea", "components.Settings.SettingsLogs.filterInfo": "Informazioa", "components.Settings.SettingsLogs.filterWarn": "<PERSON><PERSON>", "components.Settings.SettingsLogs.label": "Etiketa", "components.Settings.SettingsLogs.level": "Larritasuna", "components.Settings.SettingsLogs.logs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.message": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.pauseLogs": "Pausatu", "components.Settings.SettingsLogs.resumeLogs": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.time": "Denbora-z<PERSON>lua", "components.Settings.SonarrModal.port": "<PERSON><PERSON>", "components.Settings.address": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.default": "Lehenetsia", "components.Settings.menuAbout": "<PERSON><PERSON> b<PERSON>z", "components.Settings.menuJellyfinSettings": "{mediaServerName}", "components.Settings.menuLogs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.menuNotifications": "Jakinarazpenak", "components.Settings.menuPlexSettings": "Plex", "components.Settings.menuServices": "Zerbitzuak", "components.Settings.menuUsers": "Erabiltzaileak", "components.Settings.notifications": "Jakinarazpenak", "components.Settings.plex": "Plex", "components.Settings.saving": "<PERSON><PERSON><PERSON><PERSON>…", "components.Settings.scanning": "Sinkronizatzen…", "components.Settings.serverLocal": "lokala", "components.Settings.serverRemote": "u<PERSON><PERSON><PERSON><PERSON>", "components.Settings.serverSecure": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.serverpreset": "Zerbitzaria", "components.Settings.services": "Zerbitzuak", "components.Settings.ssl": "SSL", "components.Settings.tip": "Aholkua", "components.Settings.webhook": "Webhook", "components.Setup.continue": "<PERSON><PERSON><PERSON><PERSON>", "components.Setup.finishing": "<PERSON><PERSON><PERSON>…", "components.Setup.setup": "Konfigurazioa", "components.StatusBadge.seasonnumber": "S{seasonNumber}", "components.StatusBadge.status": "{status}", "components.TvDetails.anime": "An<PERSON>a", "components.TvDetails.cast": "Aktoreak", "components.TvDetails.recommendations": "Gomendioak", "components.TvDetails.seasonstitle": "Den<PERSON><PERSON><PERSON><PERSON>", "components.UserList.accounttype": "<PERSON><PERSON>", "components.UserList.create": "Sortu", "components.UserList.created": "Bat eginda", "components.UserList.creating": "<PERSON><PERSON><PERSON>…", "components.UserList.owner": "Jabea", "components.UserList.password": "<PERSON><PERSON><PERSON><PERSON>", "components.UserList.role": "Rola", "components.UserList.user": "Erabiltz<PERSON><PERSON>", "components.UserList.username": "Erabiltzaile-izena", "components.UserList.users": "Erabiltzaileak", "components.UserProfile.UserSettings.UserGeneralSettings.email": "E-posta", "components.UserProfile.UserSettings.UserGeneralSettings.general": "Orokorra", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "Jabea", "components.UserProfile.UserSettings.UserGeneralSettings.saving": "<PERSON><PERSON><PERSON><PERSON>…", "components.UserProfile.UserSettings.UserGeneralSettings.user": "Erabiltz<PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "Jakinarazpenak", "components.UserProfile.UserSettings.menuChangePass": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.menuGeneralSettings": "Orokorra", "components.UserProfile.UserSettings.menuNotifications": "Jakinarazpenak", "components.UserProfile.unlimited": "Mugagabeak", "i18n.advanced": "Aurreratua", "i18n.all": "<PERSON><PERSON>", "i18n.approve": "<PERSON><PERSON><PERSON>", "i18n.available": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.back": "<PERSON><PERSON><PERSON>", "i18n.blacklist": "Zerrenda beltza", "i18n.blacklisted": "Zerrenda beltzean", "i18n.cancel": "<PERSON><PERSON><PERSON>", "i18n.canceling": "<PERSON><PERSON><PERSON>…", "i18n.close": "Itxi", "i18n.collection": "<PERSON><PERSON><PERSON><PERSON>", "i18n.decline": "<PERSON><PERSON> e<PERSON>", "i18n.declined": "<PERSON><PERSON><PERSON>", "i18n.delete": "Ezabat<PERSON>", "i18n.edit": "Editatu", "i18n.experimental": "Esperimentala", "i18n.failed": "<PERSON><PERSON> egin du", "i18n.import": "Inportatu", "i18n.importing": "Inportatzen…", "i18n.loading": "<PERSON><PERSON><PERSON><PERSON>…", "i18n.movie": "Filma", "i18n.movies": "Filmak", "i18n.next": "Aurrera", "i18n.open": "<PERSON><PERSON><PERSON>", "i18n.pending": "<PERSON><PERSON>", "i18n.previous": "Aurrekoa", "i18n.request": "Eskatu", "i18n.requested": "Eskatuta", "i18n.requesting": "E<PERSON>tz<PERSON>…", "i18n.resolved": "Ebatzita", "i18n.retry": "<PERSON><PERSON><PERSON>", "i18n.retrying": "<PERSON><PERSON><PERSON>…", "i18n.saving": "<PERSON><PERSON><PERSON><PERSON>…", "i18n.settings": "Ezarpenak", "i18n.specials": "<PERSON><PERSON><PERSON><PERSON>", "i18n.test": "Frogatu", "i18n.testing": "Frogatzen…", "i18n.unavailable": "<PERSON>z dago", "i18n.view": "Ikusi", "pages.oops": "Ai!", "components.Blacklist.blacklistdate": "data", "components.CollectionDetails.requestcollection": "Eskatu bilduma", "components.IssueModal.CreateIssueModal.extras": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.overview": "Ikuspegi orokorra", "components.PermissionEdit.autorequest": "Auto-eskaera", "components.PersonDetails.appearsin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.QuotaDisplay.movie": "filmak", "components.Selector.pilot": "Pilot<PERSON>", "components.Settings.RadarrModal.announced": "<PERSON><PERSON><PERSON>", "components.Settings.timeout": "Den<PERSON><PERSON>-muga", "components.UserList.admin": "Administratzailea", "components.UserList.totalrequests": "Eskaerak", "components.UserProfile.UserSettings.UserNotificationSettings.email": "E-posta", "components.UserProfile.UserSettings.menuPermissions": "Baimenak", "components.Discover.DiscoverMovies.discovermovies": "Filmak", "components.CollectionDetails.requestcollection4k": "Eskatu bilduma 4K-n", "components.IssueDetails.issuepagetitle": "Intzidentzia", "components.IssueList.issues": "Intzidentziak", "components.ManageSlideOver.downloadstatus": "<PERSON><PERSON><PERSON><PERSON>", "components.PermissionEdit.admin": "Administratzailea", "components.RequestList.requests": "Eskaerak", "components.RequestModal.AdvancedRequester.tags": "Etiketak", "components.RequestModal.QuotaDisplay.season": "denborald<PERSON>", "components.Search.search": "Bilatu", "components.Settings.SettingsAbout.Releases.currentversion": "Unekoa", "components.Discover.CreateSlider.addcustomslider": "Sortu irristari pertsonalizatua", "components.Discover.CreateSlider.editSlider": "Editatu irristaria", "components.IssueDetails.unknownissuetype": "Ezezaguna", "components.Login.username": "Erabiltzaile-izena", "components.Settings.Notifications.encryptionNone": "Bat ere ez", "components.Settings.SettingsUsers.users": "Erabiltzaileak", "components.Settings.email": "E-posta", "components.Settings.mediaTypeMovie": "filma", "components.Settings.port": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.password": "<PERSON><PERSON><PERSON><PERSON>", "i18n.deleting": "<PERSON>za<PERSON><PERSON>…", "i18n.processing": "Prozesatzen", "components.Discover.FilterSlideover.filters": "Iragaz<PERSON>ak", "components.Discover.DiscoverTv.discovertv": "Telesailak", "components.IssueModal.issueSubtitles": "Azpititulua", "components.Login.initialsignin": "Konektatu", "components.RequestList.RequestItem.modified": "Aldatuta", "components.RequestList.RequestItem.requested": "Eskatuta", "components.RequestModal.season": "Denboraldia", "components.Settings.SettingsMain.general": "Orokorra", "components.Settings.is4k": "4K", "components.StatusBadge.seasonepisodenumber": "S{seasonNumber}E{episodeNumber}", "components.UserProfile.UserSettings.UserPermissions.permissions": "Baimenak", "i18n.status": "Egoera", "components.Discover.FilterSlideover.status": "Egoera", "components.Settings.SettingsJobsCache.cache": "Cachea", "components.Settings.SettingsJobsCache.process": "Prozesua", "components.Settings.SonarrModal.tags": "Etiketak", "components.Settings.menuGeneralSettings": "Orokorra", "components.Settings.syncing": "Sinkronizatzen", "components.TvDetails.overview": "Ikuspegi orokorra", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "Administratzailea", "components.UserProfile.UserSettings.UserGeneralSettings.role": "Rola", "i18n.approved": "<PERSON><PERSON><PERSON>", "components.Discover.CreateSlider.nooptions": "Emaitzarik ez.", "components.Discover.CreateSlider.searchGenres": "Bilatu generoak…", "components.Discover.CreateSlider.searchStudios": "Bilatu estudioak…", "components.Discover.DiscoverMovieGenre.genreMovies": "{genre} filmak", "components.Discover.DiscoverMovieKeyword.keywordMovies": "{keywordTitle} filmak", "components.Discover.FilterSlideover.studio": "Estudioa", "components.Discover.studios": "Estudioak", "components.IssueDetails.IssueDescription.description": "Deskribapena", "components.Layout.Sidebar.browsetv": "Telesailak", "components.ManageSlideOver.tvshow": "telesailak", "components.Settings.mediaTypeSeries": "telesaila", "i18n.tvshow": "Telesailak", "i18n.tvshows": "Telesailak", "components.Discover.StudioSlider.studios": "Estudioak", "components.Discover.networks": "Sareak", "components.Settings.jellyfinSettingsDescription": "<PERSON><PERSON><PERSON>, barne eta kanpo-konexioko puntuak konfiguratu {mediaServerName} zerbitzariarentzat. <PERSON><PERSON>, kan<PERSON>ko URLa barnekoaren desberdina da. <PERSON> berean, pasa<PERSON><PERSON> berrezartzeko URL pertsonalizatu bat ezar daiteke {mediaServerName} sa<PERSON><PERSON><PERSON> has<PERSON>, pasa<PERSON>z desberdin bat berrezartzeko orri bat nahi baduzu. Jellyfinen API gakoa ere aldatu daiteke, lehenago automatikoki sortua.", "components.Discover.DiscoverWatchlist.discoverwatchlist": "<PERSON><PERSON>", "component.BlacklistModal.blacklisting": "<PERSON><PERSON><PERSON>", "components.Discover.DiscoverWatchlist.watchlist": "Plex jarraipen-zerrenda", "components.Discover.FilterSlideover.originalLanguage": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.releaseDate": "Argitalpen data", "components.Discover.FilterSlideover.streamingservices": "Streaming zerbitzuak", "components.Discover.MovieGenreList.moviegenres": "Film generoak", "components.Discover.MovieGenreSlider.moviegenres": "Film generoak", "components.Discover.PlexWatchlistSlider.plexwatchlist": "<PERSON><PERSON>", "components.Discover.RecentlyAddedSlider.recentlyAdded": "<PERSON><PERSON> gut<PERSON> gehituta", "components.Discover.TvGenreList.seriesgenres": "Telesailen generoak", "components.Discover.tmdbstudio": "TMDB Estudioa", "components.Discover.tvgenres": "Telesailen generoak", "components.Discover.upcoming": "<PERSON><PERSON>ngo filmak", "components.IssueDetails.IssueDescription.deleteissue": "Ezabatu intzidentzia", "components.IssueDetails.IssueDescription.edit": "Editatu <PERSON>a", "components.IssueDetails.allepisodes": "Atal guztiak", "components.IssueDetails.allseasons": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "components.IssueDetails.closeissue": "Itxi intzidentzia", "components.IssueDetails.deleteissue": "Ezabatu intzidentzia", "components.IssueDetails.episode": "{episodeNumber} atala", "components.IssueDetails.lastupdated": "Azken eguneraketa", "components.IssueDetails.nocomments": "Iruzkinik ez.", "components.IssueDetails.reopenissue": "<PERSON><PERSON><PERSON> in<PERSON><PERSON> berriro", "components.IssueList.IssueItem.problemepisode": "Kaltetutako atala", "components.IssueList.IssueItem.viewissue": "Ikusi intzidentz<PERSON>", "components.IssueList.sortAdded": "Azkenak", "components.IssueList.sortModified": "Azkenik aldatuak", "components.IssueModal.CreateIssueModal.allepisodes": "Atal guztiak", "components.IssueModal.CreateIssueModal.allseasons": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "components.IssueModal.CreateIssueModal.episode": "{episodeNumber} atala", "components.IssueModal.CreateIssueModal.problemepisode": "Kaltetutako atala", "components.IssueModal.CreateIssueModal.problemseason": "Kaltetutako <PERSON>", "components.IssueModal.CreateIssueModal.season": "{seasonNumber} denboraldia", "components.IssueModal.CreateIssueModal.submitissue": "<PERSON><PERSON>i <PERSON>", "components.IssueModal.CreateIssueModal.toastviewissue": "Ikusi intzidentz<PERSON>", "components.IssueModal.CreateIssueModal.whatswrong": "Zer gertatzen da?", "components.LanguageSelector.languageServerDefault": "({language}) lehenetsia", "components.LanguageSelector.originalLanguageDefault": "Hizkuntza guztiak", "components.Layout.LanguagePicker.displaylanguage": "Bistarat<PERSON><PERSON>", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Film eskaerak", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "Telesail eskaerak", "components.Layout.UserDropdown.signout": "Itxi saioa", "components.Layout.VersionStatus.streamdevelop": "<PERSON><PERSON><PERSON><PERSON>", "components.Layout.VersionStatus.streamstable": "<PERSON><PERSON><PERSON>rr Stable", "components.Login.back": "<PERSON><PERSON><PERSON>", "components.Login.email": "Helbide elektronikoa", "components.Login.enablessl": "Erabili SSL", "components.Login.forgotpassword": "<PERSON><PERSON><PERSON><PERSON> ah<PERSON> du<PERSON>?", "components.Login.hostname": "{mediaServerName} URL", "components.Login.servertype": "Zerbitzari mota", "components.Login.signin": "<PERSON><PERSON> sa<PERSON>a", "components.Login.signingin": "<PERSON><PERSON> hasten…", "components.Login.title": "Gehitu e-posta", "components.Login.urlBase": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Login.validationEmailFormat": "Helbide elektroniko hau baliogabea da", "components.ManageSlideOver.manageModalClearMediaWarning": "* Honek {mediaType}-ko datu guztiak betirako e<PERSON>, eskaera guztiak barne. Elementu hau {mediaServerName} liburutegian badago, edukiaren informazioa hurrengo eskanerrean birsortuko da.", "components.Login.validationusernamerequired": "Erabiltzaile-izen bat behar da", "components.ManageSlideOver.alltime": "<PERSON><PERSON><PERSON> guz<PERSON>", "components.ManageSlideOver.manageModalClearMedia": "Garbitu datuak", "components.ManageSlideOver.manageModalIssues": "<PERSON><PERSON><PERSON><PERSON>", "components.ManageSlideOver.manageModalMedia4k": "4K edukia", "components.ManageSlideOver.manageModalNoRequests": "Eskaerarik ez.", "components.ManageSlideOver.manageModalTitle": "Kudeatu {mediaType}", "components.ManageSlideOver.playedby": "Honek erreproduzituta", "components.MediaSlider.ShowMoreCard.seemore": "Ikusi gehiago", "components.MovieDetails.MovieCast.fullcast": "Aktore g<PERSON>", "components.MovieDetails.MovieCrew.fullcrew": "Talde guztiak", "components.MovieDetails.digitalrelease": "Argitalpen digitala", "components.MovieDetails.downloadstatus": "<PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.managemovie": "Kudeatu filma", "components.MovieDetails.originallanguage": "<PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.originaltitle": "<PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.overviewunavailable": "Ikuspegi orokorra ez dago es<PERSON>garri.", "components.MovieDetails.physicalrelease": "Argital<PERSON> fisikoa", "components.MovieDetails.runtime": "{minutes} minutu", "components.MovieDetails.showless": "<PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.showmore": "Erakutsi gehiago", "components.MovieDetails.watchtrailer": "Ikusi <PERSON>ra", "components.NotificationTypeSelector.issuecomment": "Intzidentziaren iruzkina", "components.NotificationTypeSelector.issuecreated": "<PERSON>tz<PERSON><PERSON><PERSON> bidalita", "components.NotificationTypeSelector.issuereopened": "<PERSON>tz<PERSON>tz<PERSON> berriro i<PERSON>ita", "components.NotificationTypeSelector.issueresolved": "Intzidentz<PERSON> konponduta", "components.NotificationTypeSelector.mediaapproved": "Eskaera onartuta", "components.NotificationTypeSelector.mediaavailable": "Eskaera eskuragarri", "components.NotificationTypeSelector.notificationTypes": "Jakinarazpen motak", "components.PermissionEdit.advancedrequest": "Eskaera aurreratuak", "components.PermissionEdit.autoapprove4k": "Auto-onartu 4K", "components.PermissionEdit.autoapproveMovies": "Auto-onartu filmak", "components.PermissionEdit.autoapproveSeries": "Auto-onartu telesailak", "components.PermissionEdit.autorequestMovies": "Auto-eskatu filmak", "components.PermissionEdit.createissues": "<PERSON><PERSON><PERSON>", "components.PermissionEdit.manageblacklist": "Kudeatu zerrenda beltza", "components.PermissionEdit.manageissues": "Kudeatu intzidentziak", "components.PermissionEdit.managerequests": "Kudeatu eskaerak", "components.PermissionEdit.request4k": "Eskatu 4K", "components.PermissionEdit.requestMovies": "Eskatu filmak", "components.PermissionEdit.requestTv": "Eskatu telesailak", "components.PermissionEdit.users": "Kudeatu erabiltzaileak", "components.PermissionEdit.viewissues": "<PERSON><PERSON><PERSON>", "components.PermissionEdit.viewrequests": "Ikusi eskaerak", "components.PersonDetails.ascharacter": "{character} gisa", "components.PersonDetails.birthdate": "{birthdate}(e)an jaiota", "components.RequestBlock.approve": "<PERSON><PERSON><PERSON> es<PERSON>", "components.RequestBlock.decline": "<PERSON><PERSON><PERSON> eskaera", "components.RequestBlock.delete": "Ezabatu es<PERSON>", "components.RequestBlock.edit": "Editatu es<PERSON>era", "components.RequestBlock.languageprofile": "Hizkuntza profila", "components.RequestBlock.profilechanged": "Kalitate profila", "components.RequestBlock.requestdate": "Eskaera data", "components.RequestBlock.requestedby": "<PERSON>ek es<PERSON>", "components.RequestBlock.requestoverrides": "Eskaera anulazioak", "components.RequestButton.approverequest": "<PERSON><PERSON><PERSON> es<PERSON>", "components.RequestButton.declinerequest": "<PERSON><PERSON><PERSON> eskaera", "components.RequestButton.requestmore": "Eskatu gehiago", "components.RequestButton.viewrequest": "Ikusi es<PERSON>", "components.RequestCard.approverequest": "<PERSON><PERSON><PERSON> es<PERSON>", "components.RequestCard.cancelrequest": "<PERSON><PERSON><PERSON>", "components.RequestCard.declinerequest": "<PERSON><PERSON><PERSON> eskaera", "components.RequestCard.deleterequest": "Ezabatu es<PERSON>", "components.RequestCard.editrequest": "Editatu es<PERSON>era", "components.RequestCard.tmdbid": "TMDB ID", "components.RequestCard.tvdbid": "TheTVDB ID", "components.RequestCard.unknowntitle": "Izenburu ezezaguna", "components.RequestList.RequestItem.cancelRequest": "<PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.deleterequest": "Ezabatu es<PERSON>", "components.RequestList.RequestItem.editrequest": "Editatu es<PERSON>era", "components.RequestList.RequestItem.tmdbid": "TMDB ID", "components.RequestList.RequestItem.tvdbid": "TheTVDB ID", "components.RequestList.RequestItem.unknowntitle": "Izenburu ezezaguna", "components.RequestList.sortAdded": "Azkenak", "components.RequestModal.AdvancedRequester.default": "{name} (Lehenetsia)", "components.RequestModal.AdvancedRequester.destinationserver": "<PERSON><PERSON><PERSON><PERSON>o <PERSON>zar<PERSON>", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.RequestModal.AdvancedRequester.languageprofile": "Hizkuntza profila", "components.RequestModal.AdvancedRequester.qualityprofile": "Kalitate profila", "components.RequestModal.AdvancedRequester.selecttags": "Hautatu etiketak", "components.RequestModal.QuotaDisplay.requiredquota": "Gutxienez <strong>{seasons}</strong> {seasons, plural, one {season request} other {season requests}} erabil<PERSON><PERSON> izan behar duzu/dituzu telesail honetarako eskaera bat bidaltzeko.", "components.RequestModal.alreadyrequested": "Eskatuta dagoeneko", "components.RequestModal.approve": "<PERSON><PERSON><PERSON> es<PERSON>", "components.RequestModal.autoapproval": "Onarpen automatikoa", "components.RequestModal.cancel": "<PERSON><PERSON><PERSON>", "components.RequestModal.edit": "Editatu es<PERSON>era", "components.Settings.SettingsAbout.runningDevelop": "Je<PERSON><PERSON>rr-en <code>garapen</code> adarra exekutatzen ari zara, garapenean laguntzen duten edo \"bleeding-edge\" probetan laguntzen dutenentzat bakarrik gomendatzen dena.", "components.Settings.SettingsJobsCache.imagecacheDescription": "Ezarpenetan gait<PERSON>, <PERSON><PERSON><PERSON><PERSON>-ek aurrez konfiguratutako kanpoko iturrietatik proxya egin eta irudiak cacheatuko ditu. Cachean gordetako irudiak zure konfigurazio karpetan gordetzen dira. Fitxategiak <code>{appDataPath}/cache/images</code> bide-izenean aurki ditzakezu.", "components.Settings.SettingsJobsCache.jobsDescription": "<PERSON><PERSON><PERSON>rr-ek mantentze-lan batzuk egiten ditu aldizka programatutako lan gisa, baina behean eskuz ere abiarazi daitezke. Lan bat eskuz exekutatzeak ez du bere programazioa aldatuko.", "components.Settings.manualscanDescription": "Normalean, hau 24 orduz behin bakarrik egingo da. Je<PERSON><PERSON><PERSON>-ek zure Plex zerbitzariaren berriki gehitutakoak gehiagotan egiaztatuko dira. Plex konfiguratzen duzun lehen aldia bada, liburutegi osoa eskuz eskaneatzea gomendatzen da!", "components.Settings.manualscanDescriptionJellyfin": "Normalean, hau 24 orduz behin bakarrik egingo da. Je<PERSON><PERSON><PERSON>-ek zure {mediaServerName} zerbitzariaren berriki gehitutakoak gehiagotan egiaztatuko dira. Jelly<PERSON>rr konfiguratzen duzun lehen aldia bada, liburutegi osoa eskuz eskaneatzea gomendatzen da!", "components.Settings.noDefaultNon4kServer": "{serverType} zerbitzari bakarra baduzu 4K eta ez-4K edukirako (edo 4k edukia soilik deskargatzen baduzu), zure {serverType} zerbitzaria <strong>EZ</strong> da 4k zerbitzari gisa markatu behar.", "components.Settings.settingUpPlexDescription": "Plex konfigurat<PERSON><PERSON>, xehetasunak eskuz sartu edo <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink>-tik lortutako zerbitzari bat hauta dezakezu. Sakatu goitibeherako menuaren eskuinean dagoen botoia erabilgarri dauden zerbitzariak lortzeko.", "components.Settings.plexlibrariesDescription": "Jellyseerr-ek eskaneatuko dituen liburutegiak. Konfiguratu eta gorde zure Plex konexioaren ezarpenak, eta egin klik beheko botoian liburutegirik zerrendatzen ez bada.", "components.UserList.newJellyfinsigninenabled": "<strong>G<PERSON><PERSON> {mediaServerName} saio-hasiera berria</strong> ezar<PERSON>a gaituta dago une honetan. Liburutegirako sarbidea duten {mediaServerName} erabiltzaileak ez dira inportatu behar saioa hasteko.", "components.UserList.newplexsigninenabled": "<strong><PERSON><PERSON><PERSON>-en saioa hasteko modu berria</strong> ezar<PERSON>a gaituta dago une honetan. Liburutegirako sarbidea duten Plex erabiltzaileak ez dira inportatu behar saioa hasteko.", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "Zure kontuak ez du pasahitzik ezarrita. Konfiguratu pasahitz bat behean kontu hau \"erabiltzaile lokal\" gisa saioa hasteko zure helbide elektronikoa erabiliz.", "components.RequestModal.QuotaDisplay.requiredquotaUser": "Erabiltzaile honek gutxienez <strong>{seasons}</strong> {seasons, plural, one {season request} other {season requests}} erabil<PERSON><PERSON> izan behar du/ditu telesail honetarako eskaera bat bidaltzeko.", "components.Discover.DiscoverTvKeyword.keywordSeries": "{keywordTitle} Telesailak", "components.IssueDetails.problemepisode": "Kaltetutako atala", "components.IssueDetails.season": "{seasonNumber} denboraldia", "components.RequestModal.AdvancedRequester.requestas": "Eskatu erabiltzaile honen i<PERSON>ean", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "Erabiltzaile-kontu honek ez du pasahitzik ezarrita. Konfiguratu pasahitz bat behean kontu hau \"erabiltzaile lokal\" gisa sa<PERSON>a hasi dezan.", "components.IssueDetails.IssueComment.edit": "Editatu iruzkina", "components.IssueDetails.problemseason": "Kaltetutako <PERSON>", "components.RequestList.sortModified": "Azkenekoz aldatutakoak", "components.Discover.TvGenreSlider.tvgenres": "Telesailen generoak", "components.NotificationTypeSelector.mediadeclined": "Eskaera ukatuta", "components.PermissionEdit.autorequestSeries": "Auto-eskatu telesailak", "components.MovieDetails.theatricalrelease": "Zineman argitaratuta", "components.RequestBlock.server": "<PERSON><PERSON><PERSON><PERSON>o <PERSON>zar<PERSON>", "components.RequestModal.AdvancedRequester.notagoptions": "Etiketarik gabe.", "components.Settings.serviceSettingsDescription": "Konfiguratu zure {serverType} zerbitzaria(k) jarraian. Hainbat {serverType} zerbitzari konekta ditz<PERSON>, baina horietako bi bakarrik markatu daitezke lehetsi gisa (bata 4K eta bestea 4K ez dena). Administratzaileek eskaera berriak prozesatzeko erabilitako zerbitzaria baliogabetu dezakete onartu aurretik.", "components.PermissionEdit.managerequestsDescription": "Eduki-eskaerak kudeatzeko baimenak ematen ditu. Baimen hau duen erabiltzaile baten eskaera guztiak automatikoki onartuko dira.", "components.Settings.noDefault4kServer": "{serverType}-(e)ko 4K zerbitzari bat lehenetsi behar da erabiltzaileen {mediaType}-(e)ko 4K eskaerak gaitu ahal i<PERSON>teko.", "components.ResetPassword.requestresetlinksuccessmessage": "Pasahitza berrezartzeko esteka bat adierazitako helbide elektronikora bidaliko da, era<PERSON>tz<PERSON>e baliozko bati lotuta badago.", "components.AppDataWarning.dockerVolumeMissingDescription": "<code>{appDataPath}</code> bolumenaren muntaia ez da behar bezala konfiguratu. Datu guztiak ezabatu egingo dira edukiontzia gelditu edo berriro hasten denean.", "components.Login.description": "{applicationName} aplikazioan saioa hasten duzun lehen aldia den<PERSON>, b<PERSON><PERSON><PERSON> helbide elektroniko bat gehitu behar duzu.", "components.PermissionEdit.usersDescription": "Eman baimena erabiltzaileak kudeatzeko. Baimen hau duten erabiltzaileek ezin dituzte administratzaile-pribilegioa duten erabiltzaileak aldatu edo pribilegio hau eman.", "components.Settings.SettingsAbout.betawarning": "Hau BETA softwarea da. Ezaugarriak apurtura edota desegonkorrak izan daitezke. <PERSON><PERSON><PERSON>, eman edozein arazoren berri <PERSON>!", "components.Settings.SettingsJobsCache.cacheDescription": "Je<PERSON><PERSON>rr<PERSON> kanpoko APIaren eskaerak cachean gordetzen ditu, errendimendua optimizatzeko eta beharrezkoak ez diren API deirik ez egiteko.", "components.Settings.jellyfinsettingsDescription": "Konfigu<PERSON><PERSON> zer<PERSON><PERSON><PERSON> {mediaServerName}-(e)rako. {mediaServerName} liburutegiak eskaneatzen ditu, zer eduki dagoen i<PERSON>.", "components.Settings.noDefaultServer": "Gutxienez {serverType} zerbitzari bat lehenetsi gisa markatu behar da {mediaType} eskaerak prozesatu ahal i<PERSON>teko.", "components.Settings.tautulliSettingsDescription": "<PERSON><PERSON><PERSON>, konfiguratu zure Tautulli zerbitzariaren ezarpenak. Jellyseerr-ek Tautulliren Plex multimedia erreprodukzioen historialaren datuak lortu ditu.", "components.Settings.plexsettingsDescription": "Konfiguratu zure Plex zerbitzariaren ezarpenak. <PERSON><PERSON>seerr-ek zure Plexeko liburutegiak eskaneatzen ditu edukiaren erabilgarritasuna zehazteko.", "components.Settings.webAppUrlTip": "Aukeran erabiltzaileak zure zerbitzariko web aplikaziora zuzendu, web-aplikazio \"ostatuaren\" ordez", "components.UserList.deleteconfirm": "<PERSON><PERSON><PERSON> al zaude erabiltzaile hau ezabatu nahi duzula? Bere eskaera guztiak betirako ezabatuko dira.", "components.Login.emailtooltip": "<PERSON><PERSON><PERSON><PERSON> ez da zertan zure {mediaServerName} instant<PERSON>ri lotu behar.", "components.ManageSlideOver.manageModalRemoveMediaWarning": "* Honek {mediaType} hau betirako e<PERSON> du {arr}(e)tik, fitxate<PERSON> guztiak barne.", "components.NotificationTypeSelector.mediafailedDescription": "Bidali jakinarazpenak multimedia eskaerak Radarr eta Sonarr-en gehitzeak huts egiten badu.", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "Jakinarazi beste erabiltzaileek automatikoki onartzen diren multimedia eskaera berriak bidaltzen dituztenean.", "components.NotificationTypeSelector.usermediafailedDescription": "Jakinarazi multimedia eskaerak Radarr edo Sonarr-en gehitzen ez direnean.", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "Bidali jakinarazpenak erabiltzaileek automatikoki onartzen diren multimedia eskaera berriak bidaltzen dituztenean.", "components.NotificationTypeSelector.usermediarequestedDescription": "Jakinarazi beste erabiltzaile batzuek onartu beharreko multimedia eskaerak bidaltzen dituztenean.", "components.NotificationTypeSelector.mediarequestedDescription": "Bidali jakinarazpenak erabiltzaileek onarpena behar duten multimedia eskaera berriak bidaltzen dituztenean.", "components.PermissionEdit.autorequestDescription": "Baimendu Plexen jarraipen-zerrendaren bidez 4K ez diren eskaerak automatikoki bidaltzeko.", "components.PermissionEdit.autorequestMoviesDescription": "Baimendu Plexen jarraipen-zerrendaren bidez 4K ez diren film eskaerak automatikoki bidaltzeko.", "components.PermissionEdit.autorequestSeriesDescription": "Baimendu Plexen jarraipen-zerrendaren bidez 4K ez diren telesail eskaerak automatikoki bidaltzeko.", "components.RequestModal.SearchByNameModal.notvdbiddescription": "<PERSON>zin izan dugu telesail hau automatikoki lotu. <PERSON><PERSON><PERSON>, hautatu bat da<PERSON><PERSON>.", "components.Settings.Notifications.chatIdTip": "<PERSON>i txat bat zure bot-arekin, gehitu <GetIdBotLink>@get_id_bot</GetIdBotLink> eta egin <code>/my_id</code> komandoa", "components.Settings.Notifications.encryptionTip": "<PERSON><PERSON>, TLS inplizituak 465 portua eta STARTTLSk 587 portua erabiltzen dute", "components.Settings.RadarrModal.tagRequestsInfo": "Gehitu etiketa gehigarri bat automatikoki eskaeraren erabiltzaile ID eta erabiltzaile-izenarekin", "components.Settings.SettingsLogs.logsDescription": "Erregistro horiek zuzenean ikus ditzakezu <code>stdout</code> edo <code>{appDataPath}/logs/jellyseerr.log</code> bidez.", "components.Settings.SonarrModal.tagRequestsInfo": "Gehitu etiketa gehigarri bat automatikoki eskaeraren erabiltzaile ID eta erabiltzaile-izenarekin", "components.Settings.restartrequiredTooltip": "<PERSON><PERSON><PERSON><PERSON> berra<PERSON>i egin behar da aldaketa hauek indarrean jartzeko", "components.UserList.passwordinfodescription": "Konfiguratu aplikazioaren URL bat eta gaitu posta elektronikoko jakinarazpenak pasahitz sortze automatikoa ahalbidetzeko.", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "Arazoren bat izan da pasahitza gorde<PERSON>. Zure uneko pasahitza behar bezala sartu duzu?", "components.Settings.Notifications.webhookRoleIdTip": "Webhook mezuan aipatu beharreko rolaren IDa. Utzi hutsik aipamenak desgaitzeko", "components.Discover.resetwarning": "Berrezarri kontrol irristari guztiak modu lehenetsira. Honek kontrol irristari pertsonalizatuak ere ezabatuko ditu!", "components.RequestModal.QuotaDisplay.quotaLink": "<PERSON><PERSON> es<PERSON>-mugen laburpena ikus dezakezu zure <ProfileLink>profil-or<PERSON></ProfileLink>.", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "Web push jakinarazpen<PERSON> j<PERSON>, <PERSON><PERSON><PERSON>rr HTTPS bidez zerbit<PERSON><PERSON> behar da.", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {Gait<PERSON>ko iragazki #} other {Gaitutako # iragazki}}", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {Gait<PERSON>ko iragazki #} other {Gaitutako # iragazki}}", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {Gait<PERSON>ko iragazki #} other {Gaitutako # iragazki}}", "components.IssueModal.CreateIssueModal.providedetail": "<PERSON><PERSON><PERSON>, eman aurkitu duzun arazoaren azalpen zehatza.", "components.NotificationTypeSelector.mediaautorequestedDescription": "Jakinarazi multimedia eskaera berriak automatikoki bidaltzen direnean zure jarraipen-zerrendan.", "components.PermissionEdit.viewissuesDescription": "Baimendu beste erabiltzaileek sortutako multimedia arazoak ikustea.", "components.PermissionEdit.viewrecentDescription": "Baimendu orain dela gutxi gehitutako multimedia-zerrenda ikustea.", "components.PermissionEdit.viewrequestsDescription": "Baimendu beste erabiltzaile batzuek bidalitako bitarteko-eskaerak ikustea.", "components.RequestButton.approve4krequests": "Onartu {requestCount, plural, one {4K Request} other {{requestCount} 4K Requests}}", "components.RequestButton.decline4krequests": "Ukatu {requestCount, plural, one {4K Request} other {{requestCount} 4K Requests}}", "components.RequestModal.QuotaDisplay.allowedRequests": "<strong> {limit} </strong> {type} eska<PERSON> egin ditzakezu <strong>{days}</strong> egun<PERSON>.", "components.RequestModal.SearchByNameModal.nomatches": "<PERSON><PERSON> izan da telesail honetarako bat-etortze bat aurkitu.", "components.ResetPassword.validationpasswordminchars": "Pasahitza laburregia da; gutxienez 8 karaktere izan beharko lituzke", "components.Settings.Notifications.botUsernameTip": "Baimendu erabiltzaileei zure bot-ekin txat bat hasi eta jakinarazpenak konfiguratzen", "components.Settings.SettingsMain.cacheImagesTip": "Kanpotik ateratako irud<PERSON> cacheatu (biltegi kantitate handia behar du)", "components.Settings.jellyfinlibrariesDescription": "{mediaServerName}-(e)k eskaneatzen dituen liburutegiak. Egin klik beheko botoian liburutegirik zerrendatzen ez bada.", "components.Settings.scanbackground": "Eskaneatzea atzeko planoan egongo da. Bitartean konfigurazio prozesua jarraitu de<PERSON>.", "components.UserList.usercreatedfailedexisting": "Emandako helbide elektronikoa dagoeneko beste erabiltzaile batek darabil.", "components.UserList.validationpasswordminchars": "Pasahitza laburregia da; gutxienez 8 karaktere izan beharko lituzke", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailureEmailEmpty": "Beste erabiltzaile batek dagoeneko erabiltzaile-izen hori du. Helbide elektronikoa ezarri behar duzu", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "<PERSON>z duzu baimenik erabiltzaile honen pasahitza aldatzeko.", "components.UserProfile.UserSettings.unauthorizedDescription": "Ez duzu baimenik erabiltzaile honen ezarpenak aldatzeko.", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "Pasahitza laburregia da; gutxienez 8 karaktere izan beharko lituzke", "components.Settings.SettingsUsers.localLoginTip": "Baimendu erabiltzaileei haien helbide elektronikoa eta pasahitza erabiliz saioa hasten", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "Erabiltz<PERSON>e honek <strong> {limit} </strong> {type} eska<PERSON> egin ditzake <strong>{days}</strong> egun<PERSON>.", "components.RequestModal.QuotaDisplay.quotaLinkUser": "Erabiltzailearen eskaera-mugen laburpena ikus dezakezu <ProfileLink> profil or<PERSON></ProfileLink>.", "components.RequestModal.requestmovies4k": "Eskatu {count} {count, plural, one {film} other {film}} 4K-n", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {0} other {<strong>#</strong>}} {type} {remaining, plural, one {eskaera} other {eskaera}} geratzen dira", "components.Discover.CreateSlider.needresults": "Gutxienez emaitza 1 behar duzu.", "components.Discover.CreateSlider.editsuccess": "Irristaria editatuta eta pertsonalizazio ezarpenak gordeta.", "components.Discover.PlexWatchlistSlider.emptywatchlist": "Zure <PlexWatchlistSupportLink>Plex jarraipen-zerrendara</PlexWatchlistSupportLink> gehitutako multimedia hemen agertuko da.", "components.Discover.emptywatchlist": "Zure <PlexWatchlistSupportLink>Plex jarraipen-zerrendara</PlexWatchlistSupportLink> gehitutako multimedia hemen agertuko da.", "components.Discover.resetfailed": "Zerbait gaizki atera da pertsonalizazioaren konfigurazioa berrezar<PERSON>ean.", "components.IssueDetails.IssueComment.areyousuredelete": "<PERSON><PERSON><PERSON> zaude i<PERSON> hau ezabatu nahi duzula?", "components.IssueDetails.deleteissueconfirm": "<PERSON><PERSON><PERSON> zaude intz<PERSON>tzia hau ezabatu nahi duzula?", "components.Login.adminerror": "Administratzaile kontu bat erabili behar duzu saioa hasteko.", "components.Login.validationHostnameRequired": "Balioz<PERSON>-izen edo IP helbide bat eman behar duzu", "components.Login.validationUrlBaseTrailingSlash": "URLa ez du amaierako barra batean amaitu behar", "components.NotificationTypeSelector.adminissuereopenedDescription": "Jakinarazi beste erabiltzaile batzuek intzidentziak berriro i<PERSON> dituztenean.", "components.NotificationTypeSelector.adminissueresolvedDescription": "Jakinarazi beste erabiltzaile batzuek intzidentziak konpontzen dituztenean.", "components.NotificationTypeSelector.userissuecommentDescription": "Jakinarazki intzidentziak iruzkin berriak jasotzen dituz<PERSON>ean.", "components.Settings.Notifications.NotificationsPushover.validationTypes": "Gutxienez jakinarazpen mota bat aukeratu behar duzu", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicationRegistrationLink>Erregistratu aplikazio bat</ApplicationRegistrationLink> Jellyseerr-en erabiltzeko", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "Baliozko aplikazio token bat eman behar duzu", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "Baliozko JSON edukia eman behar duzu", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "Web push proba jakinarazpena bidaltzeak huts egin du.", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "Web push jakinarazpenen ezarpenak gordetzeak huts egin du.", "components.Settings.Notifications.botApiTip": "<CreateBotLink>Sortu bot bat</CreateBotLink> Je<PERSON><PERSON><PERSON>-en erabiltzeko", "components.Settings.Notifications.validationSmtpHostRequired": "Balioz<PERSON>-izen edo IP helbide bat eman behar duzu", "components.Settings.Notifications.validationPgpPrivateKey": "Baliozko PGP gako pribatu bat eman behar duzu", "components.Settings.Notifications.validationWebhookRoleId": "Baliozko Discord rol baten IDa eman behar duzu", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "URLa ez du amaierako barra batean amaitu behar", "components.Settings.RadarrModal.validationHostnameRequired": "Balioz<PERSON>-izen edo IP helbide bat eman behar duzu", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "URLak ez du barra batean amaitu behar", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "{jobScheduleHours, plural, one {hour} other {{jobScheduleHours} hours}}", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "{jobScheduleMinutes, plural, one {minute} other {{jobScheduleMinutes} minutes}}", "components.Settings.SettingsJobsCache.editJobScheduleSelectorSeconds": "{jobScheduleSeconds, plural, one {second} other {{jobScheduleSeconds} seconds}}", "components.Settings.SettingsMain.toastApiKeyFailure": "Zerbait gaizki joan da <PERSON> gako berria <PERSON>.", "components.Settings.SettingsUsers.newPlexLoginTip": "Onartu {mediaServerName} erabiltzaileak saioa hastea inportatu gabe", "components.Settings.SettingsMain.validationApplicationUrlTrailingSlash": "URLak ez du barra batean amaitu behar", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "URLa ez du amaierako barra batean amaitu behar", "components.Settings.SonarrModal.validationHostnameRequired": "Balioz<PERSON>-izen edo IP helbide bat eman behar duzu", "components.Settings.deleteserverconfirm": "<PERSON><PERSON><PERSON> zaude zerbitzari hau ezabatu nahi duzula?", "components.Settings.experimentalTooltip": "Ezarpen hau gaitzeak aplikazioaren ustekabeko portaera sortu dezake", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "URLak ez du barra batean amaitu behar", "components.Settings.jellyfinSyncFailedAutomaticGroupedFolders": "Autentifikazio pertsonalizatua liburutegiko taldekatze automatikoarekin ez da onartzen", "components.Settings.validationHostnameRequired": "Balioz<PERSON>-izen edo IP helbide bat eman behar duzu", "components.Settings.validationUrlBaseTrailingSlash": "URLa ez da barra batean amaitu behar", "components.Settings.validationUrlTrailingSlash": "URLak ez du barra batean amaitu behar", "components.StatusChecker.appUpdatedDescription": "<PERSON><PERSON><PERSON>, egin klik azpiko botoian aplikazioa freskatzeko.", "components.StatusChecker.restartRequiredDescription": "<PERSON><PERSON><PERSON>, berrabiarazi zerbitzaria eguneratutako ezarpenak aplikatzeko.", "components.TvDetails.episodeCount": "{episodeCount, plural, one {Atal #} other {# atal}}", "components.UserList.importedfromJellyfin": "<strong>{userCount}</strong> {mediaServerName} {userCount, plural, one {erabiltzaile} other {erabiltzaile}} ondo inportatu dira!", "components.UserList.importedfromplex": "<strong>{userCount}</strong> Plex {userCount, plural, one {erabiltzaile} other {erabiltzaile}} ondo inportatu dira!", "components.TvDetails.seasons": "{seasonCount, plural, one {Denbor<PERSON><PERSON> #} other {# denboraldi}}", "components.UserList.localLoginDisabled": "<strong><PERSON><PERSON><PERSON> tokiko sa<PERSON></strong> e<PERSON><PERSON>a des<PERSON>ta dago.", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "Zure Discord kontuarekin bat datorren <FindDiscordIdLink>multi-digitu ID zenbakia</FindDiscordIdLink>", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "Baliozko Discord erabiltzaile ID bat eman behar duzu", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "<PERSON><PERSON> kontuarekin bat datorren <FindDiscordIdLink>multi-digitu ID zenbakia</FindDiscordIdLink>", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "Baliozko erabiltzaile edo talde-gakoa eman behar duzu", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "Baliozko PGP gako publiko bat eman behar duzu", "components.UserProfile.emptywatchlist": "Zure <PlexWatchlistSupportLink>Plex jarraipen-zerrendara</PlexWatchlistSupportLink> gehitutako multimedia hemen agertuko da.", "components.RequestModal.requestseasons4k": "Eskatu {seasonCount} {seasonCount, plural, one {denboraldi} other {denboraldi}} 4K-n", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink><PERSON>i txat bat</TelegramBotLink>, gehitu <GetIdBotLink>@get_id_bot</GetIdBotLink>, eta egin <code>/my_id</code> komandoa", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "Gutxienez jakinarazpen mota bat aukeratu behar duzu", "components.Settings.Notifications.validationTypes": "Gutxienez jakinarazpen mota bat aukeratu behar duzu", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "Sortu token bat zure <PushbulletSettingsLink>kontuaren ezarpenetatik</PushbulletSettingsLink>", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "Baliozko erabiltzaile edo talde-gakoa eman behar duzu", "components.Settings.advancedTooltip": "Ezarpen hau gaizki konfiguratzeak funtzionalitatea apurtu dezake", "components.Settings.Notifications.NotificationsSlack.validationTypes": "Gutxienez jakinarazpen mota bat aukeratu behar duzu", "components.Discover.CreateSlider.addsuccess": "Irristari berria sortu eta pertsonalizazio ezarpenak gorde dira.", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "Gutxienez jakinarazpen mota bat aukeratu behar duzu", "components.Discover.CreateSlider.validationDatarequired": "Balio bat eman behar duzu.", "components.Discover.FilterSlideover.voteCount": "Bozka zenbakia {minValue} eta {maxValue} artean", "components.Discover.updatefailed": "Zerbait gaizki atera da ezarpen pertsonalizatuak eguneratzean.", "components.IssueDetails.toaststatusupdatefailed": "Zerbait gaizki atera da intzidentziaren egoera eguneratzean.", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {Atal} other {Atal}}", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}}", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {commit} other {commit}} atzetik", "components.IssueModal.CreateIssueModal.toastFailedCreate": "Zerbait gaizki joan da intzidentzia bid<PERSON>.", "components.Login.loginerror": "Zerbait gaizki atera da saioa hastean.", "components.Login.validationUrlTrailingSlash": "URLak ez du barra batean amaitu behar", "components.Login.validationPortRequired": "<PERSON><PERSON><PERSON> ataka zenbakia eman behar da", "components.Login.validationUrlBaseLeadingSlash": "URLak ez du hasierako barra bat izan behar", "components.Login.validationemailrequired": "Baliozko helbide elektroniko bat eman behar duzu", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> {playCount, plural, one {erreprodukzio} other {erreprodukzio}}", "components.ManageSlideOver.markallseasons4kavailable": "Markatu 4K-n eskuragarri dauden denboraldi guztiak", "components.MovieDetails.releasedate": "{releaseCount, plural, one {Kaleratze data} other {Kaleratze datak}}", "components.NotificationTypeSelector.adminissuecommentDescription": "Jakinarazi beste erabiltzaileek intzidentziatan iruzkinak bidaltzen dituztenean.", "components.MovieDetails.productioncountries": "Produkzioa {countryCount, plural, one {herrialde} other {herrialde}}", "components.NotificationTypeSelector.issuecommentDescription": "<PERSON><PERSON>i j<PERSON>azpenak intzidentziak iruzkin berriak jasotzen di<PERSON>.", "components.NotificationTypeSelector.mediaapprovedDescription": "Bidali jakinarazpenak multimedia eskaerak eskuz onartzen direnean.", "components.NotificationTypeSelector.userissuereopenedDescription": "Jakinarazki zuk bidalitako intzidentziak berriro i<PERSON> bad<PERSON>.", "components.NotificationTypeSelector.userissueresolvedDescription": "Jakinarazki zuk bidalitako intzidentziak konpontzen badira.", "components.NotificationTypeSelector.usermediaapprovedDescription": "Jakinarazki zure multimedia eskaerak onartzen badira.", "components.NotificationTypeSelector.usermediaavailableDescription": "Jakinarazi zure multimedia eskaerak eskuragarri egiten badira.", "components.NotificationTypeSelector.usermediadeclinedDescription": "Jakinarazi zure multimedia eskaerak ukatzen badira.", "components.PermissionEdit.adminDescription": "Administratzaile sarbide osoa. Beste baimen egiaztapen guztiak saihesten ditu.", "components.PermissionEdit.advancedrequestDescription": "Baimendu multimedia eskaera aukera aurreratuak editatzea.", "components.PermissionEdit.autoapprove4kDescription": "Baimendu onarpen automatikoa 4K multimedia eskaeretarako.", "components.NotificationTypeSelector.mediaavailableDescription": "Bidali jakinarazpenak multimedia eskaerak eskuragarri daudenean.", "components.NotificationTypeSelector.mediadeclinedDescription": "Bidali jakinarazpenak multimedia eskaerak ukatzen direnean.", "components.NotificationTypeSelector.userissuecreatedDescription": "Jakinarazki beste erabiltzaileek intzidentziak irekitzen dituztenean.", "components.PermissionEdit.autoapprove4kMoviesDescription": "Onartu 4K filma eskaerak automatikoki.", "components.PermissionEdit.autoapproveDescription": "Baimendu onarpen automatikoa ez-4K multimedia eskaeretarako.", "components.PermissionEdit.request4kDescription": "Baimendu 4K multimedia eskatzea.", "components.PermissionEdit.request4kMoviesDescription": "Baimendu 4K film eskaerak bidaltzea.", "components.PermissionEdit.request4kTvDescription": "Baimendu 4K telesail eskaerak bidaltzea.", "components.PermissionEdit.requestDescription": "Baimendu ez-4K multimedia eskaerak bidaltzea.", "components.PermissionEdit.requestMoviesDescription": "Baimendu ez-4K film eskaerak bidaltzea.", "components.PermissionEdit.requestTvDescription": "Baimendu ez-4K telesail eskaerak bidaltzea.", "components.PermissionEdit.autoapprove4kSeriesDescription": "Onartu 4K telesailak automatikoki.", "components.PermissionEdit.autoapproveMoviesDescription": "Onartu ez-4K filma eskaerak automatikoki.", "components.PermissionEdit.autoapproveSeriesDescription": "Onartu ez-4K telesailen eskaerak automatikoki.", "components.PermissionEdit.viewwatchlistsDescription": "Baimendu beste erabiltzaileen {mediaServerName} jarraipen-zerrendak ikustea.", "components.QuotaSelector.seasons": "{count, plural, one {denbor<PERSON>i} other {denboraldi}}", "components.RequestBlock.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}}", "components.RequestButton.approverequests": "Onartu {requestCount, plural, one {Request} other {{requestCount} Requests}}", "components.RequestButton.declinerequests": "Ukatu {requestCount, plural, one {Request} other {{requestCount} Requests}}", "components.RequestCard.failedretry": "Zerbait gaizki joan da eskaera berri<PERSON>.", "components.RequestList.RequestItem.failedretry": "Zerbait gaizki joan da eskaera berri<PERSON>.", "components.RequestCard.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}}", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}}", "components.RequestModal.AdvancedRequester.animenote": "* Telesail hau anime bat da.", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {denbor<PERSON>i} other {denboraldi}}", "components.RequestModal.errorediting": "Zerbait gaizki joan da eskaera editatzean.", "components.RequestModal.requesterror": "Zerbait gaizki joan da es<PERSON><PERSON>.", "components.RequestModal.requestseasons": "Eskatu {seasonCount} {seasonCount, plural, one {denboraldia} other {denboraldiak}}", "components.ResetPassword.validationemailrequired": "Baliozko helbide elektroniko bat eman behar duzu", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "Gotify jakinarazpen ezarpenak gordetzeak huts egin du.", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "URLak ez du barra batean amaitu behar", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "Baliozko URL bat eman behar duzu", "components.Settings.Notifications.validationBotAPIRequired": "Bot baimen token bat eman behar duzu", "components.Settings.Notifications.validationChatIdRequired": "Baliozko txat ID bat eman behar duzu", "components.Settings.Notifications.validationEmail": "Baliozko helbide elektroniko bat eman behar duzu", "components.Settings.Notifications.validationSmtpPortRequired": "<PERSON><PERSON><PERSON> ataka zenbaki bat eman behar duzu", "components.Settings.Notifications.webhookUrlTip": "Sortu <DiscordWebhookLink>webhook integrazio</DiscordWebhookLink> bat zure zer<PERSON><PERSON>ian", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "URLak ez du hasierako barra bat izan behar", "components.Settings.RadarrModal.validationPortRequired": "<PERSON><PERSON><PERSON> ataka zenbaki bat eman behar duzu", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "Zerbait gaizki joan da zeregina gordetz<PERSON>.", "components.Settings.SettingsMain.generalsettingsDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-en ezarpen global eta lehenetsiak.", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "<PERSON><PERSON><PERSON><PERSON><PERSON> hasierako barra bat izan behar du", "components.Settings.SonarrModal.validationPortRequired": "<PERSON><PERSON><PERSON> ataka zenbakia eman behar da", "components.Settings.jellyfinSettingsFailure": "Zerbait gaizki joan <PERSON> {mediaServerName} e<PERSON><PERSON><PERSON> gorde<PERSON>.", "components.Settings.serverpresetLoad": "Sakatu botoia zerbitzari eskuragarriak kargatzeko", "components.Settings.toastTautulliSettingsFailure": "Zerbait gaizki joan <PERSON> ezarpenak go<PERSON>.", "components.Settings.validationPortRequired": "<PERSON><PERSON><PERSON> ataka zenbaki bat eman behar duzu", "components.Settings.validationUrlBaseLeadingSlash": "URLak ez du hasierako barra bat izan behar", "components.Setup.subtitle": "Hasi zure multimedia zerbitzaria aukeratzearekin", "components.TvDetails.Season.somethingwentwrong": "Zerbait gaizki joan da denboraldiaren datuak lortzerakoan.", "components.TvDetails.productioncountries": "Produkzioa {countryCount, plural, one {herrialde} other {herrialde}}", "components.UserList.autogeneratepasswordTip": "Bidali zerbitzariak sortutako pasahitza erabiltzaileari e-posta bidez", "components.UserList.importfromJellyfinerror": "Zerbait gaizki joan da {mediaServerName} erabiltzaileak inportatzerakoan.", "components.UserList.importfromplexerror": "Zerbait gaizki joan da Plex erabiltzaileak inportatzerakoan.", "components.UserList.noJellyfinuserstoimport": "Ez dago {mediaServerName} erabiltzailerik inportatzeko.", "components.UserList.nouserstoimport": "Ez dago Plex erabiltzailerik inportatzeko.", "components.UserList.usercreatedfailed": "Zerbait gaizki joan da erabiltzailea sortzerakoan.", "components.UserList.userdeleteerror": "Zerbait gaizki joan da erabiltzailea ezabatzerakoan.", "components.UserList.userfail": "Zerbait gaizki joan da <PERSON>biltzailearen baimen<PERSON> go<PERSON>.", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "Automatikoki eskatu zure <PlexWatchlistSupportLink>Plex jarraipen zerrendan</PlexWatchlistSupportLink> dauden filmak", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "Sortu token bat zure <PushbulletSettingsLink>kontuaren ezarpenetatik</PushbulletSettingsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "Baliozko erabiltzaile ID bat eman behar duzu", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "Baliozko aplikazio token bat eman behar duzu", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "Baliozko txat ID bat eman behar duzu", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "Web push jakinarazpenen ezarpenak gordetzeak huts egin du.", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "Zerbait gaizki joan da pasa<PERSON>za go<PERSON>.", "i18n.removeFromBlacklistSuccess": "<strong>{title}</strong> zer<PERSON>da beltzetik ondo kendu da.", "i18n.showingresults": "<strong>{total}</strong>(e)tik <strong>{from}</strong>(e)tik <strong>{to}</strong>(e)ra emait<PERSON> erakusten", "components.IssueDetails.toasteditdescriptionfailed": "Zerbait gaizki atera da intzidentziaren deskribapena editatzean.", "components.RequestModal.requestmovies": "Eskatu {count} {count, plural, one {film} other {film}}", "components.IssueDetails.toastissuedeletefailed": "Zerbait gaizki joan da intzidentzia ezabatzean.", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "Automatikoki eskatu zure <PlexWatchlistSupportLink>Plex jarraipen zerrendan</PlexWatchlistSupportLink> dauden telesailak", "components.Settings.Notifications.NotificationsGotify.validationTypes": "Gutxienez jakinarazpen mota bat aukeratu behar duzu", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "<PERSON>z da beharrezkoa profil <code>lehenetsia</code> erabiltzen ari bazara", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "Gutxienez jakinarazpen mota bat aukeratu behar duzu", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {film} other {film}}", "components.RequestModal.requestadmin": "Eskaera hau automatikoki onartuko da.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "Gotify proba jakinarazpena bidaltzeak huts egin du.", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "LunaSea proba jakinarazpena bidaltzeak huts egin du.", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "Baliozko URL bat eman behar duzu", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "Pushbullet jakinarazpen ezarpenak gordetzeak huts egin du.", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "<PERSON><PERSON> erabiltzaile edo gailuan oinarri<PERSON> <LunaSeaLink>jakinarazpen WebHook URLa</LunaSeaLink>", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "Aplikazio token bat eman behar duzu", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "LunaSea jakinarazpen ezarpenak gordetzeak huts egin du.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "<ApplicationRegistrationLink>Erregistratu aplikazio bat</ApplicationRegistrationLink> {applicationTitle}-(r)ekin erabil<PERSON><PERSON>o", "components.Blacklist.blacklistNotFoundError": "<strong>{title}</strong> ez dago zerrenda beltzean.", "components.Blacklist.blacklistSettingsDescription": "Kudeatu zerrenda beltzean dagoen multimedia.", "components.Discover.CreateSlider.editfail": "Irristaria editatzeak huts egin du.", "components.Discover.CreateSlider.providetmdbkeywordid": "Eman TMDB gako ID bat", "components.Discover.CreateSlider.providetmdbnetwork": "Eman TMDB sarearen IDa", "components.Discover.CreateSlider.providetmdbsearch": "<PERSON><PERSON> bi<PERSON>-k<PERSON><PERSON><PERSON> bat", "components.Discover.CreateSlider.validationTitlerequired": "Izenburu bat eman behar du<PERSON>.", "components.Discover.CreateSlider.starttyping": "<PERSON>i id<PERSON> bi<PERSON>.", "components.Discover.CreateSlider.searchKeywords": "<PERSON><PERSON><PERSON> gako hitzak…", "components.Discover.CreateSlider.slidernameplaceholder": "Irristar<PERSON>en izena", "components.Discover.DiscoverMovieLanguage.languageMovies": "Film {language}-(e)ak", "components.Discover.DiscoverMovies.sortPopularityAsc": "<PERSON><PERSON><PERSON> go<PERSON>", "components.Discover.DiscoverMovies.sortPopularityDesc": "<PERSON><PERSON><PERSON> be<PERSON>", "components.Discover.DiscoverSliderEdit.deletefail": "Irristaria ezabatzeak huts egin du.", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "Lehen emisioaren data gorantz", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "Lehen emisioaren data beherantz", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "Kaleratze data gorantz", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "Kaleratze data beherantz", "components.Discover.DiscoverMovies.sortTitleAsc": "<PERSON><PERSON><PERSON><PERSON> (A-Z) gorantz", "components.Discover.DiscoverMovies.sortTitleDesc": "<PERSON><PERSON><PERSON><PERSON> (Z-A) beherantz", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "TMDB puntuazioa gorantz", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "TMDB puntuazioa beherantz", "components.Discover.DiscoverNetwork.networkSeries": "{network}-(e)ko telesailak", "components.Discover.DiscoverSliderEdit.enable": "Txandakatu ikusgarritasuna", "components.Discover.DiscoverStudio.studioMovies": "{studio}-(e)ko filmak", "components.Discover.DiscoverSliderEdit.deletesuccess": "Irristaria ondo ezabatu da.", "components.Discover.DiscoverTv.sortTitleAsc": "<PERSON><PERSON><PERSON><PERSON> (A-Z) gorantz", "components.Discover.DiscoverTv.sortTitleDesc": "<PERSON><PERSON><PERSON><PERSON> (Z-A) beherantz", "components.Discover.DiscoverTv.sortPopularityAsc": "<PERSON><PERSON><PERSON> go<PERSON>", "components.Discover.DiscoverTv.sortPopularityDesc": "<PERSON><PERSON><PERSON> be<PERSON>", "components.Discover.DiscoverTvGenre.genreSeries": "{genre}-(e)ko telesailak", "components.Discover.FilterSlideover.ratingText": "{minValue} eta {maxValue} art<PERSON><PERSON> b<PERSON>ak", "components.Discover.FilterSlideover.clearfilters": "Garbitu gait<PERSON>", "components.Discover.FilterSlideover.firstAirDate": "<PERSON>hen emisio data", "components.Discover.FilterSlideover.runtimeText": "{minValue}-{maxValue} minutuko <PERSON>", "components.Discover.FilterSlideover.from": "<PERSON><PERSON>di<PERSON>", "components.Discover.FilterSlideover.tmdbuservotecount": "TMDB erabiltzaileen bozka zenbakia", "components.Discover.FilterSlideover.tmdbuserscore": "TMDB erabiltzaileen puntuazioa", "components.Discover.FilterSlideover.to": "<PERSON><PERSON>", "components.Discover.NetworkSlider.networks": "Sareak", "components.Discover.createnewslider": "Sortu irristari berria", "components.Discover.customizediscover": "Pertsonalizatu aurkitu", "components.Discover.resettodefault": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.tmdbmoviegenre": "TMDB film generoa", "components.Discover.tmdbmoviekeyword": "TMDB film gakoa", "components.Discover.plexwatchlist": "<PERSON><PERSON>", "components.Discover.popularmovies": "Film ospetsuak", "components.Discover.populartv": "Telesail ospetsuak", "components.Discover.recentlyAdded": "<PERSON><PERSON> gut<PERSON> gehituta", "components.Discover.recentrequests": "<PERSON><PERSON><PERSON>", "components.Discover.stopediting": "Editatzen utzi", "components.Discover.discover": "Aurkitu", "components.Discover.moviegenres": "Film generoak", "components.Discover.tmdbmoviestreamingservices": "TMDB film streaming zerbitzuak", "components.Discover.resetsuccess": "Aurkitu pertsonalizazio ezar<PERSON>ak ondo berrezarri dira.", "components.DownloadBlock.formattedTitle": "{title}: {seasonNumber}. denboraldia {episodeNumber}. atala", "components.Discover.tmdbtvstreamingservices": "TMDB TV streaming zerbitzuak", "components.IssueDetails.IssueComment.postedby": "{username}-(e)k {relativeTime} datan bidalita", "components.Discover.updatesuccess": "Aurkitu pertsonalizazio ezarpenak eguneratu dira.", "components.Discover.tmdbtvgenre": "TMDB telesail generoa", "components.Discover.tmdbtvkeyword": "TMDB telesail gakoa", "components.Discover.tmdbnetwork": "TMDB sarea", "components.Discover.tmdbsearch": "TMDB bilaketa", "components.Discover.upcomingmovies": "<PERSON><PERSON>ngo filmak", "components.Discover.upcomingtv": "<PERSON><PERSON><PERSON> telesailak", "components.DownloadBlock.estimatedtime": "{time} estimatuta", "components.IssueDetails.IssueComment.delete": "Ezabatu iruzkina", "components.Discover.trending": "<PERSON><PERSON>", "components.IssueDetails.IssueComment.postedbyedited": "{username}-(e)k bidalita {relativeTime} datan (Editatuta)", "components.IssueDetails.IssueComment.validationComment": "<PERSON><PERSON> bat idatzi behar duzu", "components.IssueDetails.openedby": "#{issueId} irekita {relativeTime} datan {username}-(r)engatik", "components.IssueDetails.openin4karr": "Ireki 4K-n {arr}", "components.IssueDetails.closeissueandcomment": "Itxi iruzkinarekin", "components.IssueDetails.commentplaceholder": "<PERSON><PERSON><PERSON><PERSON> bat…", "components.IssueDetails.openinarr": "<PERSON><PERSON><PERSON> {arr}-en", "components.IssueDetails.play4konplex": "Ikusi 4K-n {mediaServerName}-(e)n", "components.IssueDetails.playonplex": "Ikusi {mediaServerName}-(e)n", "components.IssueDetails.reopenissueandcomment": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.toastissuedeleted": "<PERSON>tzidentzia ondo ezabatu da!", "components.IssueList.IssueItem.openeduserdate": "{date} {user} erabiltzailearengatik", "components.IssueDetails.toaststatusupdated": "Intzidentziaren egoera ondo editatu da!", "components.IssueList.showallissues": "Erakutsi intzidentzia guztiak", "components.IssueModal.CreateIssueModal.validationMessageRequired": "Deskribapen bat eman behar duzu", "components.Layout.UserWarnings.emailRequired": "Helbide elektronikoa beharrezkoa da.", "components.Layout.SearchInput.searchPlaceholder": "Bilatu filmak eta telesailak", "components.Layout.UserWarnings.emailInvalid": "Helbide elektronikoa baliogabea da.", "components.Layout.UserWarnings.passwordRequired": "<PERSON><PERSON><PERSON><PERSON> behar da.", "components.IssueModal.CreateIssueModal.reportissue": "<PERSON><PERSON>i <PERSON> bat", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "<strong>{title}</strong> intzidentzia txostena ondo bidali da!", "components.Login.credentialerror": "Erabiltzailea edo pasahitza baliogabeak dira.", "components.Login.invalidurlerror": "<PERSON><PERSON> da {mediaServerName} zerbitzarira konektatu.", "components.Login.validationEmailRequired": "Helbide elektroniko bat eman behar duzu", "components.Login.validationpasswordrequired": "Pa<PERSON><PERSON>z bat eman behar duzu", "components.Login.validationservertyperequired": "<PERSON><PERSON><PERSON>, hautatu zerbitzari mota bat", "components.Login.signinheader": "<PERSON>i sa<PERSON>a <PERSON>", "components.Login.signinwithjellyfin": "Erabili zure {mediaServerName} kontua", "components.Login.signinwithoverseerr": "Erabili zure {applicationTitle} kontua", "components.Login.signinwithplex": "Erabili zure Plex kontua", "components.Layout.VersionStatus.outofdate": "Zaharkituta", "components.Login.validationemailformat": "Baliozko helbide elektronikoa behar da", "components.Login.validationhostformat": "Balioz<PERSON> U<PERSON> behar da", "components.Login.validationhostrequired": "{mediaServerName} <PERSON><PERSON><PERSON> behar da", "components.ManageSlideOver.mark4kavailable": "Markatu 4K-n eskuragarri", "components.ManageSlideOver.markallseasonsavailable": "Markatu denboraldi guztiak es<PERSON>gar<PERSON>", "components.MovieDetails.mark4kavailable": "Markatu 4K-n eskuragarri", "components.ManageSlideOver.openarr4k": "Ireki 4K-n {arr}", "components.ManageSlideOver.pastdays": "Azken {days, number} egunetan", "components.ManageSlideOver.removearr4k": "Kendu 4K {arr}", "components.MovieDetails.openradarr": "Ireki filma Radarr-en", "components.ManageSlideOver.markavailable": "<PERSON><PERSON><PERSON> es<PERSON> gisa", "components.ManageSlideOver.openarr": "<PERSON><PERSON><PERSON> {arr}-en", "components.ManageSlideOver.opentautulli": "<PERSON><PERSON><PERSON>", "components.ManageSlideOver.removearr": "<PERSON><PERSON> {arr}-etik", "components.MovieDetails.addtowatchlist": "Gehitu jarraipen zerrendara", "components.MovieDetails.imdbuserscore": "IMDB erabiltzaileen puntuazioa", "components.MovieDetails.markavailable": "<PERSON><PERSON><PERSON> es<PERSON> gisa", "components.MovieDetails.openradarr4k": "Ireki filma 4K Radarr-en", "components.MovieDetails.studio": "{studioCount, plural, one {<PERSON>studio} other {Estudio}}", "components.NotificationTypeSelector.issuecreatedDescription": "<PERSON><PERSON><PERSON> j<PERSON>penak intzidentziak sortzen direnean.", "components.NotificationTypeSelector.issuereopenedDescription": "<PERSON><PERSON><PERSON> j<PERSON>ak intzidentziak berriro i<PERSON> direnea<PERSON>.", "components.NotificationTypeSelector.issueresolvedDescription": "<PERSON><PERSON><PERSON> j<PERSON>ak intzidentziak konpontzen direnean.", "components.MovieDetails.watchlistError": "Zerbait gaizki joan <PERSON>, sa<PERSON><PERSON> be<PERSON>.", "components.MovieDetails.play4k": "Ikusi 4K-n {mediaServerName}-(e)n", "components.MovieDetails.rtaudiencescore": "Rotten Tomatoes audientzia puntuazioa", "components.MovieDetails.play": "Ikusi {mediaServerName}-(e)n", "components.MovieDetails.removefromwatchlist": "Kendu jarraipen zerrendatik", "components.MovieDetails.reportissue": "<PERSON><PERSON>i <PERSON> bat", "components.MovieDetails.rtcriticsscore": "Rotten Tomatoes Tomatometer", "components.MovieDetails.streamingproviders": "<PERSON><PERSON> emititzen hemen", "components.MovieDetails.tmdbuserscore": "TMDB erabiltzaileen puntuazioa", "components.MovieDetails.viewfullcrew": "Ikus<PERSON> talde osoa", "components.NotificationTypeSelector.mediaAutoApproved": "Eskaera automatikoki onartuta", "components.MovieDetails.similar": "Antzeko filmak", "components.MovieDetails.watchlistDeleted": "<strong>{title}</strong> jarraipen-zerrendatik ondo kendu da!", "components.MovieDetails.watchlistSuccess": "<strong>{title}</strong> ondo gehitu da jarraipen zerrendara!", "components.NotificationTypeSelector.mediaautorequested": "Eskaera automatikoki bidalita", "components.NotificationTypeSelector.mediafailed": "Eskaera prozesatzeak huts egin du", "components.NotificationTypeSelector.mediarequested": "Eskaera onartzeko zain dago", "components.PermissionEdit.autoapprove4kMovies": "Auto-onartu 4K filmak", "components.PermissionEdit.autoapprove4kSeries": "Auto-onartu 4K telesailak", "components.PermissionEdit.manageissuesDescription": "Baimendu multimedia intzidentziak kudeatzea.", "components.PermissionEdit.manageblacklistDescription": "Baimendu zerrenda beltzean dagoen multimedia kudeatzea.", "components.PermissionEdit.createissuesDescription": "Baimendu multimedia intzidentziak sortzea.", "components.PermissionEdit.blacklistedItemsDescription": "Baimendu multimedia zerrenda beltzean jartzea.", "components.PermissionEdit.request4kMovies": "Eskatu 4K filmak", "components.PermissionEdit.request4kTv": "Eskatu 4K telesailak", "components.PermissionEdit.viewblacklistedItems": "Ikusi zerrenda beltzean dagoen multimedia.", "components.PermissionEdit.blacklistedItems": "Multimedia zerrenda beltzean sartu.", "components.PermissionEdit.viewblacklistedItemsDescription": "Baimendu zerrenda beltzean dagoen multimedia ikustea.", "components.QuotaSelector.days": "{count, plural, one {egun} other {egun}}", "components.QuotaSelector.movies": "{count, plural, one {film} other {film}}", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{movies} {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{seasons} {quotaDays} {days}</quotaUnits>", "components.PersonDetails.alsoknownas": "Beste izen batzuk: {names}", "components.PermissionEdit.viewrecent": "<PERSON>kusi berriki gehituta", "components.PermissionEdit.viewwatchlists": "Ikusi {mediaServerName} jarraipen zerrendak", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.RequestBlock.lastmodifiedby": "Azkenengoz editatuta honengatik", "components.RegionSelector.regionDefault": "<PERSON><PERSON><PERSON><PERSON>", "components.RegionSelector.regionServerDefault": "Lehenetsia ({region})", "components.RequestBlock.rootfolder": "<PERSON><PERSON>", "components.RequestButton.requestmore4k": "Eskatu gehiago 4K-n", "components.RequestButton.approverequest4k": "Onartu 4K eskaera", "components.RequestButton.declinerequest4k": "Ukatu 4K eskaera", "components.RequestButton.viewrequest4k": "Ikusi 4K eskaera", "components.RequestCard.mediaerror": "{mediaType} ez da aurkitu", "components.RequestList.RequestItem.mediaerror": "{mediaType} ez da aurkitu", "components.RequestList.RequestItem.modifieduserdate": "{date} {user} erabiltzailearengatik", "components.RequestList.RequestItem.removearr": "<PERSON><PERSON> {arr}-etik", "components.RequestList.showallrequests": "Erakutsi eskaera guztiak", "components.RequestList.sortDirection": "Txandakatu ordenaren norabidea", "components.RequestModal.AdvancedRequester.rootfolder": "<PERSON><PERSON>", "components.RequestModal.numberofepisodes": "Atal #", "components.RequestModal.pending4krequest": "Onartzeke 4K eskaera", "components.RequestModal.pendingrequest": "Eskaera zain", "components.RequestModal.requestCancel": "<strong>{title}</strong> es<PERSON><PERSON> u<PERSON><PERSON>.", "components.RequestModal.requestSuccess": "<strong>{title}</strong> ondo eskatu da!", "components.RequestModal.requestcancelled": "<strong>{title}</strong> es<PERSON><PERSON> u<PERSON><PERSON>.", "components.RequestModal.requestfrom": "{username}-(r)en eskaera onartzeko zain dago.", "components.RequestModal.requestmovie4ktitle": "Eskatu filma 4K-n", "components.RequestModal.requestcollectiontitle": "Eskatu bilduma", "components.RequestModal.requestmovietitle": "Eskatu filma", "components.RequestModal.requestseriestitle": "Eskatu telesailak", "components.RequestModal.seasonnumber": "{number}. denboraldia", "components.ResetPassword.confirmpassword": "<PERSON><PERSON><PERSON><PERSON>", "components.ResetPassword.email": "Helbide elektronikoa", "components.ResetPassword.gobacklogin": "Itzuli saioa hasteko or<PERSON>ra", "components.ResetPassword.passwordreset": "<PERSON><PERSON><PERSON><PERSON>", "components.ResetPassword.resetpassword": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "components.ResetPassword.validationpasswordmatch": "<PERSON><PERSON><PERSON><PERSON> bat egin behar dute", "components.ResetPassword.resetpasswordsuccessmessage": "<PERSON><PERSON><PERSON><PERSON> ondo berrezarri da!", "components.ResetPassword.validationpasswordrequired": "Pa<PERSON><PERSON>z bat eman behar duzu", "components.Search.searchresults": "<PERSON><PERSON><PERSON><PERSON>", "components.Selector.inProduction": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Selector.nooptions": "Emaitzarik ez.", "components.Selector.returningSeries": "Itzulitako telesaila", "components.Selector.searchKeywords": "<PERSON><PERSON><PERSON> gako hitzak…", "components.Selector.searchStudios": "Bilatu estudioak…", "components.Selector.showmore": "Erakutsi gehiago", "components.Selector.starttyping": "<PERSON>i id<PERSON> bi<PERSON>.", "components.Settings.Notifications.NotificationsGotify.agentenabled": "G<PERSON><PERSON> <PERSON><PERSON>", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Gotify proba jakinarazpena bidalita!", "components.Settings.Notifications.NotificationsGotify.token": "Aplikazioaren tokena", "components.Settings.Notifications.NotificationsGotify.url": "Zerbitzariaren URLa", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "G<PERSON><PERSON> <PERSON><PERSON>", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "Webhook URLa", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "Sarbide tokena", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "<PERSON><PERSON><PERSON><PERSON> et<PERSON>ta", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "Pushbullet proba jakinarazpena bidaltzen…", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Pushbullet proba jakinarazpena bidalita!", "components.Settings.Notifications.NotificationsPushover.accessToken": "Aplikazioaren API tokena", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "Pushover jakinarazpen ezarpenak gordetzeak huts egin du.", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "Pushover proba jakinarazpena bidaltzeak huts egin du.", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "Zure 30 karaktereko <UsersGroupsLink>erabiltzaile edo talde identifikatzailea</UsersGroupsLink>", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "Pushover proba jaki<PERSON><PERSON><PERSON>a bid<PERSON>…", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Pushover proba jakinarazpena bidalita!", "components.Settings.Notifications.NotificationsPushover.userToken": "Erabiltzaile edo talde gakoa", "components.Settings.Notifications.NotificationsPushover.agentenabled": "G<PERSON><PERSON> <PERSON><PERSON>", "components.Settings.Notifications.NotificationsPushover.deviceDefault": "<PERSON><PERSON>", "components.Settings.Notifications.NotificationsPushover.sound": "Jakinarazpen soinua", "components.Settings.Notifications.NotificationsSlack.agentenabled": "G<PERSON><PERSON> <PERSON><PERSON>", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Push<PERSON> jakinarazpenen ezarpenak ondo gorde dira!", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "Slack proba jakinarazpena bidaltzeak huts egin du.", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "Baliozko URL bat eman behar duzu", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "Slack proba jaki<PERSON><PERSON><PERSON>a bid<PERSON>…", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "Slack proba jakinarazpena bidalita!", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "Txa<PERSON><PERSON><PERSON> alda<PERSON> la<PERSON>a", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "Webhook URLa", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "G<PERSON><PERSON> <PERSON><PERSON>", "components.Settings.Notifications.NotificationsWebhook.authheader": "<PERSON><PERSON> go<PERSON>a", "components.Settings.Notifications.NotificationsWebhook.customJson": "JSON edukia", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "JSON edukia ondo berrezarri da!", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "Baliozko URL bat eman behar duzu", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "Webhook proba jakinarazpena bidaltzen…", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "Webhook proba jakinarazpena bidalita!", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "Webhook URLa", "components.Settings.Notifications.discordsettingsfailed": "Discord jakinarazpen ezarpenak gordetzeak huts egin du.", "components.Settings.Notifications.emailsettingsfailed": "E-posta jakinarazpen ezarpenak gordetzeak huts egin du.", "components.Settings.Notifications.pgpPasswordTip": "Sinatu z<PERSON> me<PERSON> <OpenPgpLink>OpenPGP</OpenPgpLink> erabiliz", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "Web push proba jakinarazpena bidaltzen…", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "Web push proba jakinarazpena bidalita!", "components.Settings.Notifications.encryptionDefault": "Erabili STARTTLS eskuragarri badago", "components.Settings.Notifications.allowselfsigned": "Baimendu auto-sinatutako ziurtagiriak", "components.Settings.Notifications.botAPI": "<PERSON><PERSON> baimen <PERSON>a", "components.Settings.Notifications.botAvatarUrl": "<PERSON>t avatar U<PERSON>", "components.Settings.Notifications.encryptionImplicitTls": "Erabili TLS implizitua", "components.Settings.Notifications.encryptionOpportunisticTls": "Beti erabili STARTTLS", "components.Settings.Notifications.pgpPrivateKey": "PGP gako pribatua", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "G<PERSON><PERSON> <PERSON><PERSON>", "components.Settings.Notifications.agentenabled": "G<PERSON><PERSON> <PERSON><PERSON>", "components.Settings.Notifications.authPass": "SMTP pasahitza", "components.Settings.Notifications.authUser": "SMTP erabiltzaile-izena", "components.Settings.Notifications.botUsername": "<PERSON><PERSON>izena", "components.Settings.Notifications.chatId": "Txat ID", "components.Settings.Notifications.emailsender": "<PERSON><PERSON><PERSON>", "components.Settings.Notifications.enableMentions": "<PERSON><PERSON><PERSON>", "components.Settings.Notifications.pgpPassword": "PGP pasahitza", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "Webhook jakinarazpenen ezarpenak ondo gorde dira!", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "Web push jakinarazpen ezarpenak ondo gorde dira!", "components.Settings.Notifications.discordsettingssaved": "Discord jakinarazpenen ezarpenak ondo gorde dira!", "components.Settings.Notifications.emailsettingssaved": "E-posta jakinarazpenen ezarpenak ondo gorde dira!", "components.Settings.Notifications.pgpPrivateKeyTip": "Sinatu z<PERSON> me<PERSON> <OpenPgpLink>OpenPGP</OpenPgpLink> erabiliz", "components.Settings.Notifications.telegramsettingsfailed": "Telegram jakinarazpen ezarpenak gordetzeak huts egin du.", "components.Settings.Notifications.toastDiscordTestFailed": "Discord proba jakinarazpena bidaltzeak huts egin du.", "components.Settings.Notifications.toastEmailTestFailed": "E-posta proba jakinarazpena bidaltzeak huts egin du.", "components.Settings.Notifications.toastTelegramTestFailed": "Telegram proba jakinarazpena bidaltzeak huts egin du.", "components.Settings.Notifications.validationPgpPassword": "PGP pasahitz bat eman behar duzu", "components.Settings.Notifications.validationUrl": "Baliozko URL bat eman behar duzu", "components.Settings.Notifications.sendSilentlyTip": "B<PERSON>i j<PERSON>azpenak soinurik gabe", "components.Settings.Notifications.toastDiscordTestSending": "Discord proba jakinarazpena bid<PERSON>…", "components.Settings.Notifications.toastDiscordTestSuccess": "Discord proba jakinarazpena bidalita!", "components.Settings.Notifications.toastEmailTestSending": "E-posta proba jakinarazpena bidaltzen…", "components.Settings.Notifications.toastEmailTestSuccess": "E-posta proba jakinarazpena bidalita!", "components.Settings.Notifications.toastTelegramTestSending": "Telegram proba jakinarazpena bidaltzen…", "components.Settings.Notifications.toastTelegramTestSuccess": "Telegram proba jakinarazpena bidalita!", "components.Settings.Notifications.userEmailRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON> helbide elektron<PERSON>", "components.Settings.Notifications.webhookRoleId": "Jakinarazpen Rol IDa", "components.Settings.Notifications.sendSilently": "<PERSON><PERSON><PERSON> is<PERSON>", "components.Settings.Notifications.senderName": "<PERSON><PERSON><PERSON>", "components.Settings.Notifications.smtpHost": "SMTP ostalaria", "components.Settings.Notifications.smtpPort": "SMTP ataka", "components.Settings.Notifications.webhookUrl": "Webhook URLa", "components.Settings.RadarrModal.add": "Gehitu zerbitzaria", "components.Settings.Notifications.telegramsettingssaved": "Telegram jakinarazpenen ezarpenak ondo gorde dira!", "components.Settings.RadarrModal.testFirstQualityProfiles": "Probatu konexioa kalitate profi<PERSON> ka<PERSON>o", "components.Settings.RadarrModal.testFirstRootFolders": "Probatu konexioa erro karpetak kargatzeko", "components.Settings.RadarrModal.create4kradarr": "Gehitu 4K Radarr zerbitzari berria", "components.Settings.RadarrModal.testFirstTags": "Probatu konexioa etiketak kargatzeko", "components.Settings.RadarrModal.toastRadarrTestFailure": "Radarr-era konektatzeak huts egin du.", "components.Settings.RadarrModal.createradarr": "<PERSON><PERSON><PERSON>u <PERSON>r z<PERSON>bitzar<PERSON> be<PERSON>a", "components.Settings.RadarrModal.edit4kradarr": "Editatu 4K Radarr zerbitzaria", "components.Settings.RadarrModal.default4kserver": "4K zerbitzari lehenetsia", "components.Settings.RadarrModal.editradarr": "Editatu Radarr zerbitzaria", "components.Settings.RadarrModal.enableSearch": "<PERSON><PERSON><PERSON> bilaketa automatikoa", "components.Settings.RadarrModal.loadingprofiles": "Kalitate profilak kargatzen…", "components.Settings.RadarrModal.loadingrootfolders": "<PERSON>rro karpetak kargatzen…", "components.Settings.RadarrModal.selectQualityProfile": "Hautatu kalitate profila", "components.Settings.RadarrModal.selectRootFolder": "<PERSON><PERSON><PERSON> erro ka<PERSON>", "components.Settings.RadarrModal.apiKey": "API gakoa", "components.Settings.RadarrModal.baseUrl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.defaultserver": "Zerbitzari lehenetsia", "components.Settings.RadarrModal.externalUrl": "Kanpoko URLa", "components.Settings.RadarrModal.inCinemas": "Zinematan", "components.Settings.RadarrModal.loadingTags": "<PERSON><PERSON><PERSON> et<PERSON>…", "components.Settings.RadarrModal.minimumAvailability": "<PERSON>ut<PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.notagoptions": "Etiketarik gabe.", "components.Settings.RadarrModal.qualityprofile": "Kalitate profila", "components.Settings.RadarrModal.selecttags": "Hautatu etiketak", "components.Settings.RadarrModal.server4k": "4K zerbitzaria", "components.Settings.RadarrModal.servername": "Zerbitzariaren izena", "components.Settings.RadarrModal.ssl": "Erabili SSL", "components.Settings.RadarrModal.syncEnabled": "<PERSON><PERSON><PERSON> es<PERSON>", "components.Settings.RadarrModal.tagRequests": "Etiketa eskaerak", "components.Settings.RadarrModal.validationApiKeyRequired": "API gakoa eman behar duzu", "components.Settings.RadarrModal.validationApplicationUrl": "Baliozko URL bat eman behar duzu", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "Gutxieneko es<PERSON>a hautatu behar duzu", "components.Settings.RadarrModal.validationNameRequired": "Zerbitzari-izen bat eman behar duzu", "components.Settings.RadarrModal.validationProfileRequired": "Kalitate profil bat hautatu behar duzu", "components.Settings.RadarrModal.validationRootFolderRequired": "Erro karpeta bat hautatu behar duzu", "components.Settings.SettingsAbout.Releases.releasedataMissing": "Kalertaze-data ez dago eskuragarri unean.", "components.Settings.SettingsAbout.helppaycoffee": "Lagundu kafe batera gonbidatuz", "components.Settings.SettingsAbout.Releases.viewongithub": "Ikus<PERSON> Git<PERSON>", "components.Settings.SettingsAbout.outofdate": "Zaharkituta", "components.Settings.SettingsAbout.uptodate": "Eguneratuta", "components.Settings.SettingsAbout.Releases.versionChangelog": "{version} aldaketa egunkaria", "components.Settings.SettingsAbout.Releases.viewchangelog": "Ikusi aldaketa egunkaria", "components.Settings.SettingsAbout.appDataPath": "Datuen direktorioa", "components.Settings.SettingsAbout.gettingsupport": "<PERSON><PERSON><PERSON> la<PERSON>", "components.Settings.SettingsAbout.githubdiscussions": "GitHub eztabaidak", "components.Settings.SettingsAbout.overseerrinformation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> buruz", "components.Settings.SettingsAbout.supportjellyseerr": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.supportoverseerr": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.timezone": "Ordu-eremua", "components.Settings.SettingsAbout.totalmedia": "Multimedia guztia", "components.Settings.SettingsAbout.totalrequests": "Eskaera guztiak", "components.Settings.SettingsJobsCache.jellyfin-full-scan": "<PERSON><PERSON><PERSON> liburutegi o<PERSON> es<PERSON>", "components.Settings.SettingsJobsCache.jellyfin-recently-added-scan": "<PERSON><PERSON><PERSON> berriki gehituta<PERSON> eskaneoa", "components.Settings.SettingsJobsCache.availability-sync": "Multimedia eskuragarritasun sinkronizazioa", "components.Settings.SettingsJobsCache.cacheflushed": "{cachename} cachea garbituta.", "components.Settings.SettingsJobsCache.download-sync-reset": "<PERSON><PERSON><PERSON> be<PERSON>", "components.Settings.SettingsJobsCache.image-cache-cleanup": "<PERSON><PERSON><PERSON> cache garbiketa", "components.Settings.SettingsJobsCache.imagecachesize": "<PERSON><PERSON> tamaina guz<PERSON>ra", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "<PERSON><PERSON><PERSON>a ondo editatu da!", "components.Settings.SettingsJobsCache.jobsandcache": "Ekintzak eta cachea", "components.Settings.SettingsJobsCache.cachekeys": "Gako <PERSON>", "components.Settings.SettingsJobsCache.cacheksize": "Gak<PERSON>en ta<PERSON>", "components.Settings.SettingsJobsCache.cachename": "Cache<PERSON>n i<PERSON>a", "components.Settings.SettingsJobsCache.cachevsize": "Bali<PERSON>en ta<PERSON>", "components.Settings.SettingsJobsCache.canceljob": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.download-sync": "<PERSON><PERSON>ga sinkron<PERSON>", "components.Settings.SettingsJobsCache.editJobSchedule": "Aldatu <PERSON>", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "Uneko ma<PERSON>", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.flushcache": "Garbitu cachea", "components.Settings.SettingsJobsCache.imagecache": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.imagecachecount": "Cacheat<PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname} u<PERSON><PERSON>.", "components.Settings.SettingsJobsCache.jobname": "Ekintzaren izena", "components.Settings.SettingsJobsCache.jobstarted": "{jobname} hasita.", "components.Settings.SettingsJobsCache.nextexecution": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.copiedLogMessage": "Erregistro mezua arbelera kopiatuta.", "components.Settings.SettingsJobsCache.plex-full-scan": "Plex liburutegi o<PERSON> es<PERSON>a", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Plex berriki gehitutako eskaneoa", "components.Settings.SettingsJobsCache.plex-refresh-token": "Plex freskatze tokena", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "Plex jarraipen-zerrenda sinkronizazioa", "components.Settings.SettingsLogs.copyToClipboard": "Kopiatu arbelera", "components.Settings.SettingsLogs.showall": "Erakuts<PERSON> erre<PERSON><PERSON>", "components.Settings.SettingsJobsCache.radarr-scan": "Radarr eskaneo<PERSON>", "components.Settings.SettingsJobsCache.runnow": "<PERSON><PERSON><PERSON><PERSON> orain", "components.Settings.SettingsJobsCache.sonarr-scan": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.unknownJob": "<PERSON><PERSON><PERSON><PERSON>a", "components.Settings.SettingsJobsCache.usersavatars": "Erabiltzaileen avatarrak", "components.Settings.SettingsLogs.extraData": "<PERSON><PERSON>", "components.Settings.SettingsLogs.logDetails": "Erregistroaren xeh<PERSON>", "components.Settings.SettingsLogs.viewdetails": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsMain.apikey": "API gakoa", "components.Settings.SettingsMain.applicationTitle": "Aplikazioaren izenburua", "components.Settings.SettingsMain.applicationurl": "Aplikazioaren URLa", "components.Settings.SettingsMain.streamingRegionTip": "Erakutsi streaming guneak eskualdeko eskuragarritasunaren arabera", "components.Settings.SettingsMain.toastSettingsFailure": "Zerbait gaizki joan da ezarpenak gordetz<PERSON>.", "components.Settings.SettingsMain.discoverRegionTip": "Iragazi edukia esku<PERSON> es<PERSON>garritasunarengatik", "components.Settings.SettingsMain.originallanguageTip": "Iragazi edukia iturriz<PERSON>", "components.Settings.SettingsMain.partialRequestsEnabled": "Baimendu telesailen eskaera partzialak", "components.Settings.SettingsMain.cacheImages": "<PERSON><PERSON><PERSON>a", "components.Settings.SettingsMain.hideAvailable": "Ezkutatu eskuragarri dagoen multimedia", "components.Settings.SettingsMain.discoverRegion": "Aurkitu eskualdea", "components.Settings.SettingsMain.generalsettings": "Ezarpen orokorrak", "components.Settings.SettingsMain.locale": "Bistarat<PERSON><PERSON>", "components.Settings.SettingsMain.originallanguage": "Aurkitu <PERSON>", "components.Settings.SettingsMain.streamingRegion": "Streaming eskualdea", "components.Settings.SettingsMain.validationApplicationTitle": "Aplikazioaren izenburu bat eman behar duzu", "components.Settings.SettingsMain.validationApplicationUrl": "Baliozko URL bat eman behar duzu", "components.Settings.SettingsUsers.defaultPermissionsTip": "Erabiltzaile berrie ematen zaizkien hasierako baimenak", "components.Settings.SettingsUsers.movieRequestLimitLabel": "Film eskaera muga globala", "components.Settings.SettingsUsers.newPlexLogin": "Gaitu {mediaServerName} sa<PERSON> hasiera berria", "components.Settings.SettingsUsers.localLogin": "G<PERSON><PERSON> saio hasiera lokala", "components.Settings.SettingsUsers.defaultPermissions": "<PERSON><PERSON>", "components.Settings.SonarrModal.create4ksonarr": "Gehitu 4K Sonarr zerbitzari berria", "components.Settings.SonarrModal.loadingprofiles": "Kalitate profilak kargatzen…", "components.Settings.SonarrModal.loadingrootfolders": "<PERSON>rro karpetak kargatzen…", "components.Settings.SonarrModal.selectLanguageProfile": "<PERSON><PERSON><PERSON> hizkunt<PERSON> profila", "components.Settings.SonarrModal.selectQualityProfile": "Hautatu kalitate profila", "components.Settings.SettingsUsers.tvRequestLimitLabel": "Telesail eskaera muga globala", "components.Settings.SonarrModal.edit4ksonarr": "Editatu 4K Sonarr zerbitzaria", "components.Settings.SonarrModal.hostname": "Ostalari-izena edo IP helbidea", "components.Settings.SonarrModal.animeSeriesType": "Anime telesail mota", "components.Settings.SonarrModal.animelanguageprofile": "<PERSON><PERSON> profila", "components.Settings.SonarrModal.animequalityprofile": "Anime kalitate profila", "components.Settings.SonarrModal.animerootfolder": "Anime erro ka<PERSON>eta", "components.Settings.SonarrModal.default4kserver": "4K zerbitzari lehenetsia", "components.Settings.SonarrModal.enableSearch": "<PERSON><PERSON><PERSON> bilaketa automatikoa", "components.Settings.SonarrModal.loadinglanguageprofiles": "<PERSON>z<PERSON><PERSON><PERSON> profilak karga<PERSON>en…", "components.Settings.SonarrModal.selectRootFolder": "<PERSON><PERSON><PERSON> erro ka<PERSON>", "components.Settings.SettingsUsers.userSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.add": "Gehitu zerbitzaria", "components.Settings.SonarrModal.animeTags": "<PERSON><PERSON> et<PERSON>", "components.Settings.SonarrModal.apiKey": "API gakoa", "components.Settings.SonarrModal.baseUrl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.externalUrl": "Kanpoko URLa", "components.Settings.SonarrModal.languageprofile": "Hizkuntza profila", "components.Settings.SonarrModal.loadingTags": "<PERSON><PERSON><PERSON> et<PERSON>…", "components.Settings.SonarrModal.notagoptions": "Etiketarik gabe.", "components.Settings.SonarrModal.qualityprofile": "Kalitate profila", "components.Settings.SonarrModal.rootfolder": "<PERSON><PERSON>", "components.Settings.SonarrModal.seasonfolders": "Denboraldien karpetak", "components.Settings.SonarrModal.selecttags": "Hautatu etiketak", "components.Settings.SonarrModal.seriesType": "Telesail mota", "components.Settings.SonarrModal.servername": "Zerbitzariaren izena", "components.Settings.SettingsUsers.toastSettingsSuccess": "<PERSON><PERSON>tz<PERSON><PERSON><PERSON> ezarpenak ondo gorde dira!", "components.Settings.SonarrModal.testFirstLanguageProfiles": "Probatu konexioa hizkuntza profilak ka<PERSON>o", "components.Settings.SonarrModal.testFirstQualityProfiles": "Probatu konexioa kalitate profi<PERSON> ka<PERSON>o", "components.Settings.SonarrModal.testFirstRootFolders": "Probatu konexioa erro karpetak kargatzeko", "components.Settings.SonarrModal.validationApiKeyRequired": "API gakoa eman behar duzu", "components.Settings.SonarrModal.validationApplicationUrl": "Baliozko URL bat eman behar duzu", "components.Settings.SonarrModal.validationLanguageProfileRequired": "Hizkuntza profil bat hautatu behar duzu", "components.Settings.SonarrModal.validationNameRequired": "Zerbitzari-izen bat eman behar duzu", "components.Settings.SonarrModal.validationProfileRequired": "Kalitate profil bat hautatu behar duzu", "components.Settings.SonarrModal.validationRootFolderRequired": "Erro karpeta bat hautatu behar duzu", "components.Settings.SonarrModal.testFirstTags": "Probatu konexioa etiketak kargatzeko", "components.Settings.SonarrModal.toastSonarrTestFailure": "Sonarr-era konektatzeak huts egin du.", "components.Settings.copied": "API gakoa arbelera kopiatuta.", "components.Settings.hostname": "Ostalari-izena edo IP helbidea", "components.Settings.addradarr": "Gehitu Radarr zerbitzaria", "components.Settings.addsonarr": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.currentlibrary": "Uneko liburutegia: {name}", "components.Settings.deleteServer": "Ezabatu {serverType} zerbitzaria", "components.Settings.SonarrModal.ssl": "Erabili SSL", "components.Settings.SonarrModal.syncEnabled": "<PERSON><PERSON><PERSON> es<PERSON>", "components.Settings.SonarrModal.tagRequests": "Etiketa eskaerak", "components.Settings.activeProfile": "<PERSON><PERSON><PERSON><PERSON> profila", "components.Settings.apiKey": "API gakoa", "components.Settings.cancelscan": "<PERSON><PERSON><PERSON> es<PERSON>", "components.Settings.default4k": "4K lehenetsia", "components.Settings.enablessl": "Erabili SSL", "components.Settings.externalUrl": "Kanpoko URLa", "components.Settings.SonarrModal.toastSonarrTestSuccess": "Son<PERSON>r konexioa ondo ezarri da!", "components.Settings.invalidurlerror": "<PERSON><PERSON> da {mediaServerName} zerbitzarira konektatu.", "components.Settings.jellyfinSyncFailedGenericError": "Zerbait gaizki joan da liburutegiak sinkronizatzean", "components.Settings.jellyfinSyncFailedNoLibrariesFound": "Ez da liburutegirik aurkitu", "components.Settings.jellyfinForgotPasswordUrl": "Ahaztutako p<PERSON>hitza URLa", "components.Settings.librariesRemaining": "Geratzen diren liburutegiak: {count}", "components.Settings.manualscan": "Eskuzko liburutegi eskaneoa", "components.Settings.jellyfinSettings": "{mediaServerName} ezarpenak", "components.Settings.jellyfinlibraries": "{mediaServerName} liburutegiak", "components.Settings.jellyfinsettings": "{mediaServerName} ezarpenak", "components.Settings.jellyfinSettingsSuccess": "{mediaServerName} ezar<PERSON>ak ondo gorde dira!", "components.Settings.notificationAgentSettingsDescription": "Konfiguratu eta gaitu jakinarazpen agenteak.", "components.Settings.manualscanJellyfin": "Eskuzko liburutegi eskaneoa", "components.Settings.menuJobs": "Ekintzak eta cachea", "components.Settings.notificationsettings": "Jakinarazpen ezarpenak", "components.Settings.notrunning": "<PERSON>z dago exekutatzen", "components.Settings.plexlibraries": "Plex liburutegiak", "components.Settings.plexsettings": "Plex ezarpenak", "components.Settings.radarrsettings": "<PERSON><PERSON>", "components.Settings.save": "Gorde <PERSON>", "components.Settings.scan": "Sinkronizatu liburutegiak", "components.Settings.serverpresetManualMessage": "Eskuzko konfigurazioa", "components.Settings.serverpresetRefreshing": "Zerbitzariak lortzen…", "components.Settings.sonarrsettings": "<PERSON><PERSON><PERSON>", "components.Settings.startscan": "<PERSON><PERSON>", "components.Settings.syncJellyfin": "Sinkronizatu liburutegiak", "components.Settings.tautulliApiKey": "API gakoa", "components.Settings.tautulliSettings": "<PERSON><PERSON><PERSON>", "components.Settings.toastPlexRefreshFailure": "Plex zerbitzariaren zerrenda eskuratzeak huts egin du.", "components.Settings.validationApiKey": "API gakoa eman behar duzu", "components.Settings.validationUrl": "Baliozko URL bat eman behar duzu", "components.Settings.toastPlexConnecting": "Plex zerbitzarira konektatzen saiatzen…", "components.Settings.toastPlexConnectingFailure": "Plex-era konektatzeak huts egin du.", "components.Settings.toastPlexRefresh": "Zerbitzarien zerrenda Plex-etik lortzen…", "components.Setup.signinMessage": "<PERSON><PERSON><PERSON>, hasi sa<PERSON>a", "components.Setup.signinWithEmby": "Sartu zure Emby informazioa", "components.Setup.signinWithJellyfin": "Sartu zure Jellyfin informazioa", "components.Setup.signinWithPlex": "Sartu zure Plex informazioa", "components.Settings.webAppUrl": "<WebAppLink>Web App</WebAppLink> URLa", "components.Setup.configuremediaserver": "Konfiguratu multimedia zerbitzaria", "components.Setup.servertype": "Aukeratu zerbitzariaren mota", "components.Settings.urlBase": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.webpush": "Web Push", "components.Setup.back": "<PERSON><PERSON><PERSON>", "components.Setup.configemby": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Setup.configjellyfin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Setup.configplex": "Konfiguratu Plex", "components.Setup.configureservices": "Konfigu<PERSON><PERSON>", "components.Setup.finish": "<PERSON><PERSON><PERSON> k<PERSON>figuraz<PERSON>", "components.Setup.signin": "<PERSON><PERSON> sa<PERSON>a", "components.Settings.toastPlexConnectingSuccess": "Plex konexioa ondo ezarri da!", "components.Settings.toastPlexRefreshSuccess": "Plex zerbitzarien zerrenda ondo lortu da!", "components.Settings.toastTautulliSettingsSuccess": "<PERSON><PERSON><PERSON> e<PERSON>ak ondo gorde dira!", "components.TvDetails.network": "{networkCount, plural, one {Sare} other {Sare}}", "components.TitleCard.watchlistError": "Zerbait gaizki joan <PERSON>, sa<PERSON><PERSON> be<PERSON>.", "components.TitleCard.watchlistCancel": "<strong>{title}</strong>-(r)en jarraipen-zerrenda utzita.", "components.Setup.welcome": "<PERSON><PERSON>-era", "components.StatusBadge.openinarr": "<PERSON><PERSON><PERSON> {arr}-en", "components.StatusBadge.playonplex": "Ikusi {mediaServerName}-(e)n", "components.StatusChecker.restartRequired": "Zerbitzaria berrabiarazi behar da", "components.TitleCard.addToWatchList": "Gehitu jarraipen zerrendara", "components.TitleCard.mediaerror": "{mediaType} ez da aurkitu", "components.TvDetails.Season.noepisodes": "Atal zerrenda ez dago eskuragarri.", "components.TvDetails.TvCast.fullseriescast": "Telesailaren aktore guztiak", "components.TvDetails.TvCrew.fullseriescrew": "Telesailaren talde osoa", "components.TvDetails.firstAirDate": "<PERSON>hen emisio data", "components.TvDetails.nextAirDate": "<PERSON><PERSON><PERSON>", "components.StatusBadge.managemedia": "Kudeatu {mediaType}", "components.StatusBadge.status4k": "4K {status}", "components.StatusChecker.appUpdated": "{applicationTitle} eguneratuta", "components.StatusChecker.reloadApp": "<PERSON><PERSON><PERSON> {applicationTitle} be<PERSON>ro", "components.TitleCard.cleardata": "Garbitu datuak", "components.TitleCard.tmdbid": "TMDB ID", "components.TitleCard.tvdbid": "TheTVDB ID", "components.TvDetails.episodeRuntimeMinutes": "{runtime} minutu", "components.TvDetails.manageseries": "Kudeatu telesailak", "components.TitleCard.watchlistDeleted": "<strong>{title}</strong> jarraipen zerrendatik ondo kendu da!", "components.TitleCard.watchlistSuccess": "<strong>{title}</strong> jarraipen zerrendara ondo gehitu da!", "components.TvDetails.watchlistError": "Zerbait gaizki joan <PERSON>, sa<PERSON><PERSON> be<PERSON>.", "components.TvDetails.play4k": "Ikusi 4K-n {mediaServerName}-(e)n", "components.TvDetails.rtaudiencescore": "Rotten Tomatoes audientzia puntuazioa", "components.TvDetails.play": "Ikusi {mediaServerName}-(e)n", "components.TvDetails.removefromwatchlist": "Kendu jarraipen zerrendatik", "components.TvDetails.reportissue": "<PERSON><PERSON>i <PERSON> bat", "components.TvDetails.rtcriticsscore": "Rotten Tomatoes Tomatometer", "components.TvDetails.streamingproviders": "<PERSON><PERSON> emititzen hemen", "components.TvDetails.tmdbuserscore": "TMDB erabiltzaileen puntuazioa", "components.TvDetails.viewfullcrew": "Ikus<PERSON> talde osoa", "components.UserList.autogeneratepassword": "Sortu pasahitza automatikoki", "components.UserList.createlocaluser": "Sortu erabiltzaile lokala", "components.UserList.edituser": "Editatu era<PERSON><PERSON><PERSON><PERSON> bai<PERSON>ak", "components.UserList.importfromJellyfin": "Inportatu {mediaServerName} erabiltzaileak", "components.TvDetails.originallanguage": "<PERSON><PERSON><PERSON><PERSON>", "components.TvDetails.originaltitle": "<PERSON><PERSON><PERSON><PERSON>", "components.TvDetails.overviewunavailable": "Ikuspegi orokorra ez dago es<PERSON>garri.", "components.TvDetails.seasonnumber": "{seasonNumber} denboraldia", "components.TvDetails.showtype": "Telesail mota", "components.TvDetails.similar": "Antzerako telesailak", "components.TvDetails.status4k": "4K {status}", "components.TvDetails.watchtrailer": "Ikusi <PERSON>ra", "components.UserList.bulkedit": "Multzoko edizioa", "components.UserList.deleteuser": "Ezabatu <PERSON>", "components.UserList.email": "Helbide elektronikoa", "components.TvDetails.watchlistDeleted": "<strong>{title}</strong> jarraipen-zerrendatik ondo kendu da!", "components.TvDetails.watchlistSuccess": "<strong>{title}</strong> ondo gehitu da jarraipen zerrendara!", "components.UserList.validationUsername": "Erabiltzaile-izen bat eman behar duzu", "components.UserList.importfrommediaserver": "Inportatu {mediaServerName} erabiltzaileak", "components.UserList.importfromplex": "Inportatu Plex erabiltzaileak", "components.UserList.localuser": "Erabiltzaile lokala", "components.UserList.mediaServerUser": "{mediaServerName} erabiltzailea", "components.UserList.plexuser": "Plex erabiltz<PERSON>", "components.UserList.sortCreated": "Batze data", "components.UserList.sortDisplayName": "Bistaratze izena", "components.UserList.sortRequests": "Eskaera zenbakia", "components.UserList.userlist": "Erabiltzaileen zerrenda", "components.UserList.validationEmail": "Helbide elektronikoa beharrez<PERSON>a da", "components.UserList.usercreatedsuccess": "Era<PERSON>tz<PERSON><PERSON> ondo sortu da!", "components.UserList.userdeleted": "Erabiltzailea ondo ezabatu da!", "components.UserList.userssaved": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> baimenak ondo gorde dira!", "components.UserProfile.UserSettings.UserGeneralSettings.discoverRegionTip": "Iragazi edukia esku<PERSON> es<PERSON>garritasunarengatik", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "Iragazi edukia iturriz<PERSON>", "components.UserProfile.ProfileHeader.userid": "Erabiltzailearen ID: {userid}", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "Discord erabiltzailearen ID", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "Baliogabetu muga globala", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "Film eskaera muga", "components.UserProfile.ProfileHeader.joindate": "{joindate} batu zen", "components.UserProfile.ProfileHeader.profile": "Ikusi profila", "components.UserProfile.ProfileHeader.settings": "Editatu <PERSON>ak", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "<PERSON><PERSON><PERSON>ren mota", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "Bistarat<PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.discoverRegion": "Aurkitu eskualdea", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "Bistaratze izena", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "Ezarpen orokorrak", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "({language}) lehenetsia", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "Erabiltzaile lokala", "components.UserProfile.UserSettings.UserGeneralSettings.mediaServerUser": "{mediaServerName} erabiltzailea", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "Aurkitu <PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "Plex erabiltz<PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "Auto-eskatu filmak", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "Auto-eskatu telesailak", "components.UserProfile.UserSettings.UserGeneralSettings.streamingRegionTip": "Erakutsi streaming guneak eskualdeko eskuragarritasunaren arabera", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "Zerbait gaizki joan da ezarpenak gordetz<PERSON>.", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "Iragazi edukia esku<PERSON> es<PERSON>garritasunarengatik", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailureEmail": "Helbide elektroni hau dagoeneko hartuta dago!", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "Telesail eskaera muga", "components.UserProfile.UserSettings.UserGeneralSettings.validationemailformat": "Baliozko helbide elektronikoa behar da", "components.UserProfile.UserSettings.UserGeneralSettings.region": "Aurkitu eskualdea", "components.UserProfile.UserSettings.UserGeneralSettings.save": "Gorde <PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.streamingRegion": "Streaming eskualdea", "components.UserProfile.UserSettings.UserGeneralSettings.validationemailrequired": "Helbide elektronikoa beharrez<PERSON>a da", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "<PERSON><PERSON>penak ondo gorde dira!", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "Discord jakinarazpen ezarpenak gordetzeak huts egin du.", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "E-posta jakinarazpen ezarpenak gordetzeak huts egin du.", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "Pushbullet jakinarazpen ezarpenak gordetzeak huts egin du.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "Zure 30 karaktereko <UsersGroupsLink>erabiltzaile edo talde identifikatzailea</UsersGroupsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "Pushover jakinarazpen ezarpenak gordetzeak huts egin du.", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "Telegram jakinarazpen ezarpenak gordetzeak huts egin du.", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "Z<PERSON><PERSON>u e-posta mezuak <OpenPgpLink>OpenPGP</OpenPgpLink> erabiliz", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "B<PERSON>i j<PERSON>azpenak soinurik gabe", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "Erabiltzaile edo talde gakoa", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "PGP gako publikoa", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "Erabiltzailearen <PERSON>a", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "Jakinarazpen ezarpenak", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "Sarbide tokena", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "<PERSON><PERSON><PERSON> is<PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.sound": "Jakinarazpen soinua", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "Txat ID", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "E-posta jakinarazpenen ezarpenak ondo gorde dira!", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "Pushbullet jakinarazpenen ezarpenak ondo gorde dira!", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "Push<PERSON> jakinarazpenen ezarpenak ondo gorde dira!", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "Sarbide tokena eman behar duzu", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "<PERSON><PERSON><PERSON><PERSON> berria berretsi behar duzu", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "<PERSON><PERSON> <PERSON>ko pasa<PERSON> eman behar duzu", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "<PERSON><PERSON><PERSON><PERSON> berri bat eman behar duzu", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "<PERSON><PERSON><PERSON><PERSON> bat egin behar dute", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "Web Push", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "Telegram jakinarazpenen ezarpenak ondo gorde dira!", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "Web push jakinarazpen ezarpenak ondo gorde dira!", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "<PERSON><PERSON><PERSON><PERSON> ondo gorde da!", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "Zerbait gaizki joan da ezarpenak gordetz<PERSON>.", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "<PERSON><PERSON> dituzu zure baimenak aldatu.", "components.UserProfile.pastdays": "{type} (azken {days} egunak)", "components.UserProfile.limit": "{limit}-(e)tik {remaining}", "i18n.addToBlacklist": "Gehitu zerrenda beltzera", "components.UserProfile.localWatchlist": "{username}-(r)en jarraipen zerrenda", "components.UserProfile.movierequests": "Film eskaerak", "components.UserProfile.plexwatchlist": "Plex jarraipen-zerrenda", "components.UserProfile.recentlywatched": "<PERSON><PERSON><PERSON>", "components.UserProfile.recentrequests": "<PERSON><PERSON><PERSON>", "components.UserProfile.requestsperdays": "{limit} geratzen dira", "components.UserProfile.seriesrequest": "Telesail eskaerak", "components.UserProfile.totalrequests": "Eskaera guztiak", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "Baimenak ondo gorde dira!", "i18n.blacklistDuplicateError": "<strong>{title}</strong> zer<PERSON>da belt<PERSON>an dago jada.", "i18n.blacklistError": "Zerbait gaizki joan <PERSON>, sa<PERSON><PERSON> be<PERSON>.", "i18n.resultsperpage": "<PERSON><PERSON><PERSON><PERSON> {pageSize} em<PERSON><PERSON> <PERSON><PERSON> b<PERSON>", "i18n.blacklistSuccess": "<strong>{title}</strong> zerrenda beltzera gehitu da.", "i18n.areyousure": "<PERSON><PERSON><PERSON>?", "i18n.removefromBlacklist": "Kendu zerrenda beltzetik", "i18n.request4k": "Eskatu 4K-n", "i18n.delimitedlist": "{a}, {b}", "i18n.noresults": "Emaitzarik ez.", "i18n.notrequested": "Eskatu gabe", "i18n.partiallyavailable": "Partzialki es<PERSON>garri", "i18n.restartRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON> behar da", "i18n.save": "Gorde <PERSON>", "pages.errormessagewithcode": "{statusCode} - {error}", "pages.internalservererror": "Zerbitzariar<PERSON> barn<PERSON>", "pages.pagenotfound": "Orria ez da aurkitu", "pages.somethingwentwrong": "Zerbait gaizki joan da", "i18n.usersettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pages.returnHome": "<PERSON><PERSON><PERSON> has<PERSON>", "pages.serviceunavailable": "Zerbitzua ez dago es<PERSON>garri", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "Pushbullet proba jakinarazpena bidaltzeak huts egin du.", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "Slack jakinarazpen ezarpenak gordetzeak huts egin du.", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "Webhook jakinarazpen ezarpenak gordetzeak huts egin du.", "components.ResetPassword.emailresetlink": "E-posta berreskuratze esteka", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Slack jakinarazpenen ezarpenak ondo gorde dira!", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "Sarbide tokena eman behar duzu", "components.Settings.SettingsUsers.toastSettingsFailure": "Zerbait gaizki joan da ezarpenak gordetz<PERSON>.", "components.Settings.SettingsUsers.userSettingsDescription": "Konfiguratu erabiltzaileen ezarpen global eta lehenetsiak.", "components.Discover.CreateSlider.providetmdbgenreid": "Eman TMDB genero ID bat", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "<PERSON>z dituzu den<PERSON>aldi na<PERSON>", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "Sortu <WebhookLink>Sarrera Webhook</WebhookLink> integrazio bat", "components.Discover.CreateSlider.providetmdbstudio": "Eman TMDB estudioaren IDa", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "LunaSea proba jakinarazpena bidalita!", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "TMDB puntuazioa gorantz", "components.Settings.RadarrModal.selectMinimumAvailability": "<PERSON><PERSON><PERSON><PERSON> era<PERSON>", "components.Settings.SonarrModal.editsonarr": "Editatu Sonarr z<PERSON>bitzaria", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "G<PERSON><PERSON> <PERSON><PERSON>", "components.Settings.SettingsMain.toastSettingsSuccess": "<PERSON><PERSON>penak ondo gorde dira!", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "Webhook proba jakinarazpena bidaltzeak huts egin du.", "components.RequestModal.pendingapproval": "<PERSON><PERSON> eska<PERSON> onartzeko zain dago.", "components.RequestModal.requestcollection4ktitle": "Eskatu bilduma 4K-n", "components.Settings.SonarrModal.createsonarr": "<PERSON><PERSON><PERSON><PERSON>", "components.Selector.searchGenres": "Hautatu generoak…", "components.Selector.searchStatus": "Ha<PERSON><PERSON> egoera...", "components.Selector.showless": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.NotificationsLunaSea.profileName": "<PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.rootfolder": "<PERSON><PERSON>", "components.IssueDetails.toasteditdescriptionsuccess": "Intzidentziaren deskribapena ondo editatu da!", "components.RequestModal.requestedited": "<strong>{title}</strong>-(e)rako eskaera ondo editatu da!", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Pushbullet jakinarazpenen ezarpenak ondo gorde dira!", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Discord jakinarazpenen ezarpenak ondo gorde dira!", "components.RequestModal.requestApproved": "<strong>{title}</strong> eska<PERSON> onart<PERSON>!", "components.RequestModal.requestseries4ktitle": "Eskatu telesaila 4K-n", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Gotify proba jakinarazpena bidaltzen…", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "LunaSea proba jakinarazpena bid<PERSON>…", "components.Settings.RadarrModal.hostname": "Ostalari-izena edo IP helbidea", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "TMDB puntuazioa beherantz", "components.TvDetails.addtowatchlist": "Gehitu jarraipen zerrendara", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "Aplikazioaren API tokena", "components.Discover.DiscoverTvLanguage.languageSeries": "{language}-(e)ko telesailak", "components.RequestModal.selectmovies": "Hautatu filma(k)", "components.RequestModal.selectseason": "Hauta<PERSON>(k)", "components.Settings.Notifications.encryption": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.defaultserver": "Zerbitzari lehenetsia", "components.Settings.RadarrModal.toastRadarrTestSuccess": "Radarr konexioa ondo ezarri da!", "components.Settings.SonarrModal.server4k": "4K zerbitzaria", "components.UserProfile.UserSettings.UserNotificationSettings.deviceDefault": "<PERSON><PERSON>", "components.Settings.SettingsMain.toastApiKeySuccess": "API gako berria ondo sortu da!", "components.TvDetails.episodeRuntime": "Atalaren i<PERSON>na", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Gotify jakinarazpenen ezarpenak ondo gorde dira!", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "LunaSea jakinarazpenen ezarpenak ondo gorde dira!", "components.Settings.OverrideRuleModal.qualityprofile": "Kalitate profila", "components.Settings.OverrideRuleModal.selectRootFolder": "<PERSON><PERSON><PERSON> erro-karpeta", "components.DiscoverTvUpcoming.upcomingtv": "<PERSON><PERSON><PERSON> telesailak", "components.Login.loginwithapp": "<PERSON><PERSON> sa<PERSON> {appName}(r)ekin", "components.Login.noadminerror": "Ez da administratzaile erabiltzailerik aurkitu zerbitzarian.", "components.Login.orsigninwith": "Edo hasi saioa honekin", "components.Selector.searchUsers": "Hautatu erabiltzaileak…", "components.Settings.OverrideRuleModal.conditions": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleModal.rootfolder": "<PERSON><PERSON><PERSON>ka<PERSON><PERSON>", "components.Settings.OverrideRuleModal.selectService": "Ha<PERSON>tu zerbitzua", "components.Settings.OverrideRuleModal.selecttags": "Hautatu etiketak", "components.Settings.OverrideRuleModal.service": "Zerbitzua", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.plexErrorExists": "Kontu hau dagoeneko Plex erabiltzaile batera lotuta dago", "components.Settings.overrideRulesDescription": "Gainidatze-arauek eskaera bat arauarekin bat datorrenean ordezkatuko diren propietateak zehazteko aukera ematen dute.", "components.Settings.SettingsUsers.mediaServerLoginTip": "<PERSON><PERSON> era<PERSON> {mediaServerName} kontua erabiliz saioa hasteko aukera", "components.UserProfile.UserSettings.LinkJellyfinModal.errorUnauthorized": "<PERSON><PERSON> da {mediaServerName}era konektatu zure kredentzialak erabiliz", "components.UserProfile.UserSettings.LinkJellyfinModal.errorUnknown": "Errore ezezaguna gertatu da", "components.Settings.OverrideRuleModal.createrule": "Gainidazte arau berria", "components.UserProfile.UserSettings.UserNotificationSettings.telegramMessageThreadIdTip": "<PERSON><PERSON> taldeko txatak gaiak gaituta baditu, hari/gai baten ID bat zehaztu dezakezu hemen", "components.UserProfile.UserSettings.LinkJellyfinModal.title": "Lotu {mediaServerName} kontua", "components.Settings.OverrideRuleModal.keywords": "<PERSON>ak<PERSON><PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleModal.conditionsDescription": "Parametro-aldaketak aplikatu aurretik baldintzak zehazten ditu. Eremu bakoitza balioztatu behar da aplikatu beharreko a<PERSON> (AND eragiketa). Eremu bat egiaztatutzat hartzen da propietateetariko bat, bat badatorren (OR eragiketa).", "components.Settings.Notifications.messageThreadIdTip": "<PERSON><PERSON> taldeko txatak gaiak gaituta baditu, hari/gai baten ID bat zehaztu dezakezu hemen", "components.Settings.Notifications.validationMessageThreadId": "<PERSON>/gaiaren <PERSON>a zenbaki oso positiboa izan behar da", "components.Settings.Notifications.messageThreadId": "Hari/Gai ID", "components.Settings.OverrideRuleTile.tags": "Etiketak", "components.Settings.OverrideRuleModal.users": "Erabiltzaileak", "components.Settings.OverrideRuleTile.conditions": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsNetwork.networkDisclaimer": "<PERSON><PERSON><PERSON> hauen ordez zure edukiontzi/sistemako sare-parametroak erabili behar dira. Ikusi {docs} informazio gehiagorak<PERSON>.", "components.Settings.OverrideRuleModal.settingsDescription": "<PERSON><PERSON> betetzen direnean zein ezarpen aldatuko diren zehazten du.", "components.Settings.SettingsNetwork.csrfProtectionHoverTip": "EZ ezazu ezarpen hau gaitu egiten ari zarena ez badakizu!", "components.Settings.SettingsNetwork.forceIpv4FirstTip": "<PERSON><PERSON><PERSON> IPv4 helbideak konpontzera IPv6ren ordez", "components.Settings.SettingsNetwork.csrfProtectionTip": "Ezarri kanpoko API sarbidea irakurtzeko soilik (HTTPS behar du)", "components.Settings.OverrideRuleModal.serviceDescription": "Aplikatu arau hau hautatutako zerbitzura.", "components.Settings.SettingsNetwork.networksettingsDescription": "Konfiguratu sare ezarpenak zure Jellyseerr instantziarako.", "components.Settings.OverrideRuleModal.ruleCreated": "Gainidazte araua behar bezala sortu da!", "components.Settings.OverrideRuleModal.ruleUpdated": "Gainidazte araua ondo eguneratu da!", "components.Settings.SettingsMain.enableSpecialEpisodes": "Baimendu atal berezien es<PERSON>", "components.Settings.SettingsNetwork.forceIpv4First": "Behartu IPv4 ebazpena lehen", "components.Settings.OverrideRuleModal.editrule": "Editatu gainidazte a<PERSON>ua", "components.Settings.OverrideRuleModal.selectQualityProfile": "Hautatu kalitate profila", "components.Settings.SettingsNetwork.advancedNetworkSettings": "<PERSON><PERSON> e<PERSON><PERSON> au<PERSON>", "components.Settings.SettingsNetwork.csrfProtection": "Gaitu CSRF babesa", "components.Settings.OverrideRuleModal.create": "Sortu araua", "components.Settings.OverrideRuleModal.notagoptions": "Etiketarik ez.", "components.Settings.OverrideRuleTile.qualityprofile": "Kalitate profila", "components.Settings.OverrideRuleTile.rootfolder": "<PERSON><PERSON><PERSON>ka<PERSON><PERSON>", "components.Settings.SettingsNetwork.networksettings": "<PERSON><PERSON>", "components.Settings.OverrideRuleModal.genres": "Generoak", "components.Settings.OverrideRuleModal.languages": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleModal.settings": "Ezarpenak", "components.Settings.OverrideRuleModal.tags": "Etiketak", "components.Settings.OverrideRuleTile.genre": "<PERSON><PERSON>", "components.Settings.OverrideRuleTile.keywords": "<PERSON>ak<PERSON><PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleTile.language": "Hizkuntza", "components.Settings.OverrideRuleTile.settings": "Ezarpenak", "components.Settings.OverrideRuleTile.users": "Erabiltzaileak", "components.Settings.SettingsNetwork.docs": "dokumentazioa", "components.Settings.SettingsNetwork.network": "Sarea", "components.Settings.SettingsNetwork.proxyBypassFilterTip": "<PERSON><PERSON><PERSON> ',' banatzaile gisa eta '*.' azpidomeinue<PERSON><PERSON> komodin gisa", "components.Settings.SettingsNetwork.trustProxyTip": "Baimen<PERSON>-i bezeroen IP helbideak behar bezala erregistratzea proxy baten atzean", "components.UserProfile.UserSettings.LinkJellyfinModal.description": "Sartu zure {mediaServerName} kredentzialak zure kontua {applicationName}-re<PERSON><PERSON>.", "components.Setup.librarieserror": "Baliozkotzeak huts egin du. <PERSON>, aldatu liburutegiak berriro <PERSON>.", "components.UserProfile.UserSettings.LinkJellyfinModal.errorExists": "Kontu hau dago<PERSON><PERSON> {applicationName} erabiltzaile batekin lotuta dago", "components.Settings.SettingsUsers.atLeastOneAuth": "Gutxienez autentifikazio metodo bat hautatu behar da.", "components.Settings.SettingsNetwork.toastSettingsFailure": "Zerbait gaizki joan da ezarpenak gordetz<PERSON>.", "components.Settings.SettingsNetwork.validationProxyPort": "<PERSON><PERSON><PERSON> ataka bat eman behar duzu", "components.Settings.SettingsNetwork.proxyBypassLocalAddresses": "Saihestu proxya helbide lokaletarako", "components.Settings.SettingsUsers.loginMethodsTip": "Konfiguratu saio hasiera metodoak erabiltzaileentzat.", "components.UserProfile.UserSettings.LinkJellyfinModal.passwordRequired": "<PERSON><PERSON><PERSON><PERSON> eman behar duzu", "components.UserProfile.UserSettings.LinkJellyfinModal.usernameRequired": "Erabiltzaile-izena eman behar duzu", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.deleteFailed": "Ezin da lotutako kontua ezabatu.", "components.Settings.SettingsNetwork.proxySsl": "Erabili SSL proxyrako", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.errorUnknown": "Errore ezezaguna gertatu da", "components.Settings.SettingsNetwork.proxyBypassFilter": "Proxy e<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsNetwork.toastSettingsSuccess": "<PERSON><PERSON>penak ondo gorde dira!", "components.Settings.SettingsNetwork.trustProxy": "<PERSON><PERSON><PERSON> proxy e<PERSON><PERSON><PERSON>", "components.Settings.SettingsUsers.mediaServerLogin": "Gaitu {mediaServerName} sa<PERSON> hasiera", "components.Settings.addrule": "Gainidazte arau berria", "components.Settings.SettingsNetwork.proxyEnabled": "HTTP(S) proxia", "components.Settings.SettingsNetwork.proxyHostname": "Proxi ostalari-izena", "components.Settings.SettingsNetwork.proxyPassword": "Proxy p<PERSON><PERSON><PERSON>", "components.Settings.SettingsNetwork.proxyPort": "Proxy ataka", "components.Settings.SettingsNetwork.proxyUser": "Proxy erabiltzaile-izena", "components.Settings.SettingsUsers.loginMethods": "<PERSON><PERSON><PERSON><PERSON><PERSON> metodoak", "components.Settings.overrideRules": "Gainidazte arauak", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.linkedAccounts": "Lotutako k<PERSON>uak", "components.Settings.menuNetwork": "Sarea", "components.UserProfile.UserSettings.LinkJellyfinModal.password": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.LinkJellyfinModal.save": "Lotu", "components.UserProfile.UserSettings.LinkJellyfinModal.saving": "<PERSON><PERSON><PERSON><PERSON>…", "components.UserProfile.UserSettings.LinkJellyfinModal.username": "Erabiltzaile-izena", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.noLinkedAccounts": "<PERSON>z duzu kanpoko konturik zure kontura lotuta.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.noPermissionDescription": "<PERSON>z duzu erabiltzaile honen lotutako kontuak aldatzeko baimenik.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.linkedAccountsHint": "Kan<PERSON><PERSON> kontu hauek zure {applicationName} kontuarekin lotuta daude.", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramMessageThreadId": "<PERSON>/gaiaren <PERSON>a zenbaki oso positiboa izan behar da", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.plexErrorUnauthorized": "Ezin da Plexera konektatu zure kredentzialak erabiliz", "components.UserProfile.UserSettings.UserNotificationSettings.telegramMessageThreadId": "Hari/Gai ID", "components.UserProfile.UserSettings.menuLinkedAccounts": "Lotutako k<PERSON>uak"}