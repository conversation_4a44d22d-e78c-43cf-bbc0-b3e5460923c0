# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env.local
.env.development.local
.env.test.local
.env.production.local

# vercel
.vercel

# database
config/db/*.sqlite3*
config/settings.json
config/settings.old.json

# logs
config/logs/*.log*
config/logs/*.json
config/logs/*.log.gz
config/logs/*.json.gz
config/logs/*-audit.json

# anidb mapping file
config/anime-list.xml

# dist files
dist

# sqlite journal
config/db/db.sqlite3-journal

# VS Code
.vscode/launch.json

# Cypress
cypress.env.json
cypress/videos
cypress/screenshots

# ESLint
.eslintcache

# TS Build Info
tsconfig.tsbuildinfo

# Webstorm
.idea

# Config Cache Directory
config/cache
