import { MigrationInterface, QueryRunner } from "typeorm";

export class AddBlacklistedGenres1755338845464 implements MigrationInterface {
    name = 'AddBlacklistedGenres1755338845464'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "blacklist" ADD "blacklistedGenres" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "blacklist" DROP COLUMN "blacklistedGenres"`);
    }

}
