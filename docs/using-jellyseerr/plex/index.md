---
title: Overview
description: Learn about <PERSON><PERSON><PERSON><PERSON>'s Plex integration features
sidebar_position: 1
---

# Plex Features Overview

Jellyseerr provides integration features that connect with your Plex media server to automate media management tasks.

## Available Features

- [Watchlist Auto Request](./plex/watchlist-auto-request) - Automatically request media from your Plex Watchlist
- More features coming soon!

## Prerequisites

:::info Authentication Required
To use any Plex integration features, you must have logged into <PERSON><PERSON><PERSON>rr at least once with your Plex account.
:::

**Requirements:**
- Plex account with access to the configured Plex server
- Je<PERSON><PERSON><PERSON> configured with Plex as the media server
- User authentication via Plex login
- Appropriate user permissions for specific features

## Getting Started

1. Authenticate at least once using your Plex credentials
2. Verify you have the necessary permissions for desired features
3. Follow individual feature guides for setup instructions

:::note Server Configuration
Plex server configuration is handled by your administrator. If you cannot log in with your Plex account, contact your administrator to verify the server setup.
:::