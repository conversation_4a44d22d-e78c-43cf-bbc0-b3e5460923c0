replicaCount: 1

image:
  registry: ghcr.io
  repository: fallenbagel/jellyseerr
  pullPolicy: IfNotPresent
  # -- Overrides the image tag whose default is the chart appVersion.
  tag: ""
  sha: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

# -- Deployment strategy
strategy:
  type: Recreate

# Liveness / Readiness / Startup Probes
probes:
  # -- Configure liveness probe
  livenessProbe: {}
  # initialDelaySeconds: 60
  # periodSeconds: 30
  # timeoutSeconds: 5
  # successThreshold: 1
  # failureThreshold: 5
  # -- Configure readiness probe
  readinessProbe: {}
  # initialDelaySeconds: 60
  # periodSeconds: 30
  # timeoutSeconds: 5
  # successThreshold: 1
  # failureThreshold: 5
  # -- Configure startup probe
  startupProbe: null
  #  tcpSocket:
  #    port: http

# -- Environment variables to add to the jellyseerr pods
extraEnv: []
# -- Environment variables from secrets or configmaps to add to the jellyseerr pods
extraEnvFrom: []

serviceAccount:
  # -- Specifies whether a service account should be created
  create: true
  # -- Automatically mount a ServiceAccount's API credentials?
  automount: true
  # -- Annotations to add to the service account
  annotations: {}
  # -- The name of the service account to use.
  # -- If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}
podLabels: {}

podSecurityContext: {}
# fsGroup: 2000

securityContext: {}
# capabilities:
#   drop:
#   - ALL
# readOnlyRootFilesystem: true
# runAsNonRoot: true
# runAsUser: 1000

service:
  type: ClusterIP
  port: 80

# -- Creating PVC to store configuration
config:
  persistence:
    # -- Size of persistent disk
    size: 5Gi
    # -- Annotations for PVCs
    annotations: {}
    # -- Access modes of persistent disk
    accessModes:
      - ReadWriteOnce
    # -- Config name
    name: ""
    # -- Name of the permanent volume to reference in the claim.
    # Can be used to bind to existing volumes.
    volumeName: ""

ingress:
  enabled: false
  ingressClassName: ""
  annotations: {}
  #  kubernetes.io/ingress.class: nginx
  #  kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources: {}
# We usually recommend not to specify default resources and to leave this as a conscious
# choice for the user. This also increases chances charts run on environments with little
# resources, such as Minikube. If you do want to specify resources, uncomment the following
# lines, adjust them as necessary, and remove the curly braces after 'resources:'.
# limits:
#   cpu: 100m
#   memory: 128Mi
# requests:
#   cpu: 100m
#   memory: 128Mi

# -- Additional volumes on the output Deployment definition.
volumes: []
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# -- Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: {}

tolerations: []

affinity: {}
