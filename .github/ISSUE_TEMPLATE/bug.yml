name: 🐛 Bug Report
description: Report a problem
labels: ['bug', 'awaiting triage']
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report!

        Please note that we use GitHub issues exclusively for bug reports and feature requests. For support requests, please use our other support channels to get help.
  - type: textarea
    id: description
    attributes:
      label: Description
      description: Please provide a clear and concise description of the bug or issue.
    validations:
      required: true
  - type: input
    id: version
    attributes:
      label: Version
      description: What version of Jellyseerr are you running? (You can find this in Settings → About → Version.)
    validations:
      required: true
  - type: textarea
    id: repro-steps
    attributes:
      label: Steps to Reproduce
      description: Please tell us how we can reproduce the undesired behavior.
      placeholder: |
        1. Go to [...]
        2. Click on [...]
        3. Scroll down to [...]
        4. See error in [...]
    validations:
      required: true
  - type: textarea
    id: screenshots
    attributes:
      label: Screenshots
      description: If applicable, please provide screenshots depicting the problem.
  - type: textarea
    id: logs
    attributes:
      label: Logs
      description: Please copy and paste any relevant log output. (This will be automatically formatted into code, so no need for backticks.)
      render: shell
  - type: dropdown
    id: platform
    attributes:
      label: Platform
      options:
        - desktop
        - smartphone
        - tablet
    validations:
      required: true
  - type: dropdown
    id: database
    attributes:
      options:
        - SQLite (default)
        - PostgreSQL
      label: Database
      description: Which database backend are you using?
    validations:
      required: true
  - type: input
    id: device
    attributes:
      label: Device
      description: e.g., iPhone X, Surface Pro, Samsung Galaxy Tab
    validations:
      required: true
  - type: input
    id: os
    attributes:
      label: Operating System
      description: e.g., iOS 8.1, Windows 10, Android 11
    validations:
      required: true
  - type: input
    id: browser
    attributes:
      label: Browser
      description: e.g., Chrome, Safari, Edge, Firefox
    validations:
      required: true
  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Please provide any additional information that may be relevant or helpful.
  - type: checkboxes
    id: terms
    attributes:
      label: Code of Conduct
      description: By submitting this issue, you agree to follow our [Code of Conduct](https://github.com/fallenbagel/jellyseerr/blob/develop/CODE_OF_CONDUCT.md)
      options:
        - label: I agree to follow Jellyseerr's Code of Conduct
          required: true
