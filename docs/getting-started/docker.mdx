---
title: Docker (Recommended)
description: Install Jellyseerr using Docker
sidebar_position: 1
---
# Docker
:::info
This is the recommended method for most users.
Details on how to install Docker can be found on the [official Docker website](https://docs.docker.com/get-docker/).

Refer to [Configuring Databases](/extending-jellyseerr/database-config#postgresql-options) for details on how to configure your database.
:::

## Unix (Linux, macOS)
:::warning
Be sure to replace `/path/to/appdata/config` in the below examples with a valid host directory path. If this volume mount is not configured correctly, your Jellyseerr settings/data will not be persisted when the container is recreated (e.g., when updating the image or rebooting your machine).

    The `TZ` environment variable value should also be set to the [TZ database name](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones) of your time zone!

:::

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

<Tabs groupId="docker-methods" queryString>
  <TabItem value="docker-cli" label="Docker CLI">
For details on the Docker CLI, please [review the official `docker run` documentation](https://docs.docker.com/engine/reference/run/).

#### Installation:

```bash
docker run -d \
  --name jellyseerr \
  -e LOG_LEVEL=debug \
  -e TZ=Asia/Tashkent \
  -e PORT=5055 \
  -p 5055:5055 \
  -v /path/to/appdata/config:/app/config \
  --restart unless-stopped \
  fallenbagel/jellyseerr
```

The argument `-e PORT=5055` is optional.

If you want to add a healthcheck to the above command, you can add the following flags :
```
  --health-cmd "wget --no-verbose --tries=1 --spider http://localhost:5055/api/v1/status || exit 1" \
  --health-start-period 20s \
  --health-timeout 3s \
  --health-interval 15s \
  --health-retries 3 \
```

To run the container as a specific user/group, you may optionally add `--user=[ user | user:group | uid | uid:gid | user:gid | uid:group ]` to the above command.

#### Updating:

Stop and remove the existing container:
```bash
docker stop jellyseerr && docker rm jellyseerr
```
Pull the latest image:
```bash
docker pull fallenbagel/jellyseerr
```
Finally, run the container with the same parameters originally used to create the container:
```bash
docker run -d ...
```

:::tip
You may alternatively use a third-party updating mechanism, such as [Watchtower](https://github.com/containrrr/watchtower) or [Ouroboros](https://github.com/pyouroboros/ouroboros), to keep Jellyseerr up-to-date automatically.

You could also use [diun](https://github.com/crazy-max/diun) to receive notifications when a new image is available.
:::
  </TabItem>

  <TabItem value="docker-compose" label="Docker Compose">
For details on how to use Docker Compose, please [review the official Compose documentation](https://docs.docker.com/compose/reference/).

#### Installation:
Define the `jellyseerr` service in your `compose.yaml` as follows:
```yaml
---
services:
  jellyseerr:
    image: fallenbagel/jellyseerr:latest
    container_name: jellyseerr
    environment:
      - LOG_LEVEL=debug
      - TZ=Asia/Tashkent
      - PORT=5055 #optional
    ports:
      - 5055:5055
    volumes:
      - /path/to/appdata/config:/app/config
    healthcheck:
      test: wget --no-verbose --tries=1 --spider http://localhost:5055/api/v1/status || exit 1
      start_period: 20s
      timeout: 3s
      interval: 15s
      retries: 3
    restart: unless-stopped
```

Then, start all services defined in the Compose file:
```bash
docker compose up -d
```

#### Updating:
Pull the latest image:
```bash
docker compose pull jellyseerr
```
Then, restart all services defined in the Compose file:
```bash
docker compose up -d
```
:::tip
You may alternatively use a third-party mechanism like [dockge](https://github.com/louislam/dockge) to manage your docker compose files.
:::
  </TabItem>
</Tabs>


## Unraid

1. Ensure you have the **Community Applications** plugin installed.
2. Inside the **Community Applications** app store, search for **Jellyseerr**.
3. Click the **Install Button**.
4. On the following **Add Container** screen, make changes to the **Host Port** and **Host Path 1** \(Appdata\) as needed.
5. Click apply and access "Jellyseerr" at your `<ServerIP:HostPort>` in a web browser.

## Windows

Please refer to the [Docker Desktop for Windows user manual](https://docs.docker.com/docker-for-windows/) for details on how to install Docker on Windows. There is no need to install a Linux distro if using named volumes like in the example below.
:::warning
**WSL2 will need to be installed to prevent DB corruption!** Please see the [Docker Desktop WSL 2 backend documentation](https://docs.docker.com/docker-for-windows/wsl/) for instructions on how to enable WSL2. The commands below will only work with WSL2 installed!
:::

First, create a volume to store the configuration data for Jellyseerr using using either the Docker CLI:
```bash
docker volume create jellyseerr-data
```

or the Docker Desktop app:
1. Open the Docker Desktop app
2. Head to the Volumes tab
3. Click on the "New Volume" button near the top right
4. Enter a name for the volume (example: `jellyseerr-data`) and hit "Create"

Then, create and start the Jellyseerr container:
<Tabs groupId="docker-methods" queryString>
  <TabItem value="docker-cli" label="Docker CLI">
```bash
docker run -d \
  --name jellyseerr \
  -e LOG_LEVEL=debug \
  -e TZ=Asia/Tashkent \
  -e PORT=5055 \
  -p 5055:5055 \
  -v jellyseerr-data:/app/config \
  --restart unless-stopped \
  fallenbagel/jellyseerr
```

The argument `-e PORT=5055` is optional.

If you want to add a healthcheck to the above command, you can add the following flags :
```
  --health-cmd "wget --no-verbose --tries=1 --spider http://localhost:5055/api/v1/status || exit 1" \
  --health-start-period 20s \
  --health-timeout 3s \
  --health-interval 15s \
  --health-retries 3 \
```

#### Updating:
Pull the latest image:
```bash
docker compose pull jellyseerr
```
Then, restart all services defined in the Compose file:
```bash
docker compose up -d
```
  </TabItem>

  <TabItem value="docker-compose" label="Docker Compose">
```yaml
---
services:
  jellyseerr:
    image: fallenbagel/jellyseerr:latest
    container_name: jellyseerr
    environment:
      - LOG_LEVEL=debug
      - TZ=Asia/Tashkent
    ports:
      - 5055:5055
    volumes:
      - jellyseerr-data:/app/config
    healthcheck:
      test: wget --no-verbose --tries=1 --spider http://localhost:5055/api/v1/status || exit 1
      start_period: 20s
      timeout: 3s
      interval: 15s
      retries: 3
    restart: unless-stopped

volumes:
  jellyseerr-data:
    external: true
```

#### Updating:
Pull the latest image:
```bash
docker compose pull jellyseerr
```
Then, restart all services defined in the Compose file:
```bash
docker compose up -d
```
  </TabItem>
</Tabs>

To access the files inside the volume created above, navigate to `\\wsl$\docker-desktop-data\version-pack-data\community\docker\volumes\jellyseerr-data\_data` using File Explorer.

:::info
Docker on Windows works differently than it does on Linux; it runs Docker inside of a stripped-down Linux VM. Volume mounts are exposed to Docker inside this VM via SMB mounts. While this is fine for media, it is unacceptable for the `/app/config` directory because SMB does not support file locking. This will eventually corrupt your database, which can lead to slow behavior and crashes.

**If you must run Docker on Windows, you should put the `/app/config` directory mount inside the VM and not on the Windows host.** (This also applies to other containers with SQLite databases.)

Named volumes, like in the example commands above, are automatically mounted inside the VM. Therefore the warning on the setup about the `/app/config` folder being incorrectly mounted page should be ignored.
:::


