<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
  width="456" height="96" viewBox="0 0 456 96">
  <metadata>
    <c2pa:manifest xmlns:c2pa="http://c2pa.org/manifest">
      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
    </c2pa:manifest>
  </metadata>
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-8);
        fill-rule: evenodd;
      }

      .cls-1,
      .cls-2,
      .cls-3,
      .cls-4,
      .cls-5,
      .cls-6,
      .cls-7,
      .cls-8,
      .cls-9,
      .cls-10,
      .cls-11,
      .cls-12,
      .cls-13 {
        stroke-width: 0px;
      }

      .cls-2 {
        fill: url(#linear-gradient);
      }

      .cls-3 {
        fill: url(#linear-gradient-6);
      }

      .cls-14,
      .cls-6 {
        isolation: isolate;
      }

      .cls-4 {
        fill: url(#linear-gradient-5);
      }

      .cls-5 {
        fill: none;
      }

      .cls-6 {
        fill: #000b25;
      }

      .cls-7 {
        fill: url(#linear-gradient-2);
      }

      .cls-8 {
        fill: url(#linear-gradient-7);
      }

      .cls-9 {
        fill: url(#linear-gradient-3);
      }

      .cls-15 {
        clip-path: url(#clippath);
      }

      .cls-10 {
        fill: url(#linear-gradient-9);
      }

      .cls-11 {
        fill: #fff;
      }

      .cls-12 {
        fill: url(#linear-gradient-10);
      }

      .cls-13 {
        fill: url(#linear-gradient-4);
      }
    </style>
    <clipPath id="clippath">
      <path class="cls-5"
        d="M96.1,48c0,26.31-21.18,47.71-47.48,48C22.31,96.28.68,75.33.11,49.03-.45,22.73,20.26.87,46.56.03c26.3-.85,48.37,19.63,49.5,45.92" />
    </clipPath>
    <linearGradient id="linear-gradient" x1="-1933.27" y1="3162.89" x2="-1935.8" y2="3187.3"
      gradientTransform="translate(3418.33 -5462.68) scale(1.75)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#502d95" />
      <stop offset=".1" stop-color="#6d37ac" />
      <stop offset=".57" stop-color="#6786d1" />
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="-1918.61" y1="3164.41" x2="-1921.14" y2="3188.83"
      xlink:href="#linear-gradient" />
    <linearGradient id="linear-gradient-3" x1="-1470.12" y1="2952.54" x2="-1470.12" y2="2974.75"
      gradientTransform="translate(3181.94 -6186.45) scale(2.12)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#763dcd" />
      <stop offset=".22" stop-color="#8d61eb" />
      <stop offset=".37" stop-color="#8c86ec" />
      <stop offset=".64" stop-color="#748ce8" />
      <stop offset=".9" stop-color="#6ba1e6" />
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="-1481.25" x2="-1481.25" y2="2974.75" xlink:href="#linear-gradient-3" />
    <linearGradient id="linear-gradient-5" x1="-1487.04" y1="2952.54" x2="-1487.04" y2="2974.75"
      xlink:href="#linear-gradient-3" />
    <linearGradient id="linear-gradient-6" x1="-1475.87" x2="-1475.87" y2="2974.75" xlink:href="#linear-gradient-3" />
    <linearGradient id="linear-gradient-7" x1="-1394.57" y1="3099.36" x2="-1366.7" y2="3130.32"
      gradientTransform="translate(2528.44 -5512.24) scale(1.79)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#c395fc" />
      <stop offset="1" stop-color="#4f65f5" />
    </linearGradient>
    <linearGradient id="linear-gradient-8" x1="-2155.68" y1="6519.53" x2="-2155.68" y2="6560.17"
      gradientTransform="translate(1133.03 -3304.28) scale(.51)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff" stop-opacity=".4" />
      <stop offset="1" stop-color="#fff" stop-opacity="0" />
    </linearGradient>
    <linearGradient id="linear-gradient-9" x1="-1549.99" y1="4189.28" x2="-1575.09" y2="4207.72"
      gradientTransform="translate(1639.33 -4232.26) scale(1.02)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#f9f9f9" />
      <stop offset="1" stop-color="#f9f9f9" stop-opacity="0" />
    </linearGradient>
    <linearGradient id="linear-gradient-10" x1="-1117.14" y1="3503.72" x2="-1105.01" y2="3516.1"
      gradientTransform="translate(1637.01 -4967.83) scale(1.43)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0043a2" />
      <stop offset="1" stop-color="#00133a" />
    </linearGradient>
  </defs>
  <g class="cls-14">
    <path class="cls-11"
      d="M140.4,59.2c1.9-1.6,2.8-3.7,2.8-6.2V16.6h9.3v35.1c0,4-1.6,8-4.9,12-3.3,4-7.7,6-13.3,6-4.9,0-9.2-1.8-12.8-5.3-3.6-3.5-5.4-7.6-5.4-12h9.3c0,2.8.9,5.1,2.8,6.8s3.9,2.5,6,2.5c2.3,0,4.4-.9,6.2-2.5Z" />
    <path class="cls-11"
      d="M166.5,53.2c.6,2.3,1.9,4.2,3.9,5.9s4.6,2.5,7.8,2.5c2.5,0,4.6-.6,6.3-1.8s2.9-2.3,3.5-3.4h8.7c-1.5,4-3.8,7.2-6.9,9.6-3.2,2.4-7,3.6-11.6,3.6-5.5,0-10.2-1.9-14.1-5.8-3.9-3.9-5.8-8.6-5.8-14.1s1.9-10.1,5.8-14c3.9-3.9,8.6-5.8,14.1-5.8,4.6,0,9.2,1.9,13.9,5.8,4.7,3.9,6.6,9.7,5.8,17.5,0,0-31.4,0-31.4,0ZM189.6,45.2c-.7-2.3-2.1-4-4.3-5.3s-4.5-1.9-7-1.9c-2.7,0-5,.7-6.9,2.1-1.9,1.4-3.4,3.1-4.3,5.1,0,0,22.5,0,22.5,0Z" />
    <path class="cls-11" d="M211.3,68.4h-9.3V16.6h9.3v51.8Z" />
    <path class="cls-11" d="M227.2,68.4h-9.3V16.6h9.3v51.8Z" />
    <path class="cls-11" d="M231.1,31.2h9.6l10.4,26.3,10.4-26.3h9.5l-21.2,51.7h-8.4l5.6-14.5-15.9-37.2Z" />
    <path class="cls-11"
      d="M291.5,39.8c-.9-1.1-2.3-1.7-4.1-1.8-1.7,0-3.1.4-4.1,1.3-1,.8-1.4,1.9-1.2,3,.1.8,1.1,1.7,3,2.5s3.7,1.6,5.4,2.1c2.4.8,5,2,7.6,3.5,2.7,1.5,4,4,4,7.5,0,3-1.4,5.8-4.1,8.2s-6.4,3.6-11,3.6c-5.1,0-8.9-1.6-11.4-4.7s-3.7-6-3.7-8.5h9.3c0,1.2.5,2.4,1.6,3.5s2.7,1.7,4.8,1.7,3.6-.5,4.7-1.5,1.6-2.1,1.4-3.2c-.1-.9-.6-1.7-1.7-2.4-1-.7-2.3-1.3-3.8-1.8-2.7-1.1-5.9-2.4-9.5-3.9s-5.3-4.1-5.3-7.8c0-3.1,1.4-5.8,4.1-8s6.1-3.3,10.1-3.3c5.2,0,8.9,1.7,11.2,5,2.2,3.3,3.3,6.1,3.3,8.3h-9.3c.1-1-.4-2.2-1.3-3.3Z" />
    <path class="cls-11"
      d="M314.3,53.2c.6,2.3,1.9,4.2,3.9,5.9s4.6,2.5,7.8,2.5c2.5,0,4.6-.6,6.3-1.8,1.7-1.2,2.9-2.3,3.5-3.4h8.7c-1.5,4-3.8,7.2-6.9,9.6-3.2,2.4-7,3.6-11.6,3.6-5.5,0-10.2-1.9-14.1-5.8-3.9-3.9-5.8-8.6-5.8-14.1s1.9-10.1,5.8-14c3.9-3.9,8.6-5.8,14.1-5.8,4.6,0,9.2,1.9,13.9,5.8,4.7,3.9,6.6,9.7,5.8,17.5,0,0-31.4,0-31.4,0ZM337.3,45.2c-.7-2.3-2.1-4-4.3-5.3-2.2-1.3-4.5-1.9-7-1.9-2.7,0-5,.7-6.9,2.1-1.9,1.4-3.4,3.1-4.3,5.1,0,0,22.5,0,22.5,0Z" />
    <path class="cls-11"
      d="M357.4,53.2c.6,2.3,1.9,4.2,3.9,5.9s4.6,2.5,7.8,2.5c2.5,0,4.6-.6,6.3-1.8,1.7-1.2,2.9-2.3,3.5-3.4h8.7c-1.5,4-3.8,7.2-6.9,9.6-3.2,2.4-7,3.6-11.6,3.6-5.5,0-10.2-1.9-14.1-5.8-3.9-3.9-5.8-8.6-5.8-14.1s1.9-10.1,5.8-14c3.9-3.9,8.6-5.8,14.1-5.8,4.6,0,9.2,1.9,13.9,5.8,4.7,3.9,6.6,9.7,5.8,17.5,0,0-31.4,0-31.4,0ZM380.4,45.2c-.7-2.3-2.1-4-4.3-5.3-2.2-1.3-4.5-1.9-7-1.9-2.7,0-5,.7-6.9,2.1-1.9,1.4-3.4,3.1-4.3,5.1,0,0,22.5,0,22.5,0Z" />
    <path class="cls-11"
      d="M403.6,34.5c.8-1,2.1-2,3.8-3,2.2-1.2,5.1-1.6,8.5-1v6.9c-3.7-.3-6.5.1-8.3,1.2-2.2,1.2-3.6,2.8-4.4,4.7s-1.2,3.2-1.2,3.9v21.4h-9.3V31.2h9.3v5.3c.3-.3.8-1,1.6-2Z" />
    <path class="cls-11"
      d="M430.8,34.5c.8-1,2.1-2,3.8-3,2.2-1.2,5.1-1.6,8.5-1v6.9c-3.7-.3-6.5.1-8.3,1.2-2.2,1.2-3.6,2.8-4.4,4.7s-1.2,3.2-1.2,3.9v21.4h-9.2V31.2h9.3v5.3c.2-.3.7-1,1.5-2Z" />
  </g>
  <g>
    <path id="path10669-9-1-9-9-9" class="cls-6"
      d="M96.1,48c0,26.31-21.18,47.71-47.48,48C22.31,96.28.68,75.33.11,49.03-.45,22.73,20.26.87,46.56.03c26.3-.85,48.37,19.63,49.5,45.92" />
    <g class="cls-15">
      <g>
        <g id="g6130-1-5-2-8-6">
          <path id="rect1318-7-5-3-3-5-9-9-8-5-7-5-2-3-7-5-7-0" class="cls-2"
            d="M38.05,70.69l-5.06-1.13s-1.17,7.43-1.61,11.15c-.71,6.02-1.57,14.34-1.23,20.71.37,7.01,2.29,13.76,2.92,13.76s-.34-4.29.1-13.75c.29-6.3,1.33-13.87,2.58-20.72.62-3.38,2.42-10.02,2.42-10.02h-.12Z" />
          <path id="rect1318-7-5-3-2-9-6-5-0-9-1-8-7-1-9-7-6-1-49-4-2-4" class="cls-7"
            d="M59.41,70.16h1.55c2.08,7.76,2.47,18.96,2.02,27.4-.49,9.29-3.03,18.23-3.87,18.23s.45-5.69-.13-18.21c-.39-8.35-2.16-16.2-2.35-27.41h2.78Z" />
        </g>
        <g id="g33645-2-2-8-7-6-64-4-7-0-8-1">
          <path id="rect1318-7-5-3-2-9-6-5-0-9-1-8-7-1-9-0-9-6-5-2-0" class="cls-9"
            d="M64.09,45.86h2.49c3.33,12.42,4.89,30.36,4.17,43.88-.79,14.88-4.85,29.2-6.2,29.2s.71-9.11-.21-29.17c-.62-13.38-4.41-25.95-4.7-43.91h4.46-.01Z" />
          <path id="rect1318-7-5-3-2-7-1-8-9-8-6-5-8-0-1-3-0-5-4-9-9-4" class="cls-13"
            d="M42.87,45.59h-2.49c-3.33,12.42-4.89,30.36-4.17,43.88.79,14.88,4.85,29.2,6.2,29.2s-.71-9.11.21-29.17c.62-13.38,4.41-25.95,4.7-43.91h-4.46,0Z" />
          <path id="rect1318-7-5-3-3-5-9-9-8-5-7-4-0-3-4-9-8" class="cls-4"
            d="M35.18,39.95l-5.67-2.02s-2.08,13.26-2.87,19.92c-1.26,10.75-3.75,25.61-3.14,36.99.67,12.53,4.09,24.58,5.22,24.58s-.6-7.67.18-24.56c.52-11.26,3.97-21.94,5.14-37.01.47-5.99,1.37-17.9,1.37-17.9h-.23Z" />
          <path id="rect1318-7-5-3-8-6-4-6-9-2-6-9-4-62-4-0-6-6-7" class="cls-3"
            d="M53.91,45.86l-5.11.87s.68,9.93.68,15.58c0,9.16.36,18.42.33,28.03-.03,11.05,1.81,29.55,2.77,29.55s4.06-23.82,4.72-38.06c.44-9.5-.97-17.84-1.22-23.52-.22-5.06-.93-11.88-.93-11.88l-1.25-.58h0Z" />
        </g>
        <path id="path354-3-3-0-0-7-7-5-6-8-3-92-0-0" class="cls-8"
          d="M82.09,48.88c0,12.9-2.19,13.68-5.78,19.15-2.58,3.92,2.64,6.96.55,8.04-2.5,1.29-1.71-1.05-6.67-2.38-2.15-.57-6.84.06-8.74.43-1.88.36-7.61-2.83-9.14-3.24-2.27-.61-7.84,2.35-11.23,2.35s-6.94-2.96-11.46-1.75c-5.36,1.44-11.83,4.94-12.81,3.79-1.88-2.19,4.1-3.86,1.88-7.76-1.4-2.47-6.27-8.98-6.41-15.56-.45-21.16,17.07-39.03,35.84-39.03s33.95,16.28,33.95,34.49" />
        <path id="path263-7-9-9-4-2-7-7-0-2-2-8" class="cls-1"
          d="M46.95,19.63c-10.25,0-24.58,10.61-24.58,20.86,0,1.14-.92,2.06-2.06,2.06s-2.06-.92-2.06-2.06c0-12.52,16.17-24.98,28.7-24.98,1.14,0,2.06.92,2.06,2.06s-.92,2.06-2.06,2.06Z" />
        <path id="path354-6-6-5-8-1-1-2-1-4-8-1-2-4-7-6" class="cls-10"
          d="M62.12,58.41c-1.09,1.78-2.57,3.21-4.32,4.19-.75.41-1.54.74-2.36.98-2.45,1.1-5.2,1.69-7.99,1.75-9.53.17-17.44-5.92-17.75-13.65-.15-3.79,2.11-7.72,3.86-10.75,1.48-2.56,4.03-6.97,7.39-8.73,6.85-3.6,16.08.21,20.7,8.55,1.34,2.42,2.19,5.07,2.48,7.71.21.86.33,1.74.34,2.62.03,2.29-.63,4.55-1.91,6.58-.13.26-.27.51-.42.75h-.02Z" />
        <path id="path259-2-6-4-6-7-0-1-0-5-9-4-7-1-5-7-6-2" class="cls-12"
          d="M47.07,39.46c5.94,0,10.75,4.81,10.75,10.75s-4.81,10.75-10.75,10.75-10.75-4.81-10.75-10.75c0-1.1.16-2.16.47-3.17.84,1.87,2.72,3.17,4.9,3.17,2.97,0,5.37-2.41,5.37-5.37,0-2.18-1.3-4.06-3.17-4.9,1-.31,2.06-.47,3.17-.47h.01Z" />
      </g>
    </g>
  </g>
</svg>