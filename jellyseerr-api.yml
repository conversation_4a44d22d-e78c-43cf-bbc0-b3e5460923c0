openapi: '3.0.2'
info:
  title: 'Jellyseerr API'
  version: '1.0.0'
  description: |
    This is the documentation for the Jellyseerr API backend.

    Two primary authentication methods are supported:

    - **<PERSON><PERSON> Authentication**: A valid sign-in to the `/auth/plex` or `/auth/local` will generate a valid authentication cookie.
    - **API Key Authentication**: Sign-in is also possible by passing an `X-Api-Key` header along with a valid API Key generated by Jellyseerr.
tags:
  - name: public
    description: Public API endpoints requiring no authentication.
  - name: settings
    description: Endpoints related to Jellyseerr's settings and configuration.
  - name: auth
    description: Endpoints related to logging in or out, and the currently authenticated user.
  - name: users
    description: Endpoints related to user management.
  - name: search
    description: Endpoints related to search and discovery.
  - name: request
    description: Endpoints related to request management.
  - name: movies
    description: Endpoints related to retrieving movies and their details.
  - name: tv
    description: Endpoints related to retrieving TV series and their details.
  - name: other
    description: Endpoints related to other TMDB data
  - name: person
    description: Endpoints related to retrieving person details.
  - name: media
    description: Endpoints related to media management.
  - name: collection
    description: Endpoints related to retrieving collection details.
  - name: service
    description: Endpoints related to getting service (Radarr/Sonarr) details.
  - name: watchlist
    description: Collection of media to watch later
  - name: blacklist
    description: Blacklisted media from discovery page.
servers:
  - url: '{server}/api/v1'
    variables:
      server:
        default: http://localhost:5055

components:
  schemas:
    Blacklist:
      type: object
      properties:
        tmdbId:
          type: number
          example: 1
        title:
          type: string
        media:
          $ref: '#/components/schemas/MediaInfo'
        userId:
          type: number
          example: 1
    Watchlist:
      type: object
      properties:
        id:
          type: integer
          example: 1
          readOnly: true
        tmdbId:
          type: number
          example: 1
        ratingKey:
          type: string
        type:
          type: string
        title:
          type: string
        media:
          $ref: '#/components/schemas/MediaInfo'
        createdAt:
          type: string
          example: '2020-09-12T10:00:27.000Z'
          readOnly: true
        updatedAt:
          type: string
          example: '2020-09-12T10:00:27.000Z'
          readOnly: true
        requestedBy:
          $ref: '#/components/schemas/User'
    User:
      type: object
      properties:
        id:
          type: integer
          example: 1
          readOnly: true
        email:
          type: string
          example: '<EMAIL>'
          readOnly: true
        username:
          type: string
        plexUsername:
          type: string
          readOnly: true
        plexToken:
          type: string
          readOnly: true
        jellyfinAuthToken:
          type: string
          readOnly: true
        userType:
          type: integer
          example: 1
          readOnly: true
        permissions:
          type: number
          example: 0
        avatar:
          type: string
          readOnly: true
        createdAt:
          type: string
          example: '2020-09-02T05:02:23.000Z'
          readOnly: true
        updatedAt:
          type: string
          example: '2020-09-02T05:02:23.000Z'
          readOnly: true
        requestCount:
          type: number
          example: 5
          readOnly: true
      required:
        - id
        - email
        - createdAt
        - updatedAt
    UserSettings:
      type: object
      properties:
        username:
          type: string
          nullable: true
          example: 'Mr User'
        email:
          type: string
          example: '<EMAIL>'
        discordId:
          type: string
          nullable: true
          example: '123456789'
        locale:
          type: string
          nullable: true
          example: 'en'
        discoverRegion:
          type: string
          nullable: true
          example: 'US'
        streamingRegion:
          type: string
          nullable: true
          example: 'US'
        originalLanguage:
          type: string
          nullable: true
          example: 'en'
        movieQuotaLimit:
          type: number
          nullable: true
          description: 'Maximum number of movie requests allowed'
          example: 10
        movieQuotaDays:
          type: number
          nullable: true
          description: 'Time period in days for movie quota'
          example: 30
        tvQuotaLimit:
          type: number
          nullable: true
          description: 'Maximum number of TV requests allowed'
          example: 5
        tvQuotaDays:
          type: number
          nullable: true
          description: 'Time period in days for TV quota'
          example: 14
        globalMovieQuotaDays:
          type: number
          nullable: true
          description: 'Global movie quota days setting'
          example: 30
        globalMovieQuotaLimit:
          type: number
          nullable: true
          description: 'Global movie quota limit setting'
          example: 10
        globalTvQuotaLimit:
          type: number
          nullable: true
          description: 'Global TV quota limit setting'
          example: 5
        globalTvQuotaDays:
          type: number
          nullable: true
          description: 'Global TV quota days setting'
          example: 14
        watchlistSyncMovies:
          type: boolean
          nullable: true
          description: 'Enable watchlist sync for movies'
          example: true
        watchlistSyncTv:
          type: boolean
          nullable: true
          description: 'Enable watchlist sync for TV'
          example: false
    MainSettings:
      type: object
      properties:
        apiKey:
          type: string
          readOnly: true
        appLanguage:
          type: string
          example: en
        applicationTitle:
          type: string
          example: Jellyseerr
        applicationUrl:
          type: string
          example: https://os.example.com
        hideAvailable:
          type: boolean
          example: false
        partialRequestsEnabled:
          type: boolean
          example: false
        localLogin:
          type: boolean
          example: true
        mediaServerType:
          type: number
          example: 1
        newPlexLogin:
          type: boolean
          example: true
        defaultPermissions:
          type: number
          example: 32
        enableSpecialEpisodes:
          type: boolean
          example: false
    NetworkSettings:
      type: object
      properties:
        csrfProtection:
          type: boolean
          example: false
        trustProxy:
          type: boolean
          example: true
    PlexLibrary:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
          example: Movies
        enabled:
          type: boolean
          example: false
      required:
        - id
        - name
        - enabled
    PlexSettings:
      type: object
      properties:
        name:
          type: string
          example: 'Main Server'
          readOnly: true
        machineId:
          type: string
          example: '1234123412341234'
          readOnly: true
        ip:
          type: string
          example: '127.0.0.1'
        port:
          type: number
          example: 32400
        useSsl:
          type: boolean
          nullable: true
        libraries:
          type: array
          readOnly: true
          items:
            $ref: '#/components/schemas/PlexLibrary'
        webAppUrl:
          type: string
          nullable: true
          example: 'https://app.plex.tv/desktop'
      required:
        - name
        - machineId
        - ip
        - port
    PlexConnection:
      type: object
      properties:
        protocol:
          type: string
          example: 'https'
        address:
          type: string
          example: '127.0.0.1'
        port:
          type: number
          example: 32400
        uri:
          type: string
          example: 'https://127-0-0-1.2ab6ce1a093d465e910def96cf4e4799.plex.direct:32400'
        local:
          type: boolean
          example: true
        status:
          type: number
          example: 200
        message:
          type: string
          example: 'OK'
      required:
        - protocol
        - address
        - port
        - uri
        - local
    PlexDevice:
      type: object
      properties:
        name:
          type: string
          example: 'My Plex Server'
        product:
          type: string
          example: 'Plex Media Server'
        productVersion:
          type: string
          example: '1.21'
        platform:
          type: string
          example: 'Linux'
        platformVersion:
          type: string
          example: 'default/linux/amd64/17.1/systemd'
        device:
          type: string
          example: 'PC'
        clientIdentifier:
          type: string
          example: '85a943ce-a0cc-4d2a-a4ec-f74f06e40feb'
        createdAt:
          type: string
          example: '2021-01-01T00:00:00.000Z'
        lastSeenAt:
          type: string
          example: '2021-01-01T00:00:00.000Z'
        provides:
          type: array
          items:
            type: string
            example: 'server'
        owned:
          type: boolean
          example: true
        ownerID:
          type: string
          example: '12345'
        home:
          type: boolean
          example: true
        sourceTitle:
          type: string
          example: 'xyzabc'
        accessToken:
          type: string
          example: 'supersecretaccesstoken'
        publicAddress:
          type: string
          example: '127.0.0.1'
        httpsRequired:
          type: boolean
          example: true
        synced:
          type: boolean
          example: true
        relay:
          type: boolean
          example: true
        dnsRebindingProtection:
          type: boolean
          example: false
        natLoopbackSupported:
          type: boolean
          example: false
        publicAddressMatches:
          type: boolean
          example: false
        presence:
          type: boolean
          example: true
        connection:
          type: array
          items:
            $ref: '#/components/schemas/PlexConnection'
      required:
        - name
        - product
        - productVersion
        - platform
        - device
        - clientIdentifier
        - createdAt
        - lastSeenAt
        - provides
        - owned
        - connection
    JellyfinLibrary:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
          example: Movies
        enabled:
          type: boolean
          example: false
      required:
        - id
        - name
        - enabled
    JellyfinSettings:
      type: object
      properties:
        name:
          type: string
          example: 'Main Server'
          readOnly: true
        hostname:
          type: string
          example: 'http://my.jellyfin.host'
        externalHostname:
          type: string
          example: 'http://my.jellyfin.host'
        jellyfinForgotPasswordUrl:
          type: string
          example: 'http://my.jellyfin.host/web/index.html#!/forgotpassword.html'
        adminUser:
          type: string
          example: 'admin'
        adminPass:
          type: string
          example: 'mypassword'
        libraries:
          type: array
          readOnly: true
          items:
            $ref: '#/components/schemas/JellyfinLibrary'
        serverID:
          type: string
          readOnly: true
    TautulliSettings:
      type: object
      properties:
        hostname:
          type: string
          nullable: true
          example: 'tautulli.example.com'
        port:
          type: number
          nullable: true
          example: 8181
        useSsl:
          type: boolean
          nullable: true
        apiKey:
          type: string
          nullable: true
        externalUrl:
          type: string
          nullable: true
    RadarrSettings:
      type: object
      properties:
        id:
          type: number
          example: 0
          readOnly: true
        name:
          type: string
          example: 'Radarr Main'
        hostname:
          type: string
          example: '127.0.0.1'
        port:
          type: number
          example: 7878
        apiKey:
          type: string
          example: 'exampleapikey'
        useSsl:
          type: boolean
          example: false
        baseUrl:
          type: string
        activeProfileId:
          type: number
          example: 1
        activeProfileName:
          type: string
          example: 720p/1080p
        activeDirectory:
          type: string
          example: '/movies'
        is4k:
          type: boolean
          example: false
        minimumAvailability:
          type: string
          example: 'In Cinema'
        isDefault:
          type: boolean
          example: false
        externalUrl:
          type: string
          example: http://radarr.example.com
        syncEnabled:
          type: boolean
          example: false
        preventSearch:
          type: boolean
          example: false
      required:
        - name
        - hostname
        - port
        - apiKey
        - useSsl
        - activeProfileId
        - activeProfileName
        - activeDirectory
        - is4k
        - minimumAvailability
        - isDefault
    SonarrSettings:
      type: object
      properties:
        id:
          type: number
          example: 0
          readOnly: true
        name:
          type: string
          example: 'Sonarr Main'
        hostname:
          type: string
          example: '127.0.0.1'
        port:
          type: number
          example: 8989
        apiKey:
          type: string
          example: 'exampleapikey'
        useSsl:
          type: boolean
          example: false
        baseUrl:
          type: string
        activeProfileId:
          type: number
          example: 1
        activeProfileName:
          type: string
          example: 720p/1080p
        activeDirectory:
          type: string
          example: '/tv/'
        activeLanguageProfileId:
          type: number
          example: 1
        activeAnimeProfileId:
          type: number
          nullable: true
        activeAnimeLanguageProfileId:
          type: number
          nullable: true
        activeAnimeProfileName:
          type: string
          example: 720p/1080p
          nullable: true
        activeAnimeDirectory:
          type: string
          nullable: true
        is4k:
          type: boolean
          example: false
        enableSeasonFolders:
          type: boolean
          example: false
        isDefault:
          type: boolean
          example: false
        externalUrl:
          type: string
          example: http://radarr.example.com
        syncEnabled:
          type: boolean
          example: false
        preventSearch:
          type: boolean
          example: false
      required:
        - name
        - hostname
        - port
        - apiKey
        - useSsl
        - activeProfileId
        - activeProfileName
        - activeDirectory
        - is4k
        - enableSeasonFolders
        - isDefault
    ServarrTag:
      type: object
      properties:
        id:
          type: number
          example: 1
        label:
          type: string
          example: A Label
    PublicSettings:
      type: object
      properties:
        initialized:
          type: boolean
          example: false
    MovieResult:
      type: object
      required:
        - id
        - mediaType
        - title
      properties:
        id:
          type: number
          example: 1234
        mediaType:
          type: string
        popularity:
          type: number
          example: 10
        posterPath:
          type: string
        backdropPath:
          type: string
        voteCount:
          type: number
        voteAverage:
          type: number
        genreIds:
          type: array
          items:
            type: number
        overview:
          type: string
          example: 'Overview of the movie'
        originalLanguage:
          type: string
          example: 'en'
        title:
          type: string
          example: Movie Title
        originalTitle:
          type: string
          example: Original Movie Title
        releaseDate:
          type: string
        adult:
          type: boolean
          example: false
        video:
          type: boolean
          example: false
        mediaInfo:
          $ref: '#/components/schemas/MediaInfo'
    TvResult:
      type: object
      properties:
        id:
          type: number
          example: 1234
        mediaType:
          type: string
        popularity:
          type: number
          example: 10
        posterPath:
          type: string
        backdropPath:
          type: string
        voteCount:
          type: number
        voteAverage:
          type: number
        genreIds:
          type: array
          items:
            type: number
        overview:
          type: string
          example: 'Overview of the movie'
        originalLanguage:
          type: string
          example: 'en'
        name:
          type: string
          example: TV Show Name
        originalName:
          type: string
          example: Original TV Show Name
        originCountry:
          type: array
          items:
            type: string
        firstAirDate:
          type: string
        mediaInfo:
          $ref: '#/components/schemas/MediaInfo'
    PersonResult:
      type: object
      properties:
        id:
          type: number
          example: 12345
        profilePath:
          type: string
        adult:
          type: boolean
          example: false
        mediaType:
          type: string
          default: 'person'
        knownFor:
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/MovieResult'
              - $ref: '#/components/schemas/TvResult'
    Genre:
      type: object
      properties:
        id:
          type: number
          example: 1
        name:
          type: string
          example: Adventure
    Company:
      type: object
      properties:
        id:
          type: number
          example: 1
        logo_path:
          type: string
          nullable: true
        name:
          type: string
    ProductionCompany:
      type: object
      properties:
        id:
          type: number
          example: 1
        logoPath:
          type: string
          nullable: true
        originCountry:
          type: string
        name:
          type: string
    Network:
      type: object
      properties:
        id:
          type: number
          example: 1
        logoPath:
          type: string
          nullable: true
        originCountry:
          type: string
        name:
          type: string
    RelatedVideo:
      type: object
      properties:
        url:
          type: string
          example: https://www.youtube.com/watch?v=9qhL2_UxXM0/
        key:
          type: string
          example: 9qhL2_UxXM0
        name:
          type: string
          example: Trailer for some movie (1978)
        size:
          type: number
          example: 1080
        type:
          type: string
          example: Trailer
          enum:
            - Clip
            - Teaser
            - Trailer
            - Featurette
            - Opening Credits
            - Behind the Scenes
            - Bloopers
        site:
          type: string
          enum:
            - 'YouTube'
    MovieDetails:
      type: object
      properties:
        id:
          type: number
          example: 123
          readOnly: true
        imdbId:
          type: string
          example: 'tt123'
        adult:
          type: boolean
        backdropPath:
          type: string
        posterPath:
          type: string
        budget:
          type: number
          example: 1000000
        genres:
          type: array
          items:
            $ref: '#/components/schemas/Genre'
        homepage:
          type: string
        relatedVideos:
          type: array
          items:
            $ref: '#/components/schemas/RelatedVideo'
        originalLanguage:
          type: string
        originalTitle:
          type: string
        overview:
          type: string
        popularity:
          type: number
        productionCompanies:
          type: array
          items:
            $ref: '#/components/schemas/ProductionCompany'
        productionCountries:
          type: array
          items:
            type: object
            properties:
              iso_3166_1:
                type: string
              name:
                type: string
        releaseDate:
          type: string
        releases:
          type: object
          properties:
            results:
              type: array
              items:
                type: object
                properties:
                  iso_3166_1:
                    type: string
                    example: 'US'
                  rating:
                    type: string
                    nullable: true
                  release_dates:
                    type: array
                    items:
                      type: object
                      properties:
                        certification:
                          type: string
                          example: 'PG-13'
                        iso_639_1:
                          type: string
                          nullable: true
                        note:
                          type: string
                          nullable: true
                          example: 'Blu ray'
                        release_date:
                          type: string
                          example: '2017-07-12T00:00:00.000Z'
                        type:
                          type: number
                          example: 1
        revenue:
          type: number
          nullable: true
        runtime:
          type: number
        spokenLanguages:
          type: array
          items:
            $ref: '#/components/schemas/SpokenLanguage'
        status:
          type: string
        tagline:
          type: string
        title:
          type: string
        video:
          type: boolean
        voteAverage:
          type: number
        voteCount:
          type: number
        credits:
          type: object
          properties:
            cast:
              type: array
              items:
                $ref: '#/components/schemas/Cast'
            crew:
              type: array
              items:
                $ref: '#/components/schemas/Crew'
        collection:
          type: object
          properties:
            id:
              type: number
              example: 1
            name:
              type: string
              example: A collection
            posterPath:
              type: string
            backdropPath:
              type: string
        externalIds:
          $ref: '#/components/schemas/ExternalIds'
        mediaInfo:
          $ref: '#/components/schemas/MediaInfo'
        watchProviders:
          type: array
          items:
            $ref: '#/components/schemas/WatchProviders'
    Episode:
      type: object
      properties:
        id:
          type: number
        name:
          type: string
        airDate:
          type: string
          nullable: true
        episodeNumber:
          type: number
        overview:
          type: string
        productionCode:
          type: string
        seasonNumber:
          type: number
        showId:
          type: number
        stillPath:
          type: string
          nullable: true
        voteAverage:
          type: number
        voteCount:
          type: number
    Season:
      type: object
      properties:
        id:
          type: number
        airDate:
          type: string
          nullable: true
        episodeCount:
          type: number
        name:
          type: string
        overview:
          type: string
        posterPath:
          type: string
        seasonNumber:
          type: number
        episodes:
          type: array
          items:
            $ref: '#/components/schemas/Episode'
    TvDetails:
      type: object
      properties:
        id:
          type: number
          example: 123
        backdropPath:
          type: string
        posterPath:
          type: string
        contentRatings:
          type: object
          properties:
            results:
              type: array
              items:
                type: object
                properties:
                  iso_3166_1:
                    type: string
                    example: 'US'
                  rating:
                    type: string
                    example: 'TV-14'
        createdBy:
          type: array
          items:
            type: object
            properties:
              id:
                type: number
              name:
                type: string
              gender:
                type: number
              profilePath:
                type: string
                nullable: true
        episodeRunTime:
          type: array
          items:
            type: number
        firstAirDate:
          type: string
        genres:
          type: array
          items:
            $ref: '#/components/schemas/Genre'
        homepage:
          type: string
        inProduction:
          type: boolean
        languages:
          type: array
          items:
            type: string
        lastAirDate:
          type: string
        lastEpisodeToAir:
          $ref: '#/components/schemas/Episode'
        name:
          type: string
        nextEpisodeToAir:
          $ref: '#/components/schemas/Episode'
        networks:
          type: array
          items:
            $ref: '#/components/schemas/ProductionCompany'
        numberOfEpisodes:
          type: number
        numberOfSeason:
          type: number
        originCountry:
          type: array
          items:
            type: string
        originalLanguage:
          type: string
        originalName:
          type: string
        overview:
          type: string
        popularity:
          type: number
        productionCompanies:
          type: array
          items:
            $ref: '#/components/schemas/ProductionCompany'
        productionCountries:
          type: array
          items:
            type: object
            properties:
              iso_3166_1:
                type: string
              name:
                type: string
        spokenLanguages:
          type: array
          items:
            $ref: '#/components/schemas/SpokenLanguage'
        seasons:
          type: array
          items:
            $ref: '#/components/schemas/Season'
        status:
          type: string
        tagline:
          type: string
        type:
          type: string
        voteAverage:
          type: number
        voteCount:
          type: number
        credits:
          type: object
          properties:
            cast:
              type: array
              items:
                $ref: '#/components/schemas/Cast'
            crew:
              type: array
              items:
                $ref: '#/components/schemas/Crew'
        externalIds:
          $ref: '#/components/schemas/ExternalIds'
        keywords:
          type: array
          items:
            $ref: '#/components/schemas/Keyword'
        mediaInfo:
          $ref: '#/components/schemas/MediaInfo'
        watchProviders:
          type: array
          items:
            $ref: '#/components/schemas/WatchProviders'
    MediaRequest:
      type: object
      properties:
        id:
          type: number
          example: 123
          readOnly: true
        status:
          type: number
          example: 0
          description: Status of the request. 1 = PENDING APPROVAL, 2 = APPROVED, 3 = DECLINED
          readOnly: true
        media:
          $ref: '#/components/schemas/MediaInfo'
        createdAt:
          type: string
          example: '2020-09-12T10:00:27.000Z'
          readOnly: true
        updatedAt:
          type: string
          example: '2020-09-12T10:00:27.000Z'
          readOnly: true
        requestedBy:
          $ref: '#/components/schemas/User'
        modifiedBy:
          anyOf:
            - $ref: '#/components/schemas/User'
            - type: string
              nullable: true
        is4k:
          type: boolean
          example: false
        serverId:
          type: number
        profileId:
          type: number
        rootFolder:
          type: string
      required:
        - id
        - status
    MediaInfo:
      type: object
      properties:
        id:
          type: number
          readOnly: true
        tmdbId:
          type: number
          readOnly: true
        tvdbId:
          type: number
          readOnly: true
          nullable: true
        status:
          type: number
          example: 0
          description: Availability of the media. 1 = `UNKNOWN`, 2 = `PENDING`, 3 = `PROCESSING`, 4 = `PARTIALLY_AVAILABLE`, 5 = `AVAILABLE`, 6 = `DELETED`
        requests:
          type: array
          readOnly: true
          items:
            $ref: '#/components/schemas/MediaRequest'
        createdAt:
          type: string
          example: '2020-09-12T10:00:27.000Z'
          readOnly: true
        updatedAt:
          type: string
          example: '2020-09-12T10:00:27.000Z'
          readOnly: true
    Cast:
      type: object
      properties:
        id:
          type: number
          example: 123
        castId:
          type: number
          example: 1
        character:
          type: string
          example: Some Character Name
        creditId:
          type: string
        gender:
          type: number
        name:
          type: string
          example: Some Persons Name
        order:
          type: number
        profilePath:
          type: string
          nullable: true
    Crew:
      type: object
      properties:
        id:
          type: number
          example: 123
        creditId:
          type: string
        gender:
          type: number
        name:
          type: string
          example: Some Persons Name
        job:
          type: string
        department:
          type: string
        profilePath:
          type: string
          nullable: true
    ExternalIds:
      type: object
      properties:
        facebookId:
          type: string
          nullable: true
        freebaseId:
          type: string
          nullable: true
        freebaseMid:
          type: string
          nullable: true
        imdbId:
          type: string
          nullable: true
        instagramId:
          type: string
          nullable: true
        tvdbId:
          type: number
          nullable: true
        tvrageId:
          type: number
          nullable: true
        twitterId:
          type: string
          nullable: true
    ServiceProfile:
      type: object
      properties:
        id:
          type: number
          example: 1
        name:
          type: string
          example: 720p/1080p
    PageInfo:
      type: object
      properties:
        page:
          type: number
          example: 1
        pages:
          type: number
          example: 10
        results:
          type: number
          example: 100
    DiscordSettings:
      type: object
      properties:
        enabled:
          type: boolean
          example: false
        types:
          type: number
          example: 2
        options:
          type: object
          properties:
            botUsername:
              type: string
            botAvatarUrl:
              type: string
            webhookUrl:
              type: string
            webhookRoleId:
              type: string
            enableMentions:
              type: boolean
    SlackSettings:
      type: object
      properties:
        enabled:
          type: boolean
          example: false
        types:
          type: number
          example: 2
        options:
          type: object
          properties:
            webhookUrl:
              type: string
    WebPushSettings:
      type: object
      properties:
        enabled:
          type: boolean
          example: false
        types:
          type: number
          example: 2
    WebhookSettings:
      type: object
      properties:
        enabled:
          type: boolean
          example: false
        types:
          type: number
          example: 2
        options:
          type: object
          properties:
            webhookUrl:
              type: string
            authHeader:
              type: string
            jsonPayload:
              type: string
    TelegramSettings:
      type: object
      properties:
        enabled:
          type: boolean
          example: false
        types:
          type: number
          example: 2
        options:
          type: object
          properties:
            botUsername:
              type: string
            botAPI:
              type: string
            chatId:
              type: string
            messageThreadId:
              type: string
            sendSilently:
              type: boolean
    PushbulletSettings:
      type: object
      properties:
        enabled:
          type: boolean
          example: false
        types:
          type: number
          example: 2
        options:
          type: object
          properties:
            accessToken:
              type: string
            channelTag:
              type: string
              nullable: true
    PushoverSettings:
      type: object
      properties:
        enabled:
          type: boolean
          example: false
        types:
          type: number
          example: 2
        options:
          type: object
          properties:
            accessToken:
              type: string
            userToken:
              type: string
            sound:
              type: string
    GotifySettings:
      type: object
      properties:
        enabled:
          type: boolean
          example: false
        types:
          type: number
          example: 2
        options:
          type: object
          properties:
            url:
              type: string
            token:
              type: string
    NtfySettings:
      type: object
      properties:
        enabled:
          type: boolean
          example: false
        types:
          type: number
          example: 2
        options:
          type: object
          properties:
            url:
              type: string
            topic:
              type: string
            authMethodUsernamePassword:
              type: boolean
            username:
              type: string
            password:
              type: string
            authMethodToken:
              type: boolean
            token:
              type: string
    NotificationEmailSettings:
      type: object
      properties:
        enabled:
          type: boolean
          example: false
        types:
          type: number
          example: 2
        options:
          type: object
          properties:
            emailFrom:
              type: string
              example: <EMAIL>
            senderName:
              type: string
              example: Jellyseerr
            smtpHost:
              type: string
              example: 127.0.0.1
            smtpPort:
              type: number
              example: 465
            secure:
              type: boolean
              example: false
            ignoreTls:
              type: boolean
              example: false
            requireTls:
              type: boolean
              example: false
            authUser:
              type: string
              nullable: true
            authPass:
              type: string
              nullable: true
            allowSelfSigned:
              type: boolean
              example: false
    Job:
      type: object
      properties:
        id:
          type: string
          example: job-name
        type:
          type: string
          enum: [process, command]
        interval:
          type: string
          enum: [short, long, fixed]
        name:
          type: string
          example: A Job Name
        nextExecutionTime:
          type: string
          example: '2020-09-02T05:02:23.000Z'
        running:
          type: boolean
          example: false
    PersonDetails:
      type: object
      properties:
        id:
          type: number
          example: 1
        name:
          type: string
        deathday:
          type: string
        knownForDepartment:
          type: string
        alsoKnownAs:
          type: array
          items:
            type: string
        gender:
          type: string
        biography:
          type: string
        popularity:
          type: string
        placeOfBirth:
          type: string
        profilePath:
          type: string
        adult:
          type: boolean
        imdbId:
          type: string
        homepage:
          type: string
    CreditCast:
      type: object
      properties:
        id:
          type: number
          example: 1
        originalLanguage:
          type: string
        episodeCount:
          type: number
        overview:
          type: string
        originCountry:
          type: array
          items:
            type: string
        originalName:
          type: string
        voteCount:
          type: number
        name:
          type: string
        mediaType:
          type: string
        popularity:
          type: number
        creditId:
          type: string
        backdropPath:
          type: string
        firstAirDate:
          type: string
        voteAverage:
          type: number
        genreIds:
          type: array
          items:
            type: number
        posterPath:
          type: string
        originalTitle:
          type: string
        video:
          type: boolean
        title:
          type: string
        adult:
          type: boolean
        releaseDate:
          type: string
        character:
          type: string
        mediaInfo:
          $ref: '#/components/schemas/MediaInfo'
    CreditCrew:
      type: object
      properties:
        id:
          type: number
          example: 1
        originalLanguage:
          type: string
        episodeCount:
          type: number
        overview:
          type: string
        originCountry:
          type: array
          items:
            type: string
        originalName:
          type: string
        voteCount:
          type: number
        name:
          type: string
        mediaType:
          type: string
        popularity:
          type: number
        creditId:
          type: string
        backdropPath:
          type: string
        firstAirDate:
          type: string
        voteAverage:
          type: number
        genreIds:
          type: array
          items:
            type: number
        posterPath:
          type: string
        originalTitle:
          type: string
        video:
          type: boolean
        title:
          type: string
        adult:
          type: boolean
        releaseDate:
          type: string
        department:
          type: string
        job:
          type: string
        mediaInfo:
          $ref: '#/components/schemas/MediaInfo'
    Keyword:
      type: object
      properties:
        id:
          type: number
          example: 1
        name:
          type: string
          example: 'anime'
    SpokenLanguage:
      type: object
      properties:
        englishName:
          type: string
          example: 'English'
          nullable: true
        iso_639_1:
          type: string
          example: 'en'
        name:
          type: string
          example: 'English'
    Collection:
      type: object
      properties:
        id:
          type: number
          example: 123
        name:
          type: string
          example: A Movie Collection
        overview:
          type: string
          example: Overview of collection
        posterPath:
          type: string
        backdropPath:
          type: string
        parts:
          type: array
          items:
            $ref: '#/components/schemas/MovieResult'
    SonarrSeries:
      type: object
      properties:
        title:
          type: string
          example: COVID-25
        sortTitle:
          type: string
          example: covid 25
        seasonCount:
          type: number
          example: 1
        status:
          type: string
          example: upcoming
        overview:
          type: string
          example: The thread is picked up again by Marianne Schmidt which ...
        network:
          type: string
          example: CBS
        airTime:
          type: string
          example: 02:15
        images:
          type: array
          items:
            type: object
            properties:
              coverType:
                type: string
                example: banner
              url:
                type: string
                example: /sonarr/MediaCoverProxy/6467f05d9872726ad08cbf920e5fee4bf69198682260acab8eab5d3c2c958e92/5c8f116c6aa5c.jpg
        remotePoster:
          type: string
          example: https://artworks.thetvdb.com/banners/posters/5c8f116129983.jpg
        seasons:
          type: array
          items:
            type: object
            properties:
              seasonNumber:
                type: number
                example: 1
              monitored:
                type: boolean
                example: true
        year:
          type: number
          example: 2015
        path:
          type: string
        profileId:
          type: number
        languageProfileId:
          type: number
        seasonFolder:
          type: boolean
        monitored:
          type: boolean
        useSceneNumbering:
          type: boolean
        runtime:
          type: number
        tvdbId:
          type: number
          example: 12345
        tvRageId:
          type: number
        tvMazeId:
          type: number
        firstAired:
          type: string
        lastInfoSync:
          type: string
          nullable: true
        seriesType:
          type: string
        cleanTitle:
          type: string
        imdbId:
          type: string
        titleSlug:
          type: string
        certification:
          type: string
        genres:
          type: array
          items:
            type: string
        tags:
          type: array
          items:
            type: string
        added:
          type: string
        ratings:
          type: array
          items:
            type: object
            properties:
              votes:
                type: number
              value:
                type: number
        qualityProfileId:
          type: number
        id:
          type: number
          nullable: true
        rootFolderPath:
          type: string
          nullable: true
        addOptions:
          type: array
          items:
            type: object
            properties:
              ignoreEpisodesWithFiles:
                type: boolean
                nullable: true
              ignoreEpisodesWithoutFiles:
                type: boolean
                nullable: true
              searchForMissingEpisodes:
                type: boolean
                nullable: true
    UserSettingsNotifications:
      type: object
      properties:
        notificationTypes:
          $ref: '#/components/schemas/NotificationAgentTypes'
        emailEnabled:
          type: boolean
        pgpKey:
          type: string
          nullable: true
        discordEnabled:
          type: boolean
        discordEnabledTypes:
          type: number
          nullable: true
        discordId:
          type: string
          nullable: true
        pushbulletAccessToken:
          type: string
          nullable: true
        pushoverApplicationToken:
          type: string
          nullable: true
        pushoverUserKey:
          type: string
          nullable: true
        pushoverSound:
          type: string
          nullable: true
        telegramEnabled:
          type: boolean
        telegramBotUsername:
          type: string
          nullable: true
        telegramChatId:
          type: string
          nullable: true
        telegramMessageThreadId:
          type: string
          nullable: true
        telegramSendSilently:
          type: boolean
          nullable: true
    NotificationAgentTypes:
      type: object
      properties:
        discord:
          type: number
        email:
          type: number
        pushbullet:
          type: number
        pushover:
          type: number
        slack:
          type: number
        telegram:
          type: number
        webhook:
          type: number
        webpush:
          type: number
    WatchProviders:
      type: array
      items:
        type: object
        properties:
          iso_3166_1:
            type: string
          link:
            type: string
          buy:
            type: array
            items:
              $ref: '#/components/schemas/WatchProviderDetails'
          flatrate:
            items:
              $ref: '#/components/schemas/WatchProviderDetails'
    WatchProviderDetails:
      type: object
      properties:
        displayPriority:
          type: number
        logoPath:
          type: string
        id:
          type: number
        name:
          type: string
    Issue:
      type: object
      properties:
        id:
          type: number
          example: 1
        issueType:
          type: number
          example: 1
        media:
          $ref: '#/components/schemas/MediaInfo'
        createdBy:
          $ref: '#/components/schemas/User'
        modifiedBy:
          $ref: '#/components/schemas/User'
        comments:
          type: array
          items:
            $ref: '#/components/schemas/IssueComment'
    IssueComment:
      type: object
      properties:
        id:
          type: number
          example: 1
        user:
          $ref: '#/components/schemas/User'
        message:
          type: string
          example: A comment
    DiscoverSlider:
      type: object
      properties:
        id:
          type: number
          example: 1
        type:
          type: number
          example: 1
        title:
          type: string
          nullable: true
        isBuiltIn:
          type: boolean
        enabled:
          type: boolean
        data:
          type: string
          example: '1234'
          nullable: true
      required:
        - type
        - enabled
        - title
        - data
    WatchProviderRegion:
      type: object
      properties:
        iso_3166_1:
          type: string
        english_name:
          type: string
        native_name:
          type: string
    OverrideRule:
      type: object
      properties:
        id:
          type: string
    Certification:
      type: object
      properties:
        certification:
          type: string
          example: 'PG-13'
        meaning:
          type: string
          example: 'Some material may be inappropriate for children under 13.'
          nullable: true
        order:
          type: number
          example: 3
          nullable: true
      required:
        - certification

    CertificationResponse:
      type: object
      properties:
        certifications:
          type: object
          additionalProperties:
            type: array
            items:
              $ref: '#/components/schemas/Certification'
      example:
        certifications:
          US:
            - certification: 'G'
              meaning: 'All ages admitted'
              order: 1
            - certification: 'PG'
              meaning: 'Some material may not be suitable for children under 10.'
              order: 2
  securitySchemes:
    cookieAuth:
      type: apiKey
      name: connect.sid
      in: cookie
    apiKey:
      type: apiKey
      in: header
      name: X-Api-Key

paths:
  /status:
    get:
      summary: Get Jellyseerr status
      description: Returns the current Jellyseerr status in a JSON object.
      security: []
      tags:
        - public
      responses:
        '200':
          description: Returned status
          content:
            application/json:
              schema:
                type: object
                properties:
                  version:
                    type: string
                    example: 1.0.0
                  commitTag:
                    type: string
                  updateAvailable:
                    type: boolean
                  commitsBehind:
                    type: number
                  restartRequired:
                    type: boolean
  /status/appdata:
    get:
      summary: Get application data volume status
      description: For Docker installs, returns whether or not the volume mount was configured properly. Always returns true for non-Docker installs.
      security: []
      tags:
        - public
      responses:
        '200':
          description: Application data volume status and path
          content:
            application/json:
              schema:
                type: object
                properties:
                  appData:
                    type: boolean
                    example: true
                  appDataPath:
                    type: string
                    example: /app/config
                  appDataPermissions:
                    type: boolean
                    example: true
  /settings/main:
    get:
      summary: Get main settings
      description: Retrieves all main settings in a JSON object.
      tags:
        - settings
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MainSettings'
    post:
      summary: Update main settings
      description: Updates main settings with the provided values.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MainSettings'
      responses:
        '200':
          description: 'Values were sucessfully updated'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MainSettings'
  /settings/network:
    get:
      summary: Get network settings
      description: Retrieves all network settings in a JSON object.
      tags:
        - settings
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MainSettings'
    post:
      summary: Update network settings
      description: Updates network settings with the provided values.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NetworkSettings'
      responses:
        '200':
          description: 'Values were sucessfully updated'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NetworkSettings'
  /settings/main/regenerate:
    post:
      summary: Get main settings with newly-generated API key
      description: Returns main settings in a JSON object, using the new API key.
      tags:
        - settings
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MainSettings'
  /settings/jellyfin:
    get:
      summary: Get Jellyfin settings
      description: Retrieves current Jellyfin settings.
      tags:
        - settings
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JellyfinSettings'
    post:
      summary: Update Jellyfin settings
      description: Updates Jellyfin settings with the provided values.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/JellyfinSettings'
      responses:
        '200':
          description: 'Values were successfully updated'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JellyfinSettings'
  /settings/jellyfin/library:
    get:
      summary: Get Jellyfin libraries
      description: Returns a list of Jellyfin libraries in a JSON array.
      tags:
        - settings
      parameters:
        - in: query
          name: sync
          description: Syncs the current libraries with the current Jellyfin server
          schema:
            type: string
            nullable: true
        - in: query
          name: enable
          explode: false
          allowReserved: true
          description: Comma separated list of libraries to enable. Any libraries not passed will be disabled!
          schema:
            type: string
            nullable: true
      responses:
        '200':
          description: 'Jellyfin libraries returned'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/JellyfinLibrary'
  /settings/jellyfin/users:
    get:
      summary: Get Jellyfin Users
      description: Returns a list of Jellyfin Users in a JSON array.
      tags:
        - settings
        - users
      responses:
        '200':
          description: Jellyfin users returned
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    username:
                      type: string
                    userId:
                      type: integer
  /settings/jellyfin/sync:
    get:
      summary: Get status of full Jellyfin library sync
      description: Returns sync progress in a JSON array.
      tags:
        - settings
      responses:
        '200':
          description: Status of Jellyfin sync
          content:
            application/json:
              schema:
                type: object
                properties:
                  running:
                    type: boolean
                    example: false
                  progress:
                    type: number
                    example: 0
                  total:
                    type: number
                    example: 100
                  currentLibrary:
                    $ref: '#/components/schemas/JellyfinLibrary'
                  libraries:
                    type: array
                    items:
                      $ref: '#/components/schemas/JellyfinLibrary'
    post:
      summary: Start full Jellyfin library sync
      description: Runs a full Jellyfin library sync and returns the progress in a JSON array.
      tags:
        - settings
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                cancel:
                  type: boolean
                  example: false
                start:
                  type: boolean
                  example: false
      responses:
        '200':
          description: Status of Jellyfin sync
          content:
            application/json:
              schema:
                type: object
                properties:
                  running:
                    type: boolean
                    example: false
                  progress:
                    type: number
                    example: 0
                  total:
                    type: number
                    example: 100
                  currentLibrary:
                    $ref: '#/components/schemas/JellyfinLibrary'
                  libraries:
                    type: array
                    items:
                      $ref: '#/components/schemas/JellyfinLibrary'
  /settings/plex:
    get:
      summary: Get Plex settings
      description: Retrieves current Plex settings.
      tags:
        - settings
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlexSettings'
    post:
      summary: Update Plex settings
      description: Updates Plex settings with the provided values.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PlexSettings'
      responses:
        '200':
          description: 'Values were successfully updated'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlexSettings'
  /settings/plex/library:
    get:
      summary: Get Plex libraries
      description: Returns a list of Plex libraries in a JSON array.
      tags:
        - settings
      parameters:
        - in: query
          name: sync
          description: Syncs the current libraries with the current Plex server
          schema:
            type: string
            nullable: true
        - in: query
          name: enable
          explode: false
          allowReserved: true
          description: Comma separated list of libraries to enable. Any libraries not passed will be disabled!
          schema:
            type: string
            nullable: true
      responses:
        '200':
          description: 'Plex libraries returned'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PlexLibrary'
  /settings/plex/sync:
    get:
      summary: Get status of full Plex library scan
      description: Returns scan progress in a JSON array.
      tags:
        - settings
      responses:
        '200':
          description: Status of Plex scan
          content:
            application/json:
              schema:
                type: object
                properties:
                  running:
                    type: boolean
                    example: false
                  progress:
                    type: number
                    example: 0
                  total:
                    type: number
                    example: 100
                  currentLibrary:
                    $ref: '#/components/schemas/PlexLibrary'
                  libraries:
                    type: array
                    items:
                      $ref: '#/components/schemas/PlexLibrary'
    post:
      summary: Start full Plex library scan
      description: Runs a full Plex library scan and returns the progress in a JSON array.
      tags:
        - settings
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                cancel:
                  type: boolean
                  example: false
                start:
                  type: boolean
                  example: false
      responses:
        '200':
          description: Status of Plex scan
          content:
            application/json:
              schema:
                type: object
                properties:
                  running:
                    type: boolean
                    example: false
                  progress:
                    type: number
                    example: 0
                  total:
                    type: number
                    example: 100
                  currentLibrary:
                    $ref: '#/components/schemas/PlexLibrary'
                  libraries:
                    type: array
                    items:
                      $ref: '#/components/schemas/PlexLibrary'
  /settings/plex/devices/servers:
    get:
      summary: Gets the user's available Plex servers
      description: Returns a list of available Plex servers and their connectivity state
      tags:
        - settings
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PlexDevice'
  /settings/plex/users:
    get:
      summary: Get Plex users
      description: |
        Returns a list of Plex users in a JSON array.

        Requires the `MANAGE_USERS` permission.
      tags:
        - settings
        - users
      responses:
        '200':
          description: Plex users
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: string
                    title:
                      type: string
                    username:
                      type: string
                    email:
                      type: string
                    thumb:
                      type: string
  /settings/tautulli:
    get:
      summary: Get Tautulli settings
      description: Retrieves current Tautulli settings.
      tags:
        - settings
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TautulliSettings'
    post:
      summary: Update Tautulli settings
      description: Updates Tautulli settings with the provided values.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TautulliSettings'
      responses:
        '200':
          description: 'Values were successfully updated'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TautulliSettings'
  /settings/radarr:
    get:
      summary: Get Radarr settings
      description: Returns all Radarr settings in a JSON array.
      tags:
        - settings
      responses:
        '200':
          description: 'Values were returned'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RadarrSettings'
    post:
      summary: Create Radarr instance
      description: Creates a new Radarr instance from the request body.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RadarrSettings'
      responses:
        '201':
          description: 'New Radarr instance created'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RadarrSettings'
  /settings/radarr/test:
    post:
      summary: Test Radarr configuration
      description: Tests if the Radarr configuration is valid. Returns profiles and root folders on success.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                hostname:
                  type: string
                  example: '127.0.0.1'
                port:
                  type: number
                  example: 7878
                apiKey:
                  type: string
                  example: yourapikey
                useSsl:
                  type: boolean
                  example: false
                baseUrl:
                  type: string
              required:
                - hostname
                - port
                - apiKey
                - useSsl
      responses:
        '200':
          description: Succesfully connected to Radarr instance
          content:
            application/json:
              schema:
                type: object
                properties:
                  profiles:
                    type: array
                    items:
                      $ref: '#/components/schemas/ServiceProfile'
  /settings/radarr/{radarrId}:
    put:
      summary: Update Radarr instance
      description: Updates an existing Radarr instance with the provided values.
      tags:
        - settings
      parameters:
        - in: path
          name: radarrId
          required: true
          schema:
            type: integer
          description: Radarr instance ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RadarrSettings'
      responses:
        '200':
          description: 'Radarr instance updated'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RadarrSettings'
    delete:
      summary: Delete Radarr instance
      description: Deletes an existing Radarr instance based on the radarrId parameter.
      tags:
        - settings
      parameters:
        - in: path
          name: radarrId
          required: true
          schema:
            type: integer
          description: Radarr instance ID
      responses:
        '200':
          description: 'Radarr instance updated'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RadarrSettings'
  /settings/radarr/{radarrId}/profiles:
    get:
      summary: Get available Radarr profiles
      description: Returns a list of profiles available on the Radarr server instance in a JSON array.
      tags:
        - settings
      parameters:
        - in: path
          name: radarrId
          required: true
          schema:
            type: integer
          description: Radarr instance ID
      responses:
        '200':
          description: Returned list of profiles
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ServiceProfile'
  /settings/sonarr:
    get:
      summary: Get Sonarr settings
      description: Returns all Sonarr settings in a JSON array.
      tags:
        - settings
      responses:
        '200':
          description: 'Values were returned'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SonarrSettings'
    post:
      summary: Create Sonarr instance
      description: Creates a new Sonarr instance from the request body.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SonarrSettings'
      responses:
        '201':
          description: 'New Sonarr instance created'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SonarrSettings'
  /settings/sonarr/test:
    post:
      summary: Test Sonarr configuration
      description: Tests if the Sonarr configuration is valid. Returns profiles and root folders on success.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                hostname:
                  type: string
                  example: '127.0.0.1'
                port:
                  type: number
                  example: 8989
                apiKey:
                  type: string
                  example: yourapikey
                useSsl:
                  type: boolean
                  example: false
                baseUrl:
                  type: string
              required:
                - hostname
                - port
                - apiKey
                - useSsl
      responses:
        '200':
          description: Succesfully connected to Sonarr instance
          content:
            application/json:
              schema:
                type: object
                properties:
                  profiles:
                    type: array
                    items:
                      $ref: '#/components/schemas/ServiceProfile'
  /settings/sonarr/{sonarrId}:
    put:
      summary: Update Sonarr instance
      description: Updates an existing Sonarr instance with the provided values.
      tags:
        - settings
      parameters:
        - in: path
          name: sonarrId
          required: true
          schema:
            type: integer
          description: Sonarr instance ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SonarrSettings'
      responses:
        '200':
          description: 'Sonarr instance updated'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SonarrSettings'
    delete:
      summary: Delete Sonarr instance
      description: Deletes an existing Sonarr instance based on the sonarrId parameter.
      tags:
        - settings
      parameters:
        - in: path
          name: sonarrId
          required: true
          schema:
            type: integer
          description: Sonarr instance ID
      responses:
        '200':
          description: 'Sonarr instance updated'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SonarrSettings'
  /settings/public:
    get:
      summary: Get public settings
      security: []
      description: Returns settings that are not protected or sensitive. Mainly used to determine if the application has been configured for the first time.
      tags:
        - settings
      responses:
        '200':
          description: Public settings returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PublicSettings'
  /settings/initialize:
    post:
      summary: Initialize application
      description: Sets the app as initialized, allowing the user to navigate to pages other than the setup page.
      tags:
        - settings
      responses:
        '200':
          description: Public settings returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PublicSettings'
  /settings/jobs:
    get:
      summary: Get scheduled jobs
      description: Returns list of all scheduled jobs and details about their next execution time in a JSON array.
      tags:
        - settings
      responses:
        '200':
          description: Scheduled jobs returned
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Job'
  /settings/jobs/{jobId}/run:
    post:
      summary: Invoke a specific job
      description: Invokes a specific job to run. Will return the new job status in JSON format.
      tags:
        - settings
      parameters:
        - in: path
          name: jobId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Invoked job returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Job'
  /settings/jobs/{jobId}/cancel:
    post:
      summary: Cancel a specific job
      description: Cancels a specific job. Will return the new job status in JSON format.
      tags:
        - settings
      parameters:
        - in: path
          name: jobId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Canceled job returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Job'
  /settings/jobs/{jobId}/schedule:
    post:
      summary: Modify job schedule
      description: Re-registers the job with the schedule specified. Will return the job in JSON format.
      tags:
        - settings
      parameters:
        - in: path
          name: jobId
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                schedule:
                  type: string
                  example: '0 */5 * * * *'
      responses:
        '200':
          description: Rescheduled job
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Job'
  /settings/cache:
    get:
      summary: Get a list of active caches
      description: Retrieves a list of all active caches and their current stats.
      tags:
        - settings
      responses:
        '200':
          description: Caches returned
          content:
            application/json:
              schema:
                type: object
                properties:
                  imageCache:
                    type: object
                    properties:
                      tmdb:
                        type: object
                        properties:
                          size:
                            type: number
                            example: 123456
                          imageCount:
                            type: number
                            example: 123
                      avatar:
                        type: object
                        properties:
                          size:
                            type: number
                            example: 123456
                          imageCount:
                            type: number
                            example: 123
                  apiCaches:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          example: cache-id
                        name:
                          type: string
                          example: cache name
                        stats:
                          type: object
                          properties:
                            hits:
                              type: number
                            misses:
                              type: number
                            keys:
                              type: number
                            ksize:
                              type: number
                            vsize:
                              type: number
  /settings/cache/{cacheId}/flush:
    post:
      summary: Flush a specific cache
      description: Flushes all data from the cache ID provided
      tags:
        - settings
      parameters:
        - in: path
          name: cacheId
          required: true
          schema:
            type: string
      responses:
        '204':
          description: 'Flushed cache'
  /settings/logs:
    get:
      summary: Returns logs
      description: Returns list of all log items and details
      tags:
        - settings
      parameters:
        - in: query
          name: take
          schema:
            type: number
            nullable: true
            example: 25
        - in: query
          name: skip
          schema:
            type: number
            nullable: true
            example: 0
        - in: query
          name: filter
          schema:
            type: string
            nullable: true
            enum: [debug, info, warn, error]
            default: debug
        - in: query
          name: search
          schema:
            type: string
            nullable: true
            example: plex
      responses:
        '200':
          description: Server log returned
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    label:
                      type: string
                      example: server
                    level:
                      type: string
                      example: info
                    message:
                      type: string
                      example: Server ready on port 5055
                    timestamp:
                      type: string
                      example: '2020-12-15T16:20:00.069Z'
  /settings/notifications/email:
    get:
      summary: Get email notification settings
      description: Returns current email notification settings in a JSON object.
      tags:
        - settings
      responses:
        '200':
          description: Returned email settings
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotificationEmailSettings'
    post:
      summary: Update email notification settings
      description: Updates email notification settings with provided values
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NotificationEmailSettings'
      responses:
        '200':
          description: 'Values were sucessfully updated'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotificationEmailSettings'
  /settings/notifications/email/test:
    post:
      summary: Test email settings
      description: Sends a test notification to the email agent.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NotificationEmailSettings'
      responses:
        '204':
          description: Test notification attempted
  /settings/notifications/discord:
    get:
      summary: Get Discord notification settings
      description: Returns current Discord notification settings in a JSON object.
      tags:
        - settings
      responses:
        '200':
          description: Returned Discord settings
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DiscordSettings'
    post:
      summary: Update Discord notification settings
      description: Updates Discord notification settings with the provided values.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DiscordSettings'
      responses:
        '200':
          description: 'Values were sucessfully updated'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DiscordSettings'
  /settings/notifications/discord/test:
    post:
      summary: Test Discord settings
      description: Sends a test notification to the Discord agent.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DiscordSettings'
      responses:
        '204':
          description: Test notification attempted
  /settings/notifications/pushbullet:
    get:
      summary: Get Pushbullet notification settings
      description: Returns current Pushbullet notification settings in a JSON object.
      tags:
        - settings
      responses:
        '200':
          description: Returned Pushbullet settings
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PushbulletSettings'
    post:
      summary: Update Pushbullet notification settings
      description: Update Pushbullet notification settings with the provided values.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PushbulletSettings'
      responses:
        '200':
          description: 'Values were sucessfully updated'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PushbulletSettings'
  /settings/notifications/pushbullet/test:
    post:
      summary: Test Pushbullet settings
      description: Sends a test notification to the Pushbullet agent.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PushbulletSettings'
      responses:
        '204':
          description: Test notification attempted
  /settings/notifications/pushover:
    get:
      summary: Get Pushover notification settings
      description: Returns current Pushover notification settings in a JSON object.
      tags:
        - settings
      responses:
        '200':
          description: Returned Pushover settings
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PushoverSettings'
    post:
      summary: Update Pushover notification settings
      description: Update Pushover notification settings with the provided values.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PushoverSettings'
      responses:
        '200':
          description: 'Values were sucessfully updated'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PushoverSettings'
  /settings/notifications/pushover/test:
    post:
      summary: Test Pushover settings
      description: Sends a test notification to the Pushover agent.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PushoverSettings'
      responses:
        '204':
          description: Test notification attempted
  /settings/notifications/pushover/sounds:
    get:
      summary: Get Pushover sounds
      description: Returns valid Pushover sound options in a JSON array.
      tags:
        - settings
      parameters:
        - in: query
          name: token
          required: true
          schema:
            type: string
            nullable: false
      responses:
        '200':
          description: Returned Pushover settings
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    name:
                      type: string
                    description:
                      type: string
  /settings/notifications/gotify:
    get:
      summary: Get Gotify notification settings
      description: Returns current Gotify notification settings in a JSON object.
      tags:
        - settings
      responses:
        '200':
          description: Returned Gotify settings
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GotifySettings'
    post:
      summary: Update Gotify notification settings
      description: Update Gotify notification settings with the provided values.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GotifySettings'
      responses:
        '200':
          description: 'Values were sucessfully updated'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GotifySettings'
  /settings/notifications/gotify/test:
    post:
      summary: Test Gotify settings
      description: Sends a test notification to the Gotify agent.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GotifySettings'
      responses:
        '204':
          description: Test notification attempted
  /settings/notifications/ntfy:
    get:
      summary: Get ntfy.sh notification settings
      description: Returns current ntfy.sh notification settings in a JSON object.
      tags:
        - settings
      responses:
        '200':
          description: Returned ntfy.sh settings
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NtfySettings'
    post:
      summary: Update ntfy.sh notification settings
      description: Update ntfy.sh notification settings with the provided values.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NtfySettings'
      responses:
        '200':
          description: 'Values were sucessfully updated'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NtfySettings'
  /settings/notifications/ntfy/test:
    post:
      summary: Test ntfy.sh settings
      description: Sends a test notification to the ntfy.sh agent.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NtfySettings'
      responses:
        '204':
          description: Test notification attempted
  /settings/notifications/slack:
    get:
      summary: Get Slack notification settings
      description: Returns current Slack notification settings in a JSON object.
      tags:
        - settings
      responses:
        '200':
          description: Returned slack settings
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SlackSettings'
    post:
      summary: Update Slack notification settings
      description: Updates Slack notification settings with the provided values.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SlackSettings'
      responses:
        '200':
          description: 'Values were sucessfully updated'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SlackSettings'
  /settings/notifications/slack/test:
    post:
      summary: Test Slack settings
      description: Sends a test notification to the Slack agent.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SlackSettings'
      responses:
        '204':
          description: Test notification attempted
  /settings/notifications/telegram:
    get:
      summary: Get Telegram notification settings
      description: Returns current Telegram notification settings in a JSON object.
      tags:
        - settings
      responses:
        '200':
          description: Returned Telegram settings
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TelegramSettings'
    post:
      summary: Update Telegram notification settings
      description: Update Telegram notification settings with the provided values.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TelegramSettings'
      responses:
        '200':
          description: 'Values were sucessfully updated'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TelegramSettings'
  /settings/notifications/telegram/test:
    post:
      summary: Test Telegram settings
      description: Sends a test notification to the Telegram agent.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TelegramSettings'
      responses:
        '204':
          description: Test notification attempted
  /settings/notifications/webpush:
    get:
      summary: Get Web Push notification settings
      description: Returns current Web Push notification settings in a JSON object.
      tags:
        - settings
      responses:
        '200':
          description: Returned web push settings
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebPushSettings'
    post:
      summary: Update Web Push notification settings
      description: Updates Web Push notification settings with the provided values.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WebPushSettings'
      responses:
        '200':
          description: 'Values were sucessfully updated'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebPushSettings'
  /settings/notifications/webpush/test:
    post:
      summary: Test Web Push settings
      description: Sends a test notification to the Web Push agent.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WebPushSettings'
      responses:
        '204':
          description: Test notification attempted
  /settings/notifications/webhook:
    get:
      summary: Get webhook notification settings
      description: Returns current webhook notification settings in a JSON object.
      tags:
        - settings
      responses:
        '200':
          description: Returned webhook settings
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebhookSettings'
    post:
      summary: Update webhook notification settings
      description: Updates webhook notification settings with the provided values.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WebhookSettings'
      responses:
        '200':
          description: 'Values were sucessfully updated'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebhookSettings'
  /settings/notifications/webhook/test:
    post:
      summary: Test webhook settings
      description: Sends a test notification to the webhook agent.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WebhookSettings'
      responses:
        '204':
          description: Test notification attempted
  /settings/discover:
    get:
      summary: Get all discover sliders
      description: Returns all discovery sliders. Built-in and custom made.
      tags:
        - settings
      responses:
        '200':
          description: Returned all discovery sliders
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DiscoverSlider'
    post:
      summary: Batch update all sliders.
      description: |
        Batch update all sliders at once. Should also be used for creation. Will only update sliders provided
        and will not delete any sliders not present in the request. If a slider is missing a required field,
        it will be ignored. Requires the `ADMIN` permission.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/DiscoverSlider'
      responses:
        '200':
          description: Returned all newly updated discovery sliders
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DiscoverSlider'
  /settings/discover/{sliderId}:
    put:
      summary: Update a single slider
      description: |
        Updates a single slider and return the newly updated slider. Requires the `ADMIN` permission.
      tags:
        - settings
      parameters:
        - in: path
          name: sliderId
          required: true
          schema:
            type: number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                  example: 'Slider Title'
                type:
                  type: number
                  example: 1
                data:
                  type: string
                  example: '1'
      responses:
        '200':
          description: Returns newly added discovery slider
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DiscoverSlider'
    delete:
      summary: Delete slider by ID
      description: Deletes the slider with the provided sliderId. Requires the `ADMIN` permission.
      tags:
        - settings
      parameters:
        - in: path
          name: sliderId
          required: true
          schema:
            type: number
      responses:
        '200':
          description: Slider successfully deleted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DiscoverSlider'
  /settings/discover/add:
    post:
      summary: Add a new slider
      description: |
        Add a single slider and return the newly created slider. Requires the `ADMIN` permission.
      tags:
        - settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                  example: 'New Slider'
                type:
                  type: number
                  example: 1
                data:
                  type: string
                  example: '1'
      responses:
        '200':
          description: Returns newly added discovery slider
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DiscoverSlider'
  /settings/discover/reset:
    get:
      summary: Reset all discover sliders
      description: Resets all discovery sliders to the default values. Requires the `ADMIN` permission.
      tags:
        - settings
      responses:
        '204':
          description: All sliders reset to defaults
  /settings/about:
    get:
      summary: Get server stats
      description: Returns current server stats in a JSON object.
      tags:
        - settings
      responses:
        '200':
          description: Returned about settings
          content:
            application/json:
              schema:
                type: object
                properties:
                  version:
                    type: string
                    example: '1.0.0'
                  totalRequests:
                    type: number
                    example: 100
                  totalMediaItems:
                    type: number
                    example: 100
                  tz:
                    type: string
                    nullable: true
                    example: Asia/Tokyo
                  appDataPath:
                    type: string
                    example: /app/config
  /auth/me:
    get:
      summary: Get logged-in user
      description: Returns the currently logged-in user.
      tags:
        - auth
        - users
      responses:
        '200':
          description: Object containing the logged-in user in JSON
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
  /auth/plex:
    post:
      summary: Sign in using a Plex token
      description: Takes an `authToken` (Plex token) to log the user in. Generates a session cookie for use in further requests. If the user does not exist, and there are no other users, then a user will be created with full admin privileges. If a user logs in with access to the main Plex server, they will also have an account created, but without any permissions.
      security: []
      tags:
        - auth
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                authToken:
                  type: string
              required:
                - authToken
  /auth/jellyfin:
    post:
      summary: Sign in using a Jellyfin username and password
      description: Takes the user's username and password to log the user in. Generates a session cookie for use in further requests. If the user does not exist, and there are no other users, then a user will be created with full admin privileges. If a user logs in with access to the Jellyfin server, they will also have an account created, but without any permissions.
      security: []
      tags:
        - auth
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                password:
                  type: string
                hostname:
                  type: string
                email:
                  type: string
                serverType:
                  type: number
              required:
                - username
                - password
  /auth/local:
    post:
      summary: Sign in using a local account
      description: Takes an `email` and a `password` to log the user in. Generates a session cookie for use in further requests.
      security: []
      tags:
        - auth
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                password:
                  type: string
              required:
                - email
                - password
  /auth/logout:
    post:
      summary: Sign out and clear session cookie
      description: Completely clear the session cookie and associated values, effectively signing the user out.
      tags:
        - auth
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: 'ok'
  /auth/reset-password:
    post:
      summary: Send a reset password email
      description: Sends a reset password email to the email if the user exists
      security: []
      tags:
        - users
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: 'ok'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
              required:
                - email
  /auth/reset-password/{guid}:
    post:
      summary: Reset the password for a user
      description: Resets the password for a user if the given guid is connected to a user
      security: []
      tags:
        - users
      parameters:
        - in: path
          name: guid
          required: true
          schema:
            type: string
            example: '9afef5a7-ec89-4d5f-9397-261e96970b50'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: 'ok'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                password:
                  type: string
              required:
                - password
  /user:
    get:
      summary: Get all users
      description: Returns all users in a JSON object.
      tags:
        - users
      parameters:
        - in: query
          name: take
          schema:
            type: number
            nullable: true
            example: 20
        - in: query
          name: skip
          schema:
            type: number
            nullable: true
            example: 0
        - in: query
          name: sort
          schema:
            type: string
            enum: [created, updated, requests, displayname]
            default: created
        - in: query
          name: q
          required: false
          schema:
            type: string
        - in: query
          name: includeIds
          required: false
          schema:
            type: string
      responses:
        '200':
          description: A JSON array of all users
          content:
            application/json:
              schema:
                type: object
                properties:
                  pageInfo:
                    $ref: '#/components/schemas/PageInfo'
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/User'
    post:
      summary: Create new user
      description: |
        Creates a new user. Requires the `MANAGE_USERS` permission.
      tags:
        - users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  example: '<EMAIL>'
                username:
                  type: string
                permissions:
                  type: number
      responses:
        '201':
          description: The created user
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
    put:
      summary: Update batch of users
      description: |
        Update users with given IDs with provided values in request `body.settings`. You cannot update users' Plex tokens through this request.

        Requires the `MANAGE_USERS` permission.
      tags:
        - users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                ids:
                  type: array
                  items:
                    type: integer
                permissions:
                  type: integer
      responses:
        '200':
          description: Successfully updated user details
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'
  /user/import-from-plex:
    post:
      summary: Import all users from Plex
      description: |
        Fetches and imports users from the Plex server. If a list of Plex IDs is provided in the request body, only the specified users will be imported. Otherwise, all users will be imported.

        Requires the `MANAGE_USERS` permission.
      tags:
        - users
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                plexIds:
                  type: array
                  items:
                    type: string
      responses:
        '201':
          description: A list of the newly created users
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'
  /user/import-from-jellyfin:
    post:
      summary: Import all users from Jellyfin
      description: |
        Fetches and imports users from the Jellyfin server.

        Requires the `MANAGE_USERS` permission.
      tags:
        - users
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                jellyfinUserIds:
                  type: array
                  items:
                    type: string
      responses:
        '201':
          description: A list of the newly created users
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'
  /user/registerPushSubscription:
    post:
      summary: Register a web push /user/registerPushSubscription
      description: Registers a web push subscription for the logged-in user
      tags:
        - users
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                endpoint:
                  type: string
                auth:
                  type: string
                p256dh:
                  type: string
                userAgent:
                  type: string
              required:
                - endpoint
                - auth
                - p256dh
      responses:
        '204':
          description: Successfully registered push subscription
  /user/{userId}/pushSubscriptions:
    get:
      summary: Get all web push notification settings for a user
      description: |
        Returns all web push notification settings for a user in a JSON object.
      tags:
        - users
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: number
      responses:
        '200':
          description: User web push notification settings in JSON
          content:
            application/json:
              schema:
                type: object
                properties:
                  endpoint:
                    type: string
                  p256dh:
                    type: string
                  auth:
                    type: string
                  userAgent:
                    type: string
  /user/{userId}/pushSubscription/{endpoint}:
    get:
      summary: Get web push notification settings for a user
      description: |
        Returns web push notification settings for a user in a JSON object.
      tags:
        - users
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: number
        - in: path
          name: endpoint
          required: true
          schema:
            type: string
      responses:
        '200':
          description: User web push notification settings in JSON
          content:
            application/json:
              schema:
                type: object
                properties:
                  endpoint:
                    type: string
                  p256dh:
                    type: string
                  auth:
                    type: string
                  userAgent:
                    type: string
    delete:
      summary: Delete user push subscription by key
      description: Deletes the user push subscription with the provided key.
      tags:
        - users
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: number
        - in: path
          name: endpoint
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Successfully removed user push subscription
  /user/{userId}:
    get:
      summary: Get user by ID
      description: |
        Retrieves user details in a JSON object. Requires the `MANAGE_USERS` permission.
      tags:
        - users
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: number
      responses:
        '200':
          description: Users details in JSON
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
    put:
      summary: Update a user by user ID
      description: |
        Update a user with the provided values. You cannot update a user's Plex token through this request.

        Requires the `MANAGE_USERS` permission.
      tags:
        - users
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
      responses:
        '200':
          description: Successfully updated user details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
    delete:
      summary: Delete user by ID
      description: Deletes the user with the provided userId. Requires the `MANAGE_USERS` permission.
      tags:
        - users
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: number
      responses:
        '200':
          description: User successfully deleted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
  /user/{userId}/requests:
    get:
      summary: Get requests for a specific user
      description: |
        Retrieves a user's requests in a JSON object.
      tags:
        - users
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: number
        - in: query
          name: take
          schema:
            type: number
            nullable: true
            example: 20
        - in: query
          name: skip
          schema:
            type: number
            nullable: true
            example: 0
      responses:
        '200':
          description: User's requests returned
          content:
            application/json:
              schema:
                type: object
                properties:
                  pageInfo:
                    $ref: '#/components/schemas/PageInfo'
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/MediaRequest'
  /user/{userId}/quota:
    get:
      summary: Get quotas for a specific user
      description: |
        Returns quota details for a user in a JSON object. Requires `MANAGE_USERS` permission if viewing other users.
      tags:
        - users
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: number
      responses:
        '200':
          description: User quota details in JSON
          content:
            application/json:
              schema:
                type: object
                properties:
                  movie:
                    type: object
                    properties:
                      days:
                        type: number
                        example: 7
                      limit:
                        type: number
                        example: 10
                      used:
                        type: number
                        example: 6
                      remaining:
                        type: number
                        example: 4
                      restricted:
                        type: boolean
                        example: false
                  tv:
                    type: object
                    properties:
                      days:
                        type: number
                        example: 7
                      limit:
                        type: number
                        example: 10
                      used:
                        type: number
                        example: 6
                      remaining:
                        type: number
                        example: 4
                      restricted:
                        type: boolean
                        example: false
  /blacklist:
    get:
      summary: Returns blacklisted items
      description: Returns list of all blacklisted media
      tags:
        - settings
      parameters:
        - in: query
          name: take
          schema:
            type: number
            nullable: true
            example: 25
        - in: query
          name: skip
          schema:
            type: number
            nullable: true
            example: 0
        - in: query
          name: search
          schema:
            type: string
            nullable: true
            example: dune
        - in: query
          name: filter
          schema:
            type: string
            enum: [all, manual, blacklistedTags]
            default: manual
      responses:
        '200':
          description: Blacklisted items returned
          content:
            application/json:
              schema:
                type: object
                properties:
                  pageInfo:
                    $ref: '#/components/schemas/PageInfo'
                  results:
                    type: array
                    items:
                      type: object
                      properties:
                        user:
                          $ref: '#/components/schemas/User'
                        createdAt:
                          type: string
                          example: 2024-04-21T01:55:44.000Z
                        id:
                          type: number
                          example: 1
                        mediaType:
                          type: string
                          example: movie
                        title:
                          type: string
                          example: Dune
                        tmdbId:
                          type: number
                          example: 438631
    post:
      summary: Add media to blacklist
      tags:
        - blacklist
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Blacklist'
      responses:
        '201':
          description: Item succesfully blacklisted
        '412':
          description: Item has already been blacklisted
  /blacklist/{tmdbId}:
    get:
      summary: Get media from blacklist
      tags:
        - blacklist
      parameters:
        - in: path
          name: tmdbId
          description: tmdbId ID
          required: true
          example: '1'
          schema:
            type: string
      responses:
        '200':
          description: Blacklist details in JSON
    delete:
      summary: Remove media from blacklist
      tags:
        - blacklist
      parameters:
        - in: path
          name: tmdbId
          description: tmdbId ID
          required: true
          example: '1'
          schema:
            type: string
      responses:
        '204':
          description: Succesfully removed media item
  /watchlist:
    post:
      summary: Add media to watchlist
      tags:
        - watchlist
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Watchlist'
      responses:
        '200':
          description: Watchlist data returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Watchlist'
  /watchlist/{tmdbId}:
    delete:
      summary: Delete watchlist item
      description: Removes a watchlist item.
      tags:
        - watchlist
      parameters:
        - in: path
          name: tmdbId
          description: tmdbId ID
          required: true
          example: '1'
          schema:
            type: string
      responses:
        '204':
          description: Succesfully removed watchlist item
  /user/{userId}/watchlist:
    get:
      summary: Get the Plex watchlist for a specific user
      description: |
        Retrieves a user's Plex Watchlist in a JSON object.
      tags:
        - users
        - watchlist
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: number
        - in: query
          name: page
          schema:
            type: number
            example: 1
            default: 1
      responses:
        '200':
          description: Watchlist data returned
          content:
            application/json:
              schema:
                type: object
                properties:
                  page:
                    type: number
                  totalPages:
                    type: number
                  totalResults:
                    type: number
                  results:
                    type: array
                    items:
                      type: object
                      properties:
                        tmdbId:
                          type: number
                          example: 1
                        ratingKey:
                          type: string
                        type:
                          type: string
                        title:
                          type: string
  /user/{userId}/settings/main:
    get:
      summary: Get general settings for a user
      description: Returns general settings for a specific user. Requires `MANAGE_USERS` permission if viewing other users.
      tags:
        - users
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: number
      responses:
        '200':
          description: User general settings returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserSettings'
    post:
      summary: Update general settings for a user
      description: Updates and returns general settings for a specific user. Requires `MANAGE_USERS` permission if editing other users.
      tags:
        - users
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserSettings'
      responses:
        '200':
          description: Updated user general settings returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserSettings'
  /user/{userId}/settings/password:
    get:
      summary: Get password page informatiom
      description: Returns important data for the password page to function correctly. Requires `MANAGE_USERS` permission if viewing other users.
      tags:
        - users
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: number
      responses:
        '200':
          description: User password page information returned
          content:
            application/json:
              schema:
                type: object
                properties:
                  hasPassword:
                    type: boolean
                    example: true
    post:
      summary: Update password for a user
      description: Updates a user's password. Requires `MANAGE_USERS` permission if editing other users.
      tags:
        - users
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                currentPassword:
                  type: string
                  nullable: true
                newPassword:
                  type: string
              required:
                - newPassword
      responses:
        '204':
          description: User password updated
  /user/{userId}/settings/linked-accounts/plex:
    post:
      summary: Link the provided Plex account to the current user
      description: Logs in to Plex with the provided auth token, then links the associated Plex account with the user's account. Users can only link external accounts to their own account.
      tags:
        - users
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                authToken:
                  type: string
              required:
                - authToken
      responses:
        '204':
          description: Linking account succeeded
        '403':
          description: Invalid credentials
        '422':
          description: Account already linked to a user
    delete:
      summary: Remove the linked Plex account for a user
      description: Removes the linked Plex account for a specific user. Requires `MANAGE_USERS` permission if editing other users.
      tags:
        - users
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: number
      responses:
        '204':
          description: Unlinking account succeeded
        '400':
          description: Unlink request invalid
        '404':
          description: User does not exist
  /user/{userId}/settings/linked-accounts/jellyfin:
    post:
      summary: Link the provided Jellyfin account to the current user
      description: Logs in to Jellyfin with the provided credentials, then links the associated Jellyfin account with the user's account. Users can only link external accounts to their own account.
      tags:
        - users
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                  example: 'Mr User'
                password:
                  type: string
                  example: 'supersecret'
      responses:
        '204':
          description: Linking account succeeded
        '403':
          description: Invalid credentials
        '422':
          description: Account already linked to a user
    delete:
      summary: Remove the linked Jellyfin account for a user
      description: Removes the linked Jellyfin account for a specific user. Requires `MANAGE_USERS` permission if editing other users.
      tags:
        - users
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: number
      responses:
        '204':
          description: Unlinking account succeeded
        '400':
          description: Unlink request invalid
        '404':
          description: User does not exist
  /user/{userId}/settings/notifications:
    get:
      summary: Get notification settings for a user
      description: Returns notification settings for a specific user. Requires `MANAGE_USERS` permission if viewing other users.
      tags:
        - users
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: number
      responses:
        '200':
          description: User notification settings returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserSettingsNotifications'
    post:
      summary: Update notification settings for a user
      description: Updates and returns notification settings for a specific user. Requires `MANAGE_USERS` permission if editing other users.
      tags:
        - users
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserSettingsNotifications'
      responses:
        '200':
          description: Updated user notification settings returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserSettingsNotifications'
  /user/{userId}/settings/permissions:
    get:
      summary: Get permission settings for a user
      description: Returns permission settings for a specific user. Requires `MANAGE_USERS` permission if viewing other users.
      tags:
        - users
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: number
      responses:
        '200':
          description: User permission settings returned
          content:
            application/json:
              schema:
                type: object
                properties:
                  permissions:
                    type: number
                    example: 2
    post:
      summary: Update permission settings for a user
      description: Updates and returns permission settings for a specific user. Requires `MANAGE_USERS` permission if editing other users.
      tags:
        - users
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                permissions:
                  type: number
              required:
                - permissions
      responses:
        '200':
          description: Updated user general settings returned
          content:
            application/json:
              schema:
                type: object
                properties:
                  permissions:
                    type: number
                    example: 2
  /user/{userId}/watch_data:
    get:
      summary: Get watch data
      description: |
        Returns play count, play duration, and recently watched media.

        Requires the `ADMIN` permission to fetch results for other users.
      tags:
        - users
      parameters:
        - in: path
          name: userId
          required: true
          schema:
            type: number
      responses:
        '200':
          description: Users
          content:
            application/json:
              schema:
                type: object
                properties:
                  recentlyWatched:
                    type: array
                    items:
                      $ref: '#/components/schemas/MediaInfo'
                  playCount:
                    type: number
  /search:
    get:
      summary: Search for movies, TV shows, or people
      description: Returns a list of movies, TV shows, or people a JSON object.
      tags:
        - search
      parameters:
        - in: query
          name: query
          required: true
          schema:
            type: string
            example: 'Mulan'
        - in: query
          name: page
          schema:
            type: number
            example: 1
            default: 1
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Results
          content:
            application/json:
              schema:
                type: object
                properties:
                  page:
                    type: number
                    example: 1
                  totalPages:
                    type: number
                    example: 20
                  totalResults:
                    type: number
                    example: 200
                  results:
                    type: array
                    items:
                      anyOf:
                        - $ref: '#/components/schemas/MovieResult'
                        - $ref: '#/components/schemas/TvResult'
                        - $ref: '#/components/schemas/PersonResult'
  /search/keyword:
    get:
      summary: Search for keywords
      description: Returns a list of TMDB keywords matching the search query
      tags:
        - search
      parameters:
        - in: query
          name: query
          required: true
          schema:
            type: string
            example: 'christmas'
        - in: query
          name: page
          schema:
            type: number
            example: 1
            default: 1
      responses:
        '200':
          description: Results
          content:
            application/json:
              schema:
                type: object
                properties:
                  page:
                    type: number
                    example: 1
                  totalPages:
                    type: number
                    example: 20
                  totalResults:
                    type: number
                    example: 200
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/Keyword'
  /search/company:
    get:
      summary: Search for companies
      description: Returns a list of TMDB companies matching the search query. (Will not return origin country)
      tags:
        - search
      parameters:
        - in: query
          name: query
          required: true
          schema:
            type: string
            example: 'Disney'
        - in: query
          name: page
          schema:
            type: number
            example: 1
            default: 1
      responses:
        '200':
          description: Results
          content:
            application/json:
              schema:
                type: object
                properties:
                  page:
                    type: number
                    example: 1
                  totalPages:
                    type: number
                    example: 20
                  totalResults:
                    type: number
                    example: 200
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/Company'
  /discover/movies:
    get:
      summary: Discover movies
      description: Returns a list of movies in a JSON object.
      tags:
        - search
      parameters:
        - in: query
          name: page
          schema:
            type: number
            example: 1
            default: 1
        - in: query
          name: language
          schema:
            type: string
            example: en
        - in: query
          name: genre
          schema:
            type: string
            example: 18
        - in: query
          name: studio
          schema:
            type: number
            example: 1
        - in: query
          name: keywords
          schema:
            type: string
            example: 1,2
        - in: query
          name: sortBy
          schema:
            type: string
            example: popularity.desc
        - in: query
          name: primaryReleaseDateGte
          schema:
            type: string
            example: 2022-01-01
        - in: query
          name: primaryReleaseDateLte
          schema:
            type: string
            example: 2023-01-01
        - in: query
          name: withRuntimeGte
          schema:
            type: number
            example: 60
        - in: query
          name: withRuntimeLte
          schema:
            type: number
            example: 120
        - in: query
          name: voteAverageGte
          schema:
            type: number
            example: 7
        - in: query
          name: voteAverageLte
          schema:
            type: number
            example: 10
        - in: query
          name: voteCountGte
          schema:
            type: number
            example: 7
        - in: query
          name: voteCountLte
          schema:
            type: number
            example: 10
        - in: query
          name: watchRegion
          schema:
            type: string
            example: US
        - in: query
          name: watchProviders
          schema:
            type: string
            example: 8|9
        - in: query
          name: certification
          schema:
            type: string
            example: PG-13
          description: Exact certification to filter by (used when certificationMode is 'exact')
        - in: query
          name: certificationGte
          schema:
            type: string
            example: G
          description: Minimum certification to filter by (used when certificationMode is 'range')
        - in: query
          name: certificationLte
          schema:
            type: string
            example: PG-13
          description: Maximum certification to filter by (used when certificationMode is 'range')
        - in: query
          name: certificationCountry
          schema:
            type: string
            example: US
          description: Country code for the certification system (e.g., US, GB, CA)
        - in: query
          name: certificationMode
          schema:
            type: string
            enum: [exact, range]
            example: exact
          description: Determines whether to use exact certification matching or a certification range (internal use only, not sent to TMDB API)
      responses:
        '200':
          description: Results
          content:
            application/json:
              schema:
                type: object
                properties:
                  page:
                    type: number
                    example: 1
                  totalPages:
                    type: number
                    example: 20
                  totalResults:
                    type: number
                    example: 200
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/MovieResult'
  /discover/movies/genre/{genreId}:
    get:
      summary: Discover movies by genre
      description: Returns a list of movies based on the provided genre ID in a JSON object.
      tags:
        - search
      parameters:
        - in: path
          name: genreId
          required: true
          schema:
            type: string
            example: '1'
        - in: query
          name: page
          schema:
            type: number
            example: 1
            default: 1
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Results
          content:
            application/json:
              schema:
                type: object
                properties:
                  page:
                    type: number
                    example: 1
                  totalPages:
                    type: number
                    example: 20
                  totalResults:
                    type: number
                    example: 200
                  genre:
                    $ref: '#/components/schemas/Genre'
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/MovieResult'
  /discover/movies/language/{language}:
    get:
      summary: Discover movies by original language
      description: Returns a list of movies based on the provided ISO 639-1 language code in a JSON object.
      tags:
        - search
      parameters:
        - in: path
          name: language
          required: true
          schema:
            type: string
            example: en
        - in: query
          name: page
          schema:
            type: number
            example: 1
            default: 1
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Results
          content:
            application/json:
              schema:
                type: object
                properties:
                  page:
                    type: number
                    example: 1
                  totalPages:
                    type: number
                    example: 20
                  totalResults:
                    type: number
                    example: 200
                  language:
                    $ref: '#/components/schemas/SpokenLanguage'
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/MovieResult'
  /discover/movies/studio/{studioId}:
    get:
      summary: Discover movies by studio
      description: Returns a list of movies based on the provided studio ID in a JSON object.
      tags:
        - search
      parameters:
        - in: path
          name: studioId
          required: true
          schema:
            type: string
            example: '1'
        - in: query
          name: page
          schema:
            type: number
            example: 1
            default: 1
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Results
          content:
            application/json:
              schema:
                type: object
                properties:
                  page:
                    type: number
                    example: 1
                  totalPages:
                    type: number
                    example: 20
                  totalResults:
                    type: number
                    example: 200
                  studio:
                    $ref: '#/components/schemas/ProductionCompany'
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/MovieResult'
  /discover/movies/upcoming:
    get:
      summary: Upcoming movies
      description: Returns a list of movies in a JSON object.
      tags:
        - search
      parameters:
        - in: query
          name: page
          schema:
            type: number
            example: 1
            default: 1
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Results
          content:
            application/json:
              schema:
                type: object
                properties:
                  page:
                    type: number
                    example: 1
                  totalPages:
                    type: number
                    example: 20
                  totalResults:
                    type: number
                    example: 200
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/MovieResult'
  /discover/tv:
    get:
      summary: Discover TV shows
      description: Returns a list of TV shows in a JSON object.
      tags:
        - search
      parameters:
        - in: query
          name: page
          schema:
            type: number
            example: 1
            default: 1
        - in: query
          name: language
          schema:
            type: string
            example: en
        - in: query
          name: genre
          schema:
            type: string
            example: 18
        - in: query
          name: network
          schema:
            type: number
            example: 1
        - in: query
          name: keywords
          schema:
            type: string
            example: 1,2
        - in: query
          name: sortBy
          schema:
            type: string
            example: popularity.desc
        - in: query
          name: firstAirDateGte
          schema:
            type: string
            example: 2022-01-01
        - in: query
          name: firstAirDateLte
          schema:
            type: string
            example: 2023-01-01
        - in: query
          name: withRuntimeGte
          schema:
            type: number
            example: 60
        - in: query
          name: withRuntimeLte
          schema:
            type: number
            example: 120
        - in: query
          name: voteAverageGte
          schema:
            type: number
            example: 7
        - in: query
          name: voteAverageLte
          schema:
            type: number
            example: 10
        - in: query
          name: voteCountGte
          schema:
            type: number
            example: 7
        - in: query
          name: voteCountLte
          schema:
            type: number
            example: 10
        - in: query
          name: watchRegion
          schema:
            type: string
            example: US
        - in: query
          name: watchProviders
          schema:
            type: string
            example: 8|9
        - in: query
          name: status
          schema:
            type: string
            example: 3|4
        - in: query
          name: certification
          schema:
            type: string
            example: TV-14
          description: Exact certification to filter by (used when certificationMode is 'exact')
        - in: query
          name: certificationGte
          schema:
            type: string
            example: TV-PG
          description: Minimum certification to filter by (used when certificationMode is 'range')
        - in: query
          name: certificationLte
          schema:
            type: string
            example: TV-MA
          description: Maximum certification to filter by (used when certificationMode is 'range')
        - in: query
          name: certificationCountry
          schema:
            type: string
            example: US
          description: Country code for the certification system (e.g., US, GB, CA)
        - in: query
          name: certificationMode
          schema:
            type: string
            enum: [exact, range]
            example: exact
          description: Determines whether to use exact certification matching or a certification range (internal use only, not sent to TMDB API)
      responses:
        '200':
          description: Results
          content:
            application/json:
              schema:
                type: object
                properties:
                  page:
                    type: number
                    example: 1
                  totalPages:
                    type: number
                    example: 20
                  totalResults:
                    type: number
                    example: 200
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/TvResult'
  /discover/tv/language/{language}:
    get:
      summary: Discover TV shows by original language
      description: Returns a list of TV shows based on the provided ISO 639-1 language code in a JSON object.
      tags:
        - search
      parameters:
        - in: path
          name: language
          required: true
          schema:
            type: string
            example: en
        - in: query
          name: page
          schema:
            type: number
            example: 1
            default: 1
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Results
          content:
            application/json:
              schema:
                type: object
                properties:
                  page:
                    type: number
                    example: 1
                  totalPages:
                    type: number
                    example: 20
                  totalResults:
                    type: number
                    example: 200
                  language:
                    $ref: '#/components/schemas/SpokenLanguage'
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/TvResult'
  /discover/tv/genre/{genreId}:
    get:
      summary: Discover TV shows by genre
      description: Returns a list of TV shows based on the provided genre ID in a JSON object.
      tags:
        - search
      parameters:
        - in: path
          name: genreId
          required: true
          schema:
            type: string
            example: '1'
        - in: query
          name: page
          schema:
            type: number
            example: 1
            default: 1
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Results
          content:
            application/json:
              schema:
                type: object
                properties:
                  page:
                    type: number
                    example: 1
                  totalPages:
                    type: number
                    example: 20
                  totalResults:
                    type: number
                    example: 200
                  genre:
                    $ref: '#/components/schemas/Genre'
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/TvResult'
  /discover/tv/network/{networkId}:
    get:
      summary: Discover TV shows by network
      description: Returns a list of TV shows based on the provided network ID in a JSON object.
      tags:
        - search
      parameters:
        - in: path
          name: networkId
          required: true
          schema:
            type: string
            example: '1'
        - in: query
          name: page
          schema:
            type: number
            example: 1
            default: 1
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Results
          content:
            application/json:
              schema:
                type: object
                properties:
                  page:
                    type: number
                    example: 1
                  totalPages:
                    type: number
                    example: 20
                  totalResults:
                    type: number
                    example: 200
                  network:
                    $ref: '#/components/schemas/Network'
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/TvResult'
  /discover/tv/upcoming:
    get:
      summary: Discover Upcoming TV shows
      description: Returns a list of upcoming TV shows in a JSON object.
      tags:
        - search
      parameters:
        - in: query
          name: page
          schema:
            type: number
            example: 1
            default: 1
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Results
          content:
            application/json:
              schema:
                type: object
                properties:
                  page:
                    type: number
                    example: 1
                  totalPages:
                    type: number
                    example: 20
                  totalResults:
                    type: number
                    example: 200
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/TvResult'
  /discover/trending:
    get:
      summary: Trending movies and TV
      description: Returns a list of movies and TV shows in a JSON object.
      tags:
        - search
      parameters:
        - in: query
          name: page
          schema:
            type: number
            example: 1
            default: 1
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Results
          content:
            application/json:
              schema:
                type: object
                properties:
                  page:
                    type: number
                    example: 1
                  totalPages:
                    type: number
                    example: 20
                  totalResults:
                    type: number
                    example: 200
                  results:
                    type: array
                    items:
                      anyOf:
                        - $ref: '#/components/schemas/MovieResult'
                        - $ref: '#/components/schemas/TvResult'
                        - $ref: '#/components/schemas/PersonResult'
  /discover/keyword/{keywordId}/movies:
    get:
      summary: Get movies from keyword
      description: Returns list of movies based on the provided keyword ID a JSON object.
      tags:
        - search
      parameters:
        - in: path
          name: keywordId
          required: true
          schema:
            type: number
            example: 207317
        - in: query
          name: page
          schema:
            type: number
            example: 1
            default: 1
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: List of movies
          content:
            application/json:
              schema:
                type: object
                properties:
                  page:
                    type: number
                    example: 1
                  totalPages:
                    type: number
                    example: 20
                  totalResults:
                    type: number
                    example: 200
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/MovieResult'
  /discover/genreslider/movie:
    get:
      summary: Get genre slider data for movies
      description: Returns a list of genres with backdrops attached
      tags:
        - search
      parameters:
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Genre slider data returned
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: number
                      example: 1
                    backdrops:
                      type: array
                      items:
                        type: string
                    name:
                      type: string
                      example: Genre Name
  /discover/genreslider/tv:
    get:
      summary: Get genre slider data for TV series
      description: Returns a list of genres with backdrops attached
      tags:
        - search
      parameters:
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Genre slider data returned
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: number
                      example: 1
                    backdrops:
                      type: array
                      items:
                        type: string
                    name:
                      type: string
                      example: Genre Name
  /discover/watchlist:
    get:
      summary: Get the Plex watchlist.
      tags:
        - search
      parameters:
        - in: query
          name: page
          schema:
            type: number
            example: 1
            default: 1
      responses:
        '200':
          description: Watchlist data returned
          content:
            application/json:
              schema:
                type: object
                properties:
                  page:
                    type: number
                  totalPages:
                    type: number
                  totalResults:
                    type: number
                  results:
                    type: array
                    items:
                      type: object
                      properties:
                        tmdbId:
                          type: number
                          example: 1
                        ratingKey:
                          type: string
                        type:
                          type: string
                        title:
                          type: string
  /request:
    get:
      summary: Get all requests
      description: |
        Returns all requests if the user has the `ADMIN` or `MANAGE_REQUESTS` permissions. Otherwise, only the logged-in user's requests are returned.

        If the `requestedBy` parameter is specified, only requests from that particular user ID will be returned.
      tags:
        - request
      parameters:
        - in: query
          name: take
          schema:
            type: number
            nullable: true
            example: 20
        - in: query
          name: skip
          schema:
            type: number
            nullable: true
            example: 0
        - in: query
          name: filter
          schema:
            type: string
            nullable: true
            enum:
              [
                all,
                approved,
                available,
                pending,
                processing,
                unavailable,
                failed,
                deleted,
                completed,
              ]
        - in: query
          name: sort
          schema:
            type: string
            enum: [added, modified]
            default: added
        - in: query
          name: sortDirection
          schema:
            type: string
            enum: [asc, desc]
            nullable: true
            default: desc
        - in: query
          name: requestedBy
          schema:
            type: number
            nullable: true
            example: 1
        - in: query
          name: mediaType
          schema:
            type: string
            enum: [movie, tv, all]
            nullable: true
            default: all
      responses:
        '200':
          description: Requests returned
          content:
            application/json:
              schema:
                type: object
                properties:
                  pageInfo:
                    $ref: '#/components/schemas/PageInfo'
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/MediaRequest'
    post:
      summary: Create new request
      description: |
        Creates a new request with the provided media ID and type. The `REQUEST` permission is required.

        If the user has the `ADMIN` or `AUTO_APPROVE` permissions, their request will be auomatically approved.
      tags:
        - request
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                mediaType:
                  type: string
                  enum: [movie, tv]
                  example: movie
                mediaId:
                  type: number
                  example: 123
                tvdbId:
                  type: number
                  example: 123
                seasons:
                  oneOf:
                    - type: array
                      items:
                        type: number
                        minimum: 0
                    - type: string
                      enum: [all]
                is4k:
                  type: boolean
                  example: false
                serverId:
                  type: number
                profileId:
                  type: number
                rootFolder:
                  type: string
                languageProfileId:
                  type: number
                userId:
                  type: number
                  nullable: true
              required:
                - mediaType
                - mediaId
      responses:
        '201':
          description: Succesfully created the request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MediaRequest'
  /request/count:
    get:
      summary: Gets request counts
      description: |
        Returns the number of pending and approved requests.
      tags:
        - request
      responses:
        '200':
          description: Request counts returned
          content:
            application/json:
              schema:
                type: object
                properties:
                  total:
                    type: number
                  movie:
                    type: number
                  tv:
                    type: number
                  pending:
                    type: number
                  approved:
                    type: number
                  declined:
                    type: number
                  processing:
                    type: number
                  available:
                    type: number
  /request/{requestId}:
    get:
      summary: Get MediaRequest
      description: Returns a specific MediaRequest in a JSON object.
      tags:
        - request
      parameters:
        - in: path
          name: requestId
          description: Request ID
          required: true
          example: '1'
          schema:
            type: string
      responses:
        '200':
          description: Succesfully returns request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MediaRequest'
    put:
      summary: Update MediaRequest
      description: Updates a specific media request and returns the request in a JSON object. Requires the `MANAGE_REQUESTS` permission.
      tags:
        - request
      parameters:
        - in: path
          name: requestId
          description: Request ID
          required: true
          example: '1'
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                mediaType:
                  type: string
                  enum: [movie, tv]
                seasons:
                  type: array
                  items:
                    type: number
                    minimum: 0
                is4k:
                  type: boolean
                  example: false
                serverId:
                  type: number
                profileId:
                  type: number
                rootFolder:
                  type: string
                languageProfileId:
                  type: number
                userId:
                  type: number
                  nullable: true
              required:
                - mediaType
      responses:
        '200':
          description: Succesfully updated request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MediaRequest'
    delete:
      summary: Delete request
      description: Removes a request. If the user has the `MANAGE_REQUESTS` permission, any request can be removed. Otherwise, only pending requests can be removed.
      tags:
        - request
      parameters:
        - in: path
          name: requestId
          description: Request ID
          required: true
          example: '1'
          schema:
            type: string
      responses:
        '204':
          description: Succesfully removed request
  /request/{requestId}/retry:
    post:
      summary: Retry failed request
      description: |
        Retries a request by resending requests to Sonarr or Radarr.

        Requires the `MANAGE_REQUESTS` permission or `ADMIN`.
      tags:
        - request
      parameters:
        - in: path
          name: requestId
          description: Request ID
          required: true
          schema:
            type: string
            example: '1'
      responses:
        '200':
          description: Retry triggered
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MediaRequest'
  /request/{requestId}/{status}:
    post:
      summary: Update a request's status
      description: |
        Updates a request's status to approved or declined. Also returns the request in a JSON object.

        Requires the `MANAGE_REQUESTS` permission or `ADMIN`.
      tags:
        - request
      parameters:
        - in: path
          name: requestId
          description: Request ID
          required: true
          schema:
            type: string
            example: '1'
        - in: path
          name: status
          description: New status
          required: true
          schema:
            type: string
            enum: [approve, decline]
      responses:
        '200':
          description: Request status changed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MediaRequest'
  /movie/{movieId}:
    get:
      summary: Get movie details
      description: Returns full movie details in a JSON object.
      tags:
        - movies
      parameters:
        - in: path
          name: movieId
          required: true
          schema:
            type: number
            example: 337401
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Movie details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MovieDetails'
  /movie/{movieId}/recommendations:
    get:
      summary: Get recommended movies
      description: Returns list of recommended movies based on provided movie ID in a JSON object.
      tags:
        - movies
      parameters:
        - in: path
          name: movieId
          required: true
          schema:
            type: number
            example: 337401
        - in: query
          name: page
          schema:
            type: number
            example: 1
            default: 1
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: List of movies
          content:
            application/json:
              schema:
                type: object
                properties:
                  page:
                    type: number
                    example: 1
                  totalPages:
                    type: number
                    example: 20
                  totalResults:
                    type: number
                    example: 200
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/MovieResult'
  /movie/{movieId}/similar:
    get:
      summary: Get similar movies
      description: Returns list of similar movies based on the provided movieId in a JSON object.
      tags:
        - movies
      parameters:
        - in: path
          name: movieId
          required: true
          schema:
            type: number
            example: 337401
        - in: query
          name: page
          schema:
            type: number
            example: 1
            default: 1
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: List of movies
          content:
            application/json:
              schema:
                type: object
                properties:
                  page:
                    type: number
                    example: 1
                  totalPages:
                    type: number
                    example: 20
                  totalResults:
                    type: number
                    example: 200
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/MovieResult'
  /movie/{movieId}/ratings:
    get:
      summary: Get movie ratings
      description: Returns ratings based on the provided movieId in a JSON object.
      tags:
        - movies
      parameters:
        - in: path
          name: movieId
          required: true
          schema:
            type: number
            example: 337401
      responses:
        '200':
          description: Ratings returned
          content:
            application/json:
              schema:
                type: object
                properties:
                  title:
                    type: string
                    example: Mulan
                  year:
                    type: number
                    example: 2020
                  url:
                    type: string
                    example: 'http://www.rottentomatoes.com/m/mulan_2020/'
                  criticsScore:
                    type: number
                    example: 85
                  criticsRating:
                    type: string
                    enum: ['Rotten', 'Fresh', 'Certified Fresh']
                  audienceScore:
                    type: number
                    example: 65
                  audienceRating:
                    type: string
                    enum: ['Spilled', 'Upright']
  /movie/{movieId}/ratingscombined:
    get:
      summary: Get RT and IMDB movie ratings combined
      description: Returns ratings from RottenTomatoes and IMDB based on the provided movieId in a JSON object.
      tags:
        - movies
      parameters:
        - in: path
          name: movieId
          required: true
          schema:
            type: number
            example: 337401
      responses:
        '200':
          description: Ratings returned
          content:
            application/json:
              schema:
                type: object
                properties:
                  rt:
                    type: object
                    properties:
                      title:
                        type: string
                        example: Mulan
                      year:
                        type: number
                        example: 2020
                      url:
                        type: string
                        example: 'http://www.rottentomatoes.com/m/mulan_2020/'
                      criticsScore:
                        type: number
                        example: 85
                      criticsRating:
                        type: string
                        enum: ['Rotten', 'Fresh', 'Certified Fresh']
                      audienceScore:
                        type: number
                        example: 65
                      audienceRating:
                        type: string
                        enum: ['Spilled', 'Upright']
                  imdb:
                    type: object
                    properties:
                      title:
                        type: string
                        example: I am Legend
                      url:
                        type: string
                        example: 'https://www.imdb.com/title/tt0480249'
                      criticsScore:
                        type: number
                        example: 6.5
  /tv/{tvId}:
    get:
      summary: Get TV details
      description: Returns full TV details in a JSON object.
      tags:
        - tv
      parameters:
        - in: path
          name: tvId
          required: true
          schema:
            type: number
            example: 76479
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: TV details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TvDetails'
  /tv/{tvId}/season/{seasonId}:
    get:
      summary: Get season details and episode list
      description: Returns season details with a list of episodes in a JSON object.
      tags:
        - tv
      parameters:
        - in: path
          name: tvId
          required: true
          schema:
            type: number
            example: 76479
        - in: path
          name: seasonId
          required: true
          schema:
            type: number
            example: 1
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: TV details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Season'
  /tv/{tvId}/recommendations:
    get:
      summary: Get recommended TV series
      description: Returns list of recommended TV series based on the provided tvId in a JSON object.
      tags:
        - tv
      parameters:
        - in: path
          name: tvId
          required: true
          schema:
            type: number
            example: 76479
        - in: query
          name: page
          schema:
            type: number
            example: 1
            default: 1
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: List of TV series
          content:
            application/json:
              schema:
                type: object
                properties:
                  page:
                    type: number
                    example: 1
                  totalPages:
                    type: number
                    example: 20
                  totalResults:
                    type: number
                    example: 200
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/TvResult'
  /tv/{tvId}/similar:
    get:
      summary: Get similar TV series
      description: Returns list of similar TV series based on the provided tvId in a JSON object.
      tags:
        - tv
      parameters:
        - in: path
          name: tvId
          required: true
          schema:
            type: number
            example: 76479
        - in: query
          name: page
          schema:
            type: number
            example: 1
            default: 1
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: List of TV series
          content:
            application/json:
              schema:
                type: object
                properties:
                  page:
                    type: number
                    example: 1
                  totalPages:
                    type: number
                    example: 20
                  totalResults:
                    type: number
                    example: 200
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/TvResult'
  /tv/{tvId}/ratings:
    get:
      summary: Get TV ratings
      description: Returns ratings based on provided tvId in a JSON object.
      tags:
        - tv
      parameters:
        - in: path
          name: tvId
          required: true
          schema:
            type: number
            example: 76479
      responses:
        '200':
          description: Ratings returned
          content:
            application/json:
              schema:
                type: object
                properties:
                  title:
                    type: string
                    example: The Boys
                  year:
                    type: number
                    example: 2019
                  url:
                    type: string
                    example: 'http://www.rottentomatoes.com/m/mulan_2020/'
                  criticsScore:
                    type: number
                    example: 85
                  criticsRating:
                    type: string
                    enum: ['Rotten', 'Fresh']
  /person/{personId}:
    get:
      summary: Get person details
      description: Returns person details based on provided personId in a JSON object.
      tags:
        - person
      parameters:
        - in: path
          name: personId
          required: true
          schema:
            type: number
            example: 287
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Returned person
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PersonDetails'
  /person/{personId}/combined_credits:
    get:
      summary: Get combined credits
      description: Returns the person's combined credits based on the provided personId in a JSON object.
      tags:
        - person
      parameters:
        - in: path
          name: personId
          required: true
          schema:
            type: number
            example: 287
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Returned combined credits
          content:
            application/json:
              schema:
                type: object
                properties:
                  cast:
                    type: array
                    items:
                      $ref: '#/components/schemas/CreditCast'
                  crew:
                    type: array
                    items:
                      $ref: '#/components/schemas/CreditCrew'
                  id:
                    type: number
  /media:
    get:
      summary: Get media
      description: Returns all media (can be filtered and limited) in a JSON object.
      tags:
        - media
      parameters:
        - in: query
          name: take
          schema:
            type: number
            nullable: true
            example: 20
        - in: query
          name: skip
          schema:
            type: number
            nullable: true
            example: 0
        - in: query
          name: filter
          schema:
            type: string
            nullable: true
            enum:
              [
                all,
                available,
                partial,
                allavailable,
                processing,
                pending,
                deleted,
              ]
        - in: query
          name: sort
          schema:
            type: string
            enum: [added, modified, mediaAdded]
            default: added
      responses:
        '200':
          description: Returned media
          content:
            application/json:
              schema:
                type: object
                properties:
                  pageInfo:
                    $ref: '#/components/schemas/PageInfo'
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/MediaInfo'
  /media/{mediaId}:
    delete:
      summary: Delete media item
      description: Removes a media item. The `MANAGE_REQUESTS` permission is required to perform this action.
      tags:
        - media
      parameters:
        - in: path
          name: mediaId
          description: Media ID
          required: true
          example: '1'
          schema:
            type: string
      responses:
        '204':
          description: Succesfully removed media item
  /media/{mediaId}/file:
    delete:
      summary: Delete media file
      description: Removes a media file from radarr/sonarr. The `ADMIN` permission is required to perform this action.
      tags:
        - media
      parameters:
        - in: path
          name: mediaId
          description: Media ID
          required: true
          example: '1'
          schema:
            type: string
        - in: query
          name: is4k
          description: Whether to remove from 4K service instance (true) or regular service instance (false)
          required: false
          example: false
          schema:
            type: boolean
      responses:
        '204':
          description: Successfully removed media item
  /media/{mediaId}/{status}:
    post:
      summary: Update media status
      description: Updates a media item's status and returns the media in JSON format
      tags:
        - media
      parameters:
        - in: path
          name: mediaId
          description: Media ID
          required: true
          example: '1'
          schema:
            type: string
        - in: path
          name: status
          description: New status
          required: true
          example: available
          schema:
            type: string
            enum: [available, partial, processing, pending, unknown, deleted]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                is4k:
                  type: boolean
                  example: false
      responses:
        '200':
          description: Returned media
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MediaInfo'
  /media/{mediaId}/watch_data:
    get:
      summary: Get watch data
      description: |
        Returns play count, play duration, and users who have watched the media.

        Requires the `ADMIN` permission.
      tags:
        - media
      parameters:
        - in: path
          name: mediaId
          description: Media ID
          required: true
          example: '1'
          schema:
            type: string
      responses:
        '200':
          description: Users
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      playCount7Days:
                        type: number
                      playCount30Days:
                        type: number
                      playCount:
                        type: number
                      users:
                        type: array
                        items:
                          $ref: '#/components/schemas/User'
                  data4k:
                    type: object
                    properties:
                      playCount7Days:
                        type: number
                      playCount30Days:
                        type: number
                      playCount:
                        type: number
                      users:
                        type: array
                        items:
                          $ref: '#/components/schemas/User'
  /collection/{collectionId}:
    get:
      summary: Get collection details
      description: Returns full collection details in a JSON object.
      tags:
        - collection
      parameters:
        - in: path
          name: collectionId
          required: true
          schema:
            type: number
            example: 537982
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Collection details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Collection'
  /service/radarr:
    get:
      summary: Get non-sensitive Radarr server list
      description: Returns a list of Radarr server IDs and names in a JSON object.
      tags:
        - service
      responses:
        '200':
          description: Request successful
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RadarrSettings'
  /service/radarr/{radarrId}:
    get:
      summary: Get Radarr server quality profiles and root folders
      description: Returns a Radarr server's quality profile and root folder details in a JSON object.
      tags:
        - service
      parameters:
        - in: path
          name: radarrId
          required: true
          schema:
            type: number
            example: 0
      responses:
        '200':
          description: Request successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  server:
                    $ref: '#/components/schemas/RadarrSettings'
                  profiles:
                    $ref: '#/components/schemas/ServiceProfile'
  /service/sonarr:
    get:
      summary: Get non-sensitive Sonarr server list
      description: Returns a list of Sonarr server IDs and names in a JSON object.
      tags:
        - service
      responses:
        '200':
          description: Request successful
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SonarrSettings'
  /service/sonarr/{sonarrId}:
    get:
      summary: Get Sonarr server quality profiles and root folders
      description: Returns a Sonarr server's quality profile and root folder details in a JSON object.
      tags:
        - service
      parameters:
        - in: path
          name: sonarrId
          required: true
          schema:
            type: number
            example: 0
      responses:
        '200':
          description: Request successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  server:
                    $ref: '#/components/schemas/SonarrSettings'
                  profiles:
                    $ref: '#/components/schemas/ServiceProfile'
  /service/sonarr/lookup/{tmdbId}:
    get:
      summary: Get series from Sonarr
      description: Returns a list of series returned by searching for the name in Sonarr.
      tags:
        - service
      parameters:
        - in: path
          name: tmdbId
          required: true
          schema:
            type: number
            example: 0
      responses:
        '200':
          description: Request successful
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SonarrSeries'
  /regions:
    get:
      summary: Regions supported by TMDB
      description: Returns a list of regions in a JSON object.
      tags:
        - tmdb
      responses:
        '200':
          description: Results
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    iso_3166_1:
                      type: string
                      example: US
                    english_name:
                      type: string
                      example: United States of America
  /languages:
    get:
      summary: Languages supported by TMDB
      description: Returns a list of languages in a JSON object.
      tags:
        - tmdb
      responses:
        '200':
          description: Results
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    iso_639_1:
                      type: string
                      example: en
                    english_name:
                      type: string
                      example: English
                    name:
                      type: string
                      example: English
  /studio/{studioId}:
    get:
      summary: Get movie studio details
      description: Returns movie studio details in a JSON object.
      tags:
        - tmdb
      parameters:
        - in: path
          name: studioId
          required: true
          schema:
            type: number
            example: 2
      responses:
        '200':
          description: Movie studio details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductionCompany'
  /network/{networkId}:
    get:
      summary: Get TV network details
      description: Returns TV network details in a JSON object.
      tags:
        - tmdb
      parameters:
        - in: path
          name: networkId
          required: true
          schema:
            type: number
            example: 1
      responses:
        '200':
          description: TV network details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductionCompany'
  /genres/movie:
    get:
      summary: Get list of official TMDB movie genres
      description: Returns a list of genres in a JSON array.
      tags:
        - tmdb
      parameters:
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Results
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: number
                      example: 10751
                    name:
                      type: string
                      example: Family
  /genres/tv:
    get:
      summary: Get list of official TMDB movie genres
      description: Returns a list of genres in a JSON array.
      tags:
        - tmdb
      parameters:
        - in: query
          name: language
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Results
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: number
                      example: 18
                    name:
                      type: string
                      example: Drama
  /backdrops:
    get:
      summary: Get backdrops of trending items
      description: Returns a list of backdrop image paths in a JSON array.
      security: []
      tags:
        - tmdb
      responses:
        '200':
          description: Results
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
  /issue:
    get:
      summary: Get all issues
      description: |
        Returns a list of issues in JSON format.
      tags:
        - issue
      parameters:
        - in: query
          name: take
          schema:
            type: number
            nullable: true
            example: 20
        - in: query
          name: skip
          schema:
            type: number
            nullable: true
            example: 0
        - in: query
          name: sort
          schema:
            type: string
            enum: [added, modified]
            default: added
        - in: query
          name: filter
          schema:
            type: string
            enum: [all, open, resolved]
            default: open
        - in: query
          name: requestedBy
          schema:
            type: number
            nullable: true
            example: 1
      responses:
        '200':
          description: Issues returned
          content:
            application/json:
              schema:
                type: object
                properties:
                  pageInfo:
                    $ref: '#/components/schemas/PageInfo'
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/Issue'
    post:
      summary: Create new issue
      description: |
        Creates a new issue
      tags:
        - issue
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                issueType:
                  type: number
                message:
                  type: string
                mediaId:
                  type: number
      responses:
        '201':
          description: Succesfully created the issue
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Issue'

  /issue/count:
    get:
      summary: Gets issue counts
      description: |
        Returns the number of open and closed issues, as well as the number of issues of each type.
      tags:
        - issue
      responses:
        '200':
          description: Issue counts returned
          content:
            application/json:
              schema:
                type: object
                properties:
                  total:
                    type: number
                  video:
                    type: number
                  audio:
                    type: number
                  subtitles:
                    type: number
                  others:
                    type: number
                  open:
                    type: number
                  closed:
                    type: number
  /issue/{issueId}:
    get:
      summary: Get issue
      description: |
        Returns a single issue in JSON format.
      tags:
        - issue
      parameters:
        - in: path
          name: issueId
          required: true
          schema:
            type: number
            example: 1
      responses:
        '200':
          description: Issues returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Issue'
    delete:
      summary: Delete issue
      description: Removes an issue. If the user has the `MANAGE_ISSUES` permission, any issue can be removed. Otherwise, only a users own issues can be removed.
      tags:
        - issue
      parameters:
        - in: path
          name: issueId
          description: Issue ID
          required: true
          example: '1'
          schema:
            type: string
      responses:
        '204':
          description: Succesfully removed issue
  /issue/{issueId}/comment:
    post:
      summary: Create a comment
      description: |
        Creates a comment and returns associated issue in JSON format.
      tags:
        - issue
      parameters:
        - in: path
          name: issueId
          required: true
          schema:
            type: number
            example: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                message:
                  type: string
              required:
                - message
      responses:
        '200':
          description: Issue returned with new comment
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Issue'
  /issueComment/{commentId}:
    get:
      summary: Get issue comment
      description: |
        Returns a single issue comment in JSON format.
      tags:
        - issue
      parameters:
        - in: path
          name: commentId
          required: true
          schema:
            type: string
            example: 1
      responses:
        '200':
          description: Comment returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IssueComment'
    put:
      summary: Update issue comment
      description: |
        Updates and returns a single issue comment in JSON format.
      tags:
        - issue
      parameters:
        - in: path
          name: commentId
          required: true
          schema:
            type: string
            example: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                message:
                  type: string
      responses:
        '200':
          description: Comment updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IssueComment'
    delete:
      summary: Delete issue comment
      description: |
        Deletes an issue comment. Only users with `MANAGE_ISSUES` or the user who created the comment can perform this action.
      tags:
        - issue
      parameters:
        - in: path
          name: commentId
          description: Issue Comment ID
          required: true
          example: '1'
          schema:
            type: string
      responses:
        '204':
          description: Succesfully removed issue comment
  /issue/{issueId}/{status}:
    post:
      summary: Update an issue's status
      description: |
        Updates an issue's status to approved or declined. Also returns the issue in a JSON object.

        Requires the `MANAGE_ISSUES` permission or `ADMIN`.
      tags:
        - issue
      parameters:
        - in: path
          name: issueId
          description: Issue ID
          required: true
          schema:
            type: string
            example: '1'
        - in: path
          name: status
          description: New status
          required: true
          schema:
            type: string
            enum: [open, resolved]
      responses:
        '200':
          description: Issue status changed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Issue'
  /keyword/{keywordId}:
    get:
      summary: Get keyword
      description: |
        Returns a single keyword in JSON format.
      tags:
        - other
      parameters:
        - in: path
          name: keywordId
          required: true
          schema:
            type: number
            example: 1
      responses:
        '200':
          description: Keyword returned (null if not found)
          content:
            application/json:
              schema:
                nullable: true
                $ref: '#/components/schemas/Keyword'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: 'Unable to retrieve keyword data.'
  /watchproviders/regions:
    get:
      summary: Get watch provider regions
      description: |
        Returns a list of all available watch provider regions.
      tags:
        - other
      responses:
        '200':
          description: Watch provider regions returned
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/WatchProviderRegion'
  /watchproviders/movies:
    get:
      summary: Get watch provider movies
      description: |
        Returns a list of all available watch providers for movies.
      tags:
        - other
      parameters:
        - in: query
          name: watchRegion
          required: true
          schema:
            type: string
            example: US
      responses:
        '200':
          description: Watch providers for movies returned
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/WatchProviderDetails'
  /watchproviders/tv:
    get:
      summary: Get watch provider series
      description: |
        Returns a list of all available watch providers for series.
      tags:
        - other
      parameters:
        - in: query
          name: watchRegion
          required: true
          schema:
            type: string
            example: US
      responses:
        '200':
          description: Watch providers for series returned
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/WatchProviderDetails'
  /certifications/movie:
    get:
      summary: Get movie certifications
      description: Returns list of movie certifications from TMDB.
      tags:
        - other
      security:
        - cookieAuth: []
        - apiKey: []
      responses:
        '200':
          description: Movie certifications returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CertificationResponse'
        '500':
          description: Unable to retrieve movie certifications
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: number
                    example: 500
                  message:
                    type: string
                    example: Unable to retrieve movie certifications.
  /certifications/tv:
    get:
      summary: Get TV certifications
      description: Returns list of TV show certifications from TMDB.
      tags:
        - other
      security:
        - cookieAuth: []
        - apiKey: []
      responses:
        '200':
          description: TV certifications returned
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CertificationResponse'
        '500':
          description: Unable to retrieve TV certifications
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: number
                    example: 500
                  message:
                    type: string
                    example: Unable to retrieve TV certifications.
  /overrideRule:
    get:
      summary: Get override rules
      description: Returns a list of all override rules with their conditions and settings
      tags:
        - overriderule
      responses:
        '200':
          description: Override rules returned
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/OverrideRule'
    post:
      summary: Create override rule
      description: Creates a new Override Rule from the request body.
      tags:
        - overriderule
      responses:
        '200':
          description: 'Values were successfully created'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/OverrideRule'
  /overrideRule/{ruleId}:
    put:
      summary: Update override rule
      description: Updates an Override Rule from the request body.
      tags:
        - overriderule
      parameters:
        - in: path
          name: ruleId
          required: true
          schema:
            type: number
      responses:
        '200':
          description: 'Values were successfully updated'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/OverrideRule'
    delete:
      summary: Delete override rule by ID
      description: Deletes the override rule with the provided ruleId.
      tags:
        - overriderule
      parameters:
        - in: path
          name: ruleId
          required: true
          schema:
            type: number
      responses:
        '200':
          description: Override rule successfully deleted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OverrideRule'
security:
  - cookieAuth: []
  - apiKey: []
