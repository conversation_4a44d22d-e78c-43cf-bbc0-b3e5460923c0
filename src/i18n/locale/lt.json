{"components.Discover.DiscoverMovieGenre.genreMovies": "{genre} filmai", "components.CollectionDetails.overview": "Apžvalga", "components.CollectionDetails.requestcollection": "Rezervacijų kolekcija", "components.Discover.DiscoverMovieLanguage.languageMovies": "{language} filmai", "components.Discover.DiscoverNetwork.networkSeries": "{network} serialai", "components.Discover.DiscoverTvLanguage.languageSeries": "{language} serialai", "components.Discover.StudioSlider.studios": "<PERSON><PERSON><PERSON>", "components.Discover.TvGenreList.seriesgenres": "Serialų žanrai", "components.Discover.popularmovies": "Populiarūs filmai", "components.Discover.populartv": "Populiarūs serialai", "components.Discover.recentlyAdded": "Paskutiniai pridėti", "components.Discover.TvGenreSlider.tvgenres": "Serialų žanrai", "components.Discover.trending": "Populiar<PERSON><PERSON><PERSON><PERSON>", "components.DownloadBlock.estimatedtime": "<PERSON><PERSON> {time}", "components.IssueDetails.IssueComment.areyousuredelete": "Ar tikrai norite ištrinti šį komentarą?", "components.IssueDetails.IssueComment.delete": "Ištrinti komentarą", "components.IssueDetails.IssueComment.edit": "Redaguoti komentarą", "components.IssueDetails.IssueComment.postedby": "{username} paskelbė {relativeTime}", "components.IssueDetails.IssueComment.postedbyedited": "{username} paskelbė {relativeTime} (Redaguota)", "components.IssueDetails.IssueComment.validationComment": "Privalote įrašyti tekstą", "components.IssueDetails.IssueDescription.deleteissue": "Pa<PERSON><PERSON><PERSON> problemą", "components.IssueDetails.comments": "Komentarai", "components.IssueDetails.closeissue": "Užbaig<PERSON> problema", "components.IssueDetails.closeissueandcomment": "Uždaryti su komentaru", "components.IssueDetails.commentplaceholder": "Pridėti komentarą…", "components.IssueDetails.deleteissue": "Pa<PERSON><PERSON><PERSON> problemą", "components.IssueDetails.deleteissueconfirm": "Ar tikrai norite pašalinti šią problemą?", "components.IssueDetails.episode": "{episodeNumber} epizodas", "components.IssueDetails.issuepagetitle": "Problema", "components.IssueDetails.nocomments": "Nėra komentarų.", "components.IssueDetails.play4konplex": "Groti 4k per Plex", "components.IssueModal.CreateIssueModal.allseasons": "Visi sezonai", "components.IssueModal.CreateIssueModal.episode": "{episodeNumber} epizodai", "components.IssueList.issues": "Problemos", "components.Layout.Sidebar.requests": "Rezervacijos", "components.Layout.Sidebar.settings": "Nustatymai", "components.Layout.Sidebar.users": "Vartotojai", "components.Layout.UserDropdown.myprofile": "Profilis", "components.IssueModal.issueAudio": "Audio", "components.IssueModal.issueSubtitles": "Subtitrai", "components.Layout.SearchInput.searchPlaceholder": "Ieškoti filmų ir laidų", "components.LanguageSelector.originalLanguageDefault": "Visos <PERSON>", "components.Layout.LanguagePicker.displaylanguage": "<PERSON><PERSON> kalba", "components.Login.forgotpassword": "<PERSON><PERSON> <PERSON><PERSON> slaptažodį?", "components.Login.email": "El. <PERSON>", "components.Login.signinheader": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad tęsti", "components.Login.signinwithplex": "Naudokite savo Plex prisijungimą", "components.Login.validationpasswordrequired": "<PERSON><PERSON><PERSON><PERSON> pateikti slaptažodį", "components.ManageSlideOver.alltime": "Visi", "components.ManageSlideOver.downloadstatus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.ManageSlideOver.movie": "filmas", "components.ManageSlideOver.tvshow": "serialas", "components.MovieDetails.MovieCast.fullcast": "Visi aktoriai", "components.MovieDetails.budget": "Biudžetas", "components.MovieDetails.originaltitle": "Originalus pavadinimas", "components.MovieDetails.overview": "Apžvalga", "components.MovieDetails.revenue": "Pajamos", "components.MovieDetails.viewfullcrew": "Visa komanda", "components.MovieDetails.similar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.streamingproviders": "Šiuo metu transliuojama per", "components.MovieDetails.studio": "{studioCount, plural, one {<PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON>}}", "components.PermissionEdit.request": "Rezervuoti", "components.PersonDetails.crewmember": "<PERSON><PERSON><PERSON>", "components.PersonDetails.appearsin": "<PERSON><PERSON><PERSON>", "components.QuotaSelector.unlimited": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestButton.viewrequest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestCard.deleterequest": "<PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.cancelRequest": "Atšaukti rezervaciją", "components.RequestList.RequestItem.deleterequest": "<PERSON><PERSON><PERSON><PERSON> re<PERSON>", "components.RequestList.RequestItem.editrequest": "Redaguoti rezervaciją", "components.RequestList.RequestItem.requesteddate": "Rezervuota", "components.RequestModal.AdvancedRequester.advancedoptions": "Det<PERSON>u", "components.RequestModal.AdvancedRequester.default": "{name} (<PERSON><PERSON><PERSON><PERSON><PERSON>)", "components.RequestModal.AdvancedRequester.qualityprofile": "Ko<PERSON>b<PERSON><PERSON> profilis", "components.RequestModal.AdvancedRequester.destinationserver": "<PERSON><PERSON>", "components.RequestModal.edit": "Redaguoti rezervaciją", "components.ResetPassword.email": "El. <PERSON>", "components.TvDetails.TvCrew.fullseriescrew": "Visa serialo komanda", "components.TvDetails.cast": "Aktoriai", "components.TvDetails.episodeRuntime": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "components.TvDetails.episodeRuntimeMinutes": "{runtime} minutės", "components.TvDetails.firstAirDate": "Pirmą kartą transliuota", "components.TvDetails.network": "{networkCount, plural, one {<PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON>}}", "components.TvDetails.nextAirDate": "Artimiausia transliacija", "components.TvDetails.productioncountries": "Kurta {countryCount, plural, one {šalyje} other {šalyse}}", "components.TvDetails.recommendations": "Rekomendacijos", "components.TvDetails.seasons": "{seasonCount, plural, one {# Sezonas} other {# Sezonai}}", "components.TvDetails.showtype": "Serialo tipas", "components.TvDetails.similar": "Panašūs serialai", "components.TvDetails.streamingproviders": "Šiuo metu transliuoja", "components.UserList.admin": "<PERSON><PERSON>", "i18n.approve": "<PERSON><PERSON><PERSON><PERSON>", "i18n.approved": "<PERSON><PERSON><PERSON><PERSON>", "i18n.available": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.back": "Atgal", "i18n.decline": "<PERSON><PERSON><PERSON>", "i18n.declined": "Atmestas", "i18n.delete": "<PERSON><PERSON><PERSON><PERSON>", "i18n.deleting": "<PERSON><PERSON><PERSON>…", "i18n.edit": "Red<PERSON><PERSON><PERSON>", "i18n.failed": "Nepavyko", "i18n.import": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.importing": "I<PERSON><PERSON><PERSON><PERSON><PERSON>…", "i18n.loading": "<PERSON><PERSON><PERSON><PERSON>…", "i18n.movie": "Filmas", "i18n.noresults": "Nėra rezultatų.", "i18n.notrequested": "Nerezervuotas", "i18n.open": "<PERSON><PERSON><PERSON><PERSON>", "i18n.partiallyavailable": "<PERSON><PERSON><PERSON> p<PERSON>", "i18n.processing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.resolved": "<PERSON>š<PERSON><PERSON><PERSON><PERSON>", "i18n.resultsperpage": "{pageSize} rezultatų puslapyje", "i18n.retry": "Bandyti iš naujo", "i18n.retrying": "<PERSON><PERSON> i<PERSON>…", "i18n.save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "i18n.showingresults": "<PERSON><PERSON> nuo <strong>{from}</strong> iki <strong>{to}</strong> iš <strong>{total}</strong> rezultatų", "i18n.status": "Statusas", "i18n.test": "Testas", "i18n.testing": "Testuojama…", "i18n.tvshow": "Ser<PERSON><PERSON>", "i18n.tvshows": "Serialai", "i18n.unavailable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.usersettings": "Vartotojo nustatymai", "i18n.view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pages.errormessagewithcode": "{statusCode} - {error}", "pages.internalservererror": "<PERSON><PERSON>inė serverio k<PERSON>a", "pages.serviceunavailable": "Paslauga nepasiekiama", "pages.somethingwentwrong": "<PERSON><PERSON><PERSON>", "components.CollectionDetails.numberofmovies": "{count} filmai", "components.Discover.NetworkSlider.networks": "<PERSON><PERSON><PERSON>", "components.Discover.discover": "Naršyti", "components.IssueDetails.IssueDescription.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.allseasons": "Visi sezonai", "components.CollectionDetails.requestcollection4k": "4k rezervacijų kolekcija", "components.IssueDetails.allepisodes": "Visi epizodai", "components.IssueDetails.issuetype": "Tipas", "components.IssueDetails.playonplex": "<PERSON><PERSON><PERSON> per <PERSON>lex", "components.IssueDetails.lastupdated": "Paskutinį kartą atnaujinta", "components.Discover.DiscoverStudio.studioMovies": "{studio} filmai", "components.IssueDetails.leavecomment": "Komentaras", "components.IssueDetails.openinarr": "Atverti {arr}", "components.AppDataWarning.dockerVolumeMissingDescription": "Neteisingai sukonfigūruotas <code>{appDataPath}</code> disko prisijungimas. Visi duomenys bus išvalyti kai konteineris bus sustabdytas ir perkrautas.", "components.Discover.upcomingtv": "Greit pasirodysiantys serialai", "components.Discover.DiscoverTvGenre.genreSeries": "{genre} serialai", "components.Discover.MovieGenreList.moviegenres": "Filmų žanrai", "components.Discover.recentrequests": "Paskutin<PERSON><PERSON> re<PERSON>", "components.Discover.MovieGenreSlider.moviegenres": "Filmų žanrai", "components.Discover.upcoming": "Greit pasirodysiantys filmai", "components.Discover.upcomingmovies": "Greit pasirodysiantys filmai", "components.IssueDetails.IssueDescription.edit": "Redaguoti aprašymą", "components.IssueDetails.openin4karr": "Atverti 4k {arr}", "components.Layout.UserDropdown.signout": "<PERSON>si<PERSON><PERSON><PERSON>", "components.IssueDetails.season": "{seasonNumber} sezonas", "components.Layout.Sidebar.dashboard": "Naršyti", "components.Layout.Sidebar.issues": "Problemos", "components.Layout.UserDropdown.settings": "Nustatymai", "components.Login.password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Login.signin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Login.signingin": "Prisijungia…", "components.Layout.VersionStatus.outofdate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Login.loginerror": "<PERSON><PERSON><PERSON> bandant p<PERSON>.", "components.Login.signinwithoverseerr": "Naudokite {applicationTitle} prisijungimą", "components.Login.validationemailrequired": "Prašau pateikti validų el. paštą", "components.ManageSlideOver.manageModalMedia4k": "4k medija", "components.MovieDetails.cast": "Aktoriai", "components.MovieDetails.recommendations": "Rekomendacijos", "components.ManageSlideOver.manageModalMedia": "Medija", "components.ManageSlideOver.openarr4k": "Atverti 4k {arr}", "components.ManageSlideOver.pastdays": "Per {days, number} dienas", "components.MediaSlider.ShowMoreCard.seemore": "Daugiau", "components.MovieDetails.originallanguage": "<PERSON>o kalba", "components.ManageSlideOver.openarr": "Atverti {arr}", "components.MovieDetails.runtime": "{minutes} minut<PERSON>s", "components.MovieDetails.showless": "<PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.showmore": "<PERSON><PERSON><PERSON>", "components.MovieDetails.releasedate": "{releaseCount, plural, one {Iš<PERSON><PERSON><PERSON> data} other {Iš<PERSON><PERSON><PERSON> datos}}", "components.MovieDetails.watchtrailer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> trailerį", "components.PermissionEdit.admin": "<PERSON><PERSON>", "components.PermissionEdit.autoapprove": "Auto patvirtinti", "components.IssueModal.CreateIssueModal.allepisodes": "Visi epizodai", "components.MovieDetails.productioncountries": "Kurta {countryCount, plural, one {Šalyje} other {Šalyse}}", "components.PermissionEdit.request4k": "Rezervuoti 4k", "components.PermissionEdit.viewrequests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.PersonDetails.birthdate": "<PERSON><PERSON><PERSON><PERSON><PERSON> {birthdate}", "components.QuotaSelector.movies": "{count, plural, one {filmas} other {filmai}}", "components.MovieDetails.MovieCrew.fullcrew": "Visa komanda", "components.PersonDetails.ascharacter": "kaip {character}", "components.PersonDetails.alsoknownas": "<PERSON><PERSON><PERSON> kaip: {names}", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.QuotaSelector.days": "{count, plural, one {diena} other {dienos}}", "components.RequestBlock.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {Sezonai}}", "components.RequestButton.requestmore": "Rezervuoti daug<PERSON>u", "components.RequestCard.failedretry": "<PERSON><PERSON><PERSON> nepavyko kartojant rezervacijos užklausą.", "components.QuotaSelector.seasons": "{count, plural, one {sezonas} other {sezonai}}", "components.RequestBlock.languageprofile": "Kalbų profilis", "components.RegionSelector.regionDefault": "Visi regionai", "components.RegionSelector.regionServerDefault": "Numatytasis ({region})", "components.RequestButton.requestmore4k": "Rezervuoti daugiau 4K raiška", "components.RequestList.RequestItem.failedretry": "<PERSON><PERSON><PERSON> nepavyko kartojant rezervacijos užklausą.", "components.RequestList.RequestItem.modifieduserdate": "{date} - {user}", "components.RequestList.showallrequests": "Rodyti visas rezervacijas", "components.RequestCard.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {Sezonai}}", "components.RequestList.RequestItem.requested": "Rezervuota", "components.RequestList.RequestItem.modified": "Redaguota", "components.RequestModal.requesterror": "<PERSON><PERSON><PERSON> nepavyko teikiant rezervaciją.", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {Sezonai}}", "components.RequestList.requests": "Rezervacijos", "components.RequestList.sortAdded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestList.sortModified": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.alreadyrequested": "Jau rezervu<PERSON>", "i18n.request4k": "Rezervuoti 4K raiška", "components.RequestModal.QuotaDisplay.movie": "filmas", "components.RequestModal.requestedited": "<strong>{title}</strong> rezervacija redaguota sėkmingai!", "components.ResetPassword.confirmpassword": "Patvirtinkite slaptažodį", "components.RequestModal.AdvancedRequester.rootfolder": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.cancel": "Atšaukti rezervaciją", "components.RequestModal.numberofepisodes": "# iš epi<PERSON>", "components.RequestModal.errorediting": "<PERSON><PERSON><PERSON> ne<PERSON>vy<PERSON> redaguojant rezervaciją.", "components.RequestModal.requestcancelled": "<strong>{title}</strong> rezerva<PERSON><PERSON> atšauk<PERSON>.", "components.RequestModal.requestmovies": "Rezervuota {count} {count, plural, one {filmas} other {filmų}}", "components.RequestModal.season": "Sezonas", "components.RequestModal.requestApproved": "<strong>{title}</strong> rezervacija patvirtinta!", "components.RequestModal.requestadmin": "Rezervacija bus automatiškai patvirtinta.", "components.RequestModal.selectmovies": "Pasirinkite filmą(-us)", "components.RequestModal.selectseason": "Pasirinkite sezoną(-us)", "components.Search.searchresults": "Paieškos rezultatai", "components.RequestModal.requestCancel": "<strong>{title}</strong> rezerva<PERSON><PERSON> atšauk<PERSON>.", "components.RequestModal.requestSuccess": "<strong>{title}</strong> rezervuota sėkmingai!", "components.RequestModal.requestfrom": "Rezerva<PERSON><PERSON> iš {username} la<PERSON><PERSON>.", "components.Search.search": "Pa<PERSON>š<PERSON>", "components.TvDetails.TvCast.fullseriescast": "Visi serialo aktoriai", "components.RequestModal.seasonnumber": "{number} sezonas", "components.TvDetails.anime": "Anime", "components.TvDetails.originallanguage": "<PERSON>o kalba", "components.TvDetails.viewfullcrew": "Visa komanda", "components.TvDetails.watchtrailer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> trailerį", "components.TvDetails.originaltitle": "Originalus pavadinimas", "components.TvDetails.overview": "<PERSON><PERSON><PERSON>", "components.TvDetails.overviewunavailable": "<PERSON>ė<PERSON>.", "i18n.requested": "Rezervuotas", "i18n.advanced": "Pažangu", "i18n.canceling": "<PERSON>ša<PERSON>ama…", "i18n.close": "Uždaryti", "i18n.requesting": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "i18n.saving": "Saugoma…", "i18n.settings": "Nustatymai", "components.UserList.accounttype": "Tipas", "i18n.areyousure": "Ar tu tikras?", "i18n.cancel": "<PERSON><PERSON><PERSON><PERSON>", "i18n.all": "Visi", "i18n.movies": "Filmai", "i18n.next": "Kitas", "i18n.pending": "<PERSON><PERSON><PERSON><PERSON>", "i18n.previous": "<PERSON><PERSON><PERSON><PERSON>", "i18n.request": "Rezervuoti", "pages.oops": "Ups...", "pages.returnHome": "Grįžti į pradžią", "pages.pagenotfound": "<PERSON><PERSON><PERSON><PERSON> ne<PERSON>", "components.IssueDetails.problemepisode": "Paveik<PERSON> epi<PERSON>", "components.IssueDetails.reopenissueandcomment": "Atverti su komentaru", "components.IssueList.IssueItem.problemepisode": "Paveiktas epizodas", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {Sezonai}}", "components.IssueList.IssueItem.unknownissuetype": "Nežinoma", "components.IssueList.IssueItem.viewissue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.ManageSlideOver.manageModalAdvanced": "Pažangiau", "components.ManageSlideOver.manageModalClearMedia": "Išvalyti duomenis", "components.ManageSlideOver.opentautulli": "Atvert<PERSON> per <PERSON>", "components.ManageSlideOver.playedby": "<PERSON><PERSON><PERSON>", "components.MovieDetails.mark4kavailable": "Pažymėk kaip prieinamą 4k raiška", "components.MovieDetails.markavailable": "Pažymėti ka<PERSON> p<PERSON>", "components.MovieDetails.overviewunavailable": "<PERSON><PERSON><PERSON><PERSON><PERSON> nė<PERSON>.", "components.NotificationTypeSelector.mediaavailable": "Rezervacija išpildyta", "components.NotificationTypeSelector.mediadeclined": "Rezervacija atšaukta", "components.NotificationTypeSelector.mediaapproved": "Rezervacija patvirtinta", "components.NotificationTypeSelector.mediafailed": "Rezervacijos procesas nepavyko", "components.NotificationTypeSelector.mediarequested": "Rezervacija la<PERSON>a pat<PERSON>", "components.IssueDetails.problemseason": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {Epizodas} other {Epizodai}}", "components.IssueList.IssueItem.issuestatus": "Statusas", "components.IssueDetails.reopenissue": "<PERSON><PERSON> na<PERSON>", "components.IssueList.IssueItem.opened": "Atviros", "components.IssueList.IssueItem.issuetype": "Tipas", "components.IssueList.IssueItem.openeduserdate": "{date} - {user}", "components.PermissionEdit.autoapprove4k": "Auto patvirtinti 4K", "components.IssueDetails.toasteditdescriptionfailed": "Įvyko klaida redaguojant problemos a<PERSON>.", "components.IssueDetails.toasteditdescriptionsuccess": "Problemos aprašas redaguotas s<PERSON>k<PERSON>ai!", "components.IssueDetails.toastissuedeletefailed": "Įsivėlė klaida šalinant problemą.", "components.IssueDetails.toaststatusupdated": "Problemos statusas sėkmingai atnaujintas!", "components.IssueDetails.toaststatusupdatefailed": "Įvyko klaida atnaujinant problemos statusą.", "components.IssueDetails.unknownissuetype": "Nežinoma", "components.IssueList.showallissues": "Rodyti visas problemas", "components.IssueList.sortAdded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.IssueList.sortModified": "Pa<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.IssueModal.CreateIssueModal.extras": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueModal.CreateIssueModal.problemepisode": "Paveik<PERSON> epi<PERSON>", "components.IssueModal.CreateIssueModal.providedetail": "Prašome pateikti detalų kilusios problemos aprašą.", "components.IssueModal.CreateIssueModal.reportissue": "Pranešti apie problemą", "components.IssueModal.CreateIssueModal.season": "Sezonas {seasonNumber}", "components.IssueModal.CreateIssueModal.submitissue": "Pateikti problemą", "components.IssueModal.CreateIssueModal.toastFailedCreate": "Įvyko klaida patei<PERSON> problemą.", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "<PERSON>a <strong>{title}</strong> patei<PERSON>a <PERSON>!", "components.IssueModal.CreateIssueModal.toastviewissue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.IssueModal.CreateIssueModal.validationMessageRequired": "Prašome pateikti aprašą", "components.IssueModal.CreateIssueModal.whatswrong": "<PERSON><PERSON> ne<PERSON>?", "components.IssueModal.issueOther": "<PERSON><PERSON>", "components.IssueModal.issueVideo": "Video", "components.LanguageSelector.languageServerDefault": "Numatytoji ({language})", "components.Layout.VersionStatus.streamdevelop": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "components.Layout.VersionStatus.streamstable": "<PERSON><PERSON><PERSON><PERSON>", "components.ManageSlideOver.manageModalIssues": "Atviros problemos", "components.ManageSlideOver.manageModalNoRequests": "Nėra rezervacijų.", "components.ManageSlideOver.manageModalRequests": "Rezervacijos", "components.ManageSlideOver.manageModalTitle": "<PERSON><PERSON><PERSON> {mediaType}", "components.ManageSlideOver.mark4kavailable": "Pažymėti kaip prieinamą 4K raiška", "components.ManageSlideOver.markallseasons4kavailable": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kad visi sezonai prieinami 4K raiška", "components.ManageSlideOver.markallseasonsavailable": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kad visi sezonai prieinami", "components.ManageSlideOver.markavailable": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ka<PERSON>", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> {playCount, plural, one {perži<PERSON>ra} other {peržiū<PERSON>}}", "components.IssueDetails.toastissuedeleted": "Problema p<PERSON>šalint<PERSON>k<PERSON>ai!", "components.IssueModal.CreateIssueModal.problemseason": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.openedby": "#{issueId} problema atverta {relativeTime}, {username}", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {komitas} other {komitai}} behind", "components.ManageSlideOver.manageModalClearMediaWarning": "* Tai negyž<PERSON>ai p<PERSON> {mediaType} tipo duomenis, įskaitant rezervacijas. {mediaServerName} bibliotekoje esančios medijos informacija bus atkurta kito skanavimo metu.", "components.NotificationTypeSelector.adminissuecommentDescription": "Gauti pranešimus kai kiti vartotojai komentuoja problemą.", "components.NotificationTypeSelector.adminissueresolvedDescription": "Gauti praneš<PERSON> kai kiti vartotojai uždaro problemą.", "components.NotificationTypeSelector.issuecomment": "Problemos komentaras", "components.NotificationTypeSelector.issuecommentDescription": "Siųsti pranešimus kai problema gauna nauja komentarą.", "components.NotificationTypeSelector.issuecreated": "Problema iš naujo at<PERSON>a", "components.NotificationTypeSelector.issuecreatedDescription": "<PERSON><PERSON><PERSON><PERSON>, kai problemos iš naujo atver<PERSON>.", "components.NotificationTypeSelector.issuereopened": "Problema iš naujo at<PERSON>a", "components.NotificationTypeSelector.issuereopenedDescription": "<PERSON><PERSON><PERSON><PERSON>, kai problema iš naujo at<PERSON>.", "components.NotificationTypeSelector.issueresolved": "Problema išspręsta", "components.NotificationTypeSelector.issueresolvedDescription": "<PERSON><PERSON><PERSON><PERSON>, kai <PERSON>os <PERSON>.", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "<PERSON><PERSON><PERSON><PERSON>, kai vartotojai pateikia naują medijos rezervaciją, kuri automati<PERSON> pat<PERSON>.", "components.NotificationTypeSelector.mediaapprovedDescription": "<PERSON><PERSON><PERSON><PERSON>, medijos rezervacijos patvirtinamos ranka.", "components.NotificationTypeSelector.mediaavailableDescription": "Sių<PERSON><PERSON>, kai medijos rezervacijos tampa prieinamos.", "components.NotificationTypeSelector.mediadeclinedDescription": "Siųst<PERSON>, kai medijos rezervacijos atmetamos.", "components.NotificationTypeSelector.mediafailedDescription": "Si<PERSON><PERSON><PERSON>, kai medijos rezervacijos neišeina pridėti Radarr ar Sonarr.", "components.NotificationTypeSelector.notificationTypes": "Pranešimų tipai", "components.NotificationTypeSelector.userissuereopenedDescription": "<PERSON><PERSON><PERSON>, kai <PERSON>, kurias tu pateikei iš naujo at<PERSON>.", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "<PERSON><PERSON><PERSON>, kai kiti vartotojai pateikia naują medijos rezervaciją, kurie automatiškai pat<PERSON>.", "components.NotificationTypeSelector.usermediaapprovedDescription": "<PERSON><PERSON><PERSON>, kai tavo medijos rezervacija patirtinta.", "components.NotificationTypeSelector.userissueresolvedDescription": "<PERSON><PERSON><PERSON> p<PERSON> kai problema, kurią tu pateikei išsprendž<PERSON>.", "components.NotificationTypeSelector.usermediaavailableDescription": "Gaut<PERSON> p<PERSON> kai tavo medijos rezervacija tampa prieinama.", "components.NotificationTypeSelector.usermediadeclinedDescription": "<PERSON><PERSON><PERSON> rezer<PERSON>, kai tavo medijos rezervacija atšaukiama.", "components.NotificationTypeSelector.usermediafailedDescription": "<PERSON><PERSON><PERSON>, kai medijos rezervacijos nepavyksta pridėti Radarr ar Sonarr.", "components.NotificationTypeSelector.usermediarequestedDescription": "Gauti p<PERSON> kai kiti vartotojai pateikia naują medijos p<PERSON>, kuriam reika<PERSON> pat<PERSON>.", "components.PermissionEdit.adminDescription": "Pilnas administracinis rėžimas. Ignoruoti visus leidimų reikalavimus.", "components.PermissionEdit.advancedrequest": "Detalesnės re<PERSON>", "components.PermissionEdit.advancedrequestDescription": "Sutiekti leidimus modifikuoti detalios rezervacijos pasirinkimus.", "components.PermissionEdit.createissues": "Pateikti problemas", "components.PermissionEdit.manageissues": "<PERSON><PERSON><PERSON> problemas", "components.PermissionEdit.managerequests": "<PERSON>dyti <PERSON>", "components.PermissionEdit.request4kMovies": "Rezervuoti 4K filmus", "components.PermissionEdit.request4kMoviesDescription": "Sutiekti leidimus pateikti rezervacijas 4K filmams.", "components.PermissionEdit.request4kTv": "Rezervuoti 4K Serialus", "components.PermissionEdit.requestMovies": "Rezervuoti filmus", "components.PermissionEdit.requestTv": "Rezervuoti serialus", "components.PermissionEdit.users": "<PERSON><PERSON><PERSON>", "components.PermissionEdit.viewissues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>as", "components.RequestBlock.profilechanged": "Ko<PERSON>b<PERSON><PERSON> profilis", "components.RequestBlock.requestoverrides": "Rezervacijos p<PERSON>iti<PERSON>i", "components.RequestBlock.rootfolder": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "components.RequestBlock.server": "Numatytasis serveris", "components.RequestButton.approve4krequests": "T<PERSON><PERSON><PERSON> {requestCount, plural, one {4K rezervacijas} other {{requestCount} 4K rezervacijas}}", "components.RequestButton.approverequest4k": "Tvirtinti 4K rezervacijas", "components.RequestButton.approverequests": "T<PERSON><PERSON><PERSON> {requestCount, plural, one {Rezervaciją} other {{requestCount} Rezervacijas}}", "components.RequestButton.decline4krequests": "Atmesti {requestCount, plural, one {4K rezervaciją} other {{requestCount} 4K rezervacijas}}", "components.RequestButton.declinerequests": "Atmesti {requestCount, plural, one {rezervaciją} other {{requestCount} rezervacijas}}", "components.RequestButton.viewrequest4k": "Peržiūrėti 4K rezervacijas", "components.RequestCard.mediaerror": "{mediaType} Nerasta", "components.RequestList.RequestItem.mediaerror": "Susijusio pavadinimo nebeprieinama rezervacijai.", "components.RequestModal.AdvancedRequester.animenote": "* Šis serialas yra anime.", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.RequestModal.AdvancedRequester.languageprofile": "Kalbų profilis", "components.RequestModal.AdvancedRequester.notagoptions": "Nėra žymių.", "components.RequestModal.AdvancedRequester.requestas": "<PERSON><PERSON><PERSON><PERSON><PERSON> kaip", "components.RequestModal.AdvancedRequester.selecttags": "Pasirink<PERSON>", "components.RequestModal.AdvancedRequester.tags": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "<PERSON><PERSON> var<PERSON>tojui leidž<PERSON>a rezervuoti iki <strong>{limit}</strong> {type} kas <strong>{days}</strong> dienas.", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {filmas} other {filmai}}", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "Nepakankamai sezonų rezervacijų liko", "components.RequestModal.QuotaDisplay.quotaLink": "G<PERSON>te peržiūrėti rezervacijų limitų apžvalgą savo <ProfileLink>profilio <PERSON></ProfileLink>.", "components.RequestModal.QuotaDisplay.quotaLinkUser": "Galima <PERSON>ž<PERSON>ūr<PERSON>ti vartotojo rezervacijų limitų santrauką jų <ProfileLink>profilio pusla<PERSON></ProfileLink>.", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {No} other {<strong>#</strong>}} {type} {remaining, plural, one {rezervacija} other {rezervacijų}} liko", "components.RequestModal.QuotaDisplay.requiredquota": "Jums reikia bent <strong>{seasons}</strong> {seasons, plural, one {sezono rezervacijos} other {sezonų rezervacijos}} liku<PERSON><PERSON>, kad rezervuoti šį serialą.", "components.RequestModal.QuotaDisplay.requiredquotaUser": "<PERSON><PERSON> var<PERSON> reikia <strong>{seasons}</strong> {seasons, plural, one {sezono rezervacijos} other {sezonų rezervacijų}}, kad rezervuoti šį serialą.", "components.RequestModal.QuotaDisplay.season": "sezonas", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {sezonas} other {sezonai}}", "components.RequestModal.SearchByNameModal.notvdbiddescription": "Negalėjome automatiškai surasti jūsų rezervacijos. Prašau pasirinkti tinkamą variantą iš galim<PERSON>.", "components.RequestModal.approve": "T<PERSON>tin<PERSON> re<PERSON>ją", "components.RequestModal.autoapproval": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.pending4krequest": "", "components.RequestModal.pendingrequest": "", "components.RequestModal.requestmovies4k": "Rezervacijos {count} {count, plural, one {filmo} other {filmų}} 4K raiška", "components.RequestModal.requestseasons4k": "Rezervuoti {seasonCount} {seasonCount, plural, one {sezoną} other {sezonus}} 4K raiška", "components.ResetPassword.emailresetlink": "El. pa<PERSON>to atkūrimo nuoroda", "components.ResetPassword.gobacklogin": "Grįžti į prisijungimo puslapį", "components.ResetPassword.password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.ResetPassword.passwordreset": "Slap<PERSON>žod<PERSON><PERSON> atstatymas", "components.ResetPassword.requestresetlinksuccessmessage": "Slaptažodžio atkūrimo nuoroda bus atsiųsta pateiktu el. p<PERSON>, jei yra paskyra su kuria susijęs var<PERSON>.", "components.ResetPassword.resetpassword": "Atstatyti slaptažodį", "components.ResetPassword.resetpasswordsuccessmessage": "Slap<PERSON>žodis atstatytas sėkmingai!", "components.ResetPassword.validationemailrequired": "Prašome pateikti el. pašto ad<PERSON>", "components.ResetPassword.validationpasswordmatch": "Slaptažodžiai turi sutapti", "components.ResetPassword.validationpasswordminchars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> per trumpas; jis tur<PERSON> būti mažiai 8 simbolių", "components.ResetPassword.validationpasswordrequired": "Privalote pateikti slaptažodį", "components.Settings.SettingsAbout.about": "<PERSON><PERSON>", "components.Settings.SettingsAbout.appDataPath": "Duomenų aplankas", "components.Settings.SettingsAbout.documentation": "Dokumentacija", "components.Settings.SettingsAbout.totalrequests": "Viso rezer<PERSON>jų", "components.Settings.SettingsAbout.timezone": "Laiko zona", "components.Settings.SettingsAbout.totalmedia": "Viso medijos", "components.Settings.SettingsAbout.uptodate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cacheflushed": "{cachename} ke<PERSON><PERSON> v<PERSON>.", "components.Settings.SettingsAbout.version": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cache": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachehits": "Kvietimai", "components.Settings.SettingsJobsCache.cachekeys": "Viso raktų", "components.Settings.SettingsJobsCache.cacheksize": "<PERSON><PERSON><PERSON> dydis", "components.Settings.SettingsJobsCache.cachemisses": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachename": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachevsize": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.runnow": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.filterError": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.filterInfo": "Informacija", "components.Settings.SettingsLogs.filterWarn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.label": "Etiketė", "components.Settings.SettingsLogs.level": "<PERSON><PERSON><PERSON><PERSON>", "components.NotificationTypeSelector.mediaAutoApproved": "Rezervacija automatiškai patvirtina", "components.NotificationTypeSelector.adminissuereopenedDescription": "Gauti p<PERSON>š<PERSON> kai kiti vartotojai atveria problemą.", "components.NotificationTypeSelector.mediarequestedDescription": "Sių<PERSON><PERSON>, kai vartotojas patiekia naują medijos rezervaciją, kuriai reikalingas pat<PERSON>imas.", "components.NotificationTypeSelector.userissuecommentDescription": "<PERSON><PERSON><PERSON>, kai <PERSON>, kurias tu pateikei naujai pakomentuoja<PERSON>.", "components.NotificationTypeSelector.userissuecreatedDescription": "<PERSON><PERSON><PERSON>, kai kiti vartotojai pateikia problemą.", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{movies} per {quotaDays} {days}</quotaUnits>", "components.RequestButton.approverequest": "Tvirtin<PERSON>", "components.RequestButton.declinerequest": "<PERSON><PERSON><PERSON>", "components.RequestButton.declinerequest4k": "Atmesti 4K rezervacijas", "components.RequestModal.QuotaDisplay.allowedRequests": "<PERSON>ūs galite rezervuoti iki <strong>{limit}</strong> {type} kas <strong>{days}</strong> dienas.", "components.RequestModal.pendingapproval": "Rezervacija laukia pat<PERSON>.", "components.RequestModal.requestseasons": "Rezervuoti {seasonCount} {seasonCount, plural, one {sezoną} other {sezonus}}", "components.PermissionEdit.autoapprove4kDescription": "Suteikti automatinius patvirtinimus visoms 4K medijos rezervacijoms.", "components.PermissionEdit.autoapprove4kMovies": "Auto-patvirtinti 4K filmus", "components.Settings.Notifications.NotificationsGotify.agentenabled": "Įgalinti agentą", "components.Settings.RadarrModal.add": "<PERSON><PERSON><PERSON><PERSON> serverį", "components.Settings.RadarrModal.baseUrl": "URL pagrindas", "components.Settings.RadarrModal.create4kradarr": "Pridėti naują 4K Radarr serverį", "components.Settings.RadarrModal.createradarr": "Pridėti naują Radarr serverį", "components.Settings.RadarrModal.defaultserver": "Numatytasis serveris", "components.Settings.RadarrModal.edit4kradarr": "Redaguoti 4K Radarr serverį", "components.Settings.RadarrModal.enableSearch": "Numatyti automatinę pai<PERSON>", "components.Settings.RadarrModal.externalUrl": "Išorinė nuoroda", "components.Settings.RadarrModal.hostname": "Domenas ar IP adresas", "components.Settings.RadarrModal.inCinemas": "<PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.loadingTags": "<PERSON><PERSON><PERSON><PERSON>…", "components.Settings.RadarrModal.loadingprofiles": "<PERSON><PERSON><PERSON><PERSON> kok<PERSON>b<PERSON> profilia<PERSON>…", "components.Settings.RadarrModal.loadingrootfolders": "<PERSON><PERSON><PERSON><PERSON>…", "components.Settings.RadarrModal.minimumAvailability": "<PERSON><PERSON><PERSON> p<PERSON>", "components.Settings.RadarrModal.notagoptions": "Nėra žymių.", "components.Settings.RadarrModal.port": "Portas", "components.Settings.RadarrModal.qualityprofile": "Ko<PERSON>b<PERSON><PERSON> profilis", "components.Settings.RadarrModal.released": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.rootfolder": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.selectMinimumAvailability": "Pasirinkti minimalų prieinamumą", "components.Settings.RadarrModal.selectQualityProfile": "Pasirink<PERSON> kok<PERSON> profilį", "components.Settings.RadarrModal.selectRootFolder": "Pasirinkti šakinį aplanką", "components.Settings.RadarrModal.selecttags": "Pasirink<PERSON>", "components.Settings.SettingsAbout.Releases.currentversion": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.Releases.latestversion": "Vėliausia", "components.Settings.SettingsAbout.Releases.releasedataMissing": "Išleidimo data šiuo metu neprieinama.", "components.Settings.SettingsAbout.Releases.releases": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.download-sync-reset": "Parsiuntimų sinchronizacijos atstatymas", "components.Settings.SettingsJobsCache.editJobSchedule": "Modifikuoti darbą", "components.Settings.SettingsJobsCache.download-sync": "Parsisiuntimų sinchronizavimas", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.process": "Procesas", "components.Settings.SettingsLogs.logs": "Logai", "components.Settings.SettingsUsers.defaultPermissions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsUsers.defaultPermissionsTip": "Pradiniai leidimai numatyti naujiems vartotojams", "components.Settings.SettingsUsers.localLogin": "Įgalinti lokalų prisijungimą", "components.Settings.SettingsUsers.localLoginTip": "Leisti vartotojams prisijungti naudojantis jų el.paštu ir <PERSON>, vietoje Plex OAuth", "components.Settings.SettingsUsers.movieRequestLimitLabel": "Globalus filmų rezervacijos limitas", "components.Settings.SettingsUsers.newPlexLogin": "Įgalinti naujus Plex prisijungimus", "components.Settings.SettingsUsers.newPlexLoginTip": "Leisti Plex vadotojams prisijungti praleidžiant jų importavimą", "components.Settings.SettingsUsers.toastSettingsFailure": "<PERSON><PERSON><PERSON> i<PERSON> nustatymus.", "components.Settings.SettingsUsers.toastSettingsSuccess": "Vartotojos nustatymai sėkmingai išsaugoti!", "components.Settings.SettingsUsers.userSettingsDescription": "Konfigūruoti globalius ir numatytuosius vartojų nustatymus.", "components.Settings.SettingsUsers.users": "Vartotojai", "components.Settings.SonarrModal.add": "<PERSON><PERSON><PERSON><PERSON> serverį", "components.Settings.SonarrModal.animeTags": "<PERSON><PERSON>", "components.Settings.SonarrModal.animelanguageprofile": "<PERSON><PERSON> ka<PERSON> profilis", "components.Settings.SonarrModal.animequalityprofile": "<PERSON><PERSON> profilis", "components.Settings.SonarrModal.animerootfolder": "<PERSON><PERSON>", "components.Settings.SonarrModal.apiKey": "API raktas", "components.Settings.SonarrModal.baseUrl": "URL pagrindas", "components.Settings.SonarrModal.default4kserver": "Numatytasis 4K serveris", "components.Settings.SonarrModal.defaultserver": "Numatytasis serveris", "components.Settings.SonarrModal.edit4ksonarr": "Redaguoti 4K Sonarr serverį", "components.Settings.SonarrModal.editsonarr": "Redaguoti Sonarr serverį", "components.Settings.SonarrModal.externalUrl": "Išorinė nuoroda", "components.Settings.SonarrModal.hostname": "Domenas ar IP adresas", "components.Settings.SonarrModal.languageprofile": "Kalbų profilis", "components.Settings.SonarrModal.loadingTags": "<PERSON><PERSON><PERSON><PERSON>…", "components.Settings.SonarrModal.loadinglanguageprofiles": "Įkeliami kalbų profiliai…", "components.Settings.SonarrModal.loadingprofiles": "<PERSON><PERSON><PERSON><PERSON> kok<PERSON>b<PERSON> profilia<PERSON>…", "components.Settings.SonarrModal.loadingrootfolders": "<PERSON><PERSON><PERSON><PERSON>…", "components.Settings.SonarrModal.notagoptions": "Nėra žymių.", "components.Settings.SonarrModal.port": "Portas", "components.Settings.SonarrModal.rootfolder": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.selectLanguageProfile": "Pasirinkite kalbos profilį", "components.Settings.SonarrModal.selectQualityProfile": "Pasirink<PERSON> kok<PERSON> profilį", "components.Settings.SonarrModal.selectRootFolder": "Pasirinkti šakinį aplanką", "components.Settings.SonarrModal.selecttags": "Pasirink<PERSON>", "components.Settings.SonarrModal.server4k": "4K serveris", "components.Settings.SonarrModal.servername": "Serverio pavadinimas", "components.Settings.SonarrModal.ssl": "Naudokite SSL", "components.Settings.SettingsUsers.userSettings": "Vartotojo nustatymai", "components.Settings.SettingsUsers.tvRequestLimitLabel": "Globalus serialų rezervacijos limitas", "components.Settings.SonarrModal.createsonarr": "Pridėti naują Sonarr serverį", "components.Settings.SonarrModal.tags": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.announced": "Paskelbti", "components.Settings.RadarrModal.apiKey": "API raktas", "components.Settings.RadarrModal.default4kserver": "Numatytasis 4K serveris", "components.Settings.RadarrModal.editradarr": "Redaguoti Radarr serverį", "components.Settings.SonarrModal.enableSearch": "Įjungti automatinę paie<PERSON>ą", "components.Settings.SonarrModal.create4ksonarr": "Pridėti naują 4K Sonarr serverį", "components.Settings.SonarrModal.qualityprofile": "Ko<PERSON>b<PERSON><PERSON> profilis", "components.Settings.SonarrModal.seasonfolders": "<PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.syncEnabled": "Įjungti nuskaitymą", "components.Discover.CreateSlider.nooptions": "Nėra rezultatų.", "components.Discover.DiscoverMovies.discovermovies": "Filmai", "components.Discover.DiscoverTv.discovertv": "Serialai", "components.Discover.DiscoverMovies.sortPopularityAsc": "Nuo mažiausiai populiarių", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "Nuo seniausiai išleistų", "components.Discover.DiscoverMovies.sortPopularityDesc": "Nuo populiariausių", "components.Discover.DiscoverSliderEdit.remove": "<PERSON><PERSON><PERSON>", "components.StatusBadge.openinarr": "Atverti {arr}", "components.Discover.DiscoverMovies.sortTitleAsc": "<PERSON><PERSON><PERSON><PERSON> (A-Z) pagal abėcėlę", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "<PERSON>uo aukščiausio TMDB reitingo", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {# aktyvus filtras} other {# aktyvūs filtrai}}", "components.Discover.DiscoverMovies.sortTitleDesc": "<PERSON><PERSON><PERSON><PERSON> (Z-A) nuo abėcėlės galo", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "Nuo žemiausio TMDB reitingo", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {# aktyvus filtras} other {# aktyvūs filtrai}}", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {# aktyvus filtras} other {# aktyvūs filtrai}}", "components.Discover.CreateSlider.searchGenres": "Ieškoti žanrų…", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "<PERSON><PERSON>", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "Nuo seniausiai išleistų", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "<PERSON>uo aukščiausio TMDB reitingo", "components.Discover.FilterSlideover.streamingservices": "Transliavi<PERSON> p<PERSON>", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "<PERSON><PERSON>", "components.Discover.DiscoverTv.sortPopularityAsc": "Nuo mažiausiai populiarių", "components.Discover.DiscoverTv.sortPopularityDesc": "Nuo populiariausių", "components.Discover.DiscoverTv.sortTitleAsc": "<PERSON><PERSON><PERSON><PERSON> (A-Z) pagal abėcėlę", "components.Discover.DiscoverTv.sortTitleDesc": "<PERSON><PERSON><PERSON><PERSON> (Z-A) nuo abėcėlės galo", "components.Discover.FilterSlideover.genres": "Žanrai", "components.Discover.FilterSlideover.keywords": "Raktažodžiai", "components.Discover.FilterSlideover.to": "iki", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "Nuo žemiausio TMDB reitingo", "components.Discover.FilterSlideover.from": "<PERSON><PERSON>", "components.Discover.FilterSlideover.releaseDate": "Parodymo data", "components.Discover.FilterSlideover.clearfilters": "<PERSON><PERSON><PERSON><PERSON> aktyvius filtrus", "components.Discover.FilterSlideover.originalLanguage": "<PERSON>i kalba", "components.Discover.FilterSlideover.ratingText": "Reitingas tarp {minValue} ir {maxValue}", "components.Discover.moviegenres": "Filmų žanrai", "components.Discover.FilterSlideover.filters": "Filtrai", "components.Discover.FilterSlideover.firstAirDate": "Pirmo parodymo data", "components.Discover.FilterSlideover.runtime": "Trukmė", "components.Discover.FilterSlideover.runtimeText": "{minValue}-{maxValue} minučių trukmė", "components.Discover.FilterSlideover.studio": "<PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.tmdbuserscore": "TMDB naudotojų balai", "components.Discover.networks": "<PERSON><PERSON><PERSON>", "components.PermissionEdit.autoapproveSeries": "Automatiškai patvirtinti serialą", "components.PermissionEdit.createissuesDescription": "Suteikite leidimą pranešti apie medijos problemas.", "components.RequestBlock.decline": "<PERSON><PERSON><PERSON>", "components.PermissionEdit.autoapproveMovies": "Automatiškai patvirtinti 4K filmus", "components.RequestCard.tmdbid": "TMDB ID", "components.RequestCard.editrequest": "Redaguoti rezervaciją", "components.RequestCard.tvdbid": "TVDB ID", "components.RequestList.RequestItem.tmdbid": "TMDB ID", "components.RequestBlock.edit": "Redaguoti rezervaciją", "components.RequestBlock.lastmodifiedby": "Paskutinį kartą pakeitė", "components.RequestBlock.requestedby": "Rezervuota", "components.RequestCard.declinerequest": "<PERSON><PERSON><PERSON>", "components.RequestBlock.delete": "<PERSON><PERSON><PERSON><PERSON> re<PERSON>", "components.RequestCard.cancelrequest": "Atšaukti rezervaciją", "components.RequestBlock.requestdate": "Rezervacijos data", "components.RequestCard.approverequest": "T<PERSON>tin<PERSON> re<PERSON>ją", "components.TvDetails.tmdbuserscore": "TMDB naudotojų balai", "components.PermissionEdit.viewrecentDescription": "Suteikite leidimą perž<PERSON>ūrėti neseniai pridėtos medijos s<PERSON>.", "components.TitleCard.mediaerror": "{mediaType} Nerasta", "components.TitleCard.tmdbid": "TMDB ID", "components.TitleCard.tvdbid": "TVDB ID", "components.StatusBadge.playonplex": "<PERSON><PERSON><PERSON> per <PERSON>lex", "components.TitleCard.cleardata": "Išvalyti duomenis", "components.Discover.DiscoverMovieKeyword.keywordMovies": "{keywordTitle} filmai", "components.Discover.tvgenres": "Serialų žanrai", "components.PermissionEdit.viewrecent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "components.RequestList.RequestItem.tvdbid": "TVDB ID", "components.RequestBlock.approve": "Tvirtin<PERSON>", "components.Discover.CreateSlider.searchKeywords": "Ieškoti raktažodžių…", "components.Discover.CreateSlider.searchStudios": "Ieškoti studijų…", "components.Discover.CreateSlider.starttyping": "Pradėjus rašyti bus ieškoma.", "components.Discover.CreateSlider.validationDatarequired": "Turite pateikti duomenų.", "components.Discover.CreateSlider.validationTitlerequired": "Turite pateikti pavadinimą.", "components.Discover.DiscoverTvKeyword.keywordSeries": "{keywordTitle} serialai", "components.Discover.resettodefault": "Atstatyti į numaty<PERSON>ius", "components.Discover.stopediting": "<PERSON><PERSON><PERSON><PERSON>i", "components.Discover.studios": "<PERSON><PERSON><PERSON>", "components.Discover.tmdbmoviegenre": "TMDB filmo žanras", "components.Discover.tmdbmoviekeyword": "TMDB filmo raktažodis", "components.Discover.tmdbnetwork": "TMDB tinklas", "components.Discover.tmdbsearch": "TMDB paieška", "components.Discover.tmdbstudio": "TMDB studijos", "components.Discover.tmdbtvgenre": "TMDB serialo žanras", "components.Discover.tmdbtvkeyword": "TMDB serialo raktažodis", "components.PermissionEdit.autoapproveMoviesDescription": "Suteikite automatinį patvirtinimą ne 4K filmų rezervacijoms.", "components.RequestModal.requestcollection4ktitle": "4k rezervacijų kolekcija", "components.RequestModal.requestcollectiontitle": "Rezervacijų kolekcija", "components.RequestModal.requestmovietitle": "Rezervuoti filmą", "components.RequestModal.requestseries4ktitle": "Rezervuoti serialą 4K formatu", "components.RequestModal.requestseriestitle": "Rezervuoti serialą", "components.StatusBadge.managemedia": "<PERSON><PERSON><PERSON> {mediaType}", "components.TvDetails.rtaudiencescore": "\"Rotten Tomatoes\" ži<PERSON><PERSON><PERSON> balai", "components.Settings.SettingsJobsCache.command": "<PERSON><PERSON><PERSON>", "components.PermissionEdit.autoapprove4kSeries": "Automatiškai patvirtinti 4K serialų rezervacijas", "components.Settings.plex": "Plex", "components.Settings.SettingsLogs.resumeLogs": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.address": "<PERSON><PERSON><PERSON>", "components.StatusBadge.status": "{status}", "components.Settings.SettingsMain.general": "<PERSON><PERSON>", "components.Discover.DiscoverWatchlist.discoverwatchlist": "Jūsų Plex grojaraštis", "components.Settings.menuUsers": "Vartotojai", "components.Settings.SettingsLogs.message": "Žinutė", "components.UserList.owner": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.DiscoverWatchlist.watchlist": "Plex g<PERSON>", "components.MovieDetails.reportissue": "Pranešti apie problemą", "components.PermissionEdit.autorequest": "Auto rezervacija", "components.Settings.SettingsJobsCache.jobtype": "Tipas", "components.UserProfile.UserSettings.UserGeneralSettings.role": "Rolė", "components.MovieDetails.managemovie": "Tvarkyti filmą", "components.UserList.user": "Vartotojas", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Discover.tmdbmoviestreamingservices": "TMDB filmų transliavimo paslaugos", "components.Discover.tmdbtvstreamingservices": "TMDB TV transliacijos paslaugos", "components.Layout.Sidebar.browsemovies": "Filmai", "components.Layout.Sidebar.browsetv": "Serialai", "components.MovieDetails.digitalrelease": "Skaitmeninė pre<PERSON>a", "components.MovieDetails.physicalrelease": "<PERSON><PERSON><PERSON> pre<PERSON>", "components.MovieDetails.rtaudiencescore": "\"Rotten Tomatoes\" ži<PERSON><PERSON><PERSON> balai", "components.MovieDetails.rtcriticsscore": "\"Rotten Tomatoes\" Tomatometras", "components.MovieDetails.theatricalrelease": "<PERSON><PERSON><PERSON><PERSON>", "components.PermissionEdit.autoapprove4kMoviesDescription": "Suteikite automatinį patvirtinimą 4K filmų rezervacijoms.", "components.PermissionEdit.autoapprove4kSeriesDescription": "Suteikite automatinį patvirtinimą 4K serialų rezervacijoms.", "components.PermissionEdit.request4kTvDescription": "Sutiekti teisę prašyti 4K filmų rezervacijų.", "components.Settings.Notifications.encryptionNone": "Joks", "components.Settings.RadarrModal.tags": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobs": "Darbai", "components.Settings.default": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.mediaTypeMovie": "filmas", "components.Settings.mediaTypeSeries": "serialas", "components.Settings.menuAbout": "<PERSON><PERSON>", "components.Settings.menuGeneralSettings": "<PERSON><PERSON>", "components.Settings.menuPlexSettings": "Plex", "components.Settings.notifications": "Pranešimai", "components.Settings.scanning": "Sinchronizuojama…", "components.Setup.continue": "<PERSON><PERSON><PERSON><PERSON>", "components.Setup.finishing": "Baigiama…", "components.StatusBadge.seasonepisodenumber": "S{seasonNumber}E{episodeNumber}", "components.TvDetails.seasonstitle": "Sezonai", "components.UserList.create": "<PERSON><PERSON><PERSON><PERSON>", "components.UserList.password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserList.role": "Rolė", "components.UserList.totalrequests": "Rezervacijos", "components.UserList.users": "Vartotojai", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.user": "Vartotojas", "components.UserProfile.UserSettings.UserNotificationSettings.email": "El. <PERSON>", "components.UserProfile.UserSettings.menuChangePass": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.menuGeneralSettings": "<PERSON><PERSON>", "components.AirDateBadge.airsrelative": "Premjera {relativeTime}", "components.Discover.plexwatchlist": "Jūsų Plex grojaraštis", "components.MovieDetails.tmdbuserscore": "TMDB naudotojų balai", "components.Discover.DiscoverSliderEdit.deletefail": "Nepavyko ištri<PERSON> rėžio.", "components.Discover.DiscoverSliderEdit.deletesuccess": "<PERSON><PERSON><PERSON><PERSON>k<PERSON>štrinta<PERSON>.", "components.Discover.DiscoverSliderEdit.enable": "<PERSON><PERSON><PERSON><PERSON> mat<PERSON>", "components.Discover.createnewslider": "Sukurti naują rėžį", "components.Discover.customizediscover": "Koreguoti Naršymo skiltį", "components.Discover.updatesuccess": "Atnaujinti naršymo pritaikymo nustatymus.", "components.Settings.SettingsLogs.time": "<PERSON><PERSON>", "components.AirDateBadge.airedrelative": "Premjera {relativeTime}", "components.Discover.CreateSlider.slidernameplaceholder": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.RecentlyAddedSlider.recentlyAdded": "Paskutiniai pridėti", "components.PermissionEdit.autoapproveDescription": "Suteikti automatinius patvirtinimus visoms ne 4K medijos rezervacijoms.", "components.UserList.creating": "<PERSON><PERSON><PERSON>…", "components.DownloadBlock.formattedTitle": "{title}: sezonas {seasonNumber} epizodas {episodeNumber}", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Filmų rezervacijos", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "Serijų rezervacijos", "components.Layout.UserDropdown.requests": "Rezervacijos", "components.Settings.email": "El. <PERSON>", "components.Settings.is4k": "4K", "i18n.experimental": "Eksperimentinis", "components.Discover.PlexWatchlistSlider.plexwatchlist": "Jūsų Plex grojaraštis", "components.Discover.CreateSlider.addSlider": "<PERSON><PERSON><PERSON><PERSON> slankiklį", "components.Discover.CreateSlider.addcustomslider": "Sukurti rėžį", "components.Discover.CreateSlider.addfail": "Nepavyko sukurti naujo rėž<PERSON>.", "components.Discover.CreateSlider.editSlider": "Redaguoti rėžį", "components.Discover.CreateSlider.editfail": "Nepavyko redaguoti rėžio.", "components.Discover.CreateSlider.addsuccess": "Sukurti naują rėžį ir išsaugoti nestandartinius naršyko nustatymus.", "components.Discover.CreateSlider.editsuccess": "Redaguotas rėžį ir iš<PERSON>oti nestandartinius naršymo nustatymus.", "components.Discover.CreateSlider.providetmdbgenreid": "Pateikite TMDB žanro ID", "components.Discover.CreateSlider.providetmdbkeywordid": "Pateikite TMDB raktažodžio ID", "components.Discover.CreateSlider.providetmdbnetwork": "Pateikite TMDB tinklo ID", "components.Discover.CreateSlider.providetmdbsearch": "Pateikite paieškos užklausą", "components.Discover.CreateSlider.providetmdbstudio": "Pateikite TMDB studijos ID", "components.Discover.CreateSlider.needresults": "Turite turėti bent 1 rezultatą.", "components.Discover.FilterSlideover.tmdbuservotecount": "TMDB vartotojų balsų skaičius", "components.Discover.resetfailed": "<PERSON><PERSON><PERSON> atstatant į pirminius atradimo korekcijų nustatymus.", "components.PermissionEdit.autoapproveSeriesDescription": "Suteikite automatinį patvirtinimą ne 4K serialų rezervacijoms.", "components.Discover.emptywatchlist": "Čia bus rodoma medija, pridėta prie <PlexWatchlistSupportLink> Plex grojaraščio</PlexWatchlistSupportLink>.", "components.Discover.PlexWatchlistSlider.emptywatchlist": "Čia bus rodoma į <PlexWatchlistSupportLink> Plex grojaraštyje </PlexWatchlistSupportLink> pridėta medija.", "components.Discover.FilterSlideover.voteCount": "Balsų skaičius nuo {minValue} iki {maxValue}"}