---
version: '3.8'
services:
  jellyseerr:
    build:
      context: .
      dockerfile: Dockerfile.local
    ports:
      - '5055:5055'
    environment:
      DB_TYPE: 'postgres' # Which DB engine to use. The default is "sqlite". To use postgres, this needs to be set to "postgres"
      DB_HOST: 'postgres' # The host (url) of the database
      DB_PORT: '5432' # The port to connect to
      DB_USER: 'jellyseerr' # Username used to connect to the database
      DB_PASS: 'jellyseerr' # Password of the user used to connect to the database
      DB_NAME: 'jellyseerr' # The name of the database to connect to
      DB_LOG_QUERIES: 'false' # Whether to log the DB queries for debugging
      DB_USE_SSL: 'false' # Whether to enable ssl for database connection
    volumes:
      - .:/app:rw,cached
      - /app/node_modules
      - /app/.next
    depends_on:
      - postgres
    links:
      - postgres
  postgres:
    image: postgres
    environment:
      POSTGRES_USER: jellyseerr
      POSTGRES_PASSWORD: jellyseerr
      POSTGRES_DB: jellyseerr
    ports:
      - '5432:5432'
    volumes:
      - postgres:/var/lib/postgresql/data
volumes:
  postgres:
