---
title: Troubleshooting
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

## [TMD<PERSON>] failed to retrieve/fetch XXX

### Option 1: Change your DNS servers

This error often comes from your Internet Service Provider (ISP) blocking TMDB API. The ISP may block the DNS resolution to the TMDB API hostname.

To fix this, you can change your DNS servers to a public DNS service like Google's DNS or Cloudflare's DNS:

<Tabs groupId="methods" queryString>
  <TabItem value="docker-cli" label="Docker CLI">

Add the following to your `docker run` command to use Google's DNS:
```bash
--dns=*******
```
or for Cloudflare's DNS:
```bash
--dns=*******
```
or for Quad9 DNS:
```bash
--dns=*******
```

You can try them all and see which one works for your network.

  </TabItem>

  <TabItem value="docker-compose" label="Docker Compose">

Add the following to your `compose.yaml` to use Google's DNS:
```yaml
---
services:
  jellyseerr:
    dns:
      - *******
```
or for Cloudflare's DNS:
```yaml
---
services:
  jellyseerr:
    dns:
      - *******
```
or for Quad9's DNS:
```yaml
---
services:
  jellyseerr:
    dns:
      - *******
```

You can try them all and see which one works for your network.

  </TabItem>

  <TabItem value="windows" label="Windows">

1. Open the Control Panel.
2. Click on Network and Internet.
3. Click on Network and Sharing Center.
4. Click on Change adapter settings.
5. Right-click the network interface connected to the internet and select Properties.
6. Select Internet Protocol Version 4 (TCP/IPv4) and click Properties.
7. Select Use the following DNS server addresses and enter `*******` for Google's DNS or `*******` for Cloudflare's DNS or `*******` for Quad9's DNS.

  </TabItem>

  <TabItem value="linux" label="Linux">

1. Open a terminal.
2. Edit the `/etc/resolv.conf` file with your favorite text editor.
3. Add the following line to use Google's DNS:
    ```bash
    nameserver *******
    ```
    or for Cloudflare's DNS:

    ```bash
    nameserver *******
    ```
    or for Quad9's DNS:
    ```bash
    nameserver *******
    ```

  </TabItem>
</Tabs>

### Option 2: Use Jellyseerr through a proxy

If you can't change your DNS servers or force IPV4 resolution, you can use Jellyseerr through a proxy.

In some places (like China), the ISP blocks not only the DNS resolution but also the connection to the TMDB API.

You can configure Jellyseerr to use a proxy with the [HTTP(S) Proxy](/using-jellyseerr/settings/general#https-proxy) setting.

### Option 3: Force IPV4 resolution first

Sometimes there are configuration issues with IPV6 that prevent the hostname resolution from working correctly.

You can try to force the resolution to use IPV4 first by going to `Settings > Networking > Advanced Networking` and enabling `Force IPv4 Resolution First` setting and restarting Jellyseerr.

### Option 4: Check that your server can reach TMDB API

Make sure that your server can reach the TMDB API by running the following command:

<Tabs groupId="methods" queryString>
  <TabItem value="docker-cli" label="Docker CLI">

```bash
docker exec -it jellyseerr sh -c "apk update && apk add curl && curl -L https://api.themoviedb.org"
```

  </TabItem>

  <TabItem value="docker-compose" label="Docker Compose">

```bash
docker compose exec jellyseerr sh -c "apk update && apk add curl && curl -L https://api.themoviedb.org"
```

  </TabItem>
  <TabItem value="linux" label="Linux">

In a terminal:
```bash
curl -L https://api.themoviedb.org
```

  </TabItem>
  <TabItem value="windows" label="Windows">

In a PowerShell window:
```powershell
(Invoke-WebRequest -Uri "https://api.themoviedb.org" -Method Get).Content
```

  </TabItem>

</Tabs>

If you can't get a response, then your server can't reach the TMDB API.
This is usually due to a network configuration issue or a firewall blocking the connection.

## Account does not have admin privileges

If your admin account no longer has admin privileges, this is typically because your Jellyfin/Emby user ID has changed on the server side.

This can happen if you have a new installation of Jellyfin/Emby or if you have changed the user ID of your admin account.

### Solution: Reset admin access

1. Back up your `settings.json` file (located in your Jellyseerr data directory)
2. Stop the Jellyseerr container/service
3. Delete the `settings.json` file
4. Start Jellyseerr again
5. This will force the setup page to appear
6. Go through the setup process with the same login details
7. You can skip the services setup
8. Once you reach the discover page, stop Jellyseerr
9. Restore your backed-up `settings.json` file
10. Start Jellyseerr again

This process should restore your admin privileges while preserving your settings.

If you still encounter issues, please reach out on our support channels.
