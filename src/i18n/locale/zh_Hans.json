{"components.UserList.validationpasswordminchars": "密码必须至少包含八个字符", "components.UserList.validationEmail": "请输入有效的电子邮件地址", "components.UserList.userssaved": "用户权限保存成功！", "components.UserList.users": "用户", "components.UserList.userlist": "用户清单", "components.UserList.userfail": "用户权限保存中出了点问题。", "components.UserList.userdeleteerror": "刪除用户中出了点问题。", "components.UserList.userdeleted": "用户刪除成功！", "components.UserList.usercreatedsuccess": "建立新用户成功！", "components.UserList.usercreatedfailedexisting": "你提供的电子邮件地址已由其他用户使用。", "components.UserList.usercreatedfailed": "建立新用户中出了点问题。", "components.UserList.user": "用户", "components.UserList.totalrequests": "请求数", "components.UserList.sortRequests": "请求数", "components.UserList.sortDisplayName": "显示名称", "components.UserList.sortCreated": "加入日期", "components.UserList.role": "角色", "components.UserList.plexuser": "Plex 用户", "components.UserList.passwordinfodescription": "设置应用程序网址以及启用电子邮件通知，才能自动生成密码。", "components.UserList.password": "密码", "components.UserList.owner": "所有者", "components.UserList.nouserstoimport": "没有要导入的 Plex 用户。", "components.UserList.localuser": "本地用户", "components.UserList.localLoginDisabled": "<strong>允许本地登录</strong>的设置目前被禁用。", "components.UserList.importfromplexerror": "导入 Plex 用户时出错。", "components.UserList.importfromplex": "导入 Plex 用户", "components.UserList.importedfromplex": "<strong>{userCount}</strong> Plex {userCount, plural, one {user} other {users}} 成功导入！", "components.UserList.email": "电子邮件地址", "components.UserList.edituser": "编辑用户权限", "components.UserList.deleteuser": "刪除用户", "components.UserList.deleteconfirm": "确定要刪除这个用户吗？此用户的所有储存资料将被清除。", "components.UserList.creating": "创建中…", "components.UserList.createlocaluser": "建立本地用户", "components.UserList.created": "加入", "components.UserList.create": "建立", "components.UserList.bulkedit": "批量编辑", "components.UserList.autogeneratepasswordTip": "通過电子邮件发送服务器生成的密码给用户", "components.UserList.autogeneratepassword": "自动生成密码", "components.UserList.admin": "管理员", "components.UserList.accounttype": "类型", "components.TvDetails.watchtrailer": "观看预告片", "components.TvDetails.viewfullcrew": "查看完整制作群", "components.TvDetails.similar": "类似", "components.TvDetails.showtype": "节目类型", "components.TvDetails.seasons": "{seasonCount} 季", "components.TvDetails.recommendations": "推荐", "components.TvDetails.overviewunavailable": "没有简介。", "components.TvDetails.overview": "简介", "components.TvDetails.originaltitle": "原始標題", "components.TvDetails.originallanguage": "原始语言", "components.TvDetails.nextAirDate": "下一次播出日期", "components.TvDetails.network": "电视网", "components.TvDetails.firstAirDate": "原始播出日期", "components.TvDetails.episodeRuntimeMinutes": "{runtime} 分钟", "components.TvDetails.episodeRuntime": "劇集片長", "components.TvDetails.cast": "演员阵容", "components.TvDetails.anime": "动漫", "components.TvDetails.TvCrew.fullseriescrew": "制作群", "components.TvDetails.TvCast.fullseriescast": "演员阵容", "components.StatusBadge.status4k": "4K 版{status}", "components.Setup.welcome": "欢迎來到 <PERSON><PERSON><PERSON>rr", "components.Setup.signinMessage": "首先，请使用你的 Plex 账户登入", "components.Setup.setup": "配置", "components.Setup.finishing": "完成配置中…", "components.Setup.finish": "完成配置", "components.Setup.continue": "继续", "components.Setup.configureservices": "配置服务器", "components.Settings.webpush": "网络推送", "components.Settings.webhook": "网络钩子", "components.Settings.webAppUrlTip": "使用服务器的网络应用代替“托管”的网络应用", "components.Settings.webAppUrl": "<WebAppLink>网络应用</WebAppLink>网址（URL）", "components.Settings.validationPortRequired": "请输入有效的端口", "components.Settings.validationHostnameRequired": "请输入有效的主机名称或 IP 地址", "components.Settings.toastPlexRefreshSuccess": "获取 Plex 服务器列表成功！", "components.Settings.toastPlexRefreshFailure": "获取 Plex 服务器列表失败。", "components.Settings.toastPlexRefresh": "载入中…", "components.Settings.toastPlexConnectingSuccess": "Plex 服务器连线成功！", "components.Settings.toastPlexConnectingFailure": "Plex 服务器连线失败。", "components.Settings.toastPlexConnecting": "连线中…", "components.Settings.startscan": "执行扫描", "components.Settings.ssl": "SSL", "components.Settings.sonarrsettings": "Sonarr 设置", "components.Settings.settingUpPlexDescription": "你可以手动输入你的 Plex 服务器资料，或从 <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink> 返回的设置做选择以及自动配置。请点下拉式选单右边的按钮获取服务器列表。", "components.Settings.services": "服务器", "components.Settings.serviceSettingsDescription": "关于 {serverType} 服务器的设置。{serverType} 服务器数没有最大值限制，但你只能指定兩个服务器为默认（一个非 4K、一个 4K）。", "components.Settings.serverpresetRefreshing": "载入中…", "components.Settings.serverpresetManualMessage": "手动设定", "components.Settings.serverpresetLoad": "请点右边的按钮", "components.Settings.serverpreset": "服务器", "components.Settings.serverSecure": "SSL", "components.Settings.serverRemote": "遠端", "components.Settings.serverLocal": "本地", "components.Settings.scanning": "同步中…", "components.Settings.scan": "媒体库同步", "components.Settings.radarrsettings": "Radarr 设置", "components.Settings.port": "端口", "components.Settings.plexsettingsDescription": "关于 Plex 服务器的设置。Jellyseerr 将定时执行媒体库扫描。", "components.Settings.plexsettings": "Plex 设置", "components.Settings.plexlibrariesDescription": "Jellyseerr 将扫描的媒体库。", "components.Settings.plexlibraries": "Plex 媒体库", "components.Settings.plex": "Plex", "components.Settings.notrunning": "未运行", "components.Settings.notificationsettings": "通知设置", "components.Settings.notifications": "通知", "components.Settings.notificationAgentSettingsDescription": "设置通知类型和代理服务。", "components.Settings.noDefaultServer": "你必须至少指定一个 {serverType} 服务器为默认，才能处理{mediaType}请求。", "components.Settings.noDefaultNon4kServer": "如果你只有一台 {serverType} 服务器用于非 4K 和 4K 内容（或者如果你只下载 4k 内容），你的 {serverType} 服务器 <strong>不应该</strong>被指定为 4K 服务器。", "components.Settings.noDefault4kServer": "你必须指定一个 4K {serverType} 服务器为默认，才能处理 4K 的{mediaType}请求。", "components.Settings.menuUsers": "用户", "components.Settings.menuServices": "服务器", "components.Settings.menuPlexSettings": "Plex", "components.Settings.menuNotifications": "通知", "components.Settings.menuLogs": "日志", "components.Settings.menuJobs": "作业和缓存", "components.Settings.menuGeneralSettings": "常规", "components.Settings.menuAbout": "关于 <PERSON><PERSON><PERSON><PERSON>", "components.Settings.mediaTypeSeries": "电视节目", "components.Settings.mediaTypeMovie": "电影", "components.Settings.manualscanDescription": "在正常情況下，Jellyseerr 会每24小时扫描你的 Plex 媒体库。最新添加的媒体将更频繁扫描。设置新的 Plex 服务器时，我们建议你执行一次手动扫描！", "components.Settings.manualscan": "媒体库手动扫描", "components.Settings.librariesRemaining": "媒体库剩余数： {count}", "components.Settings.is4k": "4K", "components.Settings.hostname": "主机名称或 IP 地址", "components.Settings.enablessl": "使用安全通訊协议（SSL）", "components.Settings.email": "电子邮件", "components.Settings.deleteserverconfirm": "确定要刪除这个服务器吗？", "components.Settings.default4k": "设置 4K 为默认分辨率", "components.Settings.default": "默认", "components.Settings.currentlibrary": "当前媒体库： {name}", "components.Settings.copied": "应用程序密钥已复制到剪贴板。", "components.Settings.cancelscan": "取消扫描", "components.Settings.addsonarr": "添加 Sonarr 服务器", "components.Settings.address": "网址", "components.Settings.addradarr": "添加 Radarr 服务器", "components.Settings.activeProfile": "现行质量设置", "components.Settings.SonarrModal.validationRootFolderRequired": "必须设置根目录", "components.Settings.SonarrModal.validationProfileRequired": "必须设置质量", "components.Settings.SonarrModal.validationPortRequired": "请输入有效的端口", "components.Settings.SonarrModal.validationNameRequired": "请输入服务器名称", "components.Settings.SonarrModal.validationLanguageProfileRequired": "必须设置语言", "components.Settings.SonarrModal.validationHostnameRequired": "你必须提供有效的主机名或 IP 地址", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "必须刪除結尾斜線", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "必须添加前置斜線", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "必须刪除結尾斜線", "components.Settings.SonarrModal.validationApplicationUrl": "请输入有效的网址", "components.Settings.SonarrModal.validationApiKeyRequired": "请输入应用程序密钥", "components.Settings.SonarrModal.toastSonarrTestSuccess": "Sonarr 服务器连线成功！", "components.Settings.SonarrModal.toastSonarrTestFailure": "Sonarr 服务器连线失败。", "components.Settings.SonarrModal.testFirstTags": "请先测试连线", "components.Settings.SonarrModal.testFirstRootFolders": "请先测试连线", "components.Settings.SonarrModal.testFirstQualityProfiles": "请先测试连线", "components.Settings.SonarrModal.testFirstLanguageProfiles": "请先测试连线", "components.Settings.SonarrModal.tags": "标签", "components.Settings.SonarrModal.syncEnabled": "启用扫描", "components.Settings.SonarrModal.ssl": "使用安全通訊协议（SSL）", "components.Settings.SonarrModal.servername": "服务器名称", "components.Settings.SonarrModal.server4k": "4K 服务器", "components.Settings.SonarrModal.selecttags": "设定标签", "components.Settings.SonarrModal.selectRootFolder": "设定根目录", "components.Settings.SonarrModal.selectQualityProfile": "设定质量", "components.Settings.SonarrModal.selectLanguageProfile": "设定语言", "components.Settings.SonarrModal.seasonfolders": "季数档案夹", "components.Settings.SonarrModal.rootfolder": "根目录", "components.Settings.SonarrModal.qualityprofile": "质量设置", "components.Settings.SonarrModal.port": "端口", "components.PermissionEdit.autoapproveSeriesDescription": "自动批准非 4K 电视节目请求。", "components.PermissionEdit.autoapproveSeries": "电视节目自动批准", "components.PermissionEdit.autoapproveMoviesDescription": "自动批准非 4K 电影请求。", "components.PermissionEdit.autoapproveMovies": "电影自动批准", "components.PermissionEdit.autoapproveDescription": "自动批准所有非 4K 媒体请求。", "components.PermissionEdit.autoapprove4kSeriesDescription": "自动批准 4K 电视节目请求。", "components.PermissionEdit.autoapprove4kSeries": "4K 电视节目自动批准", "components.PermissionEdit.autoapprove4kMoviesDescription": "自动批准 4K 电影请求。", "components.PermissionEdit.autoapprove4kMovies": "4K 电影自动批准", "components.PermissionEdit.autoapprove4kDescription": "自动批准所有 4K 媒体请求。", "components.PermissionEdit.autoapprove4k": "自动批准 4K", "components.PermissionEdit.autoapprove": "自动批准", "components.PermissionEdit.advancedrequestDescription": "授予修改高级媒体请求选项的权限。", "components.PermissionEdit.advancedrequest": "进阶请求", "components.PermissionEdit.adminDescription": "授予最高权限；旁路所有权限检查。", "components.PermissionEdit.admin": "管理员", "components.NotificationTypeSelector.usermediarequestedDescription": "当其他用户提交需要管理员批准的请求时得到通知。", "components.NotificationTypeSelector.usermediafailedDescription": "当 Radarr 或 Sonarr 处理请求失败时得到通知。", "components.NotificationTypeSelector.usermediadeclinedDescription": "当你的请求被拒绝时得到通知。", "components.NotificationTypeSelector.usermediaavailableDescription": "当你请求的媒体可观看时得到通知。", "components.NotificationTypeSelector.usermediaapprovedDescription": "当你的请求被手动批准时得到通知。", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "当其他用户提交自动批准的请求时得到通知。", "components.NotificationTypeSelector.notificationTypes": "通知类型", "components.NotificationTypeSelector.mediarequestedDescription": "当用户提交需要管理员批准的请求时发送通知。", "components.NotificationTypeSelector.mediarequested": "请求待批准", "components.NotificationTypeSelector.mediafailedDescription": "当 Radarr 或 Sonarr 处理请求失败时发送通知。", "components.NotificationTypeSelector.mediafailed": "请求处理失败", "components.NotificationTypeSelector.mediadeclinedDescription": "当请求拒被絕时发送通知。", "components.NotificationTypeSelector.mediadeclined": "请求被拒", "components.NotificationTypeSelector.mediaavailableDescription": "当请求的媒体可观看时发送通知。", "components.NotificationTypeSelector.mediaavailable": "请求可用", "components.NotificationTypeSelector.mediaapprovedDescription": "当请求被手动批准时发送通知。", "components.NotificationTypeSelector.mediaapproved": "请求已获批准", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "当用户提交自动批准的请求时发送通知。", "components.NotificationTypeSelector.mediaAutoApproved": "请求自动批准", "components.MovieDetails.watchtrailer": "观看预告片", "components.MovieDetails.viewfullcrew": "查看完整制作群", "components.MovieDetails.studio": "制作公司", "components.MovieDetails.similar": "类似", "components.MovieDetails.showmore": "显示更多", "components.MovieDetails.showless": "显示更少", "components.MovieDetails.runtime": "{minutes} 分钟", "components.MovieDetails.revenue": "收入", "components.MovieDetails.releasedate": "上映日期", "components.MovieDetails.recommendations": "推荐", "components.MovieDetails.overviewunavailable": "没有简介。", "components.MovieDetails.overview": "简介", "components.MovieDetails.originaltitle": "原始標題", "components.MovieDetails.originallanguage": "原始语言", "components.MovieDetails.markavailable": "標记为可观看", "components.MovieDetails.mark4kavailable": "標记 4K 版为可观看", "components.MovieDetails.cast": "演员阵容", "components.MovieDetails.budget": "电影成本", "components.MovieDetails.MovieCrew.fullcrew": "制作群", "components.MovieDetails.MovieCast.fullcast": "演员阵容", "components.MediaSlider.ShowMoreCard.seemore": "更多", "components.Login.validationpasswordrequired": "请输入你的密码", "components.Login.validationemailrequired": "请输入有效的电子邮件地址", "components.Login.signinwithplex": "使用你的 Plex 账户", "components.Login.signinwithoverseerr": "使用你的 {applicationTitle} 账户", "components.Login.signinheader": "请先登入", "components.Login.signingin": "登入中…", "components.Login.signin": "登入", "components.Login.password": "密码", "components.Login.loginerror": "登入中出了点问题。", "components.Login.forgotpassword": "忘记密码？", "components.Login.email": "电子邮件地址", "components.Layout.VersionStatus.streamstable": "<PERSON><PERSON><PERSON><PERSON> 稳定版", "components.Layout.VersionStatus.streamdevelop": "<PERSON><PERSON><PERSON><PERSON> 开发版", "components.Layout.VersionStatus.outofdate": "過时", "components.Layout.VersionStatus.commitsbehind": "落后 {commitsBehind} 次提交", "components.Layout.UserDropdown.signout": "登出", "components.Layout.UserDropdown.settings": "用户设定", "components.Layout.UserDropdown.myprofile": "个人档案", "components.Layout.Sidebar.browsemovies": "电影", "components.Layout.Sidebar.browsetv": "电视节目", "components.Layout.Sidebar.users": "用户", "components.Layout.Sidebar.settings": "设定", "components.Layout.Sidebar.requests": "请求", "components.Layout.Sidebar.dashboard": "探索", "components.Layout.SearchInput.searchPlaceholder": "搜索电影、电视节目", "components.Layout.LanguagePicker.displaylanguage": "显示语言", "components.LanguageSelector.originalLanguageDefault": "所有语言", "components.LanguageSelector.languageServerDefault": "默认设置（{language}）", "components.DownloadBlock.estimatedtime": "预计：{time}", "components.Discover.upcomingtv": "即将上映的电视节目", "components.Discover.upcomingmovies": "即将上映的电影", "components.Discover.upcoming": "即将上映的电影", "components.Discover.trending": "趋势", "components.Discover.recentrequests": "最新请求", "components.Discover.recentlyAdded": "最新添加", "components.Discover.RecentlyAddedSlider.recentlyAdded": "最新添加", "components.Discover.populartv": "热门电视节目", "components.Discover.popularmovies": "热门电影", "components.Discover.discover": "探索", "components.Discover.TvGenreSlider.tvgenres": "电视节目类型", "components.Discover.TvGenreList.seriesgenres": "电视节目类型", "components.Discover.StudioSlider.studios": "制作公司", "components.Discover.NetworkSlider.networks": "电视网", "components.Discover.MovieGenreSlider.moviegenres": "电影类型", "components.Discover.MovieGenreList.moviegenres": "电影类型", "components.Discover.DiscoverTvLanguage.languageSeries": "{language}电视节目", "components.Discover.DiscoverTvGenre.genreSeries": "{genre}电视节目", "components.Discover.DiscoverStudio.studioMovies": "{studio} 电影", "components.Discover.DiscoverNetwork.networkSeries": "{network} 电视节目", "components.Discover.DiscoverMovieLanguage.languageMovies": "{language}电影", "components.Discover.DiscoverMovieGenre.genreMovies": "{genre}电影", "components.CollectionDetails.requestcollection4k": "提交 4K 系列请求", "components.CollectionDetails.requestcollection": "提交系列请求", "components.CollectionDetails.overview": "简介", "components.CollectionDetails.numberofmovies": "{count} 部电影", "components.AppDataWarning.dockerVolumeMissingDescription": "必须使用繫結掛载（bind mount）指定某个宿主机器的资料夹跟容器內的 <code>{appDataPath}</code> 资料夹連通，才能保存 Jellyseerr 的配置和数据。", "components.PersonDetails.ascharacter": "饰演 {character}", "pages.somethingwentwrong": "出了点问题", "pages.serviceunavailable": "服务器无法使用", "pages.returnHome": "返回首页", "pages.pagenotfound": "页面不存在", "pages.oops": "哎呀", "pages.internalservererror": "內部服务器错误", "pages.errormessagewithcode": "{statusCode}－{error}", "i18n.view": "查看", "i18n.usersettings": "用户设定", "i18n.unavailable": "不可观看", "i18n.tvshows": "电视节目", "i18n.tvshow": "电视节目", "i18n.testing": "测试中…", "i18n.test": "测试", "i18n.status": "状态", "i18n.showingresults": "<strong>{from}</strong>－<strong>{to}</strong> 列（共 <strong>{total}</strong> 列）", "i18n.settings": "设定", "i18n.saving": "保存中…", "i18n.save": "保存", "i18n.retrying": "重试中…", "i18n.retry": "重试", "i18n.resultsperpage": "每页显示 {pageSize} 列", "i18n.requesting": "提交请求中…", "i18n.requested": "已经有请求", "i18n.request4k": "提交 4K 请求", "i18n.request": "提交请求", "i18n.processing": "处理中", "i18n.previous": "上一页", "i18n.pending": "待处理", "i18n.partiallyavailable": "部分可观看", "i18n.notrequested": "没有请求", "i18n.noresults": "没有結果。", "i18n.next": "下一页", "i18n.movies": "电影", "i18n.movie": "电影", "i18n.loading": "载入中…", "i18n.failed": "失败", "i18n.experimental": "实验性", "i18n.edit": "编辑", "i18n.delimitedlist": "{a}、{b}", "i18n.deleting": "刪除中…", "i18n.delete": "刪除", "i18n.declined": "已拒绝", "i18n.decline": "拒绝", "i18n.close": "关闭", "i18n.canceling": "取消中…", "i18n.cancel": "取消", "i18n.back": "返回", "i18n.available": "可观看", "i18n.areyousure": "确定吗？", "i18n.approved": "已批准", "i18n.approve": "批准", "i18n.all": "所有", "i18n.advanced": "进阶", "components.UserProfile.unlimited": "无限", "components.UserProfile.totalrequests": "请求总数", "components.UserProfile.seriesrequest": "电视节目请求", "components.UserProfile.requestsperdays": "剩余 {limit}", "components.UserProfile.recentrequests": "最新请求", "components.UserProfile.pastdays": "{type}（前 {days} 天）", "components.UserProfile.movierequests": "电影请求", "components.UserProfile.limit": "{limit} 之 {remaining}", "components.UserProfile.UserSettings.unauthorizedDescription": "你无权编辑此用户的设置。", "components.UserProfile.UserSettings.menuPermissions": "权限", "components.UserProfile.UserSettings.menuNotifications": "通知", "components.UserProfile.UserSettings.menuGeneralSettings": "常规", "components.UserProfile.UserSettings.menuChangePass": "密码", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "你不能编辑自己的权限。", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "权限设置保存成功！", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "保存设置中出了点问题。", "components.UserProfile.UserSettings.UserPermissions.permissions": "权限设置", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "密码必须至少包含八个字符", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "请输入新密码", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "请输入当前的密码", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "密码必须匹配", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "密码必须匹配", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "密码设置成功！", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "重设密码中出了点问题。你确定输入的当前密码是正确的吗？", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "重设密码中出了点问题。", "components.UserProfile.UserSettings.UserPasswordChange.password": "密码设置", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "你无权设置此用户的密码。", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "你的帐户目前没有设置密码。在下方配置密码，使你能够作为“本地用户”登录。", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "此用户帐户目前没有设置密码。配置下面的密码以使此帐户能够作为“本地用户”登录。", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "新密码", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "当前的密码", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "确认密码", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "网络推送知设置保存成功！", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "网络推送知设置保存失败。", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "网络推送", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "请输入聊天室 ID", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "请输入有效的 PGP 公钥", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "请输入有效的用户 ID", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "Telegram 通知设置保存成功！", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "Telegram 通知设置保存失败。", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "先<TelegramBotLink>建立一个聊天室</TelegramBotLink>以及把 <GetIdBotLink>@get_id_bot</GetIdBotLink> 加到聊天室，然后在聊天室里发出 <code>/my_id</code> 命令", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "聊天室 ID", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "发送没有声音警报的通知", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "无声通知", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "使用 <OpenPgpLink>OpenPGP</OpenPgpLink> 电子邮件加密", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "PGP 公钥", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "通知设置", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "通知", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "电子邮件通知设置保存成功！", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "电子邮件通知设置保存失败。", "components.UserProfile.UserSettings.UserNotificationSettings.email": "电子邮件", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Discord 通知设置保存成功！", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "Discord 通知设置保存失败。", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "和用户账号关联的<FindDiscordIdLink>多位数 ID 号码</FindDiscordIdLink>", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "用户 ID", "components.UserProfile.UserSettings.UserGeneralSettings.user": "用户", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "设置保存成功！", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "保存设置中出了点问题。", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "电视节目请求限制", "components.UserProfile.UserSettings.UserGeneralSettings.role": "角色", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "以地区可用性筛选結果", "components.UserProfile.UserSettings.UserGeneralSettings.region": "探索地区", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "Plex 用户", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "所有者", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "以原始语言筛选結果", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "探索语言", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "电影请求限制", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "本地用户", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "默认设置（{language}）", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "常规设置", "components.UserProfile.UserSettings.UserGeneralSettings.general": "常规", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "覆盖全局限制", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "显示名称", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "显示语言", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "管理员", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "用户类型", "components.UserProfile.ProfileHeader.userid": "用户 ID：{userid}", "components.UserProfile.ProfileHeader.settings": "用户设定", "components.UserProfile.ProfileHeader.profile": "个人档案", "components.UserProfile.ProfileHeader.joindate": "建立日期：{joindate}", "components.Settings.SonarrModal.notagoptions": "没有标签。", "components.Settings.SonarrModal.loadingrootfolders": "载入中…", "components.Settings.SonarrModal.loadingprofiles": "载入中…", "components.Settings.SonarrModal.loadinglanguageprofiles": "载入中…", "components.Settings.SonarrModal.loadingTags": "载入中…", "components.Settings.SonarrModal.languageprofile": "语言设置", "components.Settings.SonarrModal.hostname": "主机名称或 IP 地址", "components.Settings.SonarrModal.externalUrl": "外部网址", "components.Settings.SonarrModal.enableSearch": "启用自动搜索", "components.Settings.SonarrModal.editsonarr": "编辑 Sonarr 服务器", "components.Settings.SonarrModal.edit4ksonarr": "编辑 4K Sonarr 服务器", "components.Settings.SonarrModal.defaultserver": "默认服务器", "components.Settings.SonarrModal.default4kserver": "默认 4K 服务器", "components.Settings.SonarrModal.createsonarr": "添加 Sonarr 服务器", "components.Settings.SonarrModal.create4ksonarr": "添加 4K Sonarr 服务器", "components.Settings.SonarrModal.baseUrl": "网站根目录", "components.Settings.SonarrModal.apiKey": "应用程序密钥", "components.Settings.SonarrModal.animerootfolder": "动漫根目录", "components.Settings.SonarrModal.animequalityprofile": "动漫质量设置", "components.Settings.SonarrModal.animelanguageprofile": "动漫语言设置", "components.Settings.SonarrModal.animeTags": "动漫标签", "components.Settings.SonarrModal.add": "添加服务器", "components.Settings.SettingsUsers.users": "用户", "components.Settings.SettingsUsers.userSettingsDescription": "关于用户的全局和默认设置。", "components.Settings.SettingsUsers.userSettings": "用户设置", "components.Settings.SettingsUsers.tvRequestLimitLabel": "电视节目请求全局限制", "components.Settings.SettingsUsers.toastSettingsSuccess": "用户设置保存成功！", "components.Settings.SettingsUsers.toastSettingsFailure": "保存设置中出了点问题。", "components.Settings.SettingsUsers.newPlexLoginTip": "让还没导入的 Plex 用户登录", "components.Settings.SettingsUsers.newPlexLogin": "允许新的 Plex 登录", "components.Settings.SettingsUsers.movieRequestLimitLabel": "电影请求全局限制", "components.Settings.SettingsUsers.localLoginTip": "让用户使用电子邮件地址和密码登录", "components.Settings.SettingsUsers.localLogin": "允许本地登录", "components.Settings.SettingsUsers.defaultPermissionsTip": "授予给新用户的权限", "components.Settings.SettingsUsers.defaultPermissions": "默认权限", "components.Settings.SettingsLogs.time": "时间戳", "components.Settings.SettingsLogs.showall": "查看所有日志", "components.Settings.SettingsLogs.resumeLogs": "恢复", "components.Settings.SettingsLogs.pauseLogs": "暫停", "components.Settings.SettingsLogs.message": "消息", "components.Settings.SettingsLogs.logsDescription": "你也可以直接查看这些日志，方法是借助 <code>stdout</code>, 或者打开 <code>{appDataPath}/logs/overseerr.log</code>。", "components.Settings.SettingsLogs.logs": "日志", "components.Settings.SettingsLogs.logDetails": "日志详細信息", "components.Settings.SettingsLogs.level": "等級", "components.Settings.SettingsLogs.label": "标签", "components.Settings.SettingsLogs.filterWarn": "警告", "components.Settings.SettingsLogs.filterInfo": "消息", "components.Settings.SettingsLogs.filterError": "错误", "components.Settings.SettingsLogs.filterDebug": "除错", "components.Settings.SettingsLogs.extraData": "附加数据", "components.Settings.SettingsLogs.copyToClipboard": "复制到剪贴板", "components.Settings.SettingsLogs.copiedLogMessage": "日志消息已复制到剪贴板。", "components.Settings.SettingsJobsCache.unknownJob": "未知作业", "components.Settings.SettingsJobsCache.sonarr-scan": "Sonarr 扫描", "components.Settings.SettingsJobsCache.runnow": "执行", "components.Settings.SettingsJobsCache.radarr-scan": "Radarr 扫描", "components.Settings.SettingsJobsCache.process": "程序", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Plex 最新添加扫描", "components.Settings.SettingsJobsCache.plex-full-scan": "Plex 媒体库扫描", "components.Settings.SettingsJobsCache.nextexecution": "下一次执行时间", "components.Settings.SettingsJobsCache.jobtype": "作业类型", "components.Settings.SettingsJobsCache.jobstarted": "{jobname} 已开始运行。", "components.Settings.SettingsJobsCache.jobsandcache": "作业和缓存", "components.Settings.SettingsJobsCache.jobsDescription": "Jellyseerr 将定时运行以下的维护任务。手动执行工作不会影响它正常的时间表。", "components.Settings.SettingsJobsCache.jobs": "作业", "components.Settings.SettingsJobsCache.jobname": "作业名", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname}已被取消。", "components.Settings.SettingsJobsCache.flushcache": "清除缓存", "components.Settings.SettingsJobsCache.download-sync-reset": "下载状态同步复位", "components.Settings.SettingsJobsCache.download-sync": "下载状态同步", "components.Settings.SettingsJobsCache.command": "命令", "components.Settings.SettingsJobsCache.canceljob": "取消作业", "components.Settings.SettingsJobsCache.cachevsize": "值储存大小", "components.Settings.SettingsJobsCache.cachename": "缓存名", "components.Settings.SettingsJobsCache.cachemisses": "失误数", "components.Settings.SettingsJobsCache.cacheksize": "键储存大小", "components.Settings.SettingsJobsCache.cachekeys": "键数", "components.Settings.SettingsJobsCache.cachehits": "击中数", "components.Settings.SettingsJobsCache.cacheflushed": "{cachename} 缓存已清除。", "components.Settings.SettingsJobsCache.cacheDescription": "外部应用程序介面（external API）请求将存到缓存，以減少 API 呼叫次数。", "components.Settings.SettingsJobsCache.cache": "缓存", "components.Settings.SettingsAbout.version": "软件版本", "components.Settings.SettingsAbout.uptodate": "最新", "components.Settings.SettingsAbout.totalrequests": "请求数", "components.Settings.SettingsAbout.totalmedia": "媒体数", "components.Settings.SettingsAbout.timezone": "时区", "components.Settings.SettingsAbout.supportoverseerr": "支持 <PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.preferredmethod": "首选", "components.Settings.SettingsAbout.overseerrinformation": "关于 <PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.outofdate": "過时", "components.Settings.SettingsAbout.helppaycoffee": "请开发者喝咖啡", "components.Settings.SettingsAbout.githubdiscussions": "GitHub 讨论区", "components.Settings.SettingsAbout.gettingsupport": "支援", "components.Settings.SettingsAbout.documentation": "文档", "components.Settings.SettingsAbout.betawarning": "这是测试版软件，所以可能会不稳定或被破坏。请向 GitHub 报告问题！", "components.Settings.SettingsAbout.about": "关于 <PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.Releases.viewongithub": "在 GitHub 上查看", "components.Settings.SettingsAbout.Releases.viewchangelog": "查看变更日志", "components.Settings.SettingsAbout.Releases.versionChangelog": "{version} 更新日志", "components.Settings.SettingsAbout.Releases.releases": "软件版本", "components.Settings.SettingsAbout.Releases.releasedataMissing": "软件发行数据当前不可用。", "components.Settings.SettingsAbout.Releases.latestversion": "最新软件版本", "components.Settings.SettingsAbout.Releases.currentversion": "当前版本", "components.Settings.RadarrModal.validationRootFolderRequired": "必须设置根目录", "components.Settings.RadarrModal.validationProfileRequired": "必须设置质量", "components.Settings.RadarrModal.validationPortRequired": "请输入有效的端口", "components.Settings.RadarrModal.validationNameRequired": "请输入服务器名称", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "必须设置最低状态", "components.Settings.RadarrModal.validationHostnameRequired": "你必须提供有效的主机名或 IP 地址", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "URL base 不能以尾部斜杠结束", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "URL base 必须有前置斜杠", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "必须刪除結尾斜線", "components.Settings.RadarrModal.validationApplicationUrl": "请输入有效的网址", "components.Settings.RadarrModal.validationApiKeyRequired": "请输入应用程序密钥", "components.Settings.RadarrModal.toastRadarrTestSuccess": "Radarr 服务器连线成功！", "components.Settings.RadarrModal.toastRadarrTestFailure": "Radarr 服务器连线失败。", "components.Settings.RadarrModal.testFirstTags": "请先测试连线", "components.Settings.RadarrModal.testFirstRootFolders": "请先测试连线", "components.Settings.RadarrModal.testFirstQualityProfiles": "请先测试连线", "components.Settings.RadarrModal.tags": "标签", "components.Settings.RadarrModal.syncEnabled": "启用扫描", "components.Settings.RadarrModal.ssl": "使用安全通訊协议（SSL）", "components.Settings.RadarrModal.servername": "服务器名称", "components.Settings.RadarrModal.server4k": "4K 服务器", "components.Settings.RadarrModal.selecttags": "设定标签", "components.Settings.RadarrModal.selectRootFolder": "设定根目录", "components.Settings.RadarrModal.selectQualityProfile": "设定质量", "components.Settings.RadarrModal.selectMinimumAvailability": "设定最低状态", "components.Settings.RadarrModal.rootfolder": "根目录", "components.Settings.RadarrModal.qualityprofile": "质量设置", "components.Settings.RadarrModal.port": "端口", "components.Settings.RadarrModal.notagoptions": "没有标签。", "components.Settings.RadarrModal.minimumAvailability": "最低状态", "components.Settings.RadarrModal.loadingrootfolders": "载入中…", "components.Settings.RadarrModal.loadingprofiles": "载入中…", "components.Settings.RadarrModal.loadingTags": "载入中…", "components.Settings.RadarrModal.hostname": "主机名称或 IP 地址", "components.Settings.RadarrModal.externalUrl": "外部网址（URL）", "components.Settings.RadarrModal.enableSearch": "启用自动搜索", "components.Settings.RadarrModal.editradarr": "编辑 Radarr 服务器", "components.Settings.RadarrModal.edit4kradarr": "编辑 4K Radarr 服务器", "components.Settings.RadarrModal.defaultserver": "默认服务器", "components.Settings.RadarrModal.default4kserver": "默认 4K 服务器", "components.Settings.RadarrModal.createradarr": "添加 Radarr 服务器", "components.Settings.RadarrModal.create4kradarr": "添加 4K Radarr 服务器", "components.Settings.RadarrModal.baseUrl": "网站根目录", "components.Settings.RadarrModal.apiKey": "应用程序密钥", "components.Settings.RadarrModal.add": "添加服务器", "components.Settings.Notifications.webhookUrlTip": "在你的服务器里创建一个<DiscordWebhookLink>网络钩子</DiscordWebhookLink>", "components.Settings.Notifications.webhookUrl": "网络钩子网址（URL）", "components.Settings.Notifications.validationUrl": "请输入有效的网址", "components.Settings.Notifications.validationTypes": "请选择通知类型", "components.Settings.Notifications.validationSmtpPortRequired": "请输入有效的端口", "components.Settings.Notifications.validationSmtpHostRequired": "请输入有效的主机名称或 IP 地址", "components.Settings.Notifications.validationPgpPrivateKey": "请输入有效的 PGP 私钥", "components.Settings.Notifications.validationPgpPassword": "请输入 PGP 解密密码", "components.Settings.Notifications.validationEmail": "请输入有效的电子邮件地址", "components.Settings.Notifications.validationChatIdRequired": "请输入有效的聊天室 ID", "components.Settings.Notifications.validationBotAPIRequired": "请输入机器人授权令牌", "components.Settings.Notifications.toastTelegramTestSuccess": "Telegram 测试通知已发送！", "components.Settings.Notifications.toastTelegramTestSending": "发送 Telegram 测试通知中…", "components.Settings.Notifications.toastTelegramTestFailed": "Telegram 测试通知发送失败。", "components.Settings.Notifications.toastEmailTestSuccess": "电子邮件测试通知已发送！", "components.Settings.Notifications.toastEmailTestSending": "发送电子邮件测试通知中…", "components.Settings.Notifications.toastEmailTestFailed": "电子邮件测试通知发送失败。", "components.Settings.Notifications.toastDiscordTestSuccess": "Discord 测试通知已发送！", "components.Settings.Notifications.toastDiscordTestSending": "发送 Discord 测试通知中…", "components.Settings.Notifications.toastDiscordTestFailed": "Discord 测试通知发送失败。", "components.Settings.Notifications.telegramsettingssaved": "Telegram 通知设置保存成功！", "components.Settings.Notifications.telegramsettingsfailed": "Telegram 通知设置保存失败。", "components.Settings.Notifications.smtpPort": "SMTP 端口", "components.Settings.Notifications.smtpHost": "SMTP 主机", "components.Settings.Notifications.senderName": "发件人姓名", "components.Settings.Notifications.sendSilentlyTip": "发送没有声音警报的通知", "components.Settings.Notifications.sendSilently": "无声通知", "components.Settings.Notifications.pgpPrivateKeyTip": "使用 <OpenPgpLink>OpenPGP</OpenPgpLink> 电子邮件加密与签章", "components.Settings.Notifications.pgpPrivateKey": "PGP 私钥", "components.Settings.Notifications.pgpPasswordTip": "使用 <OpenPgpLink>OpenPGP</OpenPgpLink> 电子邮件加密与签章", "components.Settings.Notifications.pgpPassword": "PGP 解密密码", "components.Settings.Notifications.encryptionTip": "TLS 通常会使用端口 465，而 STARTTLS 通常会使用端口 587", "components.Settings.Notifications.NotificationsWebhook.authheader": "Authorization 頭欄位", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "启用通知", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "网络推送通知设置保存成功！", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "网络推送通知设置保存失败。", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "网络推送测试通知已发送！", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "发送网络推送测试通知中…", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "网络推送测试通知发送失败。", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "Jellyseerr 必须通過 HTTPS 投放才能使用网络推送通知。", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "启用通知", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "创建一个 <WebhookLink>incoming webhook</WebhookLink> 集成", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "网络钩子网址（URL）", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "请输入有效的网址", "components.Settings.Notifications.NotificationsSlack.validationTypes": "请选择通知类型", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "Slack 测试通知已发送！", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "发送 Slack 测试通知中…", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "Slack 测试通知发送失败。", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Slack 通知设置保存成功！", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "Slack 通知设置保存失败。", "components.Settings.Notifications.NotificationsSlack.agentenabled": "启用通知", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "请输入有效的用户或群组令牌", "components.Settings.Notifications.NotificationsPushover.validationTypes": "请选择通知类型", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "请输入应用程序 API 令牌", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "你的 30 个字符的<UsersGroupsLink>用户或群组標識符</UsersGroupsLink>", "components.Settings.Notifications.NotificationsPushover.userToken": "用户或群组令牌", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Pushover 测试通知已发送！", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "发送 Pushover 测试通知中…", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "Pushover 测试通知发送失败。", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Pushover 通知设置保存成功！", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "Pushover 通知设置保存失败。", "components.Settings.Notifications.NotificationsPushover.agentenabled": "启用通知", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "建立一个 Jellyseerr 专用的<ApplicationRegistrationLink>应用程序</ApplicationRegistrationLink>", "components.Settings.Notifications.NotificationsPushover.accessToken": "应用程序 API 令牌", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "请选择通知类型", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "请输入 API 令牌", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Pushbullet 测试通知已发送！", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "发送 Pushbullet 测试通知中…", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "Pushbullet 测试通知发送失败。", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Pushbullet 通知设置保存成功！", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "Pushbullet 通知设置保存失败。", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "启用通知", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "从你的<PushbulletSettingsLink>账户设定</PushbulletSettingsLink>取得 API 令牌", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "API 令牌", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "用户或设備通知的<LunaSeaLink>网络钩子网址</LunaSeaLink>", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "网络钩子网址（URL）", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "请输入有效的网址", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "请选择通知类型", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "LunaSea 测试通知已发送！", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "发送 LunaSea 测试通知中…", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "LunaSea 测试通知发送失败。", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "LunaSea 通知设置保存成功！", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "LunaSea 通知设置保存失败。", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "不使用 <code>default</code> 默认设定档才必须输入", "components.Settings.Notifications.NotificationsLunaSea.profileName": "设定档名", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "启用通知", "components.Search.searchresults": "搜索結果", "components.Search.search": "搜索", "components.ResetPassword.validationpasswordrequired": "请输入密码", "components.ResetPassword.validationpasswordminchars": "密码必须至少包含八个字符", "components.ResetPassword.validationpasswordmatch": "密码必须匹配", "components.ResetPassword.validationemailrequired": "请输入有效的电子邮件地址", "components.ResetPassword.resetpasswordsuccessmessage": "密码重设成功！", "components.ResetPassword.resetpassword": "重设密码", "components.ResetPassword.requestresetlinksuccessmessage": "通過电子邮件发送了密码重设鏈接。", "components.ResetPassword.passwordreset": "密码重设", "components.ResetPassword.password": "密码", "components.ResetPassword.gobacklogin": "返回", "components.ResetPassword.emailresetlink": "发送密码重设电子邮件", "components.ResetPassword.email": "电子邮件地址", "components.ResetPassword.confirmpassword": "确认密码", "components.RequestModal.selectseason": "季数选择", "components.RequestModal.seasonnumber": "第 {number} 季", "components.RequestModal.season": "季数", "components.RequestModal.requestseasons": "提交请求", "components.RequestModal.requestfrom": "{username} 的请求待处理。", "components.RequestModal.requesterror": "提交请求中出了点问题。", "components.RequestModal.requestedited": "<strong>{title}</strong> 的请求编辑成功！", "components.RequestModal.requestcancelled": "<strong>{title}</strong> 的请求已被取消。", "components.RequestModal.requestadmin": "此请求将自动被批准。", "components.RequestModal.requestSuccess": "为 <strong>{title}</strong> 提交请求成功！", "components.RequestModal.requestCancel": "<strong>{title}</strong> 的请求已被取消。", "components.RequestModal.pendingrequest": "待处理请求", "components.RequestModal.pendingapproval": "你的请求正在等待管理员批准。", "components.RequestModal.pending4krequest": "待处理的 4K 请求", "components.RequestModal.numberofepisodes": "集数", "components.RequestModal.errorediting": "编辑请求中出了点问题。", "components.RequestModal.edit": "编辑请求", "components.RequestModal.cancel": "取消请求", "components.RequestModal.autoapproval": "自动批准", "components.RequestModal.alreadyrequested": "已经有请求", "components.RequestModal.SearchByNameModal.notvdbiddescription": "我们无法自动匹配这个连续剧。请选择下方正确的匹配。", "components.RequestModal.QuotaDisplay.seasonlimit": "个季数", "components.RequestModal.QuotaDisplay.season": "电视节目季数", "components.RequestModal.QuotaDisplay.requiredquotaUser": "此用户的电视节目请求数量必须至少剩余 <strong>{seasons}</strong> 个季数才能为此节目提交请求。", "components.RequestModal.QuotaDisplay.requiredquota": "你的电视节目请求数量必须至少剩余 <strong>{seasons}</strong> 个季数才能为此节目提交请求。", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {电影请求剩余数不足} other {剩余 <strong>#</strong> 个{type}请求}}", "components.RequestModal.QuotaDisplay.quotaLinkUser": "访问此用户的<ProfileLink>个人资料页面</ProfileLink>以查看用户的请求限制 。", "components.RequestModal.QuotaDisplay.quotaLink": "访问你的<ProfileLink>个人资料页面</ProfileLink>以查看你的请求限制 。", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "请求剩余数不足", "components.RequestModal.QuotaDisplay.movielimit": "部电影", "components.RequestModal.QuotaDisplay.movie": "电影", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "此用户每 <strong>{days}</strong> 天能为 <strong>{limit}</strong> {type}提交请求。", "components.RequestModal.QuotaDisplay.allowedRequests": "你每 <strong>{days}</strong> 天能为 <strong>{limit}</strong> {type}提交请求。", "components.RequestModal.AdvancedRequester.tags": "标签", "components.RequestModal.AdvancedRequester.selecttags": "设定标签", "components.RequestModal.AdvancedRequester.rootfolder": "根目录", "components.RequestModal.AdvancedRequester.requestas": "请求者", "components.RequestModal.AdvancedRequester.qualityprofile": "质量设置", "components.RequestModal.AdvancedRequester.notagoptions": "没有标签。", "components.RequestModal.AdvancedRequester.languageprofile": "语言设置", "components.RequestModal.AdvancedRequester.folder": "{path}（{space}）", "components.RequestModal.AdvancedRequester.destinationserver": "目標服务器", "components.RequestModal.AdvancedRequester.default": "{name}（默认）", "components.RequestModal.AdvancedRequester.animenote": "＊这是个动漫节目。", "components.RequestModal.AdvancedRequester.advancedoptions": "进阶选项", "components.RequestList.sortModified": "最后修改时间", "components.RequestList.sortAdded": "最新", "components.RequestList.showallrequests": "查看所有请求", "components.RequestList.requests": "请求", "components.RequestList.RequestItem.seasons": "季数", "components.RequestList.RequestItem.requesteddate": "请求日期", "components.RequestList.RequestItem.requested": "请求者", "components.RequestList.RequestItem.modifieduserdate": "{user}（{date}）", "components.RequestList.RequestItem.modified": "最后修改者", "components.RequestList.RequestItem.mediaerror": "未找到{mediaType}", "components.RequestList.RequestItem.failedretry": "重试提交请求中出了点问题。", "components.RequestList.RequestItem.editrequest": "编辑请求", "components.RequestList.RequestItem.deleterequest": "刪除请求", "components.RequestList.RequestItem.cancelRequest": "取消请求", "components.RequestCard.seasons": "季数", "components.RequestCard.mediaerror": "未找到{mediaType}", "components.RequestCard.failedretry": "重试提交请求中出了点问题。", "components.RequestCard.deleterequest": "刪除请求", "components.RequestButton.viewrequest4k": "查看 4K 请求", "components.RequestButton.viewrequest": "查看请求", "components.RequestButton.requestmore4k": "再提交 4K 请求", "components.RequestButton.requestmore": "提交更多季数的请求", "components.RequestButton.declinerequests": "拒绝{requestCount, plural, one {Request} other {{requestCount} Requests}}", "components.RequestButton.declinerequest4k": "拒绝 4K 请求", "components.RequestButton.declinerequest": "拒绝请求", "components.RequestButton.decline4krequests": "拒绝 {requestCount, plural, one {4K Request} other {{requestCount} 4K Requests}}", "components.RequestButton.approverequests": "批准 {requestCount, plural, one {Request} other {{requestCount} Requests}}", "components.RequestButton.approverequest4k": "批准 4K 请求", "components.RequestButton.approverequest": "批准请求", "components.RequestButton.approve4krequests": "批准 {requestCount, plural, one {4K Request} other {{requestCount} 4K Requests}}", "components.RequestBlock.server": "目標服务器", "components.RequestBlock.seasons": "季数", "components.RequestBlock.rootfolder": "根目录", "components.RequestBlock.requestoverrides": "覆盖请求", "components.RequestBlock.profilechanged": "质量设置", "components.RegionSelector.regionServerDefault": "默认设置（{region}）", "components.RegionSelector.regionDefault": "所有地区", "components.QuotaSelector.unlimited": "无限", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{seasons} 每 {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.seasons": "季", "components.QuotaSelector.movies": "部电影", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{movies} 每 {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.days": "天", "components.PersonDetails.lifespan": "{birthdate}－{deathdate}", "components.PersonDetails.crewmember": "制作群成员", "components.PersonDetails.birthdate": "{birthdate}－", "components.PersonDetails.appearsin": "演出", "components.PersonDetails.alsoknownas": "別名：{names}", "components.PermissionEdit.viewrequestsDescription": "授予查看其他用户提交的媒体请求的权限。", "components.PermissionEdit.viewrequests": "查看请求", "components.PermissionEdit.usersDescription": "授予管理用户的权限。 拥有此权限的用户无法修改具有管理员权限的用户或授予管理员权限。", "components.PermissionEdit.users": "用户管理", "components.PermissionEdit.requestTvDescription": "授予提交非 4K 电视剧请求的权限。", "components.PermissionEdit.requestTv": "提交电视节目请求", "components.PermissionEdit.requestMoviesDescription": "授予提交非 4K 电影请求的权限。", "components.PermissionEdit.requestMovies": "提交电影请求", "components.PermissionEdit.requestDescription": "授予提交非 4K 媒体请求的权限。", "components.PermissionEdit.request4kTvDescription": "授予提交 4K 电视剧请求的权限。", "components.PermissionEdit.request4kTv": "提交 4K 电视节目请求", "components.PermissionEdit.request4kMoviesDescription": "授予提交 4K 影片请求的权限。", "components.PermissionEdit.request4kMovies": "提交 4K 电影请求", "components.PermissionEdit.request4kDescription": "授予提交 4K 媒体请求的权限。", "components.PermissionEdit.request4k": "提交 4K 请求", "components.PermissionEdit.request": "提交请求", "components.PermissionEdit.managerequestsDescription": "授予管理媒体请求的权限。 拥有此权限的用户提出的所有请求都将被自动批准。", "components.PermissionEdit.managerequests": "请求管理", "components.Settings.Notifications.emailsettingssaved": "电子邮件通知设置保存成功！", "components.Settings.Notifications.emailsettingsfailed": "电子邮件通知设置保存失败。", "components.Settings.Notifications.emailsender": "发件人电子邮件地址", "components.Settings.Notifications.discordsettingssaved": "Discord 通知设置保存成功！", "components.Settings.Notifications.discordsettingsfailed": "Discord 通知设置保存失败。", "components.Settings.Notifications.chatIdTip": "先与你的机器人建立一个聊天室以及把 <GetIdBotLink>@get_id_bot</GetIdBotLink> 也加到聊天室，然后在聊天室里发出 <code>/my_id</code> 命令", "components.Settings.Notifications.chatId": "聊天室 ID", "components.Settings.Notifications.botUsernameTip": "允许用户也把机器人加到自己的聊天室以及设定自己的通知", "components.Settings.Notifications.botUsername": "Bot 机器人名", "components.Settings.Notifications.botAvatarUrl": "Bot 机器人頭像网址（URL）", "components.Settings.Notifications.botApiTip": "建立一个 Jellyseerr 专用的<CreateBotLink>机器人</CreateBotLink>", "components.Settings.Notifications.botAPI": "Bot 机器人授权令牌", "components.Settings.Notifications.authUser": "SMTP 用户", "components.Settings.Notifications.authPass": "SMTP 密码", "components.Settings.Notifications.allowselfsigned": "允许自签名证书", "components.Settings.Notifications.agentenabled": "启用通知", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "网络钩子通知设置保存成功！", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "网络钩子通知设置保存失败。", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "网络钩子网址（URL）", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "请输入有效的网址", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "请选择通知类型", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "请输入有效的 JSON 有效负载", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "网络钩子测试通知已发送！", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "发送网络钩子测试通知中…", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "网络钩子测试通知发送失败。", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "幫助", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "JSON 有效负载重设为默认负载成功！", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "重置为默认", "components.Settings.Notifications.NotificationsWebhook.customJson": "JSON 有效负载", "components.Settings.Notifications.encryptionOpportunisticTls": "始終使用 STARTTLS", "components.Settings.Notifications.encryptionNone": "不使用加密", "components.Settings.Notifications.encryptionImplicitTls": "使用传输层安全标准（TLS）", "components.Settings.Notifications.encryptionDefault": "盡可能使用 STARTTLS", "components.Settings.Notifications.encryption": "加密方式", "components.StatusBadge.status": "{status}", "components.IssueDetails.IssueComment.areyousuredelete": "你确定删除此条评论吗？", "components.IssueDetails.IssueComment.delete": "删除评论", "components.IssueDetails.IssueComment.edit": "编辑评论", "components.IssueDetails.IssueComment.postedby": "由 {username} 发布于 {relativeTime}", "components.IssueDetails.IssueComment.postedbyedited": "由 {username} 发布于 {relativeTime}（已编辑）", "components.IssueDetails.IssueComment.validationComment": "你必须输入一条消息", "components.IssueDetails.IssueDescription.deleteissue": "删除 Issue", "components.IssueDetails.allseasons": "所有季数", "components.IssueDetails.nocomments": "没有评论。", "components.IssueDetails.openedby": "#{issueId} 由 {username} 报告于 {relativeTime}", "components.IssueDetails.toaststatusupdatefailed": "更新问题状态时出了点问题。", "components.IssueDetails.unknownissuetype": "未知", "components.IssueDetails.IssueDescription.description": "描述", "components.IssueDetails.IssueDescription.edit": "编辑描述", "components.IssueDetails.closeissue": "关闭 Issue", "components.IssueDetails.closeissueandcomment": "评论后关闭", "components.IssueDetails.comments": "评论", "components.IssueDetails.deleteissueconfirm": "你是否确实要删除此 issue？", "components.IssueDetails.episode": "第 {episodeNumber} 集", "components.IssueDetails.issuepagetitle": "问题", "components.IssueDetails.lastupdated": "最后更新时间", "components.IssueDetails.leavecomment": "评论", "components.IssueDetails.openinarr": "在 {arr} 中打开", "components.IssueDetails.problemseason": "有问题的季数", "components.IssueDetails.toasteditdescriptionfailed": "编辑问题描述时出了点问题。", "components.IssueDetails.toastissuedeletefailed": "删除问题时出了点问题。", "components.IssueDetails.play4konplex": "在 {mediaServerName} 上播放 4K 版", "components.IssueDetails.openin4karr": "在 4K {arr} 中打开", "components.IssueDetails.playonplex": "在 {mediaServerName} 上播放", "components.IssueDetails.problemepisode": "有问题的集数", "components.IssueDetails.toasteditdescriptionsuccess": "Issue 描述编辑成功！", "components.IssueDetails.toaststatusupdated": "Issue 状态更新成功！", "components.IssueDetails.reopenissue": "重新打开 Issue", "components.IssueDetails.allepisodes": "所有剧集", "components.IssueDetails.issuetype": "类型", "components.IssueDetails.deleteissue": "删除 Issue", "components.IssueDetails.reopenissueandcomment": "评论后重新打开", "components.IssueDetails.season": "第 {seasonNumber} 季", "components.IssueDetails.toastissuedeleted": "Issue 删除成功！", "components.IssueModal.CreateIssueModal.episode": "第 {episodeNumber} 集", "components.IssueDetails.commentplaceholder": "添加评论…", "components.IssueList.IssueItem.issuestatus": "状态", "components.IssueList.IssueItem.issuetype": "类型", "components.IssueList.IssueItem.openeduserdate": "{date} by {user}", "components.IssueList.IssueItem.problemepisode": "受影响的剧集", "components.IssueList.IssueItem.episodes": "集数", "components.IssueList.IssueItem.opened": "打开", "components.Settings.Notifications.NotificationsGotify.agentenabled": "开启通知", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "Gotify通知设置保存失败。", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Gotify通知设置保存成功！", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "Gotify 测试通知发送失败。", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Gotify测试通知发送中…", "components.Settings.Notifications.enableMentions": "允许提及", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "每 {jobScheduleMinutes, plural, one {minute} other {{jobScheduleMinutes} minutes}}", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "应用 API 令牌", "components.UserList.newplexsigninenabled": "<strong>允许新的 Plex 用户登录</strong> 设置目前已启用。还没有导入的Plex用户也能登录。", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "Pushover 通知设置保存失败。", "components.NotificationTypeSelector.issuecomment": "问题评论", "components.MovieDetails.streamingproviders": "当前可播放", "components.Settings.RadarrModal.inCinemas": "已上映", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "从您的<PushbulletSettingsLink>账号设置</PushbulletSettingsLink>获取API令牌", "components.Settings.SettingsAbout.appDataPath": "数据目录", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "每 {jobScheduleHours, plural, one {hour} other {{jobScheduleHours} hours}}", "components.Settings.tautulliSettings": "Tautulli 设置", "components.Settings.tautulliSettingsDescription": "关于 Tautulli 服务器的设置。Jellyseerr 会从 Tautulli 获取 Plex 媒体的观看历史记录。", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "Discord 用户ID", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "Pushbullet 通知设置保存失败。", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "您的<FindDiscordIdLink>Discord 用户 ID </FindDiscordIdLink>", "components.IssueList.IssueItem.unknownissuetype": "未知", "components.IssueList.IssueItem.viewissue": "查看问题", "components.IssueList.issues": "问题", "components.IssueList.showallissues": "查看所有问题", "components.IssueList.sortAdded": "最新", "components.IssueList.IssueItem.seasons": "季", "components.IssueList.sortModified": "最后修改", "components.IssueModal.CreateIssueModal.allepisodes": "所有集数", "components.IssueModal.CreateIssueModal.allseasons": "所有季数", "components.IssueModal.CreateIssueModal.submitissue": "提交问题", "components.IssueModal.CreateIssueModal.toastFailedCreate": "提交问题报告时出了点问题。", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "成功为 <strong>{title}</strong> 报告问题！", "components.IssueModal.CreateIssueModal.toastviewissue": "查看问题", "components.IssueModal.CreateIssueModal.validationMessageRequired": "请输入问题说明", "components.IssueModal.CreateIssueModal.whatswrong": "请描述您遇到的问题？", "components.IssueModal.issueAudio": "音频", "components.IssueModal.issueOther": "其他", "components.Layout.Sidebar.issues": "问题", "components.ManageSlideOver.downloadstatus": "下载状态", "components.ManageSlideOver.manageModalClearMedia": "清除数据", "components.NotificationTypeSelector.issuecreatedDescription": "当用户报告问题时发送通知。", "components.NotificationTypeSelector.issuereopenedDescription": "当问题重新开启时发送通知。", "components.PermissionEdit.createissuesDescription": "授予报告媒体问题的权限。", "components.PermissionEdit.manageissuesDescription": "授予管理媒体问题的权限。", "components.RequestModal.requestmovies": "提出请求", "components.RequestModal.requestseasons4k": "提出4K请求", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "作业编辑成功！", "i18n.resolved": "已解决", "components.NotificationTypeSelector.issuereopened": "问题重新开启", "components.NotificationTypeSelector.userissueresolvedDescription": "当您报告的问题解决时获取通知。", "components.ManageSlideOver.alltime": "历史", "components.ManageSlideOver.manageModalAdvanced": "高级", "components.ManageSlideOver.manageModalClearMediaWarning": "* 这将会删除所有和{mediaType}相关的数据和所有请求。如果{mediaType}在您的{mediaServerName}服务器存在，数据将会在媒体库扫描时重新建立。", "components.ManageSlideOver.manageModalIssues": "未解决问题", "components.ManageSlideOver.manageModalMedia": "媒体", "components.ManageSlideOver.manageModalMedia4k": "4K 媒体", "components.ManageSlideOver.manageModalRequests": "请求", "components.ManageSlideOver.manageModalTitle": "管理{mediaType}", "components.ManageSlideOver.markavailable": "标记为可观看", "components.ManageSlideOver.mark4kavailable": "标记4K版本可观看", "components.ManageSlideOver.markallseasons4kavailable": "标记所有季的4K版本可观看", "components.ManageSlideOver.markallseasonsavailable": "标记所有季可观看", "components.ManageSlideOver.opentautulli": "在 Tautulli 中查看", "components.ManageSlideOver.pastdays": "过去 {days, number} 天", "components.ManageSlideOver.playedby": "观看者", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> 次", "components.MovieDetails.productioncountries": "出品国家", "components.NotificationTypeSelector.adminissuecommentDescription": "当其他用户评论时可以获取通知。", "components.NotificationTypeSelector.adminissuereopenedDescription": "当其他用户重新开启问题时获取通知。", "components.NotificationTypeSelector.adminissueresolvedDescription": "当其他用户解决问题时获取通知。", "components.NotificationTypeSelector.issuecommentDescription": "当问题有新评论时发送通知。", "components.NotificationTypeSelector.issuecreated": "问题报告", "components.NotificationTypeSelector.issueresolved": "问题解决", "components.NotificationTypeSelector.issueresolvedDescription": "当问题解决时发送通知。", "components.NotificationTypeSelector.userissuecommentDescription": "当您报告的问题有新评论时获取通知。", "components.NotificationTypeSelector.userissuecreatedDescription": "当其他用户报告问题时获取通知。", "components.NotificationTypeSelector.userissuereopenedDescription": "当您报告的问题重新开启时获取通知。", "components.PermissionEdit.createissues": "报告问题", "components.PermissionEdit.viewissues": "查看问题", "components.PermissionEdit.viewissuesDescription": "授予查看其他用户报告的媒体问题的权限。", "components.RequestModal.selectmovies": "请选择电影", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Gotify 测试通知已发送！", "components.Settings.Notifications.NotificationsGotify.token": "应用令牌", "components.Settings.Notifications.NotificationsGotify.url": "服务器地址", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "请输入应用令牌", "components.Settings.Notifications.NotificationsGotify.validationTypes": "请选择通知类型", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "请输入有效的网址", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "请删除网址结尾斜杠", "components.Settings.urlBase": "网站根路径", "components.Settings.validationApiKey": "请输入API key", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "您 30 个字符的<UsersGroupsLink>用户或群组ID</UsersGroupsLink>", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "频道标签", "components.Settings.RadarrModal.announced": "已公布", "components.Settings.RadarrModal.released": "已发布", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "新频率", "components.Settings.externalUrl": "外部网址", "components.Settings.tautulliApiKey": "应用程序密钥", "components.Settings.toastTautulliSettingsFailure": "保存 Tautulli 设置时出了点问题。", "components.Settings.toastTautulliSettingsSuccess": "Tautulli 设置保存成功！", "components.Settings.validationUrlTrailingSlash": "请删除结尾斜杠", "components.Settings.validationUrlBaseLeadingSlash": "请添加前置斜杠", "components.Settings.validationUrlBaseTrailingSlash": "请删除结尾斜杠", "components.TvDetails.productioncountries": "出品国家", "components.TvDetails.streamingproviders": "当前可播放", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "API 令牌", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "Pushbullet 通知设置保存成功！", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "建立一个 {applicationTitle} 专用的 <ApplicationRegistrationLink>应用</ApplicationRegistrationLink>", "components.UserProfile.recentlywatched": "最近观看", "components.IssueModal.issueSubtitles": "字幕", "components.IssueModal.issueVideo": "视频", "components.ManageSlideOver.manageModalNoRequests": "没有请求。", "i18n.open": "未解决", "components.PermissionEdit.manageissues": "管理问题", "components.RequestModal.approve": "批准请求", "components.RequestModal.requestApproved": "<strong>{title}</strong> 的请求已被批准！", "components.RequestModal.requestmovies4k": "提出4K请求", "components.IssueModal.CreateIssueModal.extras": "特辑", "components.IssueModal.CreateIssueModal.problemepisode": "有问题的集数", "components.IssueModal.CreateIssueModal.problemseason": "有问题的季数", "components.IssueModal.CreateIssueModal.providedetail": "请详细描述您遇到的问题。", "components.IssueModal.CreateIssueModal.reportissue": "报告问题", "components.IssueModal.CreateIssueModal.season": "第 {seasonNumber} 季", "components.ManageSlideOver.openarr": "打开{arr}服务器", "components.ManageSlideOver.openarr4k": "打开4K {arr} 服务器", "components.ManageSlideOver.tvshow": "个剧集", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "请输入有效的 API 令牌", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "请输入 API 令牌", "components.ManageSlideOver.movie": "部电影", "components.Settings.SettingsAbout.runningDevelop": "您正在使用 Jellyseerr 的 <code>develop</code> 开发板。我们只建议开发者和协助测试的人员使用。", "components.Settings.SettingsJobsCache.editJobSchedule": "编辑作业", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "保存作业设置时出了点问题。", "components.Settings.validationUrl": "请输入有效的网址", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "请输入有效的 Discord 用户 ID", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "用户或群组令牌", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "Pushover 通知设置保存成功！", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "请输入有效的用户或群组令牌", "i18n.import": "导入", "i18n.importing": "导入中…", "components.RequestBlock.languageprofile": "语言配置文件", "components.TitleCard.mediaerror": "未找到{mediaType}", "components.MovieDetails.digitalrelease": "数字发行", "components.MovieDetails.physicalrelease": "物理释放", "components.MovieDetails.theatricalrelease": "剧场版", "components.PermissionEdit.viewrecent": "查看最近添加的内容", "components.PermissionEdit.viewrecentDescription": "授予查看最近添加的媒体列表的权限。", "components.StatusChecker.appUpdated": "{applicationTitle} 已更新", "components.StatusChecker.restartRequired": "需要重启服务器", "components.StatusChecker.appUpdatedDescription": "请点击下面的按钮，重新加载应用程序。", "components.StatusChecker.reloadApp": "重新加载 {applicationTitle}", "i18n.restartRequired": "需要重新启动", "components.Settings.deleteServer": "删除 {serverType} 服务器", "components.StatusChecker.restartRequiredDescription": "请重新启动服务器以应用更新的设置。", "components.RequestList.RequestItem.tmdbid": "TMDB ID", "components.Discover.DiscoverWatchlist.watchlist": "Plex 关注列表", "components.MovieDetails.managemovie": "管理电影", "components.MovieDetails.reportissue": "报告问题", "components.NotificationTypeSelector.mediaautorequested": "自动提交的请求", "components.PermissionEdit.viewwatchlistsDescription": "授权查看其他用户的Plex关注列表。", "components.RequestList.RequestItem.tvdbid": "TheTVDB ID", "components.Settings.advancedTooltip": "错误配置此设置可能会导致功能不可用", "components.Settings.experimentalTooltip": "启用此设置可能会导致意外的应用程序行为", "components.TvDetails.reportissue": "报告问题", "components.RequestCard.tmdbid": "TMDB ID", "components.Settings.SettingsLogs.viewdetails": "查看详情", "components.Layout.UserDropdown.requests": "请求", "components.Settings.restartrequiredTooltip": "必须重新启动 Jellyseerr 才能使更改的设置生效", "components.TvDetails.manageseries": "管理电视节目", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "自动请求您的 <PlexWatchlistSupportLink>Plex 关注列表</PlexWatchlistSupportLink>的媒体", "components.AirDateBadge.airedrelative": "{relativeTime}播出", "components.AirDateBadge.airsrelative": "{relativeTime}播出", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "电影请求", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "电视节目请求", "components.NotificationTypeSelector.mediaautorequestedDescription": "当 Plex 关注列表中的项目自动提交新媒体请求时，会收到通知。", "components.PermissionEdit.viewwatchlists": "查看 Plex 关注列表", "components.TvDetails.Season.somethingwentwrong": "在检索季元数据时出了问题。", "components.UserProfile.plexwatchlist": "Plex 关注列表", "components.RequestCard.tvdbid": "TheTVDB ID", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "当前频率", "components.StatusBadge.playonplex": "在 Plex 上观看", "components.TitleCard.cleardata": "清除数据", "components.Discover.DiscoverWatchlist.discoverwatchlist": "您的 Plex 关注列表", "components.Discover.plexwatchlist": "您的 Plex 关注列表", "components.PermissionEdit.autorequestSeriesDescription": "授予自动请求 Plex 关注列表中的非 4K 电视节目的权限。", "components.PermissionEdit.autorequest": "自动请求", "components.PermissionEdit.autorequestDescription": "授予自动请求 Plex 关注列表中的非 4K 媒体的权限。", "components.TvDetails.episodeCount": "{episodeCount} 集", "components.MovieDetails.rtcriticsscore": "烂番茄专业评分", "components.MovieDetails.tmdbuserscore": "TMDB 用户评分", "components.RequestBlock.approve": "批准请求", "components.RequestBlock.lastmodifiedby": "最后修改者", "components.MovieDetails.rtaudiencescore": "烂番茄观众评分", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "电视节目自动请求", "components.TvDetails.rtaudiencescore": "烂番茄观众评分", "components.TvDetails.rtcriticsscore": "烂番茄专业评分", "components.TvDetails.tmdbuserscore": "TMDB 用户评分", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "同步 Plex 关注列表", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "自动请求您的 <PlexWatchlistSupportLink>Plex 关注列表</PlexWatchlistSupportLink> 中的电影", "components.PermissionEdit.autorequestSeries": "电视节目自动请求", "components.PermissionEdit.autorequestMoviesDescription": "授予自动请求 Plex 关注列表中的非 4K 电影的权限。", "components.PermissionEdit.autorequestMovies": "电影自动请求", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "电影自动请求", "components.RequestBlock.decline": "拒绝请求", "components.RequestModal.requestcollection4ktitle": "提交 4K 系列请求", "components.RequestModal.requestmovie4ktitle": "提交 4K 电影请求", "components.RequestModal.requestmovietitle": "提交电影请求", "components.RequestModal.requestseriestitle": "提交电视节目请求", "components.RequestBlock.edit": "编辑请求", "components.RequestModal.requestseries4ktitle": "提交 4K 电视节目请求", "components.RequestBlock.delete": "删除请求", "components.RequestBlock.requestdate": "请求日期", "components.RequestBlock.requestedby": "请求者", "components.RequestCard.approverequest": "批准请求", "components.RequestModal.requestcollectiontitle": "提交系列请求", "components.TvDetails.Season.noepisodes": "剧集列表不可用。", "components.StatusBadge.managemedia": "管理{mediaType}", "components.StatusBadge.openinarr": "在 {arr} 中打开", "components.TvDetails.status4k": "4K 版{status}", "components.UserProfile.emptywatchlist": "您的 <PlexWatchlistSupportLink>Plex 关注列表</PlexWatchlistSupportLink>中的媒体会显示在这里。", "components.TvDetails.seasonnumber": "第 {seasonNumber} 季", "components.TvDetails.seasonstitle": "季数", "components.RequestModal.SearchByNameModal.nomatches": "找不到此电视节目的数据。", "components.Discover.emptywatchlist": "您的 <PlexWatchlistSupportLink>Plex 关注列表</PlexWatchlistSupportLink>中的媒体会显示在这里。", "components.RequestCard.cancelrequest": "取消请求", "components.RequestCard.declinerequest": "拒绝请求", "components.RequestCard.editrequest": "编辑请求", "components.TitleCard.tmdbid": "TMDB ID", "components.TitleCard.tvdbid": "TheTVDB ID", "components.Settings.SettingsJobsCache.imagecachecount": "已缓存图片", "components.Settings.SettingsJobsCache.image-cache-cleanup": "清理图片缓存", "components.Settings.SettingsJobsCache.imagecacheDescription": "当此功能在设置中启用时，Jellyseerr 将代理并缓存预配置的外部来源中的图像。缓存的图片保存于你的配置文件夹中，你可以在 <code>{appDataPath}/cache/images</code> 目录下找到这些文件。", "components.Settings.SettingsJobsCache.imagecache": "图片缓存", "components.Settings.SettingsJobsCache.imagecachesize": "缓存总大小", "components.StatusBadge.seasonepisodenumber": "S{seasonNumber}E{episodeNumber}", "components.Discover.PlexWatchlistSlider.plexwatchlist": "您的 Plex 关注列表", "components.Discover.moviegenres": "电影类型", "components.Discover.networks": "电视网", "components.Discover.studios": "制作公司", "components.Discover.DiscoverMovieKeyword.keywordMovies": "{keywordTitle} 电影", "components.Settings.SettingsMain.apikey": "应用程序密钥", "components.Settings.SettingsMain.applicationTitle": "应用程序标题", "components.Settings.SettingsMain.applicationurl": "应用程序网址", "components.Settings.SettingsMain.cacheImagesTip": "缓存外部来源中的图像，这将需要大量的磁盘空间", "components.Settings.SettingsMain.generalsettingsDescription": "为 Jellyseerr 配置全局和默认设置。", "components.Settings.SettingsMain.generalsettings": "常规设置", "components.Settings.SettingsMain.general": "常规", "components.Settings.SettingsMain.locale": "显示语言", "components.Settings.SettingsMain.hideAvailable": "隐藏可观看的电影和电视节目", "components.Settings.SettingsMain.toastApiKeyFailure": "生成新的用户程序密钥中出了点问题。", "components.Settings.SettingsMain.toastSettingsFailure": "保存设置中出了点问题。", "components.Settings.SettingsMain.toastApiKeySuccess": "已成功生成新的应用程序密钥！", "components.Settings.SettingsMain.toastSettingsSuccess": "成功保存设置！", "components.Discover.CreateSlider.nooptions": "没有结果。", "components.Discover.DiscoverTv.discovertv": "电视节目", "components.Settings.SettingsMain.cacheImages": "启用图片缓存", "components.Discover.FilterSlideover.keywords": "关键词", "components.Discover.FilterSlideover.from": "从", "components.Discover.FilterSlideover.to": "到", "components.Selector.searchKeywords": "搜索关键词…", "components.Discover.FilterSlideover.releaseDate": "上映日期", "components.Selector.showmore": "显示更多", "components.Discover.FilterSlideover.runtimeText": "{minValue}-{maxValue} 分钟时长", "components.Discover.CreateSlider.searchStudios": "搜索制作公司…", "components.Selector.searchStudios": "搜索制作公司…", "components.Discover.FilterSlideover.studio": "制作公司", "components.Discover.FilterSlideover.originalLanguage": "原始语言", "components.Discover.FilterSlideover.streamingservices": "流媒体平台", "components.Discover.FilterSlideover.tmdbuserscore": "TMDB 用户评分", "components.Discover.FilterSlideover.runtime": "时长", "components.Discover.FilterSlideover.clearfilters": "清除使用中的筛选项", "components.Discover.DiscoverMovies.discovermovies": "电影", "components.Discover.FilterSlideover.ratingText": "评分从 {minValue} 到 {maxValue}", "components.Discover.CreateSlider.searchKeywords": "搜索关键词…", "components.Selector.nooptions": "没有结果。", "components.Discover.DiscoverTv.sortPopularityAsc": "人气递增", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "TMDB 评分递增", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "TMDB 评分递减", "components.Discover.DiscoverMovies.sortPopularityAsc": "人气递增", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "TMDB 评分递增", "components.Discover.DiscoverTv.sortTitleAsc": "标题 (A-Z) 递增", "components.Discover.DiscoverMovies.sortPopularityDesc": "人气递减", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "上映日期递增", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "上映日期递减", "components.Discover.DiscoverTv.sortPopularityDesc": "人气递减", "components.Discover.DiscoverTv.sortTitleDesc": "标题 (Z-A) 递减", "components.Settings.SettingsMain.partialRequestsEnabled": "允许电视节目不完整请求", "components.Discover.DiscoverMovies.sortTitleDesc": "标题 (Z-A) 递减", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "TMDB 评分递减", "components.Discover.DiscoverMovies.sortTitleAsc": "标题 (A-Z) 递增", "components.Settings.SettingsMain.validationApplicationUrlTrailingSlash": "必须删除结尾斜线", "components.Settings.SettingsMain.validationApplicationUrl": "请输入有效的网址", "components.Discover.FilterSlideover.firstAirDate": "首次播出日期", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "首次播出日期递增", "components.Discover.tvgenres": "电视节目类型", "components.Selector.searchGenres": "选择类型…", "components.Discover.FilterSlideover.filters": "筛选项", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "首次播出日期递减", "components.Discover.FilterSlideover.genres": "类型", "components.Discover.CreateSlider.searchGenres": "搜索类型…", "components.Discover.PlexWatchlistSlider.emptywatchlist": "您的 <PlexWatchlistSupportLink>Plex 关注列表</PlexWatchlistSupportLink>中的媒体会显示在这里。", "components.Selector.starttyping": "开始打字以进行搜索。", "components.Discover.CreateSlider.starttyping": "开始打字以进行搜索。", "components.Discover.CreateSlider.needresults": "你需要至少有 1 个结果。", "components.Selector.showless": "显示更少", "components.Discover.resetfailed": "重置探索媒体设置时出了点问题。", "components.Settings.SettingsMain.validationApplicationTitle": "你必须提供一个应用程序标题", "components.Discover.updatefailed": "更新探索媒体设置时出了点问题。", "components.Settings.SettingsMain.originallanguageTip": "以原始语言筛选结果", "components.Settings.SettingsMain.originallanguage": "探索语言", "components.Discover.tmdbnetwork": "TMDB 电视网", "components.Discover.tmdbsearch": "TMDB 搜索", "components.Discover.tmdbstudio": "TMDB 制作公司", "components.Discover.CreateSlider.editfail": "编辑滑动框失败。", "components.Discover.CreateSlider.validationTitlerequired": "你必须提供滑动框的名称。", "components.Discover.DiscoverSliderEdit.deletesuccess": "成功删除滑动框。", "components.Discover.resettodefault": "重置为默认", "components.Discover.stopediting": "退出编辑", "components.Discover.resetwarning": "将所有滑动框重置为默认。这也将删除所有自定义滑动框！", "components.Discover.CreateSlider.editSlider": "编辑滑动框", "components.Discover.CreateSlider.addSlider": "添加滑动框", "components.Discover.CreateSlider.addcustomslider": "创建自定义滑动框", "components.Discover.DiscoverSliderEdit.deletefail": "删除滑动框失败。", "components.Discover.DiscoverSliderEdit.remove": "移除", "components.Discover.CreateSlider.addfail": "创建新滑动框失败。", "components.Discover.CreateSlider.addsuccess": "已创建新的滑动框并保存探索自定义设置。", "components.Discover.tmdbmoviegenre": "TMDB 电影类型", "components.Discover.tmdbtvgenre": "TMDB 电视节目类型", "components.Discover.tmdbtvkeyword": "TMDB 电视节目关键词", "components.Discover.CreateSlider.providetmdbgenreid": "提供一个 TMDB 类型 ID", "components.Discover.CreateSlider.providetmdbkeywordid": "提供一个TMDB 关键词 ID", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {# 个使用中的筛选项} other {# 个使用中的筛选项}}", "components.Discover.DiscoverSliderEdit.enable": "切换可见性", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {# 个使用中的筛选项} other {# 个使用中的筛选项}}", "components.Discover.DiscoverTvKeyword.keywordSeries": "{keywordTitle}电视节目", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {# 个使用中的筛选项} other {# 个使用中的筛选项}}", "components.Discover.updatesuccess": "已更新探索自定义设置。", "components.RequestCard.unknowntitle": "未知标题", "components.RequestList.RequestItem.unknowntitle": "未知标题", "components.DownloadBlock.formattedTitle": "{title}: 第 {seasonNumber} 季 第 {episodeNumber} 集", "components.Discover.CreateSlider.providetmdbsearch": "提供一个搜索内容", "components.Discover.customizediscover": "自定义探索", "components.Discover.createnewslider": "创建新的滑动框", "components.Discover.tmdbmoviekeyword": "TMDB 电影关键词", "components.Discover.CreateSlider.editsuccess": "已编辑滑动框并保存探索自定义设置。", "components.Discover.CreateSlider.validationDatarequired": "你必须提供一个供搜索的内容。", "components.Discover.CreateSlider.providetmdbstudio": "提供 TMDB 制作公司 ID", "components.Discover.CreateSlider.providetmdbnetwork": "提供一个 TMDB 电视网 ID", "components.Discover.CreateSlider.slidernameplaceholder": "滑动框名称", "components.Discover.resetsuccess": "成功重置探索自定义设置。", "components.Settings.SettingsJobsCache.availability-sync": "同步媒体可用性", "components.Discover.tmdbmoviestreamingservices": "TMDB 电影流媒体服务", "components.Discover.tmdbtvstreamingservices": "TMDB 电视流媒体服务", "components.Settings.SettingsJobsCache.editJobScheduleSelectorSeconds": "每 {jobScheduleSeconds, plural, one {second} other {{jobScheduleSeconds} seconds}}", "components.Discover.FilterSlideover.voteCount": "在 {minValue} 和 {maxValue} 之间的评分数", "components.Settings.RadarrModal.tagRequests": "标签请求", "components.Settings.RadarrModal.tagRequestsInfo": "自动添加带有请求者的用户 ID 和显示名称的附加标签", "components.Settings.SonarrModal.tagRequests": "标记请求", "i18n.collection": "合集", "components.Discover.FilterSlideover.tmdbuservotecount": "TMDB 用户评分数", "components.Settings.SonarrModal.tagRequestsInfo": "自动添加带有请求者的用户 ID 和显示名称的附加标签", "components.Layout.UserWarnings.passwordRequired": "需要输入密码。", "components.Login.emailtooltip": "地址不需要与{mediaServerName}实例相关联。", "components.Login.initialsignin": "连接", "components.Login.initialsigningin": "连接中……", "components.Login.save": "添加", "components.Login.saving": "添加中……", "components.Login.signinwithjellyfin": "使用您的{mediaServerName}帐户", "components.Login.title": "添加邮件", "components.Login.username": "用户名", "components.Login.validationEmailFormat": "无效的邮件地址", "components.Login.validationEmailRequired": "你必须提供一个电子邮件", "components.Login.validationemailformat": "需要有效的电子邮件", "components.Login.validationhostformat": "需要有效的URL", "components.Login.validationusernamerequired": "需要用户名", "components.ManageSlideOver.manageModalRemoveMediaWarning": "* 这将不可逆地从{arr}中删除{mediaType}，包括所有文件。", "components.ManageSlideOver.removearr4k": "移除4K {arr}", "components.MovieDetails.downloadstatus": "下载状态", "components.MovieDetails.imdbuserscore": "IMDB用户评分", "components.MovieDetails.openradarr": "在Radarr中打开电影", "components.MovieDetails.play": "播放{mediaServerName}", "components.MovieDetails.play4k": "播放 4K {mediaServerName}", "components.Settings.Notifications.NotificationsPushover.sound": "通知声音", "components.Settings.SettingsJobsCache.jellyfin-full-scan": "Jellyfin全库扫描", "components.Settings.SonarrModal.seriesType": "系列类型", "components.Settings.jellyfinSettingsFailure": "保存{mediaServerName}设置时出错。", "components.Settings.jellyfinSettingsSuccess": "{mediaServerName}设置保存成功!", "components.Settings.jellyfinlibraries": "{mediaServerName}库", "components.Settings.jellyfinlibrariesDescription": "库{mediaServerName}用于扫描标题。如果没有列出库，请单击下面的按钮。", "components.Settings.jellyfinsettings": "{mediaServerName}设置", "components.Settings.manualscanJellyfin": "手动扫描库", "components.Settings.menuJellyfinSettings": "{mediaServerName}", "components.Settings.saving": "保存中……", "components.Settings.syncJellyfin": "同步库", "components.Settings.syncing": "同步中", "components.Settings.timeout": "超时", "components.Setup.signin": "登录", "components.Setup.signinWithJellyfin": "使用您的{mediaServerName}帐户", "components.TitleCard.addToWatchList": "添加到监视列表", "components.TitleCard.watchlistDeleted": "<strong>{title}</strong>从监视列表中删除成功!", "components.TitleCard.watchlistError": "出了问题，再试一次。", "components.TvDetails.play": "在 {mediaServerName} 播放", "components.TvDetails.play4k": "mediaServerName} 播放 4K", "components.UserList.importfrommediaserver": "导入{mediaServerName}用户", "components.UserList.mediaServerUser": "{mediaServerName} 用户", "components.UserList.noJellyfinuserstoimport": "在{mediaServerName}中没有用户要导入。", "components.UserList.newJellyfinsigninenabled": "<strong>启用 {mediaServerName} 登录</strong> 设置当前已启用. {mediaServerName} 具有库访问权限的用户不需要导入即可登录。", "components.UserProfile.UserSettings.UserGeneralSettings.mediaServerUser": "{mediaServerName} 用户", "components.UserProfile.UserSettings.UserNotificationSettings.deviceDefault": "设备默认", "components.Layout.UserWarnings.emailInvalid": "邮件地址无效。", "components.Layout.UserWarnings.emailRequired": "需要填写电子邮件地址。", "components.Login.credentialerror": "用户名或密码错误。", "components.Login.description": "由于这是您第一次登录{applicationName}，您需要添加一个有效的电子邮件地址。", "components.Login.validationhostrequired": "{mediaServerName} URL是必需的", "components.ManageSlideOver.removearr": "从{arr}中删除", "components.MovieDetails.openradarr4k": "在 4K Radarr 中打开电影", "components.Settings.Notifications.NotificationsPushover.deviceDefault": "设备默认", "components.Settings.Notifications.userEmailRequired": "获取用户邮箱", "components.Settings.SettingsAbout.supportjellyseerr": "支持<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.jellyfin-recently-added-scan": "Jellyfin最近新增扫描", "components.Settings.SonarrModal.animeSeriesType": "动漫系列", "components.Settings.jellyfinSettings": "{mediaServerName}设置", "components.Settings.jellyfinSettingsDescription": "可以为您的{mediaServerName}服务器配置内部和外部端点。在大多数情况下，外部URL与内部URL不同。如果你想重定向到不同的密码重置页面，也可以为{mediaServerName}登录设置自定义密码重置URL。", "components.Settings.jellyfinsettingsDescription": "配置{mediaServerName}服务器的设置。{mediaServerName}扫描{mediaServerName}库以查看可用的内容。", "components.Settings.manualscanDescriptionJellyfin": "正常情况下，每24小时只会运行一次。Jellyseerr将更积极地检查您的{mediaServerName}服务器最近添加的内容。如果这是您第一次配置Jellyseerr，建议您手动进行一次完整的库扫描!", "components.Settings.save": "保存更改", "components.Setup.configuremediaserver": "配置媒体服务器", "components.Setup.signinWithPlex": "使用您的 Plex 帐户", "components.TitleCard.watchlistCancel": "<strong>{title}</strong>的监视列表已取消。", "components.TitleCard.watchlistSuccess": "<strong>{title}</strong>添加到监视列表成功!", "components.UserList.importfromJellyfin": "导入{mediaServerName}用户", "components.UserProfile.UserSettings.UserGeneralSettings.email": "电子邮件", "components.UserProfile.UserSettings.UserGeneralSettings.save": "保存更改", "components.UserList.importfromJellyfinerror": "导入{mediaServerName}用户时出错。", "components.UserProfile.UserSettings.UserNotificationSettings.sound": "通知声音", "components.UserProfile.UserSettings.UserGeneralSettings.saving": "保存中……", "components.UserProfile.localWatchlist": "{username}的监视列表", "components.UserList.importedfromJellyfin": "<strong>{userCount}</strong> {mediaServerName} {userCount, plural, one {user} other {users}} 导入成功!", "components.Settings.SettingsNetwork.proxyBypassLocalAddresses": "代理绕过本地地址", "components.MovieDetails.addtowatchlist": "添加到监视列表", "components.MovieDetails.watchlistError": "出了点问题，请重试。", "components.PermissionEdit.manageblacklistDescription": "授予管理黑名单媒体的权限。", "components.Login.invalidurlerror": "无法连接到 {mediaServerName} 服务器。", "components.Login.enablessl": "使用 SSL", "components.PermissionEdit.manageblacklist": "管理黑名单", "components.Login.validationUrlBaseTrailingSlash": "请删除结尾斜杠", "components.Login.noadminerror": "没有在服务器上找到管理员账户。", "components.Login.port": "端口", "components.Login.validationHostnameRequired": "你必须提供有效的主机名或 IP 地址", "components.Login.validationPortRequired": "请输入有效的端口", "components.Login.validationUrlBaseLeadingSlash": "请添加前置斜杠", "components.Login.validationUrlTrailingSlash": "请删除结尾斜杠", "components.MovieDetails.removefromwatchlist": "从监视列表中移除", "components.Settings.SettingsNetwork.csrfProtection": "启用 CSRF 保护", "components.Settings.SettingsNetwork.advancedNetworkSettings": "高级网络设置", "components.Settings.SettingsMain.discoverRegion": "探索地区", "components.Settings.SettingsMain.enableSpecialEpisodes": "允许特别剧集请求", "components.Settings.SettingsNetwork.proxyEnabled": "HTTP(S) 代理", "components.Settings.SettingsNetwork.proxyHostname": "代理主机名", "components.Settings.SettingsNetwork.proxyPassword": "代理密码", "components.Settings.SettingsNetwork.proxyPort": "代理端口", "components.Settings.SettingsNetwork.proxySsl": "使用 SSL 进行代理", "components.Settings.SettingsNetwork.proxyUser": "代理用户名", "components.Settings.SettingsNetwork.trustProxy": "启用代理支持", "components.Settings.SettingsNetwork.validationProxyPort": "您必须提供有效端口", "components.Settings.SettingsUsers.loginMethods": "登录方式", "components.Settings.SettingsUsers.mediaServerLogin": "允许 {mediaServerName} 登录", "components.Settings.SettingsNetwork.networksettings": "网络设置"}