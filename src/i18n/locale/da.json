{"components.MediaSlider.ShowMoreCard.seemore": "Se <PERSON>", "components.Login.validationpasswordrequired": "Angiv et kodeord", "components.Login.validationemailrequired": "Angiv en gyldig email adresse", "components.Login.signinwithplex": "Brug din Plex konto", "components.Login.signinheader": "Log ind for at forsætte", "components.Login.signingin": "<PERSON><PERSON> Ind…", "components.Login.signin": "Log Ind", "components.Login.password": "Kodeord", "components.Login.loginerror": "Noget gik galt under log ind.", "components.Login.forgotpassword": "G<PERSON><PERSON>?", "components.Login.email": "<PERSON><PERSON>", "components.Layout.VersionStatus.streamdevelop": "<PERSON><PERSON><PERSON><PERSON>udgave", "components.Layout.VersionStatus.outofdate": "<PERSON><PERSON><PERSON><PERSON>", "components.Layout.UserDropdown.signout": "Log ud", "components.Layout.UserDropdown.settings": "<PERSON><PERSON><PERSON><PERSON>", "components.Layout.UserDropdown.myprofile": "Profil", "components.Layout.Sidebar.users": "Brugere", "components.Layout.Sidebar.settings": "<PERSON><PERSON><PERSON><PERSON>", "components.Layout.Sidebar.requests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Layout.Sidebar.dashboard": "Udforsk", "components.Layout.SearchInput.searchPlaceholder": "Søg i Film & Serier", "components.Layout.LanguagePicker.displaylanguage": "Grænsefladesprog", "components.LanguageSelector.originalLanguageDefault": "Alle Sprog", "components.LanguageSelector.languageServerDefault": "Standard ({language})", "components.Discover.upcomingtv": "Kommende Serier", "components.Discover.upcomingmovies": "Kommende Film", "components.Discover.upcoming": "Kommende Film", "components.Discover.trending": "Aktuelle", "components.Discover.recentrequests": "<PERSON><PERSON>", "components.Discover.recentlyAdded": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.populartv": "Populære Serier", "components.Discover.popularmovies": "Populære Film", "components.Discover.discover": "Udforsk", "components.Discover.TvGenreSlider.tvgenres": "Seriegenrer", "components.Discover.TvGenreList.seriesgenres": "Seriegenrer", "components.Discover.NetworkSlider.networks": "Netværk", "components.Discover.MovieGenreSlider.moviegenres": "Filmgenrer", "components.Discover.MovieGenreList.moviegenres": "Filmgenrer", "components.Discover.DiscoverStudio.studioMovies": "{studio} Film", "components.Discover.DiscoverNetwork.networkSeries": "{network} Serier", "components.Discover.DiscoverMovieLanguage.languageMovies": "Film på {language}", "components.Discover.DiscoverMovieGenre.genreMovies": "{genre} Film", "components.CollectionDetails.requestcollection4k": "Forespørg Samling i 4K", "components.CollectionDetails.requestcollection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.CollectionDetails.overview": "Overblik", "components.StatusBadge.status": "{status}", "components.MovieDetails.originaltitle": "Originaltitel", "components.AppDataWarning.dockerVolumeMissingDescription": "Volume mount'et, <code>{appDataPath}</code>, var ikke konfigureret korrekt. Al data vil blive slettet når containeren stoppes eller genstartes.", "components.CollectionDetails.numberofmovies": "{count} Film", "components.Discover.DiscoverTvGenre.genreSeries": "{genre} Serier", "components.Discover.DiscoverTvLanguage.languageSeries": "Serier på {language}", "components.Discover.StudioSlider.studios": "<PERSON><PERSON><PERSON>", "components.DownloadBlock.estimatedtime": "Est<PERSON><PERSON> {time}", "components.MovieDetails.mark4kavailable": "Mark<PERSON>r som Tilgængelig i 4K", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {ændring} other {ændringer}} bagud", "components.Layout.VersionStatus.streamstable": "Jellyseerr stabil", "components.MovieDetails.markavailable": "<PERSON><PERSON><PERSON> som Tilgænge<PERSON>g", "components.MovieDetails.originallanguage": "Originalsprog", "components.Login.signinwithoverseerr": "Brug din {applicationTitle} konto", "components.MovieDetails.MovieCast.fullcast": "<PERSON><PERSON>", "components.MovieDetails.MovieCrew.fullcrew": "Filmstab", "components.MovieDetails.budget": "Budget", "components.MovieDetails.overview": "Overblik", "components.MovieDetails.recommendations": "<PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.overviewunavailable": "Overblik ikke tilgængelig.", "components.MovieDetails.releasedate": "{releaseCount, plural, one {Udgivelsesdato} other {Udgivelsesdatoer}}", "components.MovieDetails.revenue": "Omsætning", "components.MovieDetails.runtime": "{minutes} minutter", "components.MovieDetails.similar": "Lignende Titler", "components.MovieDetails.streamingproviders": "Kan I Øjeblikket Streames På", "components.MovieDetails.viewfullcrew": "Vis Filmstab", "components.MovieDetails.watchtrailer": "<PERSON>", "components.PermissionEdit.advancedrequestDescription": "Giv tilladelse til at modificere avancerede medieforespørg<PERSON><PERSON>dstillinger.", "components.PermissionEdit.autoapprove4kSeries": "Auto-Godkend 4K Serier", "components.PermissionEdit.autoapprove4kMoviesDescription": "Giv automatisk godkendelse for 4K filmforespørgsler.", "components.PermissionEdit.managerequests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.PermissionEdit.request4k": "Forespørg 4K", "components.PermissionEdit.request4kMovies": "Forespørg 4K Film", "components.PermissionEdit.request4kTv": "Forespørg 4K Serier", "components.PermissionEdit.requestMovies": "Forespørg Film", "components.PermissionEdit.request": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.PersonDetails.ascharacter": "som {character}", "components.PersonDetails.birthdate": "<PERSON><PERSON><PERSON> {birthdate}", "components.RequestButton.approverequest4k": "Godkend 4K Forespørgsel", "components.RequestButton.approverequests": "Godkend {requestCount, plural, one {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>} other {{requestCount} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "components.RequestList.RequestItem.editrequest": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.mediaerror": "Den forbundne titel for denne forespø<PERSON> er ikke længere tilgængelig.", "components.RequestList.RequestItem.modified": "<PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.modifieduserdate": "{date} af {user}", "components.RequestList.RequestItem.requesteddate": "Forespurgt", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}}", "components.RequestList.requests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.ResetPassword.resetpassword": "Nulstil dit kodeord", "components.PermissionEdit.users": "Administ<PERSON><PERSON>", "components.PermissionEdit.usersDescription": "<PERSON><PERSON> till<PERSON><PERSON>e til at adminstrere brugere. Brugere med denne rettighed kan ikke modificere brugere med eller give <PERSON><PERSON> rettigheder.", "components.PermissionEdit.viewissuesDescription": "<PERSON><PERSON> tillade<PERSON>e til at se medieproblemer rapporteret af andre brugere.", "components.PermissionEdit.viewrequests": "<PERSON><PERSON>", "components.PersonDetails.alsoknownas": "Også Kendt Som: {names}", "components.PersonDetails.appearsin": "Medvirket i", "components.PersonDetails.crewmember": "Besætningsmedlem", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{movies} per {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.seasons": "{count, plural, one {sæson} other {sæsoner}}", "components.QuotaSelector.unlimited": "Ubegrænset", "components.RequestBlock.profilechanged": "Kvalitetsprofil", "components.RequestBlock.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}}", "components.RequestButton.approve4krequests": "Godkend {requestCount, plural, one {4K Forespørgsel} other {{requestCount} 4K Forespørgsler}}", "components.RequestButton.approverequest": "Godkend Forespørgsel", "components.RequestButton.decline4krequests": "Afvis {requestCount, plural, one {4K Forespørgsel} other {{requestCount} 4K Forespørgsler}}", "components.RequestButton.declinerequest4k": "Afvis 4K Forespørgsel", "components.RequestButton.requestmore4k": "Forespørg om Mere i 4K", "components.RequestButton.viewrequest": "<PERSON><PERSON>", "components.RequestModal.QuotaDisplay.movie": "film", "components.ResetPassword.password": "Kodeord", "components.Search.searchresults": "Søgeresultater", "components.IssueDetails.IssueComment.areyousuredelete": "Er du sikker på du vil slette denne kommentar?", "components.IssueDetails.IssueComment.delete": "Slet Kommentar", "components.IssueDetails.IssueComment.edit": "<PERSON><PERSON><PERSON><PERSON> kommentar", "components.IssueDetails.IssueComment.postedby": "Sendt {relativeTime} af {username}", "components.IssueDetails.IssueComment.postedbyedited": "Sendt {relativeTime} af {username} (Redigeret)", "components.IssueDetails.problemepisode": "Påvirket Episode", "components.ManageSlideOver.downloadstatus": "Download Status", "components.ManageSlideOver.manageModalNoRequests": "<PERSON><PERSON> forespørgsler.", "components.ManageSlideOver.movie": "film", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.QuotaSelector.movies": "{count, plural, one {film} other {film}}", "components.RequestButton.requestmore": "Forespørg om mere", "components.RequestModal.QuotaDisplay.quotaLinkUser": "Du kan se et overblik over denne brugers forespørgselsgrænser på deres <ProfileLink>profilside</ProfileLink>.", "components.RequestModal.selectseason": "<PERSON><PERSON><PERSON><PERSON>(er)", "components.ResetPassword.confirmpassword": "Bekræft Kodeord", "components.ResetPassword.email": "<PERSON><PERSON>", "components.ResetPassword.gobacklogin": "Vend Tilbage til Login-siden", "components.IssueDetails.IssueDescription.deleteissue": "Slet Problem", "components.IssueDetails.IssueDescription.description": "Beskrivelse", "components.IssueDetails.IssueDescription.edit": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.allepisodes": "Alle Episoder", "components.IssueDetails.allseasons": "<PERSON><PERSON>", "components.IssueDetails.closeissue": "Luk Problem", "components.IssueDetails.closeissueandcomment": "Luk med Kommentar", "components.IssueDetails.comments": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.deleteissue": "Slet Problem", "components.IssueDetails.deleteissueconfirm": "Er du sikker på du vil slette dette problem?", "components.IssueDetails.episode": "Episode {episodeNumber}", "components.IssueDetails.issuepagetitle": "Problem", "components.IssueDetails.lastupdated": "Sidst Opdateret", "components.IssueDetails.leavecomment": "Kommentar", "components.IssueDetails.openinarr": "Åbn i {arr}", "components.IssueDetails.season": "<PERSON><PERSON><PERSON> {seasonNumber}", "components.IssueDetails.toasteditdescriptionfailed": "Noget gik galt under redigeringen af problembeskrivelsen.", "components.IssueDetails.toastissuedeletefailed": "Noget gik galt under sletningen af problemet.", "components.IssueDetails.toaststatusupdatefailed": "Noget gik galt under opdateringen af problemstatus.", "components.IssueDetails.unknownissuetype": "Ukendt", "components.IssueModal.CreateIssueModal.problemepisode": "Påvirket Episode", "components.PermissionEdit.viewissues": "<PERSON><PERSON>", "components.RequestBlock.server": "Destinationsserver", "components.IssueModal.CreateIssueModal.extras": "Ekstra", "components.IssueModal.CreateIssueModal.problemseason": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueModal.CreateIssueModal.providedetail": "G<PERSON> venligst en detaljeret beskrivelse af problemet du stødte på.", "components.IssueModal.CreateIssueModal.reportissue": "Rapportér et Problem", "components.IssueModal.CreateIssueModal.season": "<PERSON><PERSON><PERSON> {seasonNumber}", "components.IssueModal.CreateIssueModal.toastFailedCreate": "Noget gik galt under indsendelsen af problemet.", "components.IssueModal.CreateIssueModal.toastviewissue": "Vis Problem", "components.IssueModal.CreateIssueModal.validationMessageRequired": "Du skal give en beskrivelse", "components.PermissionEdit.viewrequestsDescription": "G<PERSON> tilladelse til at se medieforespørgsler indsendt af andre brugere.", "components.RequestButton.declinerequest": "<PERSON><PERSON><PERSON>", "components.IssueDetails.playonplex": "Afspil i {mediaServerName}", "components.IssueDetails.problemseason": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.issuetype": "Type", "components.IssueDetails.nocomments": "Ingen kommentarer.", "components.IssueDetails.openedby": "#{issueId} åbnet {relativeTime} af {username}", "components.IssueDetails.openin4karr": "Åbn i 4K {arr}", "components.IssueDetails.play4konplex": "Afspil i 4K i {mediaServerName}", "components.IssueDetails.reopenissue": "Genåbn Problem", "components.IssueDetails.reopenissueandcomment": "Genåbn med Kommentar", "components.IssueDetails.toasteditdescriptionsuccess": "Problembeskrivelse er blevet redigeret!", "components.IssueDetails.toastissuedeleted": "Problem er blevet slettet!", "components.IssueDetails.toaststatusupdated": "Problemstatus er blevet opdateret!", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {Episode} other {Episoder}}", "components.IssueList.IssueItem.issuestatus": "Status", "components.IssueList.IssueItem.issuetype": "Type", "components.IssueList.IssueItem.opened": "<PERSON><PERSON><PERSON>", "components.IssueList.IssueItem.openeduserdate": "{date} af {user}", "components.IssueList.IssueItem.problemepisode": "Påvirket Episode", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}}", "components.IssueList.IssueItem.unknownissuetype": "Ukendt", "components.IssueList.IssueItem.viewissue": "Vis Problem", "components.IssueList.issues": "<PERSON>er", "components.IssueList.showallissues": "<PERSON><PERSON>", "components.IssueList.sortAdded": "Seneste", "components.IssueList.sortModified": "<PERSON><PERSON> ændret", "components.IssueModal.CreateIssueModal.allepisodes": "Alle Episoder", "components.IssueModal.CreateIssueModal.allseasons": "<PERSON><PERSON>", "components.IssueModal.CreateIssueModal.episode": "Episode {episodeNumber}", "components.IssueModal.issueSubtitles": "Undertekst", "components.IssueModal.issueVideo": "Video", "components.Layout.Sidebar.issues": "<PERSON>er", "components.ManageSlideOver.manageModalClearMedia": "<PERSON><PERSON>", "components.ManageSlideOver.manageModalClearMediaWarning": "* Dette vil slette alle data for denne {mediaType} uden mulighed for gendannelse, inklusiv alle forespørgsler. Hvis dette objekt findes i dit {mediaServerName} bibliotek vil medieinformationen blive genskabt under næste skanning.", "components.IssueModal.CreateIssueModal.whatswrong": "Hvad er galt?", "components.IssueModal.issueAudio": "Lyd", "components.IssueModal.issueOther": "And<PERSON>", "components.ManageSlideOver.tvshow": "serier", "components.MovieDetails.studio": "{studioCount, plural, one {<PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON>}}", "components.NotificationTypeSelector.adminissuecommentDescription": "<PERSON><PERSON><PERSON> notificeret når andre brugere kommenterer på problemer.", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "Send notifikationer når brugere indsender nye medieforespørgsler som automatisk godkendes.", "components.ManageSlideOver.manageModalRequests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.ManageSlideOver.manageModalTitle": "Administrér {mediaType}", "components.ManageSlideOver.mark4kavailable": "Mark<PERSON>r som Tilgængelig i 4K", "components.ManageSlideOver.markavailable": "<PERSON><PERSON><PERSON> som Tilgænge<PERSON>g", "components.ManageSlideOver.openarr": "Åbn i {arr}", "components.ManageSlideOver.openarr4k": "Åbn i 4K {arr}", "components.PermissionEdit.autoapproveMovies": "Auto-Godkend Film", "components.PermissionEdit.autoapproveSeries": "Auto-Godkend Serier", "components.PermissionEdit.autoapprove4k": "Auto-Godkend 4K", "components.PermissionEdit.autoapprove4kMovies": "Auto-Godkend 4K Film", "components.PermissionEdit.requestTv": "Forespørg Serier", "components.PermissionEdit.autoapproveMoviesDescription": "Giv automatisk godkendelse for alle ikke-4K filmforespørgsler.", "components.PermissionEdit.requestTvDescription": "<PERSON><PERSON>v notificeret når problemer er genåbnet af andre brugere.", "components.RegionSelector.regionServerDefault": "Standard ({region})", "components.RequestCard.deleterequest": "Slet Forespørgsel", "components.RequestCard.mediaerror": "Den forbundne titel for denne forespø<PERSON> er ikke længere tilgængelig.", "components.RequestCard.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}}", "components.RequestList.RequestItem.cancelRequest": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.deleterequest": "Slet Forespørgsel", "components.RequestModal.AdvancedRequester.default": "{name} (Standard)", "components.RequestModal.AdvancedRequester.destinationserver": "Destinationsserver", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.RequestList.RequestItem.requested": "Forespurgt", "components.RequestList.showallrequests": "<PERSON><PERSON> <PERSON>", "components.RequestList.sortAdded": "Seneste", "components.RequestList.sortModified": "<PERSON><PERSON> ændret", "components.RequestModal.AdvancedRequester.advancedoptions": "<PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.animenote": "* Denne serie er en anime.", "components.RequestModal.AdvancedRequester.selecttags": "Væ<PERSON>g tags", "components.RequestModal.AdvancedRequester.languageprofile": "Sprogprofil", "components.RequestModal.AdvancedRequester.notagoptions": "Ingen tags.", "components.RequestModal.AdvancedRequester.qualityprofile": "Kvalitetsprofil", "components.RequestModal.AdvancedRequester.requestas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.rootfolder": "<PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.tags": "Tags", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {film} other {film}}", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "<PERSON>k<PERSON> tilstrækkelig med sæsonforespørgsler tilbage", "components.RequestModal.QuotaDisplay.quotaLink": "Du kan se et overblik over dine forespørgselsgrænser på din <ProfileLink>profilside</ProfileLink>.", "components.RequestModal.QuotaDisplay.allowedRequests": "Du kan forespørge om <strong>{limit}</strong> {type} hver <strong>{days}</strong> dag.", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "<PERSON><PERSON> bruger kan forespørge om <strong>{limit}</strong> {type} hver <strong>{days}</strong> dag.", "components.IssueDetails.IssueComment.validationComment": "Du skal skrive en besked", "components.ManageSlideOver.manageModalIssues": "<PERSON><PERSON><PERSON>", "components.MovieDetails.showless": "Vis <PERSON>", "components.MovieDetails.cast": "Medvirkende", "components.IssueModal.CreateIssueModal.submitissue": "Indsend Problem", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "Problemrapport for <strong>{title}</strong> er blevet indsendt!", "components.MovieDetails.showmore": "<PERSON><PERSON>", "components.PermissionEdit.autoapprove": "Auto-Godkend", "components.QuotaSelector.days": "{count, plural, one {dag} other {dage}}", "components.RequestBlock.requestoverrides": "Forespørgselstilsidesættelser", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{seasons} per {quotaDays} {days}</quotaUnits>", "components.RequestButton.declinerequests": "Afvis {requestCount, plural, one {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>} other {{requestCount} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "components.RegionSelector.regionDefault": "Alle Regioner", "components.RequestBlock.rootfolder": "<PERSON><PERSON><PERSON>", "components.RequestButton.viewrequest4k": "Vis 4K Forespørgsel", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "Aktivér Agent", "components.RequestModal.seasonnumber": "<PERSON><PERSON><PERSON> {number}", "components.NotificationTypeSelector.mediadeclinedDescription": "Send notifikationer når medieforespørgsler afvises.", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "For at kunne modtage web push-notifikationer skal Jellyseerr benytte HTTPS.", "components.NotificationTypeSelector.mediaavailable": "Me<PERSON> Tilgænge<PERSON>g", "components.NotificationTypeSelector.mediafailed": "<PERSON><PERSON>", "components.PermissionEdit.admin": "Administrator", "components.PermissionEdit.adminDescription": "Fuld administratoradgang. Omgår alle andre tilladelseskontroller.", "components.PermissionEdit.advancedrequest": "<PERSON><PERSON><PERSON><PERSON>", "components.PermissionEdit.autoapprove4kSeriesDescription": "Giv automatisk godkendelse for 4K serieforespørgsler.", "components.PermissionEdit.managerequestsDescription": "G<PERSON> tilladelse til at administrere medieforespørgsler. Alle forespørgsler lavet af en bruger med denne tilladelse vil automatisk blive godkendt.", "components.PermissionEdit.request4kDescription": "<PERSON><PERSON> tilladelse til at indsende forespørgsler for 4K medier.", "components.PermissionEdit.request4kMoviesDescription": "<PERSON><PERSON> tillade<PERSON><PERSON> til at indsende forespørgsler for 4K film.", "components.PermissionEdit.request4kTvDescription": "Giv tilladelse til at indsende forespørgsler for 4K serier.", "components.PermissionEdit.requestDescription": "<PERSON><PERSON> tilladelse til at indsende forespørgsler for ikke-4K medier.", "components.RequestList.RequestItem.failedretry": "Noget gik galt ved nyt forsøg på forespørgslen.", "components.RequestModal.QuotaDisplay.requiredquota": "Du skal mindst have <strong>{seasons}</strong> {seasons, plural, one {sæsonforespørgsel} other {sæsonforespørgsler}} tilbage for at indsende en forespørgsel på denne serie.", "components.RequestModal.QuotaDisplay.requiredquotaUser": "<PERSON>ne bruger skal mindst have <strong>{seasons}</strong> {seasons, plural, one {sæsonforespørgsel} other {sæsonforespørgsler}} tilbage for at indsende en forespørgsel fore denne serie.", "components.RequestModal.QuotaDisplay.season": "<PERSON><PERSON><PERSON>", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {sæson} other {sæsoner}}", "components.RequestModal.alreadyrequested": "Allerede Forespurgt", "components.RequestModal.cancel": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.edit": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.errorediting": "Noget gik galt under redigeringen af forespørgslen.", "components.RequestModal.numberofepisodes": "<PERSON><PERSON>", "components.RequestModal.pending4krequest": "Afventende 4K Forespørgsler", "components.RequestModal.pendingapproval": "<PERSON> forespø<PERSON><PERSON> afventer godkendel<PERSON>.", "components.ResetPassword.resetpasswordsuccessmessage": "Kodeord er nulstillet!", "components.Settings.Notifications.NotificationsLunaSea.profileName": "Profilnavn", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "LunaSea testnotifikation er afsendt!", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "Du skal vælge mindst én notifikationstype", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "Pushbullet testnotifikation kunne ikke sendes.", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicationRegistrationLink>Registrér en applikation</ApplicationRegistrationLink> til brug med Je<PERSON>seerr", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Pushover notifikationsindstillinger er blevet gemt!", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "Pushover testnotifikation kunne ikke afsendes.", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "Du skal angive en gyldig applikationstoken", "components.Settings.Notifications.NotificationsPushover.validationTypes": "Du skal vælge mindst én notifikationstype", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "Opret en <WebhookLink>Indkommende Webhook</WebhookLink> integration", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "Aktivér Agent", "components.Settings.Notifications.NotificationsWebhook.customJson": "JSON indhold", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "Nulstil", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "Du skal angive et gyldigt URL", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "Webhook URL", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "Webhook notifikationsindstillinger kunne ikke gemmes.", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "Webhook notifikationsindstillinger er blevet gemt!", "components.Settings.Notifications.agentenabled": "Aktivér Agent", "components.NotificationTypeSelector.adminissuereopenedDescription": "<PERSON><PERSON>v notificeret når problemer er oprettet af andre brugere.", "components.NotificationTypeSelector.adminissueresolvedDescription": "<PERSON><PERSON>v notificeret når problemer er løst af andre brugere.", "components.NotificationTypeSelector.issuereopened": "Problem <PERSON>", "components.NotificationTypeSelector.issuereopenedDescription": "Send notifikationer når problemer gen<PERSON>.", "components.NotificationTypeSelector.mediafailedDescription": "Send notifikationer når medieforespørgsler ikke kunne tilføjes til Radarr eller Sonarr.", "components.NotificationTypeSelector.mediarequested": "<PERSON><PERSON>", "components.NotificationTypeSelector.userissuecommentDescription": "Bliv notificeret når problemer du har rapporteret får nye kommentarer.", "components.NotificationTypeSelector.userissuecreatedDescription": "<PERSON><PERSON><PERSON> not<PERSON>ret når andre brugere rapporterer problemer.", "components.NotificationTypeSelector.userissuereopenedDescription": "Bliv notificeret når problemer du har rapporteret er genåbnet.", "components.NotificationTypeSelector.userissueresolvedDescription": "B<PERSON>v notificeret når problemer du har rapporteret er løst.", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "Bliv notificeret når andre brugere indsender nye medieforespørgsler som automatisk godkendes.", "components.NotificationTypeSelector.usermediaapprovedDescription": "<PERSON><PERSON><PERSON> notificeret når dine medieforespørgsler godkendes.", "components.NotificationTypeSelector.usermediaavailableDescription": "Bliv notificeret når dine medieforespørgsler bliver tilgængelige.", "components.PermissionEdit.requestMoviesDescription": "<PERSON><PERSON> tilladelse til at indsende forespørgsler for ikke-4K film.", "components.RequestModal.requestCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for <strong>{title}</strong> er annulleret.", "components.RequestModal.requestSuccess": "<strong>{title}</strong> er blevet forespurgt!", "components.RequestModal.requestadmin": "<PERSON> forespø<PERSON><PERSON> vil blive godkendt automatisk.", "components.RequestModal.requestcancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for <strong>{title}</strong> er annulleret.", "components.RequestModal.requestedited": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for <strong>{title}</strong> er redigeret!", "components.RequestModal.requesterror": "Noget gik galt under indsendelsen af forespørgslen.", "components.RequestModal.requestfrom": "{username}'s fore<PERSON><PERSON><PERSON><PERSON> af<PERSON>er god<PERSON>.", "components.RequestModal.requestseasons": "Fores<PERSON>ørg om {seasonCount} {seasonCount, plural, one {<PERSON>æson} other {<PERSON><PERSON><PERSON>er}}", "components.RequestModal.season": "<PERSON><PERSON><PERSON>", "components.ResetPassword.passwordreset": "Nulstil <PERSON>", "components.ResetPassword.requestresetlinksuccessmessage": "Et link til nulstilling af dit kodeord vil blive sendt til den angivne email adresse såfremt der eksisterer en konto til den.", "components.ResetPassword.validationemailrequired": "Du skal angive en gyldig email adresse", "components.ResetPassword.validationpasswordmatch": "Kodeordene skal være ens", "components.ResetPassword.validationpasswordminchars": "Kodeordet er for kort; det skal være mindst 8 tegn", "components.ResetPassword.validationpasswordrequired": "Du skal angive et kodeord", "components.Search.search": "<PERSON><PERSON><PERSON>", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "<PERSON>n påkr<PERSON><PERSON> hvis du benytter en anden profil end <code>default</code>", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "LunaSea notifikationsindstillinger kunne ikke gemmes.", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "LunaSea notifikationsindstillinger er blevet gemt!", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "LunaSea testnotifikation kunne ikke afs<PERSON>.", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "Sender <PERSON><PERSON><PERSON> testnotifikation…", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "Du skal angive en gyldig URL", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "Webhook URL", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "<PERSON> bruger- el<PERSON> enhed<PERSON><PERSON><PERSON>e <LunaSeaLink>webhook URL for notifikationer</LunaSeaLink>", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "Adgangstoken", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "Opret en token fra dine <PushbulletSettingsLink>Kontoindstillinger</PushbulletSettingsLink>", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "Aktivér Agent", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "Pushbullet notifikationsindstillinger kunne ikke gemmes.", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Pushbullet notifikationsindstillinger er blevet gemt!", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "Sender Pushbullet testnotifikation…", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Pushbullet testnotifikation er afsendt!", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "Du skal angive en adgangstoken", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "Du skal vælge mindst én notifikationstype", "components.Settings.Notifications.NotificationsPushover.accessToken": "Applikations API-token", "components.Settings.Notifications.NotificationsPushover.agentenabled": "Aktivér Agent", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "Pushover notifikationsindstillinger kunne ikke gemmes.", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "Sender <PERSON><PERSON>over testnotifikation…", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Pushover testnotifikation er afsendt!", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "Webhook URL", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "Sender web push testnotifikation…", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "Web push testnotifikation kunne ikke sendes.", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "Web push notifikationsindstillinger kunne ikke gemmes.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "Web push testnotifikation er afsendt!", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "Web push notifikationdsindstillinger er blevet gemt!", "components.Settings.Notifications.NotificationsWebhook.authheader": "Autorisations-header", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "JSON indhold er blevet nulstillet!", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "Hjælp til Skabelonsvariabler", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "Webbook testnotifikation kunne ikke afsendes.", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "Sender webhook testnotifikation…", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "Webhook testnotifikation er afsendt!", "components.NotificationTypeSelector.usermediadeclinedDescription": "<PERSON><PERSON><PERSON> notificeret når dine medieforespørgsler afvises.", "components.RequestModal.autoapproval": "Automatisk Godkendelse", "components.ResetPassword.emailresetlink": "Send email med gendannelseslink", "components.NotificationTypeSelector.mediaAutoApproved": "Medie Automatisk Godkendt", "components.NotificationTypeSelector.mediarequestedDescription": "Send notifikationer når brugere indsender nye medieforespørgsler som kræver godkendelse.", "components.NotificationTypeSelector.notificationTypes": "Notifikationstyper", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "Aktivér Agent", "components.NotificationTypeSelector.mediaapproved": "<PERSON><PERSON>", "components.NotificationTypeSelector.issuecomment": "Problemkommentar", "components.NotificationTypeSelector.issuecommentDescription": "Send notifikationer når problemer får nye kommentarer.", "components.NotificationTypeSelector.issuecreated": "Problem Rapporteret", "components.NotificationTypeSelector.issuecreatedDescription": "Send notifikationer når problemer rapporteres.", "components.NotificationTypeSelector.issueresolved": "Problem Løst", "components.NotificationTypeSelector.issueresolvedDescription": "Send notifikationer når problemer er løst.", "components.NotificationTypeSelector.mediaapprovedDescription": "Send notifikationer når medieforespørgsler manuelt godkendes.", "components.NotificationTypeSelector.mediaavailableDescription": "Send notifikationer når medieforespø<PERSON>sler bliver tilgængelige.", "components.NotificationTypeSelector.mediadeclined": "<PERSON><PERSON>", "components.NotificationTypeSelector.usermediafailedDescription": "Bliv notificeret når medieforespørgsler ikke kunne tilføjes til Radarr eller Sonarr.", "components.NotificationTypeSelector.usermediarequestedDescription": "<PERSON><PERSON>v notificeret når andre brugere indsender nye medieforespørgsler som kræver godkendelse.", "components.PermissionEdit.autoapprove4kDescription": "Giv automatisk godkendelse for alle 4K medieforespørgsler.", "components.PermissionEdit.autoapproveDescription": "Giv automatisk godkendelse for alle ikke-4K medieforespørgsler.", "components.PermissionEdit.autoapproveSeriesDescription": "Giv automatisk godkendelse for ikke-4K serieforespørgsler.", "components.PermissionEdit.createissues": "<PERSON><PERSON><PERSON><PERSON>", "components.PermissionEdit.createissuesDescription": "<PERSON><PERSON> till<PERSON><PERSON>e til at rapportere medieproblemer.", "components.PermissionEdit.manageissues": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.PermissionEdit.manageissuesDescription": "<PERSON><PERSON> till<PERSON><PERSON>e til at administrere medieproblemer.", "components.RequestCard.failedretry": "Noget gik galt ved nyt forsøg på forespørgslen.", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {Ingen} other {<strong>#</strong>}} {type} {remaining, plural, one {forespørgsel} other {foresp<PERSON><PERSON><PERSON>}} tilbage", "components.RequestModal.SearchByNameModal.notvdbiddescription": "Vi kunne ikke automatisk matche din forespørgsel. Vælg venligst det korrekte match fra listen nedenfor.", "components.RequestModal.pendingrequest": "Afventende Forespørgsel", "components.Settings.Notifications.NotificationsPushover.userToken": "Bruger- <PERSON><PERSON>", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "Dit 30-tegns <UsersGroupsLink>bruger- eller gruppe-ID</UsersGroupsLink>", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "Du skal angive en gyldig bruger- eller gruppenøgle", "components.Settings.Notifications.NotificationsSlack.agentenabled": "Aktivér Agent", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "Slack notifikationsindstillinger kunne ikke gemmes.", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Slack notificationsindstillinger er blevet gemt!", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "Slack testnotifikation kunne ikke sendes.", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "Slack testnotifikation er afsendt!", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "Sender <PERSON><PERSON>ck testnotifikation…", "components.Settings.Notifications.NotificationsSlack.validationTypes": "Du skal vælge mindst én notifikationstype", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "Du skal angive et gyldigt URL", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "Du skal angive et gyldigt JSON indhold", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "Du skal vælge mindst én notifikationstype", "components.Settings.Notifications.allowselfsigned": "<PERSON><PERSON> selv-signerede certifikater", "components.Settings.Notifications.authPass": "SMTP Kodeord", "components.Settings.Notifications.botAvatarUrl": "Bot Avatar-URL", "components.Settings.Notifications.botUsername": "Bot Brugernavn", "components.Settings.Notifications.botUsernameTip": "<PERSON><PERSON> brugere og<PERSON> at kunne starte en chat med din bot og konfigurere deres egne notifikationer", "components.Settings.Notifications.encryptionTip": "I de fleste tilfælde bruger implicit TLC port 465 og STARTTLS port 587", "components.Settings.Notifications.pgpPrivateKey": "PGP Privat Nøgle", "components.Settings.Notifications.sendSilentlyTip": "Send notifikationer uden lyd", "components.Settings.Notifications.smtpHost": "SMTP Vært", "components.Settings.Notifications.senderName": "Afsendernavn", "components.Settings.Notifications.toastDiscordTestFailed": "Discord testnotifikation kunne ikke sendes.", "components.Settings.Notifications.validationSmtpHostRequired": "Du skal angive et gyldigt domænenavn eller en gyldig IP-adresse", "components.Settings.Notifications.validationPgpPrivateKey": "Du skal angive en gyldig PGP privatnøgle", "components.Settings.Notifications.validationUrl": "Du skal angive et gyldigt URL", "components.Settings.Notifications.validationTypes": "Du skal vælge mindst én notifikationstype", "components.Settings.RadarrModal.add": "Tilføj Server", "components.Settings.RadarrModal.apiKey": "API-nøgle", "components.Settings.RadarrModal.loadingprofiles": "Indlæser kvalitetsprofiler…", "components.Settings.RadarrModal.notagoptions": "Ingen tags.", "components.Settings.RadarrModal.testFirstRootFolders": "Test forbindelse for at indlæse rodmapper", "components.Settings.RadarrModal.testFirstQualityProfiles": "Test forbindelse for at indlæse kvalitetsprofiler", "components.Settings.RadarrModal.validationApplicationUrl": "Du skal angive en gyldig URL", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "URL'en må ikke afsluttes med en skråstreg", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "Base-URL'en må ikke afsluttes med en skråstreg", "components.Settings.RadarrModal.validationHostnameRequired": "Du skal angive et gyldigt domænenavn eller en gyldig IP-adresse", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "Base-URL'en må ikke starte med en skråstreg", "components.Settings.RadarrModal.validationPortRequired": "Du skal angive et gyldigt port-nummer", "components.Settings.RadarrModal.validationProfileRequired": "Du skal vælge en kvalitetsprofil", "components.Settings.SettingsAbout.Releases.latestversion": "Nyeste", "components.Settings.SettingsAbout.Releases.versionChangelog": "{version} <PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.Releases.releases": "Versioner", "components.Settings.SettingsAbout.totalmedia": "Total Antal Medier", "components.Settings.SettingsAbout.totalrequests": "Total Antal For<PERSON>ø<PERSON>sler", "components.Settings.SettingsAbout.supportoverseerr": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.timezone": "Tidszone", "components.Settings.SettingsAbout.uptodate": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.version": "Version", "components.Settings.SettingsJobsCache.cache": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.cacheDescription": "Jellyseerr cacher forespørgsler til eksterne API-slutpunkter for at optimere ydeevne og undgå udnødvendige API kald.", "components.Settings.SettingsJobsCache.cacheksize": "Nøglestørrelse", "components.Settings.SettingsJobsCache.cachemisses": "<PERSON><PERSON>", "components.Settings.RadarrModal.tags": "Tags", "components.Settings.Notifications.chatId": "Chat-ID", "components.Settings.Notifications.chatIdTip": "Start en chat med din bot, til<PERSON>ø<PERSON> <GetIdBotLink>@get_id_bot</GetIdBotLink> og send kommandoen <code>/my_id</code>", "components.Settings.Notifications.pgpPrivateKeyTip": "Signér krypterede emailbeskeder med <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.RadarrModal.loadingTags": "Indlæser tags…", "components.Settings.RadarrModal.syncEnabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "Du skal vælge minimum tilgængelighed", "components.Settings.RadarrModal.validationRootFolderRequired": "Du skal vælge en rodmappe", "components.Settings.SettingsAbout.betawarning": "Dette er BETA software. Funktioner kan være i stykker og/eller ustabile. Rapportér gerne eventuelle problemer på GitHub!", "components.Settings.RadarrModal.validationNameRequired": "Du skal angive et servernavn", "components.Settings.SettingsJobsCache.command": "Kommando", "components.Settings.SettingsJobsCache.download-sync-reset": "Nulstil Downloadsynkronisering", "components.Settings.Notifications.botApiTip": "<CreateBotLink>Opret en bot</CreateBotLink> til brug med Je<PERSON>rr", "components.Settings.Notifications.discordsettingsfailed": "Discord notifikationsindstillinger kunne ikke gemmes.", "components.Settings.Notifications.discordsettingssaved": "Discord notifikationsindstillinger er blevet gemt!", "components.Settings.Notifications.emailsender": "Afsenderadresse", "components.Settings.Notifications.emailsettingsfailed": "Email notifikationsindstillinger kunne ikke gemmes.", "components.Settings.Notifications.encryptionNone": "Ingen", "components.Settings.Notifications.encryptionOpportunisticTls": "Brug altid STARTTLS", "components.Settings.RadarrModal.selecttags": "Væ<PERSON>g tags", "components.Settings.RadarrModal.server4k": "4K Server", "components.Settings.RadarrModal.servername": "Servernavn", "components.Settings.RadarrModal.ssl": "Brug SSL", "components.Settings.RadarrModal.testFirstTags": "Test forbindelse for at indlæse tags", "components.Settings.RadarrModal.toastRadarrTestFailure": "<PERSON><PERSON> ikke forbinde til Radarr.", "components.Settings.RadarrModal.toastRadarrTestSuccess": "<PERSON>bin<PERSON><PERSON> til Radarr er oprettet!", "components.Settings.RadarrModal.validationApiKeyRequired": "Du skal angive en API-nøgle", "components.Settings.SettingsAbout.Releases.currentversion": "Nuværende", "components.Settings.SettingsAbout.Releases.releasedataMissing": "Versionsdata er i øjeblikket ikke tilgængeligt.", "components.Settings.SettingsAbout.Releases.viewchangelog": "<PERSON><PERSON>", "components.Settings.SettingsAbout.Releases.viewongithub": "<PERSON><PERSON> p<PERSON>", "components.Settings.SettingsAbout.about": "Om", "components.Settings.SettingsAbout.documentation": "Dokumentation", "components.Settings.SettingsAbout.gettingsupport": "<PERSON><PERSON>", "components.Settings.SettingsAbout.githubdiscussions": "GitHub Diskussioner", "components.Settings.SettingsAbout.helppaycoffee": "Støt med en kop kaffe", "components.Settings.SettingsAbout.overseerrinformation": "<PERSON><PERSON>", "components.Settings.SettingsAbout.preferredmethod": "Foretrukket", "components.Settings.SettingsAbout.runningDevelop": "<PERSON> <code>develop</code> udgaven af <PERSON><PERSON><PERSON>, som kun er anbefalet for dem der bidrager til udviklingen eller assisterer med testing af de nyeste funktioner.", "components.Settings.SettingsJobsCache.cacheflushed": "{cachename} cache er tømt.", "components.Settings.SettingsJobsCache.cachehits": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachekeys": "Total antal nøgler", "components.Settings.SettingsJobsCache.cachename": "Cache-navn", "components.Settings.SettingsJobsCache.cachevsize": "<PERSON><PERSON><PERSON>st<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.canceljob": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.download-sync": "Downloadsynkronisering", "components.Settings.SettingsJobsCache.editJobSchedule": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "Hvert {jobScheduleHours}. time", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "Hvert {jobScheduleMinutes}. minut", "components.Settings.Notifications.authUser": "SMTP Brugernavn", "components.Settings.Notifications.botAPI": "Bot Autorisationstoken", "components.Settings.Notifications.encryptionImplicitTls": "Brug implicit TLS", "components.Settings.Notifications.toastTelegramTestSuccess": "Telegram testnotifikation er blevet sendt!", "components.Settings.Notifications.validationChatIdRequired": "Du skal angive et gyldigt chat-ID", "components.Settings.Notifications.validationBotAPIRequired": "Du skal angive en bot autorisationstoken", "components.Settings.RadarrModal.enableSearch": "Aktivér Automatisk Søgning", "components.Settings.RadarrModal.selectQualityProfile": "Væ<PERSON>g kvalitetsprofil", "components.Settings.RadarrModal.selectRootFolder": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.outofdate": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.flushcache": "<PERSON><PERSON><PERSON>", "components.Settings.Notifications.emailsettingssaved": "Email notifikationsindstillinger er blevet gemt!", "components.Settings.Notifications.encryption": "Krypteringsmetode", "components.Settings.Notifications.encryptionDefault": "Brug STARTTLS hvis tilgængeligt", "components.Settings.Notifications.pgpPassword": "PGP Kodeord", "components.Settings.Notifications.pgpPasswordTip": "Signér krypterede emailbeskeder med <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.sendSilently": "<PERSON>", "components.Settings.Notifications.smtpPort": "SMTP Port", "components.Settings.Notifications.telegramsettingsfailed": "Telegram notifikationsindstillinger kunne ikke gemmes.", "components.Settings.Notifications.telegramsettingssaved": "Telegram notifikationsindstillinger er blevet gemt!", "components.Settings.Notifications.toastDiscordTestSending": "Sender Discord testnotifikation…", "components.Settings.Notifications.toastDiscordTestSuccess": "Discord testnotifikation er blevet sendt!", "components.Settings.Notifications.toastEmailTestFailed": "Email testnotifikation kunne ikke sendes.", "components.Settings.Notifications.toastEmailTestSending": "Sender email testnotifikation…", "components.Settings.Notifications.toastTelegramTestFailed": "Telegram testnotifikation kunne ikke sendes.", "components.Settings.Notifications.toastTelegramTestSending": "Sender Telegram testnotifikation…", "components.Settings.Notifications.toastEmailTestSuccess": "Email testnotifikation er blevet sendt!", "components.Settings.Notifications.validationEmail": "Du skal angive en gyldig emailadresse", "components.Settings.Notifications.validationPgpPassword": "Du skal angive et PGP kodeord", "components.Settings.Notifications.validationSmtpPortRequired": "Du skal angive et gyldigt port-nummer", "components.Settings.Notifications.webhookUrl": "Webhook URL", "components.Settings.Notifications.webhookUrlTip": "Opret en<DiscordWebhookLink>webhook integration</DiscordWebhookLink> til din server", "components.Settings.RadarrModal.baseUrl": "URL Kilde", "components.Settings.RadarrModal.create4kradarr": "Tilføj Ny 4K Radarr Server", "components.Settings.RadarrModal.createradarr": "Tilføj Ny Radarr Server", "components.Settings.RadarrModal.default4kserver": "Standard 4K Server", "components.Settings.RadarrModal.defaultserver": "Standard Server", "components.Settings.RadarrModal.edit4kradarr": "Redigér 4K Radarr Server", "components.Settings.RadarrModal.editradarr": "Redigér Radarr Server", "components.Settings.RadarrModal.externalUrl": "Ekstern URL", "components.Settings.RadarrModal.hostname": "<PERSON><PERSON><PERSON>avn eller IP-adresse", "components.Settings.RadarrModal.loadingrootfolders": "<PERSON><PERSON><PERSON><PERSON> rodmapper…", "components.Settings.RadarrModal.minimumAvailability": "Minimum Tilgængelighed", "components.Settings.RadarrModal.port": "Port", "components.Settings.RadarrModal.qualityprofile": "Kvalitetsprofil", "components.Settings.RadarrModal.rootfolder": "<PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.selectMinimumAvailability": "<PERSON><PERSON><PERSON><PERSON> <PERSON> tilgængelighed", "components.Settings.is4k": "4K", "components.Settings.SettingsJobsCache.unknownJob": "Ukendt Job", "components.Settings.SonarrModal.edit4ksonarr": "Redigér 4K Sonarr Server", "components.Settings.SonarrModal.editsonarr": "<PERSON>ig<PERSON>r <PERSON>r <PERSON>", "components.Settings.SonarrModal.notagoptions": "Ingen tags...", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "Base URL'en skal have en indledende skråstreg", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "URL'en må ikke afsluttes med en skråstreg", "components.Settings.activeProfile": "Aktiv Profil", "components.Settings.cancelscan": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.default": "Standard", "components.Settings.default4k": "Standard 4K", "components.Settings.deleteserverconfirm": "Er du sikker på du vil slette denne server?", "components.Settings.notificationsettings": "Notifikationsindstillinger", "components.Settings.plexlibraries": "Plex Biblioteker", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Seneste tilføjet fra Plex-skanning", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname} er annulleret.", "components.Settings.SettingsJobsCache.jobtype": "Type", "components.Settings.SettingsLogs.logs": "<PERSON>g<PERSON><PERSON>", "components.Settings.SonarrModal.syncEnabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.seasonfolders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "Noget gik galt i forsøget på at gemme jobbet.", "components.Settings.SettingsJobsCache.jobname": "Jobnavn", "components.Settings.SettingsJobsCache.jobs": "Jobs", "components.Settings.SettingsJobsCache.jobsDescription": "<PERSON><PERSON><PERSON>rr udfører visse vedligeholdelsesopgaver som regelmæssige planlagte jobs men de kan også blive eksekveret manuelt nedenfor. En manuel eksekvering af et job vil ikke ændre på dens tidsplan.", "components.Settings.SettingsLogs.logsDescription": "Du kan og<PERSON><PERSON> se disse logs direkte fra <code>stdout</code> eller i <code>{appDataPath}/logs/overseerr.log</code>.", "components.Settings.SettingsUsers.userSettingsDescription": "Konfigurér globale- og standardbrugerindstillinger.", "components.Settings.SonarrModal.animeTags": "Anime Tags", "components.Settings.SonarrModal.animelanguageprofile": "<PERSON><PERSON>rogprofil", "components.Settings.SonarrModal.apiKey": "API-nøgle", "components.Settings.SonarrModal.selectRootFolder": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.selecttags": "Væ<PERSON>g tags", "components.Settings.SonarrModal.server4k": "4K Server", "components.Settings.SonarrModal.servername": "Servernavn", "components.Settings.SonarrModal.ssl": "Benyt SSL", "components.Settings.SonarrModal.tags": "Tags", "components.Settings.SonarrModal.testFirstLanguageProfiles": "Test for<PERSON><PERSON><PERSON> for at indlæse sprogprofiler", "components.Settings.SonarrModal.testFirstTags": "Test for<PERSON><PERSON><PERSON> for at indlæse tags", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "Base URL'en må ikke slutte med en skråstreg", "components.Settings.SonarrModal.validationHostnameRequired": "Du skal angive et gyldigt domænenavn eller en gyldig IP adresse", "components.Settings.SonarrModal.validationLanguageProfileRequired": "Du skal vælge en sprogprofil", "components.Settings.SonarrModal.validationNameRequired": "Du skal angive et servernavn", "components.Settings.SonarrModal.validationPortRequired": "Du skal angive et gyldigt port-nummer", "components.Settings.SonarrModal.validationProfileRequired": "Du skal angive en kvalitetsprofil", "components.Settings.SonarrModal.validationRootFolderRequired": "Du skal angive en rodmappe", "components.Settings.address": "<PERSON><PERSON><PERSON>", "components.Settings.addsonarr": "Tilføj <PERSON>arr Server", "components.Settings.copied": "API-nøgle er kopieret til udklipsholder.", "components.Settings.currentlibrary": "Nuværende Bibliotek: {name}", "components.Settings.email": "Email", "components.Settings.enablessl": "Benyt SSL", "components.Settings.librariesRemaining": "Tilbageværende Biblioteker: {count}", "components.Settings.manualscanDescription": "Normalt vil dette kun eksekveres én gang i døgnet. <PERSON><PERSON><PERSON><PERSON> vil tjekke din Plex-server's senest tilføjede medier mere aggressivt. Hvis dette er din første gang du konfigurerer Plex vil en enkelt manuel biblioteksskanning anbefales!", "components.Settings.menuGeneralSettings": "Generelt", "components.Settings.menuJobs": "Jobs & Cache", "components.Settings.menuLogs": "<PERSON>g<PERSON><PERSON>", "components.Settings.menuNotifications": "Notifikationer", "components.Settings.menuPlexSettings": "Plex", "components.Settings.menuServices": "T<PERSON>nes<PERSON>", "components.Settings.menuUsers": "Brugere", "components.Settings.noDefault4kServer": "En 4K {serverType}server skal være markeret som standard for at aktivere muligheden for at brugere kan indsende 4K {mediaType}foresp<PERSON><PERSON><PERSON>.", "components.Settings.noDefaultNon4kServer": "<PERSON><PERSON> du kun har en enkelt {serverType}server for både ikke-4K og 4K medier (eller du kun downloader 4K medier), skal din {serverType}server <strong>IKKE</strong> sættes som en 4K server.", "components.Settings.noDefaultServer": "Mindst én {serverType}server skal markeres som standard for at {mediaType}forespørgsler kan afvikles.", "components.Settings.notificationAgentSettingsDescription": "Konfigurér og aktivér notifikationsagenter.", "components.Settings.notifications": "Notifikationer", "components.Settings.plex": "Plex", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "Jobbet er blevet redigeret!", "components.Settings.hostname": "Dom<PERSON>nenavn eller IP Adresse", "components.Settings.menuAbout": "Om", "components.Settings.SettingsLogs.label": "Label", "components.Settings.SettingsLogs.level": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.logDetails": "Loginformation", "components.Settings.SonarrModal.baseUrl": "URL Kilde", "components.Settings.SonarrModal.create4ksonarr": "Tilføj Ny 4K Sonarr Server", "components.Settings.SonarrModal.loadingprofiles": "Indlæser kvalitetsprofiler…", "components.Settings.manualscan": "<PERSON>", "components.Settings.mediaTypeMovie": "film", "components.Settings.mediaTypeSeries": "serier", "components.Settings.notrunning": "Stoppet", "components.Settings.SettingsJobsCache.jobsandcache": "Jobs & Cache", "components.Settings.SettingsUsers.newPlexLogin": "Aktiver Ny {mediaServerName} Login", "components.Settings.SettingsUsers.users": "Brugere", "components.Settings.addradarr": "Tilføj Radarr Server", "components.Settings.SettingsJobsCache.jobstarted": "{jobname} startet.", "components.Settings.SettingsJobsCache.nextexecution": "Næste udførelse", "components.Settings.SettingsJobsCache.plex-full-scan": "Fuld Plex Biblioteksskanning", "components.Settings.SettingsJobsCache.process": "Proces", "components.Settings.SettingsJobsCache.radarr-scan": "<PERSON><PERSON>-skanning", "components.Settings.SettingsJobsCache.runnow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.sonarr-scan": "Sonarr-skanning", "components.Settings.SettingsLogs.copiedLogMessage": "Logbesked er kopieret til udklipsholder.", "components.Settings.SettingsLogs.copyToClipboard": "<PERSON><PERSON><PERSON><PERSON> til Udklipsholder", "components.Settings.SettingsLogs.extraData": "Yderligere Data", "components.Settings.SettingsLogs.filterDebug": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.filterError": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.filterInfo": "Info", "components.Settings.SettingsLogs.filterWarn": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.message": "Besked", "components.Settings.SettingsLogs.pauseLogs": "Pause", "components.Settings.SettingsLogs.resumeLogs": "Fortsæt", "components.Settings.SettingsLogs.showall": "Vis Alle Logs", "components.Settings.SettingsLogs.time": "Tidsspunkt", "components.Settings.SettingsUsers.defaultPermissions": "Standard Rettigheder", "components.Settings.SettingsUsers.defaultPermissionsTip": "Initierende rettigheder som tildeles nye brugere", "components.Settings.SettingsUsers.localLogin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsUsers.localLoginTip": "<PERSON><PERSON> brugere at logge ind med deres email adresse og kodeord i stedet for Plex OAuth", "components.Settings.SettingsUsers.movieRequestLimitLabel": "Global Grænse For Filmforespørgsler", "components.Settings.SettingsUsers.newPlexLoginTip": "Tillad {mediaServerName}-bruge<PERSON> at logge ind uden først at være importeret", "components.Settings.SettingsUsers.toastSettingsFailure": "Noget gik galt i forsøget på at gemme indstillingerne.", "components.Settings.SettingsUsers.toastSettingsSuccess": "Brugerindstillingerne er blevet gemt!", "components.Settings.SettingsUsers.tvRequestLimitLabel": "Global Grænse For Serieforespørgsler", "components.Settings.SettingsUsers.userSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.add": "Tilføj Server", "components.Settings.SonarrModal.animequalityprofile": "<PERSON><PERSON>", "components.Settings.SonarrModal.animerootfolder": "<PERSON><PERSON>", "components.Settings.SonarrModal.createsonarr": "Tilføj Ny Sonarr Server", "components.Settings.SonarrModal.default4kserver": "Standard 4K Server", "components.Settings.SonarrModal.defaultserver": "Standard Server", "components.Settings.SonarrModal.enableSearch": "Aktivér Automatisk Søgning", "components.Settings.SonarrModal.externalUrl": "Ekstern URL", "components.Settings.SonarrModal.loadingrootfolders": "<PERSON><PERSON><PERSON><PERSON> rodmapper…", "components.Settings.SonarrModal.hostname": "Domænenavn eller IP adresse", "components.Settings.SonarrModal.languageprofile": "Sprogprofil", "components.Settings.SonarrModal.port": "Port", "components.Settings.SonarrModal.loadingTags": "Indlæser tags…", "components.Settings.SonarrModal.loadinglanguageprofiles": "Indlæser sprogprofiler…", "components.Settings.SonarrModal.qualityprofile": "Kvalitetsprofil", "components.Settings.SonarrModal.rootfolder": "<PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.selectLanguageProfile": "<PERSON><PERSON><PERSON><PERSON> sprogprofil", "components.Settings.SonarrModal.selectQualityProfile": "Væ<PERSON>g kvalitetsprofil", "components.Settings.SonarrModal.testFirstQualityProfiles": "Test for<PERSON><PERSON><PERSON> for at indlæse kvalitetsprofiler", "components.Settings.SonarrModal.testFirstRootFolders": "Test for<PERSON><PERSON><PERSON> for at indlæse rodmapper", "components.Settings.SonarrModal.toastSonarrTestFailure": "<PERSON>nne ikke forbinde til Sonarr.", "components.Settings.SonarrModal.toastSonarrTestSuccess": "Forbindelse til Sonarr er blevet etableret!", "components.Settings.SonarrModal.validationApiKeyRequired": "Du skal angive en API-nøgle", "components.Settings.SonarrModal.validationApplicationUrl": "Du skal angive en gyldig URL", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "Notifikationsindstillinger", "components.Settings.port": "Port", "components.Settings.serverpreset": "Server", "components.Settings.sonarrsettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.ssl": "SSL", "components.Settings.startscan": "Start Skanning", "components.Settings.webAppUrlTip": "Brugere dirigeres som alternativ til web-app'en på din server i stedet for den \"hostede\" web-app", "components.Settings.webAppUrl": "<WebAppLink>Web-App</WebAppLink>-URL", "components.Settings.webhook": "Webhook", "components.StatusBadge.status4k": "4K {status}", "components.Setup.welcome": "Velkommen til Jellyseerr", "components.TvDetails.anime": "Anime", "components.TvDetails.cast": "Roller", "components.TvDetails.episodeRuntimeMinutes": "{runtime} minutter", "components.TvDetails.originallanguage": "Originalsprog", "components.TvDetails.originaltitle": "Originaltitel", "components.UserList.importedfromplex": "{userCount, plural, one {# ny bruger} other {# nye brugere}} er blevet importeret fra Plex!", "components.UserList.localLoginDisabled": "Indstillingen <strong><PERSON><PERSON><PERSON><PERSON><PERSON></strong> er i øjeblikket deaktiveret.", "components.UserList.validationpasswordminchars": "Kodeordet er for kort; det skal mindst bestå af 8 tegn", "components.UserProfile.ProfileHeader.settings": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "Kontotype", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "Kaldenavn", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "Tilsidesæt Global Grænse", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "Filtrer indhold efter originalsprog", "components.UserProfile.UserSettings.UserGeneralSettings.role": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Notifikationsindstillingerne for Discord er blevet gemt!", "components.UserProfile.UserSettings.UserNotificationSettings.email": "Email", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "Notifikationsindstillingerne for email er blevet gemt!", "components.Settings.toastPlexConnecting": "Forsøger at forbinde itl Plex…", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "Bruger- <PERSON><PERSON>", "components.Setup.signinMessage": "Kom i gang ved at logge ind med din Plex-konto", "components.TvDetails.episodeRuntime": "Episode Spi<PERSON><PERSON>d", "components.TvDetails.network": "{networkCount, plural, one {Netværk} other {Netværk}}", "components.TvDetails.similar": "Lignende Serier", "components.UserList.user": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "Notifikationsindstillinger for Pushbullet kunne ikke gemmes.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "Notifikationsindstillinger for Pushover kunne ikke gemmes.", "components.Settings.plexlibrariesDescription": "Bibliotekerne Jellyseerr skanner for titler. Konfigurér og gem dine Plex-forbindelsesindstillinger og klik på knappen nedenfor hvis der ikke er vist nogle biblioteker.", "components.Settings.plexsettingsDescription": "Konfigurér indstillingerne for din Plex server. <PERSON><PERSON><PERSON><PERSON> skanner dine Plex-biblioteker for at afgøre tilgængeligheden af indhold.", "components.Settings.radarrsettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.TvDetails.nextAirDate": "<PERSON>æste Udsendelsesdato", "components.TvDetails.recommendations": "<PERSON><PERSON><PERSON><PERSON>", "components.TvDetails.seasons": "{seasonCount, plural, one {# <PERSON><PERSON><PERSON>} other {# <PERSON><PERSON><PERSON>er}}", "components.TvDetails.streamingproviders": "Kan I Øjeblikket Streames På", "components.TvDetails.viewfullcrew": "<PERSON><PERSON>", "components.TvDetails.watchtrailer": "<PERSON>", "components.UserList.accounttype": "Type", "components.UserList.admin": "Administrator", "components.UserList.autogeneratepassword": "Generér Automatisk Kodeord", "components.UserList.autogeneratepasswordTip": "Email et servergenereret kodeord til brugeren", "components.UserList.bulkedit": "Masseredigering", "components.UserList.create": "<PERSON><PERSON>", "components.UserList.created": "Tilsluttet", "components.UserList.createlocaluser": "<PERSON><PERSON> lokal bruger", "components.UserList.creating": "<PERSON><PERSON><PERSON>…", "components.UserList.importfromplexerror": "Noget gik galt under importeringen af brugere fra Plex.", "components.UserList.sortRequests": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "Grænse for Serieforespørgsler", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "Indstillingerne er blevet gemt!", "components.UserProfile.UserSettings.UserGeneralSettings.user": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "Bruger-ID", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "<FindDiscordIdLink>ID-nummeret</FindDiscordIdLink> for din brugerkonto", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "Notifikationsindstillingerne for Discord kunne ikke gemmes.", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "<PERSON><PERSON><PERSON><PERSON><PERSON> emailbeskeder ved hjælp af <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "Adgangstoken", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "Opret en token fra dine <PushbulletSettingsLink>k<PERSON><PERSON><PERSON><PERSON><PERSON></PushbulletSettingsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "Notifikationsindstillinger for Pushbullet er blevet gemt!", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "API-token for applikationen", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "<ApplicationRegistrationLink>Registrér en applikation</ApplicationRegistrationLink> for brug med {applicationTitle}", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "Dit 30-tegns <UsersGroupsLink>bruger- eller gruppe-ID</UsersGroupsLink>", "components.Settings.scan": "Synkronisér <PERSON>", "components.Settings.scanning": "Synkron<PERSON>er…", "components.Settings.serverLocal": "lokal", "components.Settings.serverRemote": "fjern", "components.Settings.serverSecure": "sikker", "components.Settings.serverpresetLoad": "Klik på knappen for at indlæse tilgængelige servere", "components.Settings.services": "T<PERSON>nes<PERSON>", "components.Settings.webpush": "Web Push", "components.UserList.userlist": "Brugerliste", "components.Settings.plexsettings": "Plex-inds<PERSON>linger", "components.TvDetails.TvCast.fullseriescast": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "Filtrér indhold efter regional tilgængelighed", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "Noget gik galt under opdateringen af indstillingerne.", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "Notifikationer", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "Notifikationsindstillingerne for email kunne ikke gemmes.", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "Offentlig PGP-nøgle", "components.TvDetails.showtype": "Serietype", "components.UserList.sortCreated": "Tilmeldingsdato", "components.UserList.deleteconfirm": "Er du sikker på at du vil slette denne bruger? Alle deres forespørgselsdata vil blive slettet permanent.", "components.Settings.serverpresetManualMessage": "<PERSON> konfiguration", "components.Settings.serverpresetRefreshing": "<PERSON><PERSON> servere…", "components.Settings.serviceSettingsDescription": "<PERSON>n<PERSON><PERSON><PERSON><PERSON> dine {serverType}server(e) nedenfor. Du kan forbinde til flere forskellige {serverType}servere men kun to af dem kan markeres som standard (én ikke-4K og én 4K). Administratorer kan ændre på serveren der bruges til at behandle nye forespørgsler inden godkendelse.", "components.Settings.settingUpPlexDescription": "For at sætte Plex op skal du enten indtaste oplysningerne manuelt eller vælge en server som hentes fra <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink>. Klik på knappen til højre for rullemenuen for at hente en liste af tilgængelige servere.", "components.Settings.toastPlexConnectingFailure": "<PERSON><PERSON> ikke forbinde til Plex.", "components.Settings.toastPlexConnectingSuccess": "Plex forbindelse er etableret!", "components.Settings.toastPlexRefresh": "Henter serverliste fra Plex…", "components.Settings.toastPlexRefreshFailure": "<PERSON><PERSON> ikke hente Plex-serverliste.", "components.Settings.toastPlexRefreshSuccess": "Plex-serverliste er blevet hentet!", "components.Settings.validationHostnameRequired": "Du skal angive et gyldigt domænenavn eller en gyldig IP adresse", "components.Settings.validationPortRequired": "Du skal angive et gyldigt port-nummer", "components.Setup.configureservices": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Setup.continue": "<PERSON><PERSON><PERSON>", "components.Setup.finish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Setup.finishing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>…", "components.Setup.setup": "Opsætning", "components.TvDetails.firstAirDate": "<PERSON><PERSON><PERSON><PERSON> Udsendelsesdato", "components.TvDetails.overview": "Oversigt", "components.TvDetails.TvCrew.fullseriescrew": "<PERSON><PERSON><PERSON>", "components.TvDetails.overviewunavailable": "Oversigt utilgængelig.", "components.UserList.localuser": "<PERSON><PERSON>", "components.UserList.deleteuser": "<PERSON><PERSON>", "components.UserList.nouserstoimport": "Ingen nye brugere som kan importeres fra Plex.", "components.UserList.edituser": "<PERSON><PERSON><PERSON><PERSON>", "components.UserList.email": "<PERSON><PERSON>", "components.UserList.importfrommediaserver": "Importér <PERSON> fra {mediaServerName}", "components.UserList.importfromplex": "Importér <PERSON> fra Plex", "components.UserList.owner": "<PERSON><PERSON>", "components.UserList.password": "Kodeord", "components.UserList.passwordinfodescription": "Konfigurér en applikations-URL og aktivér emailnotifikationer for at tillade automatisk kodeordsgenerering.", "components.UserList.plexuser": "<PERSON><PERSON>", "components.UserList.role": "<PERSON><PERSON>", "components.UserList.sortDisplayName": "Kaldenavn", "components.UserList.totalrequests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserList.usercreatedfailed": "Noget gik galt under oprettelsen af brugeren.", "components.UserList.usercreatedfailedexisting": "Den angivne email adresse er allerede i brug af en anden bruger.", "components.UserProfile.ProfileHeader.profile": "Vis Profil", "components.UserProfile.ProfileHeader.userid": "Bruger-ID: {userid}", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "Administrator", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "Grænsefladesprog", "components.UserProfile.UserSettings.UserGeneralSettings.general": "Generelt", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "Standard ({language})", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "Grænse for Filmforespørgsler", "components.UserList.usercreatedsuccess": "Brugeren er blevet oprettet!", "components.UserList.userdeleted": "Brugeren er blevet slettet!", "components.UserList.userdeleteerror": "Noget gik galt under sletningen af brugeren.", "components.UserList.userfail": "Noget gik galt under opdateringen af brugertilladelser.", "components.UserList.users": "Brugere", "components.UserList.userssaved": "Brugertilladelser er blevet gemt!", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "Udforsk Sprog", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "Plex-bruger", "components.UserList.validationEmail": "Du skal angive en gyldig email adresse", "components.UserProfile.ProfileHeader.joindate": "Tilsluttet {joindate}", "components.UserProfile.UserSettings.UserGeneralSettings.region": "Udforsk region", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "Notifikationsindstillinger for Pushover er blevet gemt!", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "Notifikationsindstillingerne for Telegram er blevet gemt!", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "Web Push", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "Chat-ID", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink>Start en chat</TelegramBotLink>, tilføj <GetIdBotLink>@get_id_bot</GetIdBotLink> og benyt <code>/my_id</code> kommandoen", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "Nuværende Kodeord", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "Notifikationsindstillingerne for Telegram kunne ikke gemmes.", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "Du skal angive en adgangstoken", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "Du skal angive en gyldig applikationstoken", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "Du skal angive en gyldig bruger- eller gruppenøgle", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "Du skal angive et gyldigt chat-ID", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "Du skal angive et bruger-ID", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "Du skal angive en gyldig offentlig PGP-nøgle", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "Notifikationsindstillingerne for web push kunne ikke gemmes.", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "Notifikationsindstillingerne for web push er blevet gemt!", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "Bekræft Kodeord", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "Denne brugerkonto har i øjeblikket ikke et kodeord. Konfigurér et kodeord nedenfor så denne konto kan logge ind som en \"lokal bruger.\"", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "Din konto har i øjeblikket ikke et kodeord. Konfigurér et kodeord nedenfor så du kan logge ind som \"lokal bruger\" med din email adresse.", "i18n.next": "<PERSON><PERSON><PERSON>", "pages.oops": "Ups", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "Send notifikationer uden lyd", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "Kodeordet er blevet gemt!", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "Du skal bekræfte det nye kodeord", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "Du skal angive et nyt kodeord", "components.UserProfile.UserSettings.UserPermissions.permissions": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.menuGeneralSettings": "Generelt", "components.UserProfile.UserSettings.menuNotifications": "Notifikationer", "components.UserProfile.movierequests": "Film<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.limit": "{remaining} ud af {limit}", "i18n.available": "Tilgængelig", "i18n.experimental": "Eksperimentel", "i18n.delimitedlist": "{a}, {b}", "i18n.edit": "<PERSON>ig<PERSON><PERSON>", "i18n.failed": "<PERSON><PERSON><PERSON>", "i18n.noresults": "Ingen resultater.", "i18n.notrequested": "<PERSON><PERSON><PERSON>", "i18n.settings": "<PERSON><PERSON><PERSON><PERSON>", "i18n.usersettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.tvshows": "Serier", "i18n.unavailable": "Utilgængelig", "i18n.view": "Vis", "components.UserProfile.unlimited": "Ubegrænset", "components.UserProfile.UserSettings.menuChangePass": "Kodeord", "i18n.request": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.processing": "<PERSON><PERSON><PERSON>", "i18n.request4k": "Forespørg i 4K", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "<PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "Du har ikke tilladelse til at ænd<PERSON> denne bruger's kodeord.", "components.UserProfile.UserSettings.UserPasswordChange.password": "Kodeord", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "Noget gik galt, kodeordet kunne ikke gemmes.", "components.UserProfile.UserSettings.menuPermissions": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.unauthorizedDescription": "Du har ikke tilladelse til at ænd<PERSON> denne bruger's indstillinger.", "components.UserProfile.pastdays": "{type} (seneste {days} dage)", "components.UserProfile.recentrequests": "<PERSON><PERSON>", "components.UserProfile.requestsperdays": "{limit} tilbage", "components.UserProfile.totalrequests": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "Noget gik galt, kodeordet kunne ikke gemmes. Havde du indtastet dit nuværende kodeord korrekt?", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "Kodeordene skal være ens", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "Du skal angive dit nuværende kodeord", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "Kodeordet er for kort; det skal bestå af mindst 8 tegn", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "Noget gik galt da indstillingerne skulle gemmes.", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "Tilladelserne er blevet gemt!", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "Du kan ikke ændre dine egne tilladelser.", "components.UserProfile.seriesrequest": "Serie<PERSON>res<PERSON>ø<PERSON>sler", "i18n.declined": "<PERSON><PERSON><PERSON>", "i18n.delete": "Slet", "i18n.deleting": "Sletter…", "i18n.loading": "<PERSON><PERSON><PERSON><PERSON>…", "i18n.movie": "Film", "i18n.movies": "Film", "i18n.open": "<PERSON><PERSON>", "i18n.partiallyavailable": "<PERSON><PERSON>", "i18n.pending": "<PERSON><PERSON><PERSON><PERSON>", "i18n.requesting": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "i18n.resolved": "<PERSON><PERSON><PERSON>", "i18n.resultsperpage": "Vis {pageSize} resultater pr. side", "i18n.retry": "Forsøg igen", "i18n.retrying": "<PERSON><PERSON><PERSON><PERSON> igen…", "i18n.save": "<PERSON><PERSON>", "i18n.previous": "<PERSON><PERSON><PERSON>", "i18n.approve": "Godkend", "i18n.approved": "Godkendt", "i18n.areyousure": "<PERSON>r du sikker?", "i18n.back": "Tilbage", "i18n.cancel": "<PERSON><PERSON><PERSON><PERSON>", "i18n.canceling": "<PERSON><PERSON><PERSON>…", "i18n.close": "Luk", "i18n.decline": "<PERSON><PERSON><PERSON>", "i18n.showingresults": "Viser <strong>{from}</strong> til <strong>{to}</strong> af <strong>{total}</strong> resultater", "i18n.status": "Status", "i18n.testing": "Tester…", "i18n.tvshow": "Serie", "pages.errormessagewithcode": "{statusCode} - {error}", "pages.internalservererror": "Intern serverfejl", "pages.pagenotfound": "<PERSON><PERSON>", "pages.returnHome": "<PERSON><PERSON> tilbage til startsiden", "i18n.all": "Alle", "i18n.advanced": "<PERSON><PERSON><PERSON>", "i18n.requested": "Forespurgt", "pages.serviceunavailable": "Service Utilgængelig", "pages.somethingwentwrong": "Noget Gik Galt", "i18n.saving": "<PERSON><PERSON><PERSON>…", "i18n.test": "Test", "components.RequestModal.requestseasons4k": "For<PERSON><PERSON><PERSON><PERSON> efter {seasonCount} {seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}} i 4K", "components.IssueDetails.commentplaceholder": "Til<PERSON>øj en kommentar…", "components.RequestModal.approve": "Godkend Forespørgsel", "components.RequestModal.requestApproved": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for <strong>{title}</strong> er godkendt!", "components.RequestModal.requestmovies4k": "<PERSON><PERSON><PERSON><PERSON><PERSON> efter {count} Film i 4K", "components.RequestModal.selectmovies": "Vælg Film", "components.MovieDetails.productioncountries": "Produktions{countryCount, plural, one {land} other {lande}}", "components.TvDetails.productioncountries": "Produktions{countryCount, plural, one {land} other {lande}}", "components.RequestModal.requestmovies": "<PERSON><PERSON><PERSON><PERSON><PERSON> efter {count} Film", "components.Settings.Notifications.NotificationsGotify.agentenabled": "Aktivér Agent", "components.Settings.Notifications.NotificationsGotify.url": "Server URL", "components.ManageSlideOver.markallseasonsavailable": "<PERSON><PERSON><PERSON>er som Tilgængelige", "components.ManageSlideOver.pastdays": "Forrige {days, number} Dage", "components.ManageSlideOver.opentautulli": "Åben i Tautulli", "components.ManageSlideOver.playedby": "Afspillet Af", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Gotify notifikationsindstillinger er blevet gemt!", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "Gotify testnotifikation kunne ikke afsendes.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Sender Gotify testnotifikation…", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Gotify testnotifikation er afsendt!", "components.Settings.Notifications.NotificationsGotify.token": "Applikations Token", "components.Settings.Notifications.NotificationsGotify.validationTypes": "Du skal vælge mindst én notifikationstype", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "Der skal angives en valid URL", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "URL'en må ikke afsluttes med en skråstreg", "components.ManageSlideOver.markallseasons4kavailable": "<PERSON><PERSON><PERSON> Alle Sæsoner som Tilgængelige i 4K", "components.ManageSlideOver.manageModalAdvanced": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "Der skal angives en applikations token", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "Gemning notifikationsindstillinger kunne ikke gemmes.", "components.Settings.validationUrlTrailingSlash": "URL'en må ikke afsluttes med en skråstreg", "components.ManageSlideOver.manageModalMedia": "Me<PERSON>", "components.Settings.RadarrModal.released": "Udgivet", "components.Settings.tautulliApiKey": "API nøgle", "components.Settings.tautulliSettings": "<PERSON><PERSON><PERSON>", "components.Settings.tautulliSettingsDescription": "Alternativt kan du konfigurere indstillingerne for din Tautulli server. <PERSON><PERSON><PERSON><PERSON> henter visningshistorikken for dit Plex medie fra Tautulli.", "components.Settings.toastTautulliSettingsFailure": "<PERSON><PERSON> gik galt, da du gemte <PERSON> indstillinger.", "components.Settings.validationApiKey": "Du skal angive en API-nøgle", "components.UserProfile.recentlywatched": "For nylig set", "components.Settings.toastTautulliSettingsSuccess": "<PERSON><PERSON>lli indstillingerne blev gemt succesfuldt!", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "Discord-bruger-id", "components.Settings.validationUrl": "Du skal angive en gyldig URL", "components.Settings.validationUrlBaseLeadingSlash": "URL-basen skal have en foranstillet skråstreg", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "Det <FindDiscordIdLink> flercifrede ID-nummer</FindDiscordIdLink>, der er knyttet til din Discord-brugerkonto", "components.Settings.validationUrlBaseTrailingSlash": "URL-basen må ikke ende i en efterfølgende skråstreg", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "Du skal oplyse et gyldigt <PERSON>-bruger-id", "i18n.import": "Importere", "components.ManageSlideOver.manageModalMedia4k": "4K medier", "components.Settings.externalUrl": "Ekstern URL", "components.Settings.RadarrModal.announced": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.inCinemas": "I biograferne", "components.Settings.urlBase": "URL base", "components.UserList.newplexsigninenabled": "Indstillingen <strong>Aktiver nyt Plex-login</strong> er i øjeblikket aktiveret. Plex-brugere med biblioteksadgang behøver ikke at blive importeret for at kunne logge ind.", "components.Settings.SettingsAbout.appDataPath": "Data mappe", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "Kanal tag", "i18n.importing": "Importerer…", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "Aktuel frekvens", "components.Settings.SettingsJobsCache.image-cache-cleanup": "Oprydning af billedcache", "components.Settings.SettingsJobsCache.imagecache": "Billedcache", "components.Settings.experimentalTooltip": "Aktivering af denne indstilling kan resultere i uventet programadfærd", "components.TvDetails.Season.noepisodes": "Episodelisten er ikke tilgængelig.", "components.RequestBlock.edit": "<PERSON><PERSON> foresp<PERSON><PERSON><PERSON>", "components.TitleCard.tmdbid": "TMDB ID", "components.Discover.DiscoverWatchlist.discoverwatchlist": "<PERSON> Watchlist", "components.Layout.UserDropdown.requests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.physicalrelease": "Fysisk udgivelse", "components.PermissionEdit.autorequestSeries": "Forespørg automatisk serier", "components.RequestCard.cancelrequest": "<PERSON><PERSON><PERSON> fore<PERSON><PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.tvdbid": "TheTVDB ID", "components.RequestModal.requestcollectiontitle": "<PERSON><PERSON><PERSON> samling", "components.Settings.SettingsJobsCache.imagecacheDescription": "N<PERSON><PERSON> det er aktiveret i inds<PERSON><PERSON>, vil <PERSON><PERSON> proxy og cache billeder fra forudkonfigurerede eksterne kilder. Cachelagrede billeder gemmes i din konfigurationsmappe. Du kan finde filerne i <code>{appDataPath}/cache/images</code>.", "components.Settings.SettingsJobsCache.imagecachecount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.viewdetails": "<PERSON> <PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.imagecachesize": "Samlet cachestørrelse", "components.StatusChecker.appUpdated": "{applicationTitle} Opdateret", "components.StatusChecker.appUpdatedDescription": "Klik på knappen nedenfor for at genindlæse applikationen.", "components.StatusChecker.reloadApp": "Genindlæs {applicationTitle}", "components.TitleCard.cleardata": "Ryd data", "components.TitleCard.tvdbid": "TheTVDB ID", "components.RequestModal.requestmovie4ktitle": "Forespørg film i 4K", "components.Settings.restartrequiredTooltip": "<PERSON><PERSON><PERSON><PERSON> skal genstartes for at ændringer til denne indstilling kan træde i kraft", "components.StatusChecker.restartRequiredDescription": "Genstart venligst serveren for at anvende de opdaterede indstillinger.", "components.UserProfile.emptywatchlist": "<PERSON><PERSON> føjet til din <PlexWatchlistSupportLink>Plex Watchlist</PlexWatchlistSupportLink> vises her.", "components.MovieDetails.theatricalrelease": "Biografpremiere", "components.PermissionEdit.viewrecent": "<PERSON><PERSON> nyl<PERSON>t til<PERSON>ø<PERSON>", "components.PermissionEdit.viewrecentDescription": "<PERSON><PERSON> till<PERSON><PERSON><PERSON> til at se listen over nyl<PERSON>t tilføjede medier.", "components.RequestCard.tmdbid": "TMDB ID", "components.RequestCard.tvdbid": "TVDB ID", "components.StatusChecker.restartRequired": "Server genstar<PERSON> p<PERSON>r<PERSON>", "components.RequestList.RequestItem.tmdbid": "TMDB ID", "components.MovieDetails.digitalrelease": "Digital udgivelse", "components.PermissionEdit.autorequest": "Automatisk forespørgsel", "components.PermissionEdit.autorequestMovies": "Forespørg film automatisk", "components.Settings.deleteServer": "Slet {serverType} server", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "Forespørg automatisk film", "components.ManageSlideOver.alltime": "<PERSON><PERSON> tiden", "components.Settings.Notifications.enableMentions": "Aktiver omtaler", "components.TvDetails.manageseries": "Administrer serier", "components.MovieDetails.reportissue": "Rapporter et problem", "components.MovieDetails.managemovie": "Administrer film", "components.PermissionEdit.autorequestMoviesDescription": "Giv tilladelse til automatisk at indsende forespørgsler om ikke-4K-film via Plex Watchlist.", "components.PermissionEdit.autorequestSeriesDescription": "Giv tilladelse til automatisk at indsende forespørgsler om ikke-4K-serier via Plex Watchlist.", "components.TitleCard.mediaerror": "{mediaType} Ikke fundet", "components.Discover.DiscoverWatchlist.watchlist": "Plex Watchlist", "components.Discover.plexwatchlist": "<PERSON> Watchlist", "components.NotificationTypeSelector.mediaautorequested": "Forespørgsel indsendt automatisk", "components.NotificationTypeSelector.mediaautorequestedDescription": "<PERSON><PERSON> besked, når der automatisk indsendes nye medieanmodninger for emner på din Plex Watchlist.", "components.PermissionEdit.viewwatchlists": "Se Plex Watchlists", "components.Settings.advancedTooltip": "Forkert konfiguration af denne indstilling kan resultere i ødelagt funktionalitet", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "Forespørg automatisk serier på din<PlexWatchlistSupportLink>Plex Watchlist</PlexWatchlistSupportLink>", "components.UserProfile.plexwatchlist": "Plex Watchlist", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Film forespørgsler", "components.AirDateBadge.airedrelative": "Sendt {relativeTime}", "components.AirDateBadge.airsrelative": "Sendes {relativeTime}", "components.MovieDetails.rtaudiencescore": "Rotten Tomatoes publikumsscore", "components.PermissionEdit.viewwatchlistsDescription": "<PERSON><PERSON> till<PERSON><PERSON><PERSON> til at se andre brugeres Plex Watchlists.", "components.RequestBlock.approve": "<PERSON><PERSON><PERSON> fore<PERSON><PERSON><PERSON><PERSON>", "components.RequestBlock.decline": "<PERSON><PERSON><PERSON> fore<PERSON>", "components.RequestBlock.lastmodifiedby": "Sidst ændret af", "components.RequestBlock.requestdate": "<PERSON><PERSON><PERSON><PERSON><PERSON> dato", "components.RequestCard.declinerequest": "<PERSON><PERSON><PERSON> fore<PERSON>", "components.RequestCard.editrequest": "<PERSON><PERSON> foresp<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "Synkronisering af Plex Watchlist", "components.StatusBadge.managemedia": "Administrer {mediaType}", "components.StatusBadge.openinarr": "Åbn i {arr}", "components.StatusBadge.playonplex": "Spil i Plex", "components.TvDetails.Season.somethingwentwrong": "Noget gik galt under hentning af sæsondata.", "components.TvDetails.reportissue": "Rapporter et problem", "components.TvDetails.episodeCount": "{episodeCount, plural, one {# episode} other {# episoder}}", "components.TvDetails.rtaudiencescore": "Rotten Tomatoes Publikumsresultat", "components.TvDetails.rtcriticsscore": "Rotten Tomatoes Tomatometer", "components.RequestBlock.languageprofile": "Sprogprofil", "i18n.restartRequired": "Genstart påkrævet", "components.Discover.emptywatchlist": "<PERSON><PERSON>, der er tilføjet til din <PlexWatchlistSupportLink>Plex Watchlist</PlexWatchlistSupportLink>, vises her.", "components.MovieDetails.rtcriticsscore": "Rotten Tomatoes Tomatometer", "components.MovieDetails.tmdbuserscore": "TMDB brugerscore", "components.RequestBlock.delete": "Slet forespørgsel", "components.RequestBlock.requestedby": "Forespurgt af", "components.RequestCard.approverequest": "<PERSON><PERSON><PERSON> fore<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.SearchByNameModal.nomatches": "Vi var ikke i stand til at finde et match for denne serie.", "components.RequestModal.requestcollection4ktitle": "Anmod om samling i 4K", "components.RequestModal.requestmovietitle": "<PERSON>es<PERSON>ørg film", "components.RequestModal.requestseries4ktitle": "Forespørg serie i 4K", "components.RequestModal.requestseriestitle": "Forespørg serie", "components.TvDetails.seasonnumber": "<PERSON><PERSON><PERSON> {seasonNumber}", "components.TvDetails.seasonstitle": "<PERSON><PERSON><PERSON><PERSON>", "components.TvDetails.status4k": "4K {status}", "components.TvDetails.tmdbuserscore": "TMDB brugerscore", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "Serie forespørgsler", "components.PermissionEdit.autorequestDescription": "<PERSON><PERSON> tilladelse til automatisk at indsende forespørgsler om ikke-4K-medier via Plex Watchlist.", "components.ManageSlideOver.plays": "<strong>{playCount, antal}</strong> {playCount, plural, one {spil} other{spiller}}", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "Forespørg automatisk film på din <PlexWatchlistSupportLink>Plex Watchlist</PlexWatchlistSupportLink>", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "Forespørg automatisk serier", "components.Discover.studios": "<PERSON><PERSON><PERSON>", "components.Discover.tmdbmoviegenre": "TMDB filmgenre", "components.Settings.SettingsMain.apikey": "API nøgle", "components.Settings.SettingsMain.applicationTitle": "Applikationstitel", "components.Settings.SettingsMain.applicationurl": "Applikations-URL", "components.Settings.SettingsMain.cacheImages": "Aktiver billedcaching", "components.Settings.SettingsMain.locale": "Grænsefladesprog", "components.Settings.SettingsMain.originallanguageTip": "Filtrer indhold efter originalsprog", "components.Settings.SettingsMain.originallanguage": "Discover sprog", "components.Discover.CreateSlider.searchStudios": "<PERSON><PERSON><PERSON> studier…", "components.Discover.CreateSlider.slidernameplaceholder": "Navn på skyder", "components.Discover.DiscoverMovieKeyword.keywordMovies": "{keywordTitle} Film", "components.Discover.DiscoverSliderEdit.deletefail": "<PERSON>nne ikke slette skyderen.", "components.Discover.DiscoverSliderEdit.deletesuccess": "Skyderen blev slettet.", "components.Discover.DiscoverSliderEdit.enable": "Slå Synlighed til/fra", "components.Discover.DiscoverSliderEdit.remove": "<PERSON><PERSON><PERSON>", "components.Discover.DiscoverTvKeyword.keywordSeries": "{keywordTitle} Serie", "components.Discover.CreateSlider.needresults": "Du skal have mindst 1 resultat.", "components.Discover.CreateSlider.nooptions": "Ingen resultater.", "components.Discover.CreateSlider.providetmdbgenreid": "Angiv et TMDB-genre-ID", "components.Discover.CreateSlider.providetmdbkeywordid": "Angiv et TMDB-søgeords-id", "components.Discover.CreateSlider.providetmdbnetwork": "Angiv TMDB-netværks-id", "components.Discover.CreateSlider.providetmdbsearch": "Angiv en søgeforespørgsel", "components.Discover.CreateSlider.providetmdbstudio": "Angiv TMDB Studio ID", "components.Discover.CreateSlider.searchGenres": "<PERSON><PERSON><PERSON>r…", "components.Discover.CreateSlider.searchKeywords": "<PERSON><PERSON><PERSON> n<PERSON>…", "components.Discover.CreateSlider.starttyping": "<PERSON><PERSON><PERSON> at skrive for at søge.", "components.Discover.CreateSlider.validationTitlerequired": "Du skal angive en titel.", "components.Discover.CreateSlider.addSlider": "<PERSON><PERSON><PERSON><PERSON><PERSON> skyder", "components.Discover.CreateSlider.addcustomslider": "<PERSON>ret brugerdefineret skyder", "components.Discover.CreateSlider.addfail": "<PERSON>nne ikke oprette ny skyder.", "components.Discover.CreateSlider.validationDatarequired": "Du skal angive en dataværdi.", "components.Discover.createnewslider": "<PERSON><PERSON> ny skyder", "components.Discover.customizediscover": "Tilpas Discover", "components.Discover.PlexWatchlistSlider.emptywatchlist": "<PERSON><PERSON> føjet til din <PlexWatchlistSupportLink>Plex Watchlist</PlexWatchlistSupportLink> vises her.", "components.Discover.resetwarning": "Nulstil alle skydere til standard. Dette vil også slette eventuelle brugerdefinerede skydere!", "components.Settings.SettingsMain.cacheImagesTip": "<PERSON><PERSON> e<PERSON><PERSON> (anvender en betydelig mængde diskplads)", "components.Settings.SettingsMain.generalsettings": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsMain.toastApiKeySuccess": "Ny API-nøgle er blevet genereret!", "components.Settings.SettingsMain.toastSettingsFailure": "Noget gik galt da indstillingerne skulle gemmes.", "components.Settings.SettingsMain.toastSettingsSuccess": "Indstillingerne er blevet gemt!", "components.DownloadBlock.formattedTitle": "{title}: <PERSON><PERSON><PERSON> {seasonNumber} Episode {episodeNumber}", "components.RequestCard.unknowntitle": "Ukendt titel", "components.RequestList.RequestItem.unknowntitle": "Ukendt titel", "components.StatusBadge.seasonepisodenumber": "S{seasonNumber}E{episodeNumber}", "components.Discover.CreateSlider.addsuccess": "Oprettede en ny skyder og gemte indstillingerne for tilpasninger.", "components.Discover.CreateSlider.editSlider": "Rediger skyder", "components.Discover.CreateSlider.editfail": "Kunne ikke redigere skyderen.", "components.Discover.CreateSlider.editsuccess": "Redigerede skyder og gemte Discover-tilpasningsindstillinger.", "components.Discover.PlexWatchlistSlider.plexwatchlist": "<PERSON> Watchlist", "components.Discover.RecentlyAddedSlider.recentlyAdded": "Tilføjet for nyligt", "components.Discover.moviegenres": "Filmgenrer", "components.Discover.networks": "Netværk", "components.Discover.resetfailed": "Noget gik galt med at nulstille indstillingerne for Discover-tilpasning.", "components.Discover.resetsuccess": "Nulstilling af discover tilpasning lykkedes.", "components.Discover.resettodefault": "Nulstil til standard", "components.Discover.stopediting": "Stop redigering", "components.Discover.tmdbmoviekeyword": "TMDB film nøgleord", "components.Discover.tmdbnetwork": "TMDB netværk", "components.Discover.tmdbsearch": "TMDB-søgning", "components.Discover.tmdbstudio": "TMDB-studiet", "components.Discover.tmdbtvgenre": "TMDB-serie Genre", "components.Discover.tmdbtvkeyword": "TMDB-se<PERSON><PERSON> n<PERSON>", "components.Discover.tvgenres": "Seriegenrer", "components.Discover.updatefailed": "Noget gik galt med at nulstille indstillingerne for Discover-tilpasning.", "components.Discover.updatesuccess": "Opdaterede Discover-tilpasningsindstillinger.", "components.Settings.SettingsMain.general": "Generelt", "components.Settings.SettingsMain.generalsettingsDescription": "Konfigurér global- og standardindstillinger for Jellyseerr.", "components.Settings.SettingsMain.hideAvailable": "Skjul Tilgængelige Medier", "components.Settings.SettingsMain.partialRequestsEnabled": "<PERSON><PERSON> delvise se<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsMain.toastApiKeyFailure": "Noget gik galt under genereringen af en nye API-nøgle.", "components.Settings.SettingsMain.validationApplicationTitle": "Du skal angive en applikationstitel", "components.Settings.SettingsMain.validationApplicationUrl": "Du skal angive en gyldig URL", "components.Settings.SettingsMain.validationApplicationUrlTrailingSlash": "URL'en må ikke afsluttes med en skråstreg", "components.Discover.DiscoverMovies.sortPopularityAsc": "Popularitet stigende", "components.Discover.DiscoverMovies.sortPopularityDesc": "Popular<PERSON>t fald<PERSON>e", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "Udgivelsesdato stigende", "components.Discover.DiscoverMovies.discovermovies": "Film", "components.Discover.DiscoverMovies.activefilters": "{count, flertal, en {# Active Filter} andre {# Active Filters}}", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "Udgivelses<PERSON><PERSON> faldende", "components.Discover.DiscoverMovies.sortTitleAsc": "Titel (A-Z) stigende", "components.Discover.DiscoverMovies.sortTitleDesc": "Titel (Z-A) faldende", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "TMDB-rating stigende", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "TMDB-rating faldende", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {# Active Filter} andre{# Active Filters}}", "components.Discover.DiscoverTv.discovertv": "Serier", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "Første udsendelsesdato stigende", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "F<PERSON>rste udsendelsesdato faldende", "components.Discover.DiscoverTv.sortTitleAsc": "Titel (A-Z) stigende", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "TMDB-rating stigende", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "TMDB-rating faldende", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {# Active Filter} andre{# Active Filters}}", "components.Discover.FilterSlideover.clearfilters": "Ryd aktive filtre", "components.Discover.FilterSlideover.filters": "Filtre", "components.Discover.FilterSlideover.from": "<PERSON>a", "components.Discover.FilterSlideover.genres": "<PERSON><PERSON>", "components.Discover.FilterSlideover.keywords": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.originalLanguage": "Originalsprog", "components.Discover.FilterSlideover.ratingText": "Bed<PERSON>mmelser mellem {minValue} og {maxValue}", "components.Discover.FilterSlideover.releaseDate": "Udgivelsesdato", "components.Discover.FilterSlideover.runtime": "S<PERSON>lletid", "components.Discover.FilterSlideover.runtimeText": "{minValue}-{maxValue} spilletidsminutter", "components.Discover.FilterSlideover.studio": "<PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.tmdbuserscore": "TMDB brugerscore", "components.Layout.Sidebar.browsemovies": "Film", "components.Layout.Sidebar.browsetv": "Serier", "components.Selector.nooptions": "Ingen resultater.", "components.Selector.searchGenres": "<PERSON><PERSON><PERSON><PERSON> genrer…", "components.Selector.searchKeywords": "<PERSON><PERSON><PERSON> n<PERSON>…", "components.Selector.starttyping": "<PERSON><PERSON><PERSON> at skrive for at søge.", "components.Discover.DiscoverTv.sortPopularityDesc": "Popular<PERSON>t fald<PERSON>e", "components.Discover.DiscoverTv.sortPopularityAsc": "Popularitet stigende", "components.Discover.DiscoverTv.sortTitleDesc": "Titel (Z-A) faldende", "components.Discover.FilterSlideover.firstAirDate": "Første udsendelsesdato", "components.Selector.searchStudios": "<PERSON><PERSON><PERSON> efter studier…", "components.Discover.FilterSlideover.to": "Til", "components.Discover.FilterSlideover.streamingservices": "Streamingtjenester", "components.Selector.showless": "Vis mindre", "components.Selector.showmore": "Vis mere", "components.Settings.SettingsJobsCache.editJobScheduleSelectorSeconds": "Hvert {jobScheduleSeconds, plural, one {second} other {{jobScheduleSeconds} seconds}}", "components.Discover.tmdbmoviestreamingservices": "TMDB Film Streaming Services", "components.Discover.tmdbtvstreamingservices": "TMDB TV Streaming Services", "components.Settings.SettingsJobsCache.availability-sync": "Synkronisering af mediernes tilgængelighed", "components.Discover.FilterSlideover.tmdbuservotecount": "Antal TMDB bruger stemmer", "components.Discover.FilterSlideover.voteCount": "Antal af stemmer mellem {minValue} og {maxValue}", "components.Settings.RadarrModal.tagRequests": "<PERSON> forespørgsler", "components.Settings.SonarrModal.tagRequests": "<PERSON> forespørgsler", "components.Settings.SonarrModal.tagRequestsInfo": "Tilføj automatisk et ekstra tag med forespørgerens bruger ID og viste navn", "i18n.collection": "<PERSON><PERSON>", "components.Settings.RadarrModal.tagRequestsInfo": "Tilføj automatisk et ekstra tag med forespørgerens bruger id og viste navn", "components.MovieDetails.imdbuserscore": "IMDB Bruger Score", "components.Settings.SonarrModal.animeSeriesType": "Anime Serie Type", "components.Settings.SonarrModal.seriesType": "Serie Type", "components.Settings.Notifications.NotificationsPushover.deviceDefault": "Enhedsstandard", "components.Settings.Notifications.NotificationsPushover.sound": "Notifikationslyd", "components.UserProfile.UserSettings.UserNotificationSettings.deviceDefault": "Enhedsstandard", "components.UserProfile.UserSettings.UserNotificationSettings.sound": "Notifikationslyd", "components.UserList.validationUsername": "Angiv et brugernavn", "components.Login.signinwithjellyfin": "Brug din {mediaServerName} konto", "components.Login.username": "Brugernavn", "components.Login.validationEmailFormat": "Ugyldig e-mail", "components.Login.validationEmailRequired": "Angiv en e-mail", "components.Login.validationusernamerequired": "Brugernavn kræves", "components.UserList.username": "Brugernavn", "components.Login.credentialerror": "Brugernavnet eller kodeordet er forkert."}