{"components.Login.email": "Adreça electrònica", "components.UserList.email": "Adreça electrònica", "components.UserList.edituser": "<PERSON><PERSON> els permisos d'usuari", "components.UserList.deleteuser": "Suprimeix l'usuari", "components.UserList.deleteconfirm": "Esteu segur que voleu suprimir aquest usuari? S'eliminaran totes les sol·licituds de forma permanent.", "components.UserList.creating": "S'està creant …", "components.UserList.createlocaluser": "Crea un usuari local", "components.UserList.created": "Registre", "components.UserList.create": "<PERSON><PERSON>", "components.UserList.bulkedit": "Edició massiva", "components.UserList.autogeneratepassword": "Genereu automàticament una contrasenya", "components.UserList.admin": "Administrador", "components.UserList.accounttype": "<PERSON><PERSON><PERSON>", "components.RequestModal.QuotaDisplay.quotaLinkUser": "Podeu veure un resum dels límits de sol·licituds d’aquest usuari a la seva <ProfileLink>pàgina de perfil</ProfileLink>.", "components.RequestModal.QuotaDisplay.requiredquota": "Necessites tenir al menys <strong>{seasons}</strong> {seasons, plural, one {petició de temporada} other {peticions de temporades}} restant(s) per a poder enviar una petició per a aquesta sèrie.", "components.RequestModal.pendingrequest": "Sol·licitud pendent", "components.RequestModal.pending4krequest": "Sol·licitud en 4K pendent", "components.RequestModal.numberofepisodes": "# d'episodis", "components.RequestModal.errorediting": "S'ha produït un error en editar la sol·licitud.", "components.RequestModal.cancel": "Cancel·la la sol·licitud", "components.RequestModal.autoapproval": "Aprovació automàtica", "components.RequestModal.alreadyrequested": "Ja s'ha sol·licitat", "components.RequestModal.SearchByNameModal.notvdbiddescription": "No s'ha pogut trobar coincidència d'aquesta serie. Seleccioneu l'emparellament correcte de la llista següent.", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {temporada} other {temporades}}", "components.RequestModal.QuotaDisplay.season": "temporada", "components.RequestModal.QuotaDisplay.requiredquotaUser": "Aquest usuari ha de tenir almenys <strong>{seasons}</strong> {seasons, plural, one {sol·licitud de temporada} other {sol·licituds de temporades}} per enviar una sol·licitud per a aquesta sèrie.", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {No} other {<strong>#</strong>}} {type} {remaining, plural, one {sol·licitud} other {sol·licituds}} restant(s)", "components.RequestModal.QuotaDisplay.quotaLink": "Podeu veure un resum dels límits de sol·licituds a la vostra <ProfileLink>pàgina de perfil</ProfileLink>.", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "No podeu sol·licitar més temporades", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {pel·lícula} other {pel·lícules}}", "components.RequestModal.QuotaDisplay.movie": "Pel·lícula", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "Aquest usuari pot sol·licitar <strong>{limit}</strong> {type} cada <strong>{days}</strong> dies.", "components.RequestModal.QuotaDisplay.allowedRequests": "Podeu sol·licitar <strong>{limit}</strong> {type} cada <strong>{days}</strong> dies.", "components.RequestModal.AdvancedRequester.rootfolder": "Carpeta arrel", "components.RequestModal.AdvancedRequester.requestas": "Sol·licita com a", "components.RequestModal.AdvancedRequester.qualityprofile": "Perfil de qualitat", "components.RequestModal.AdvancedRequester.languageprofile": "Perfil d'idioma", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.RequestModal.AdvancedRequester.destinationserver": "<PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.default": "{name} (Predeterminat)", "components.RequestModal.AdvancedRequester.animenote": "* Aquesta sèrie es un anime.", "components.RequestModal.AdvancedRequester.advancedoptions": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestList.sortModified": "Última modificació", "components.RequestList.sortAdded": "Més recent", "components.RequestList.showallrequests": "Mostra totes les sol·licituds", "components.MovieDetails.studio": "{studioCount, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}}", "components.RequestList.RequestItem.requested": "Sol·licitat", "components.PermissionEdit.request": "Sol·licitud", "components.RequestBlock.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {Temporades}}", "components.PermissionEdit.usersDescription": "Concedeix permís per gestionar els usuaris d'Jellyseerr. Els usuaris amb aquest permís no poden modificar els usuaris administrador ni concedir aquest privilegi a altres.", "components.PermissionEdit.request4k": "Sol·licita 4K", "components.MovieDetails.revenue": "Recaptació", "components.RequestCard.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {Temporades}}", "components.RequestList.requests": "Sol·licituds", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {Temporades}}", "components.RequestList.RequestItem.modifieduserdate": "{date} per {user}", "components.RequestList.RequestItem.modified": "Modificat", "components.RequestList.RequestItem.failedretry": "S'ha produït un error en tornar a demanar la sol·licitud.", "components.RequestButton.viewrequest4k": "Veure sol·licitud 4K", "components.RequestButton.viewrequest": "Veure sol·licitud", "components.RequestButton.requestmore4k": "Sol·licita més en 4K", "components.RequestButton.requestmore": "Sol·licita'n més", "components.RequestButton.declinerequests": "Rebutja {requestCount, plural, one {sol·licitud} other {{requestCount} sol·licituds}", "components.RequestButton.declinerequest4k": "Rebutja sol·licitud 4K", "components.RequestButton.declinerequest": "Rebutja sol·licitud", "components.RequestButton.decline4krequests": "Rebutja {requestCount, plural, one {sol·licitud en 4K} other {{requestCount} sol·licituds en 4K}", "components.RequestButton.approverequests": "Aprova {requestCount, plural, one {sol·licitud} other {{requestCount} sol·licituds}}", "components.RequestButton.approverequest4k": "Aprova la sol·licitud 4K", "components.RequestButton.approverequest": "Aprova la sol·licitud", "components.RequestButton.approve4krequests": "Aprova {requestCount, plural, one {solicitud en 4K} other {{requestCount} sol·licituds en 4K}}", "components.RequestBlock.server": "<PERSON><PERSON><PERSON>", "components.RequestBlock.rootfolder": "Carpeta arrel", "components.RequestBlock.requestoverrides": "Anul·lacions de sol·licituds", "components.RequestBlock.profilechanged": "Perfil de qualitat", "components.RegionSelector.regionServerDefault": "Predeterminada ({Region})", "components.RegionSelector.regionDefault": "Totes les regions", "components.QuotaSelector.unlimited": "Il·limitat", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.PersonDetails.crewmember": "Equip", "components.PersonDetails.birthdate": "Nascut/da {birthdate}", "components.PersonDetails.ascharacter": "com a {character}", "components.PersonDetails.appearsin": "<PERSON>pect<PERSON>", "components.PersonDetails.alsoknownas": "Tam<PERSON>é conegut com: {names}", "components.PermissionEdit.viewrequestsDescription": "Concedeix permís per veure les sol·licituds de continguts d'altres usuaris.", "components.PermissionEdit.viewrequests": "Veure sol·licituds", "components.PermissionEdit.users": "Gest<PERSON><PERSON> els usuaris", "components.PermissionEdit.requestDescription": "Concedeix permís per sol·licitar contingut no 4K.", "components.PermissionEdit.request4kTvDescription": "Concedeix permís per sol·licitar sèrie en 4K.", "components.PermissionEdit.request4kTv": "Sol·licita Sèrie en 4K", "components.PermissionEdit.request4kMoviesDescription": "Concedeix permís per sol·licitar pel·lícules en 4K.", "components.PermissionEdit.request4kMovies": "Sol·licitud de pel·lícules en 4K", "components.PermissionEdit.request4kDescription": "Concedeix permís per sol·licitar contingut en 4K.", "components.PermissionEdit.managerequestsDescription": "Concedeix permís per gestionar les sol·licituds de contingut d'Jellyseerr. Totes les sol·licituds que faci un usuari amb aquest permís s’aprovaran automàticament.", "components.PermissionEdit.managerequests": "Gestiona les sol·licituds", "components.PermissionEdit.autoapproveSeriesDescription": "Concedeix l’aprovació automàtica de les sol·licituds de sèries que no siguin 4K.", "components.PermissionEdit.autoapproveSeries": "Aprovació automàtica de sèries", "components.PermissionEdit.autoapproveMoviesDescription": "Concedeix l’aprovació automàtica de les sol·licituds de pel·lícules que no siguin 4K.", "components.PermissionEdit.autoapproveMovies": "Aprovació automàtica de pel·lícules", "components.PermissionEdit.autoapproveDescription": "Concedeix l’aprovació automàtica a totes les sol·licituds de contingut que no siguin 4K.", "components.PermissionEdit.autoapprove4kSeriesDescription": "Concedeix l'aprovació automàtica de les sol·licituds de sèries 4K.", "components.PermissionEdit.autoapprove4kSeries": "Aprovació automàtica Sèries en 4K", "components.PermissionEdit.autoapprove4kMoviesDescription": "Concedeix l’aprovació automàtica per a les sol·licituds de pel·lícules 4K.", "components.PermissionEdit.autoapprove4kMovies": "Aprova automàticament pel·lícules 4K", "components.PermissionEdit.autoapprove4kDescription": "Concedeix l’aprovació automàtica a totes les sol·licituds de contingut 4K.", "components.PermissionEdit.autoapprove4k": "Aprovació automàtica 4K", "components.PermissionEdit.autoapprove": "Aprovació automàtica", "components.NotificationTypeSelector.mediadeclined": "Sol·licitud rebutjada", "components.NotificationTypeSelector.mediarequested": "Sol·licitud pendent d'aprovació", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "Envieu notificacions quan els usuaris envien sol·licituds de contingut nous que s’aprovin automàticament.", "components.PermissionEdit.advancedrequestDescription": "Dona permís per modificar opcions avançades en les sol·licituds de contingut.", "components.PermissionEdit.advancedrequest": "Sol·licituds avançades", "components.PermissionEdit.adminDescription": "Accés complet d'administrador. Ignora totes les altres comprovacions de permisos.", "components.PermissionEdit.admin": "Administrador", "components.NotificationTypeSelector.mediarequestedDescription": "Envia notificacions quan es sol·licita contingut que requereix aprovació.", "components.NotificationTypeSelector.mediafailedDescription": "Envia notificacions quan el contingut sol·licitat no s’afegeix a Radarr o Sonarr.", "components.NotificationTypeSelector.mediafailed": "S'ha produït un error en el processament de la sol·licitud", "components.NotificationTypeSelector.mediadeclinedDescription": "Envia notificacions quan es rebutja una sol·licitud.", "components.NotificationTypeSelector.mediaavailableDescription": "Envia notificacions quan la sol·licitud estigui disponible.", "components.NotificationTypeSelector.mediaavailable": "Sol·licitud disponible", "components.NotificationTypeSelector.mediaapprovedDescription": "Envia notificacions quan la sol·licitud s’aprova manualment.", "components.NotificationTypeSelector.mediaapproved": "Sol·licitud aprovada", "components.NotificationTypeSelector.mediaAutoApproved": "Sol·licituds aprovades automàticament", "components.MovieDetails.watchtrailer": "<PERSON><PERSON>", "components.MovieDetails.viewfullcrew": "Veure equip complet", "components.MovieDetails.similar": "<PERSON><PERSON><PERSON><PERSON>s", "components.MovieDetails.runtime": "Ingressos", "components.MovieDetails.releasedate": "{releaseCount, plural, one {Data} other {Dates}} de llançament", "components.MovieDetails.recommendations": "Recomanacions", "components.MovieDetails.overviewunavailable": "Descripció general no disponible.", "components.MovieDetails.overview": "Visió general", "components.MovieDetails.originaltitle": "Títol original", "components.MovieDetails.originallanguage": "Idioma original", "components.MovieDetails.markavailable": "Marca com a disponible", "components.MovieDetails.mark4kavailable": "Marca com a disponible en 4K", "components.Settings.RadarrModal.validationProfileRequired": "<PERSON><PERSON> de seleccionar un perfil de qualitat", "components.Settings.RadarrModal.validationPortRequired": "Heu de proporcionar un número de port vàlid", "components.Settings.RadarrModal.validationNameRequired": "Heu de proporcionar un nom de servidor", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "<PERSON><PERSON> de seleccionar una disponibilitat mínima", "components.Settings.RadarrModal.validationHostnameRequired": "Heu de proporcionar un nom d’amfitrió o una adreça IP vàlides", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "L'URL base no pot acabar amb una barra inclinada final", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "L'URL base ha de tenir una barra inclinada", "components.Settings.RadarrModal.validationApplicationUrl": "Heu de proporcionar un URL vàlid", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "L'URL no pot acabar amb una barra inclinada final", "components.Settings.RadarrModal.validationApiKeyRequired": "Heu de proporcionar una clau API", "components.Settings.RadarrModal.toastRadarrTestSuccess": "La connexió amb Radarr s'ha establert correctament!", "components.Settings.RadarrModal.toastRadarrTestFailure": "No s'ha pogut connectar amb Radarr.", "components.Settings.RadarrModal.testFirstRootFolders": "Proveu la connexió per carregar les carpetes arrel", "components.Settings.RadarrModal.testFirstQualityProfiles": "Prova la connexió per carregar perfils de qualitat", "components.Settings.RadarrModal.syncEnabled": "Activa l’escaneig", "components.Settings.RadarrModal.ssl": "Utilitza SSL", "components.Settings.RadarrModal.servername": "Nom del Servidor", "components.Settings.RadarrModal.server4k": "Servidor 4K", "components.MovieDetails.cast": "Repartiment", "components.MovieDetails.budget": "Pressupost", "components.MovieDetails.MovieCrew.fullcrew": "Equip complet", "components.MovieDetails.MovieCast.fullcast": "Repartiment complet", "components.MediaSlider.ShowMoreCard.seemore": "<PERSON><PERSON><PERSON> més", "components.Login.validationpasswordrequired": "Heu de proporcionar una contrasenya", "components.Login.validationemailrequired": "Heu de proporcionar una adreça de correu electrònic vàlida", "components.Login.signinwithplex": "<PERSON><PERSON><PERSON>eu el vostre compte de Plex", "components.Login.signinwithoverseerr": "<PERSON><PERSON><PERSON>eu el vostre compte de {applicationTitle}", "components.Login.signinheader": "<PERSON><PERSON><PERSON> sessió per continuar", "components.Login.signingin": "S'està iniciant la sessió…", "components.Login.signin": "<PERSON><PERSON><PERSON>ssió", "components.Login.password": "Contrasenya", "components.Login.loginerror": "S'ha produït un error en intentar iniciar la sessió.", "components.Login.forgotpassword": "Has oblidat la contrasenya?", "components.Layout.UserDropdown.signout": "Tanqueu la sessió", "components.Layout.UserDropdown.settings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Layout.UserDropdown.myprofile": "Perfil", "components.Layout.Sidebar.users": "<PERSON><PERSON><PERSON>", "components.Layout.Sidebar.settings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Layout.Sidebar.requests": "Sol·licituds", "components.Layout.Sidebar.dashboard": "<PERSON><PERSON><PERSON><PERSON>", "components.Layout.SearchInput.searchPlaceholder": "Cerqueu Pel·lícules i Sèries", "components.Discover.upcomingtv": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Discover.upcomingmovies": "Pròximes pel·lícules", "components.Discover.upcoming": "Pròximes pel·lícules", "components.Discover.trending": "Tendències", "components.Discover.recentrequests": "Sol·licituds recents", "components.Discover.recentlyAdded": "Afegit recentment", "components.Discover.populartv": "S<PERSON>ries populars", "components.Discover.popularmovies": "Pel·lícules populars", "components.Discover.discover": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.TvGenreSlider.tvgenres": "<PERSON><PERSON><PERSON>", "components.Discover.TvGenreList.seriesgenres": "<PERSON><PERSON><PERSON>", "components.Discover.StudioSlider.studios": "Est<PERSON><PERSON>", "components.Discover.NetworkSlider.networks": "Emissors", "components.Discover.MovieGenreSlider.moviegenres": "Gèneres de Pel·lícules", "components.Discover.MovieGenreList.moviegenres": "Gèneres de Pel·lícules", "components.Discover.DiscoverTvLanguage.languageSeries": "<PERSON><PERSON><PERSON> en {language}", "components.Discover.DiscoverTvGenre.genreSeries": "<PERSON><PERSON><PERSON> de {genre}", "components.Discover.DiscoverStudio.studioMovies": "Pel·lícules de {studio}", "components.Discover.DiscoverNetwork.networkSeries": "<PERSON><PERSON><PERSON> de {network}", "components.Discover.DiscoverMovieLanguage.languageMovies": "Pel·lícules en {language}", "components.Discover.DiscoverMovieGenre.genreMovies": "Pel·lícules de {genre}", "components.CollectionDetails.requestcollection4k": "Sol·licita Col·lecció en 4K", "components.CollectionDetails.requestcollection": "Sol·licita Col·lecció", "components.CollectionDetails.overview": "<PERSON><PERSON><PERSON>", "components.CollectionDetails.numberofmovies": "{count} Pel·lícules", "components.AppDataWarning.dockerVolumeMissingDescription": "El muntatge de volum <code> {appDataPath} </code> no s'ha configurat correctament. Totes les dades s’esborraran quan el contenidor s’aturi o es reiniciï.", "components.RequestModal.requestfrom": "La sol·licitud de {username} està pendent d'aprovació.", "components.RequestModal.requesterror": "S'ha produït un error en enviar la sol·licitud.", "components.RequestModal.requestedited": "Sol·licitud per a <strong>{title}</strong> editada correctament!", "components.RequestModal.requestcancelled": "S'ha cancel·lat la sol·licitud de <strong>{title}</strong>.", "components.RequestModal.requestadmin": "Aquesta sol·licitud s'aprovarà automàticament.", "components.RequestModal.requestCancel": "S'ha cancel·lat la sol·licitud de <strong>{title}</strong>.", "components.RequestModal.requestSuccess": "<strong>{title}</strong> s'ha sol·licitat correctament!", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "Heu de proporcionar un URL vàlid", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "La configuració de notificacions Slack s'ha desat correctament!", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "No s'ha pogut desar la configuració de notificacions Slack.", "components.Settings.Notifications.NotificationsSlack.agentenabled": "Activa l'agent", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "Heu de proporcionar una clau d'usuari o grup vàlida", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "Heu de proporcionar un testimoni d’aplicació vàlid", "components.Settings.Notifications.NotificationsPushover.userToken": "<PERSON><PERSON> d'usuari o grup", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "La configuració de notificacions Pushover s'ha desat correctament!", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "No s'ha pogut desar la configuració de les notificacions de Pushover.", "components.Settings.Notifications.NotificationsPushover.agentenabled": "Activa l'agent", "components.Settings.Notifications.NotificationsPushover.accessToken": "Testimoni de l'API de l'aplicació", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "Heu de proporcionar un testimoni d'accés", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "La configuració de les notificacions de pushbullet s'ha desat correctament!", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "No s'ha pogut desar la configuració de notificacions de Pushbullet.", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "Activa l'agent", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "Testimoni d'accés", "components.Search.searchresults": "Resultats de la cerca", "components.Search.search": "Cerca", "components.ResetPassword.validationpasswordrequired": "Heu de proporcionar una contrasenya", "components.ResetPassword.validationpasswordminchars": "La contrasenya és massa curta; ha de tenir un mínim de 8 caràcters", "components.ResetPassword.validationpasswordmatch": "Les contrasenyes han de coincidir", "components.ResetPassword.validationemailrequired": "Heu de proporcionar una adreça de correu electrònic vàlida", "components.ResetPassword.resetpasswordsuccessmessage": "Contrasenya restablerta amb èxit!", "components.ResetPassword.resetpassword": "Restableix la contrasenya", "components.ResetPassword.requestresetlinksuccessmessage": "S'enviarà un enllaç de restabliment de contrasenya a l'adreça electrònica proporcionada si està associada amb un usuari vàlid.", "components.ResetPassword.passwordreset": "Reinicialització de la contrasenya", "components.ResetPassword.password": "Contrasenya", "components.ResetPassword.gobacklogin": "Torna a la pàgina d'inici de sessió", "components.ResetPassword.emailresetlink": "Envieu l'enllaç de recuperació per correu electrònic", "components.ResetPassword.email": "Adreça electrònica", "components.ResetPassword.confirmpassword": "Confirmeu la contrasenya", "components.RequestModal.selectseason": "Selecciona les temporades", "components.RequestModal.seasonnumber": "Temporada {number}", "components.RequestModal.season": "Temporada", "components.RequestModal.requestseasons": "Sol·licita {seasonCount} {seasonCount, plural, one {Temporada} other {Temporades}}", "components.Settings.Notifications.discordsettingssaved": "La configuració de les notificacions de Discord s'ha desat correctament!", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "No s'ha pogut desar la configuració de notificacions del Webhook.", "components.Settings.Notifications.discordsettingsfailed": "No s'ha pogut desar la configuració de les notificacions de Discord.", "components.Settings.Notifications.chatId": "Identificador del xat", "components.Settings.Notifications.botUsername": "Nom d'usuari del Bot", "components.Settings.Notifications.botAvatarUrl": "URL de l’avatar del Bot", "components.Settings.Notifications.botAPI": "Testimoni d'autorització del Bot", "components.Settings.Notifications.authUser": "Nom d'usuari SMTP", "components.Settings.Notifications.authPass": "Contrasenya SMTP", "components.Settings.Notifications.allowselfsigned": "Permet certificats autosignats", "components.Settings.Notifications.agentenabled": "Activa l'agent", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "La configuració de notificacions del Webhook s'ha desat correctament!", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "URL del Webhook", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "Heu de proporcionar un URL vàlid", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "Heu de proporcionar un payload JSON vàlid", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "Ajuda de la variable de plantilla", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "El Payload de JSON s'ha restablert correctament!", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "Restableix els valors per defecte", "components.Settings.Notifications.NotificationsWebhook.customJson": "Payload de JSON", "components.Settings.Notifications.NotificationsWebhook.authheader": "Capçalera d'autorització", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "Activa l'agent", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "URL del Webhook", "i18n.processing": "En procés", "i18n.close": "Tanca", "i18n.advanced": "<PERSON><PERSON><PERSON><PERSON>", "i18n.previous": "<PERSON><PERSON><PERSON><PERSON>", "pages.somethingwentwrong": "Alguna cosa ha anat malament", "pages.serviceunavailable": "Servei no disponible", "pages.returnHome": "Torna a l'inici", "pages.pagenotfound": "No s'ha trobat la pàgina", "pages.oops": "<PERSON><PERSON>", "pages.internalservererror": "S'ha produït un error intern al servidor", "pages.errormessagewithcode": "{statusCode} - {error}", "i18n.view": "Mostra", "i18n.usersettings": "Configuració de l'usuari", "i18n.unavailable": "No disponible", "i18n.tvshows": "<PERSON><PERSON><PERSON>", "i18n.tvshow": "<PERSON><PERSON><PERSON>", "i18n.testing": "S'està provant…", "i18n.test": "<PERSON><PERSON>", "i18n.status": "Estat", "i18n.showingresults": "Mostrant <strong>{from}</strong> a <strong>{to}</strong> de <strong>{total}</strong> resultats", "i18n.settings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.saving": "S'està desant…", "i18n.save": "<PERSON><PERSON> el<PERSON> canvis", "i18n.retry": "<PERSON>na-ho a provar", "i18n.resultsperpage": "Mostra {pageSize} resultats per pàgina", "i18n.requesting": "S'està sol·licitant …", "i18n.requested": "Sol·licitat", "i18n.request4k": "Sol·licita en 4K", "i18n.request": "Sol·licita", "i18n.pending": "<PERSON><PERSON>", "i18n.partiallyavailable": "Parcialment disponible", "i18n.notrequested": "No sol·licitat", "i18n.noresults": "Sense resultats.", "i18n.next": "Endavant", "i18n.movies": "Pel·lícules", "i18n.movie": "Pel·lícula", "i18n.loading": "S'està carregant…", "i18n.failed": "Fallit", "i18n.experimental": "Experimental", "i18n.edit": "<PERSON><PERSON>", "i18n.delimitedlist": "{a}, {b}", "i18n.delete": "Suprimeix", "i18n.deleting": "S'està suprimint…", "i18n.declined": "<PERSON><PERSON><PERSON><PERSON>", "i18n.decline": "<PERSON><PERSON><PERSON>", "i18n.canceling": "S'està cancel·lant …", "i18n.cancel": "Canceŀla", "i18n.back": "<PERSON><PERSON>", "i18n.available": "Disponible", "i18n.areyousure": "N'esteu segur?", "i18n.approved": "<PERSON><PERSON><PERSON>", "i18n.approve": "<PERSON><PERSON>", "i18n.all": "Totes", "components.UserProfile.unlimited": "Il·limitat", "components.UserProfile.totalrequests": "Sol·licituds totals", "components.UserProfile.seriesrequest": "Sol·licituds de sèries", "components.UserProfile.requestsperdays": "{limit} restants", "components.UserProfile.recentrequests": "Sol·licituds recents", "components.UserProfile.pastdays": "{type} (últims {days} dies)", "components.UserProfile.movierequests": "Sol·licituds de pel·lícules", "components.UserProfile.limit": "{remaining} de {limit}", "components.UserProfile.UserSettings.unauthorizedDescription": "No teniu permís per modificar la configuració d'aquest usuari.", "components.UserProfile.UserSettings.menuPermissions": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.menuNotifications": "Notificacions", "components.UserProfile.UserSettings.menuGeneralSettings": "General", "components.UserProfile.UserSettings.menuChangePass": "Contrasenya", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "No podeu modificar els vostres propis permisos.", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "Els permisos s'han desat correctament!", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "S'ha produït un error en desar la configuració.", "components.UserProfile.UserSettings.UserPermissions.permissions": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "La contrasenya és massa curta; ha de tenir un mínim de 8 caràcters", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "Heu de proporcionar una nova contrasenya", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "Heu de proporcionar la vostra contrasenya actual", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "Les contrasenyes han de coincidir", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "<PERSON><PERSON> de confirmar la nova contrasenya", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "La contrasenya s'ha desat correctament!", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "S'ha produït un error en desar la contrasenya. La vostra contrasenya actual s'ha introduït correctament?", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "S'ha produït un error en desar la contrasenya.", "components.UserProfile.UserSettings.UserPasswordChange.password": "Contrasenya", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "No teniu permís per modificar la contrasenya d'aquest usuari.", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "Nova contrasenya", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "Contrasenya actual", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "Confirmeu la contrasenya", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "Heu de proporcionar un identificador de xat vàlid", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "Heu de proporcionar un identificador d'usuari vàlid", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "La configuració s'ha desat correctament!", "components.UserProfile.UserSettings.UserGeneralSettings.role": "Rol", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink>Inicieu un xat</TelegramBotLink>, afegiu <GetIdBotLink>@get_id_bot</GetIdBotLink> i executeu l'ordre <code>/ my_id</code>", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "Identificador del xat", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "Envia notificacions sense so", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "Envia missatges silenciosament", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "Configuració de les notificacions", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "Notificacions", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "El <FindDiscordIdLink>número d'ID de diversos dígits</FindDiscordIdLink> associat al vostre compte d'usuari", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "ID de Discord", "components.UserProfile.UserSettings.UserGeneralSettings.user": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "S'ha produït un error en desar la configuració.", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "Límit de sol·licituds de sèries", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "Filtra el contingut per disponibilitat regional", "components.UserProfile.UserSettings.UserGeneralSettings.region": "Regió de Descobriu", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "Us<PERSON>ri de Plex", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "Propietari", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "Filtra el contingut per l'idioma original", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "Idioma per a la secció «Descobriu»", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "Límit de sol·licituds de pel·lícules", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "Usuari local", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "Configuració general", "components.UserProfile.UserSettings.UserGeneralSettings.general": "General", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "Sobreescriu el límit global", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "Nom de visualització", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "Administrador", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "Tipus de compte", "components.UserProfile.ProfileHeader.userid": "ID d'usuari: {userid}", "components.UserProfile.ProfileHeader.settings": "Edita la configuració", "components.UserProfile.ProfileHeader.profile": "Mostra el perfil", "components.UserProfile.ProfileHeader.joindate": "Unit el {joindate}", "components.UserList.validationpasswordminchars": "La contrasenya és massa curta; ha de tenir un mínim de 8 caràcters", "components.UserList.validationEmail": "Heu de proporcionar una adreça de correu electrònic vàlida", "components.UserList.userssaved": "Els permisos d'usuari s'han desat correctament!", "components.UserList.users": "<PERSON><PERSON><PERSON>", "components.UserList.userlist": "Llista d'usuaris", "components.UserList.userfail": "S'ha produït un error en desar els permisos de l'usuari.", "components.UserList.userdeleteerror": "S'ha produït un error en suprimir l'usuari.", "components.TvDetails.overviewunavailable": "Sinopsi no disponible.", "components.TvDetails.overview": "<PERSON><PERSON><PERSON>", "components.TvDetails.originaltitle": "Títol original", "components.TvDetails.originallanguage": "Idioma original", "components.TvDetails.nextAirDate": "Pròxima data d'emissió", "components.TvDetails.network": "{networkCount, plural, one {Emissor} other {Emissors}}", "components.TvDetails.firstAirDate": "Primera data d'emissió", "components.TvDetails.episodeRuntimeMinutes": "{runtime} minuts", "components.TvDetails.episodeRuntime": "Durac<PERSON>ó de l'episodi", "components.TvDetails.cast": "Repartiment", "components.TvDetails.anime": "Anime", "components.TvDetails.TvCrew.fullseriescrew": "Equip complet de la sèrie", "components.TvDetails.TvCast.fullseriescast": "Repartiment complet de la sèrie", "components.StatusBadge.status4k": "4K {status}", "components.Setup.welcome": "Ben<PERSON>ut a Jellyseerr", "components.Setup.signinMessage": "Comenceu iniciant se<PERSON><PERSON> amb el vostre compte de Plex", "components.Setup.setup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Setup.finishing": "S'està acabant…", "components.Setup.finish": "Finalitza la configuració", "components.Setup.continue": "Continua", "components.Setup.configureservices": "Configu<PERSON><PERSON> el<PERSON>", "components.Settings.toastPlexRefresh": "S'està recuperant la llista de servidors de Plex…", "components.Settings.webhook": "Webhook", "components.Settings.validationPortRequired": "Heu de proporcionar un número de port vàlid", "components.Settings.validationHostnameRequired": "Heu de proporcionar un nom d’amfitrió o una adreça IP vàlida", "components.Settings.toastPlexRefreshSuccess": "La llista de servidors Plex s'ha recuperat correctament!", "components.Settings.toastPlexRefreshFailure": "No s'ha pogut recuperar la llista de servidors Plex.", "components.Settings.toastPlexConnectingSuccess": "La connexió amb Plex s'ha establert correctament!", "components.Settings.toastPlexConnectingFailure": "No s'ha pogut connectar amb Plex.", "components.Settings.toastPlexConnecting": "S'està intentant connectar amb Plex…", "components.Settings.startscan": "Inicia l'exploració", "components.Settings.ssl": "SSL", "components.Settings.sonarrsettings": "Configurac<PERSON><PERSON> Sonarr", "components.Settings.settingUpPlexDescription": "Per configurar Plex, podeu introduir les dades manualment o seleccioneu un servidor recuperat de <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink>. Premeu el botó situat a la dreta del menú desplegable per recuperar els servidors disponibles.", "components.Settings.services": "<PERSON><PERSON><PERSON>", "components.Settings.serverpresetRefreshing": "S'estan recuperant els servidors…", "components.Settings.serverpresetManualMessage": "Configuració manual", "components.Settings.serverpresetLoad": "Premeu el botó per carregar els servidors disponibles", "components.Settings.serverpreset": "<PERSON><PERSON><PERSON>", "components.Settings.serverRemote": "remot", "components.Settings.serverLocal": "local", "components.Settings.scanning": "S'està sincronitzant …", "components.Settings.scan": "Sincronitza les biblioteques", "components.Settings.radarrsettings": "Configurac<PERSON><PERSON>", "components.Settings.port": "Port", "components.Settings.plexsettingsDescription": "Configureu la paràmetres del vostre servidor Plex. Jellyseerr explora les vostres biblioteques Plex per determinar la disponibilitat de continguts.", "components.Settings.plexsettings": "Configuració de Plex", "components.Settings.plexlibrariesDescription": "Les biblioteques en les que Jellyseerr explora títols. Configureu i deseu la configuració de la connexió Plex i feu clic al botó següent si no apareix cap.", "components.Settings.plexlibraries": "Biblioteques Plex", "components.Settings.plex": "Plex", "components.Settings.manualscanDescription": "Normalment, només s’executarà una vegada cada 24 hores. <PERSON><PERSON><PERSON>rr comprovarà de forma més agressiva el contingut afegit recentment del seu servidor Plex. Si és la primera vegada que configureu Plex, es recomana fer una exploració manual completa de la biblioteca!", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Exploració d'elements de Plex afegits recentment", "components.Settings.notrunning": "No s'està executant", "components.Settings.notificationsettings": "Configuració de les notificacions", "components.Settings.notifications": "Notificacions", "components.Settings.notificationAgentSettingsDescription": "Configureu i activeu els agents de notificació.", "components.Settings.menuUsers": "<PERSON><PERSON><PERSON>", "components.Settings.menuServices": "<PERSON><PERSON><PERSON>", "components.Settings.menuPlexSettings": "Plex", "components.Settings.menuNotifications": "Notificacions", "components.Settings.menuLogs": "Registres", "components.Settings.menuJobs": "Tasques programades i memòria cau", "components.Settings.menuGeneralSettings": "General", "components.Settings.menuAbout": "Quant a", "components.Settings.manualscan": "Exploració manual de la biblioteca", "components.Settings.librariesRemaining": "Biblioteques restants: {count}", "components.Settings.hostname": "Nom de l’amfitrió o adreça IP", "components.Settings.deleteserverconfirm": "Esteu segur que voleu suprimir aquest servidor?", "components.Settings.currentlibrary": "Biblioteca actual: {name}", "components.Settings.cancelscan": "Cancel·la l'exploració", "components.Settings.addsonarr": "Afegeix un servidor Sonarr", "components.Settings.activeProfile": "Perfil actiu", "components.Settings.enablessl": "Utilitza SSL", "components.Settings.email": "Adreça electrònica", "components.Settings.default4k": "4K predeterminat", "components.Settings.default": "Predeterminat", "components.Settings.copied": "S'ha copiat la clau API al porta-retalls.", "components.Settings.address": "Adreça", "components.Settings.addradarr": "Afegeix un servidor Radarr", "components.Settings.SonarrModal.validationRootFolderRequired": "<PERSON><PERSON> de seleccionar una carpeta arrel", "components.Settings.SonarrModal.validationProfileRequired": "<PERSON><PERSON> de seleccionar un perfil de qualitat", "components.Settings.SonarrModal.validationPortRequired": "Heu de proporcionar un número de port vàlid", "components.Settings.SonarrModal.validationNameRequired": "Heu de proporcionar un nom de servidor", "components.Settings.SonarrModal.validationLanguageProfileRequired": "<PERSON><PERSON> de seleccionar un perfil d'idioma", "components.Settings.SonarrModal.validationHostnameRequired": "Heu de proporcionar un nom d’amfitrió o una adreça IP vàlides", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "L'URL base no pot acabar amb una barra inclinada final", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "L'URL base ha de tenir una barra inclinada", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "L'URL no pot acabar amb una barra inclinada final", "components.Settings.SonarrModal.validationApplicationUrl": "Heu de proporcionar un URL vàlid", "components.Settings.SonarrModal.validationApiKeyRequired": "Heu de proporcionar una clau API", "components.Settings.SonarrModal.toastSonarrTestSuccess": "La connexió amb Sonarr s'ha establert correctament!", "components.Settings.SonarrModal.toastSonarrTestFailure": "No s'ha pogut connectar amb Sonarr.", "components.Settings.SonarrModal.testFirstRootFolders": "Proveu la connexió per carregar les carpetes arrel", "components.Settings.SonarrModal.testFirstQualityProfiles": "Prova la connexió per carregar perfils de qualitat", "components.Settings.SonarrModal.testFirstLanguageProfiles": "Prova la connexió per carregar els perfils d'idioma", "components.Settings.SonarrModal.syncEnabled": "Activa l’escaneig", "components.Settings.SonarrModal.ssl": "Utilitza SSL", "components.Settings.SonarrModal.servername": "Nom del Servidor", "components.Settings.SonarrModal.server4k": "Servidor 4K", "components.Settings.RadarrModal.selectRootFolder": "Seleccioneu la carpeta arrel", "components.Settings.SonarrModal.selectRootFolder": "Seleccioneu la carpeta arrel", "components.Settings.SonarrModal.selectQualityProfile": "Seleccioneu un perfil de qualitat", "components.Settings.SonarrModal.selectLanguageProfile": "Seleccioneu el perfil d'idioma", "components.Settings.SonarrModal.seasonfolders": "Carpeta per temporada", "components.Settings.SonarrModal.rootfolder": "Carpeta arrel", "components.Settings.SonarrModal.qualityprofile": "Perfil de qualitat", "components.Settings.SonarrModal.port": "Port", "components.Settings.SonarrModal.loadingrootfolders": "S'estan carregant les carpetes arrel…", "components.Settings.SonarrModal.loadingprofiles": "S'estan carregant els perfils de qualitat…", "components.Settings.SonarrModal.loadinglanguageprofiles": "S'estan carregant els perfils d'idioma…", "components.Settings.SonarrModal.languageprofile": "Perfil d'idioma", "components.Settings.SonarrModal.hostname": "Nom de l’amfitrió o adreça IP", "components.Settings.SonarrModal.externalUrl": "URL extern", "components.Settings.SonarrModal.editsonarr": "<PERSON><PERSON> el servidor Sonarr", "components.Settings.SonarrModal.defaultserver": "<PERSON><PERSON><PERSON> predeterminat", "components.Settings.SonarrModal.createsonarr": "Afegeix un nou servidor Sonarr", "components.Settings.SonarrModal.animequalityprofile": "Perfil de qualitat de l'anime", "components.Settings.SettingsUsers.userSettingsDescription": "Configureu la configuració global i predeterminada de l'usuari.", "components.Settings.SettingsUsers.toastSettingsFailure": "S'ha produït un error en desar la configuració.", "components.Settings.RadarrModal.baseUrl": "Base d’URL", "components.Settings.SonarrModal.baseUrl": "Base d'URL", "components.Settings.SonarrModal.apiKey": "Clau API", "components.Settings.SonarrModal.animerootfolder": "Carpeta arrel de l'anime", "components.Settings.SonarrModal.animelanguageprofile": "Perfil d'idioma per a Anime", "components.Settings.SonarrModal.add": "Afegeix un servidor", "components.Settings.SettingsUsers.users": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsUsers.userSettings": "Configuració de l'usuari", "components.Settings.SettingsUsers.tvRequestLimitLabel": "Límit global de sol·licituds de Sèries", "components.Settings.SettingsUsers.toastSettingsSuccess": "La configuració de l'usuari s'ha desat correctament!", "components.Settings.SettingsAbout.gettingsupport": "<PERSON><PERSON><PERSON><PERSON> <PERSON>a<PERSON>", "components.Settings.SettingsUsers.localLogin": "Activa l'inici de sessió local", "components.Settings.SettingsLogs.time": "<PERSON>a de temps", "components.Settings.SettingsLogs.resumeLogs": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.pauseLogs": "Pausa", "components.Settings.SettingsLogs.filterInfo": "Info", "components.Settings.SettingsLogs.filterDebug": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.extraData": "<PERSON><PERSON> addicionals", "components.Settings.SettingsLogs.copyToClipboard": "Copia al porta-retalls", "components.Settings.SettingsLogs.copiedLogMessage": "S'ha copiat el missatge de registre al porta-retalls.", "components.Settings.SettingsJobsCache.runnow": "Executa-ho ara", "components.Settings.SettingsJobsCache.plex-full-scan": "Exploració completa de la biblioteca plex", "components.Settings.SettingsJobsCache.jellyfin-full-scan": "Exploració completa de la biblioteca Jellyfin", "components.Settings.SettingsJobsCache.jellyfin-recently-added-scan": "Exploració d'elements de Jellyfin afegits recentment", "components.Settings.SettingsJobsCache.nextexecution": "Pròxima execució", "components.Settings.SettingsJobsCache.jobstarted": "S'ha iniciat {jobname}.", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname} s'ha cancel·lat.", "components.Settings.SettingsJobsCache.flushcache": "Buida la memòria cau", "components.Settings.SettingsJobsCache.command": "Ordre", "components.Settings.SettingsJobsCache.cachevsize": "Mida del valor", "components.Settings.SettingsJobsCache.cachename": "Nom de la memòria cau", "components.Settings.SettingsJobsCache.cacheksize": "Mida de la clau", "components.Settings.SettingsJobsCache.cachekeys": "Claus totals", "components.Settings.SettingsJobsCache.cacheflushed": "La memòria cau de {cachename} s'ha buidat.", "components.Settings.SettingsAbout.totalrequests": "Sol·licituds totals", "components.Settings.SettingsAbout.totalmedia": "Total de contingut", "components.Settings.SettingsAbout.preferredmethod": "Preferit", "components.Settings.SettingsAbout.githubdiscussions": "<PERSON><PERSON><PERSON> G<PERSON>", "components.Settings.SettingsAbout.Releases.viewongithub": "Visualitza a GitHub", "components.Settings.SettingsAbout.Releases.viewchangelog": "Visualitza el registre de canvis", "components.Settings.SettingsAbout.Releases.versionChangelog": "Registre de canvis {version}", "components.Settings.SettingsUsers.movieRequestLimitLabel": "Límit global de sol·licituds de pel·lícules", "components.Settings.SettingsUsers.defaultPermissions": "<PERSON><PERSON><PERSON> per defecte", "components.Settings.SettingsLogs.showall": "Mostra tots els registres", "components.Settings.SettingsLogs.message": "Missatge", "components.Settings.SettingsLogs.logsDescription": "<PERSON><PERSON>é podeu veure aquests registres directament mitjançant <code>stdout</code> o a <code>{appDataPath}/logs/overseerr.log</code>.", "components.Settings.SettingsLogs.logs": "Registres", "components.Settings.SettingsLogs.logDetails": "Detalls del registre", "components.Settings.SettingsLogs.level": "Gravetat", "components.Settings.SettingsLogs.label": "Etiqueta", "components.Settings.SettingsLogs.filterWarn": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.filterError": "Error", "components.Settings.SettingsJobsCache.unknownJob": "Tasca programada desconeguda", "components.Settings.SettingsJobsCache.sonarr-scan": "Escaneig de Sonarr", "components.Settings.SettingsJobsCache.radarr-scan": "Escaneig de Radar<PERSON>", "components.Settings.SettingsJobsCache.process": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobtype": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobsandcache": "Tasques programades i memòria cau", "components.Settings.SettingsJobsCache.jobsDescription": "Jellyseerr realitza certes tasques de manteniment com a feines programades regularment, però també es poden activar manualment a continuació. L’execució manual d’un treball no alterarà la seva programació.", "components.Settings.SettingsJobsCache.jobs": "Tasques programades", "components.Settings.SettingsJobsCache.jobname": "Nom de la tasca programada", "components.Settings.SettingsJobsCache.download-sync-reset": "Restableix la sincronització de descàrregues", "components.Settings.SettingsJobsCache.download-sync": "Sincronitza les baixades", "components.Settings.SettingsJobsCache.canceljob": "Cancel·la la tasca programada", "components.Settings.SettingsJobsCache.cachemisses": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachehits": "Consultes", "components.Settings.SettingsJobsCache.cacheDescription": "Je<PERSON>seerr emmagatzema a la memòria cau les sol·licituds als punts finals de l'API externa per optimitzar el rendiment i evitar fer peticions d'API innecessàries.", "components.Settings.SettingsJobsCache.cache": "Memòria cau", "components.Settings.SettingsAbout.version": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.timezone": "Zona horària", "components.Settings.SettingsAbout.supportoverseerr": "Dóna suport a Jellyseerr", "components.Settings.SettingsAbout.overseerrinformation": "<PERSON><PERSON>", "components.Settings.SettingsAbout.helppaycoffee": "A<PERSON><PERSON><PERSON>-me convidant-me a un cafè", "components.Settings.SettingsAbout.documentation": "Documentació", "components.Settings.SettingsAbout.about": "Quant a", "components.Settings.SettingsAbout.Releases.currentversion": "Actualitzat", "components.Settings.RadarrModal.selectQualityProfile": "Seleccioneu un perfil de qualitat", "components.Settings.RadarrModal.selectMinimumAvailability": "Seleccioneu la disponibilitat mínima", "components.Settings.RadarrModal.minimumAvailability": "Disponibilitat mínima", "components.Settings.RadarrModal.loadingrootfolders": "S'estan carregant les carpetes arrel…", "components.Settings.RadarrModal.loadingprofiles": "S'estan carregant els perfils de qualitat…", "components.Settings.RadarrModal.hostname": "Nom de l’amfitrió o adreça IP", "components.Settings.RadarrModal.defaultserver": "<PERSON><PERSON><PERSON> predeterminat", "components.Settings.RadarrModal.createradarr": "Afegiu un servidor Radarr nou", "components.Settings.RadarrModal.add": "Afegeix un servidor", "components.Settings.Notifications.validationSmtpHostRequired": "Heu de proporcionar un nom d’amfitrió o una adreça IP vàlids", "components.Settings.Notifications.validationEmail": "Heu de proporcionar una adreça de correu electrònic vàlida", "components.Settings.Notifications.validationChatIdRequired": "Heu de proporcionar un identificador de xat vàlid", "components.Settings.Notifications.validationBotAPIRequired": "Heu de proporcionar un testimoni d'autorització del bot", "components.Settings.Notifications.telegramsettingsfailed": "No s'ha pogut desar la configuració de les notificacions de Telegram.", "components.Settings.Notifications.smtpHost": "Amfitrió SMTP", "components.Settings.Notifications.emailsettingsfailed": "No s'ha pogut desar la configuració de les notificacions per correu electrònic.", "components.Settings.RadarrModal.apiKey": "Clau API", "components.Settings.SettingsAbout.Releases.releases": "Versions", "components.Settings.SettingsAbout.Releases.releasedataMissing": "La informació de la versió no està disponible.", "components.Settings.SettingsAbout.Releases.latestversion": "Última versió", "components.Settings.RadarrModal.validationRootFolderRequired": "<PERSON><PERSON> de seleccionar una carpeta arrel", "components.Settings.RadarrModal.rootfolder": "Carpeta arrel", "components.Settings.RadarrModal.qualityprofile": "Perfil de qualitat", "components.Settings.RadarrModal.port": "Port", "components.Settings.RadarrModal.externalUrl": "URL extern", "components.Settings.RadarrModal.editradarr": "<PERSON>eu el servidor Radarr", "components.Settings.Notifications.webhookUrl": "URL del Webhook", "components.Settings.Notifications.validationUrl": "Heu de proporcionar un URL vàlid", "components.Settings.Notifications.validationSmtpPortRequired": "Heu de proporcionar un número de port vàlid", "components.Settings.Notifications.telegramsettingssaved": "La configuració de les notificacions de Telegram s'ha desat correctament!", "components.Settings.Notifications.smtpPort": "Port SMTP", "components.Settings.Notifications.senderName": "Nom de l'emissor", "components.Settings.Notifications.sendSilentlyTip": "Envia notificacions sense so", "components.Settings.Notifications.sendSilently": "Envia-ho silenciosament", "components.Settings.Notifications.pgpPrivateKeyTip": "Signa missatges de correu electrònic xifrats utilitzant <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.pgpPrivateKey": "Clau privada PGP", "components.Settings.Notifications.pgpPasswordTip": "Signa missatges de correu electrònic utilitzant <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.pgpPassword": "Contrasenya de PGP", "components.Settings.Notifications.emailsettingssaved": "La configuració de les notificacions per correu electrònic s'ha desat correctament!", "components.Settings.Notifications.emailsender": "Adreça de l'emissor", "components.LanguageSelector.originalLanguageDefault": "Tots els idiomes", "components.LanguageSelector.languageServerDefault": "Predeterminat ({language})", "components.UserList.userdeleted": "L'usuari s'ha suprimit correctament!", "components.UserList.usercreatedsuccess": "L'usuari s'ha creat correctament!", "components.UserList.usercreatedfailed": "S'ha produït un error en crear l'usuari.", "components.UserList.user": "<PERSON><PERSON><PERSON>", "components.UserList.totalrequests": "Sol·licituds", "components.UserList.sortRequests": "Recompte de sol·licituds", "components.UserList.sortDisplayName": "Nom de visualització", "components.UserList.sortCreated": "Data de registre", "components.UserList.role": "Rol", "components.UserList.plexuser": "Us<PERSON>ri de Plex", "components.UserList.passwordinfodescription": "Configureu l'URL d'una aplicació i activeu les notificacions per correu electrònic per permetre la generació automàtica de contrasenyes.", "components.UserList.password": "Contrasenya", "components.UserList.owner": "Propietari", "components.UserList.nouserstoimport": "No hi ha usuaris nous de Plex a importar.", "components.UserList.localuser": "Usuari local", "components.UserList.importfromplexerror": "S'ha produït un error en importar usuaris de Plex.", "components.UserList.importfrommediaserver": "Importeu usuaris de {mediaServerName}", "components.UserList.importfromplex": "Importeu usuaris de Plex", "components.UserList.importedfromplex": "<strong>{userCount}</strong> {userCount, plural, one {usuari} other {usuaris}} de Plex importat correctament!", "components.TvDetails.watchtrailer": "<PERSON>eure el tràiler", "components.TvDetails.viewfullcrew": "Mostreu equip complet", "components.TvDetails.similar": "<PERSON><PERSON><PERSON> similars", "components.TvDetails.showtype": "<PERSON><PERSON><PERSON> <PERSON>", "components.TvDetails.seasons": "{seasonCount, plural, one {# Temporada} other {# Temporades}}", "components.TvDetails.recommendations": "Recomanacions", "components.Settings.SonarrModal.testFirstTags": "Proveu la connexió per carregar etiquetes", "components.Settings.SonarrModal.tags": "Etiquetes", "components.Settings.SonarrModal.selecttags": "Seleccioneu les etiquetes", "components.Settings.SonarrModal.notagoptions": "Sense etiquetes.", "components.Settings.SonarrModal.loadingTags": "S'estan carregant les etiquetes…", "components.Settings.SonarrModal.edit4ksonarr": "Editeu el servidor Sonarr 4K", "components.Settings.SonarrModal.default4kserver": "Servidor 4K predeterminat", "components.Settings.SonarrModal.create4ksonarr": "Afegiu un nou servidor Sonarr 4K", "components.Settings.SonarrModal.animeTags": "Etiquetes d'anime", "components.Settings.RadarrModal.testFirstTags": "Proveu la connexió per carregar etiquetes", "components.Settings.RadarrModal.tags": "Etiquetes", "components.Settings.RadarrModal.selecttags": "Seleccioneu les etiquetes", "components.Settings.RadarrModal.notagoptions": "Sense etiquetes.", "components.Settings.RadarrModal.loadingTags": "S'estan carregant les etiquetes…", "components.Settings.RadarrModal.edit4kradarr": "Editeu el servidor Radarr 4K", "components.Settings.RadarrModal.default4kserver": "Servidor 4K predeterminat", "components.Settings.RadarrModal.create4kradarr": "Afegiu un nou servidor Radarr 4K", "components.RequestModal.AdvancedRequester.tags": "Etiquetes", "components.RequestModal.AdvancedRequester.selecttags": "Seleccioneu les etiquetes", "components.RequestModal.AdvancedRequester.notagoptions": "Sense etiquetes.", "components.Settings.mediaTypeMovie": "pel·lícula", "i18n.retrying": "S'està tornant a provar…", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "El vostre compte no té cap contrasenya definida. Configureu una contrasenya a continuació per habilitar l'inici de sessió com a \"usuari local\" mitjançant la vostra adreça de correu electrònic.", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "Aquest compte d'usuari no té cap contrasenya definida. Configureu una contrasenya a continuació perquè aquest compte pugui iniciar la sessió com a \"usuari local\"", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "Heu de proporcionar una clau pública PGP vàlida", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "La configuració de les notificacions de Telegram s'ha desat correctament!", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "No s'ha pogut desar la configuració de les notificacions de Telegram.", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "Xifra els missatges de correu electrònic mitjançant <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "Clau pública PGP", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "La configuració de les notificacions per correu electrònic s'ha desat correctament!", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "No s'ha pogut desar la configuració de les notificacions per correu electrònic.", "components.UserProfile.UserSettings.UserNotificationSettings.email": "<PERSON><PERSON>u elect<PERSON>ò<PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "La configuració de les notificacions de Discord s'ha desat correctament!", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "No s'ha pogut desar la configuració de les notificacions de Discord.", "components.UserList.autogeneratepasswordTip": "Envieu a l'usuari una contrasenya generada per servidor", "components.Settings.serviceSettingsDescription": "Configureu els vostres servidors {serverType} a continuació. Podeu connectar diversos servidors {serverType}, però només dos es poden marcar com a valors predeterminats (un no 4K i un 4K). Els administradors poden substituir el servidor utilitzat per processar noves sol·licituds abans de l’aprovació.", "components.Settings.serverSecure": "segur", "components.Settings.noDefaultServer": "Cal marcar com a mínim un servidor {serverType} com a predeterminat perquè es processin les sol·licituds de {mediaType}.", "components.Settings.noDefaultNon4kServer": "Si només teniu un servidor únic {serverType} per a contingut no 4K i 4K (o si només descarregueu contingut 4K), el vostre servidor {serverType} <strong> NO</strong> deuria marcar-se com a servidor 4K.", "components.Settings.mediaTypeSeries": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.uptodate": "Actualitzat", "components.Settings.SettingsAbout.outofdate": "No està actualitzat", "components.Settings.Notifications.validationPgpPrivateKey": "Heu de proporcionar una clau privada PGP vàlida", "components.Settings.Notifications.validationPgpPassword": "Heu de proporcionar una contrasenya PGP", "components.Settings.Notifications.botUsernameTip": "Permetre als usuaris iniciar un xat amb el bot i configurar les seves pròpies notificacions", "components.RequestModal.pendingapproval": "La vostra sol·licitud està pendent d'aprovació.", "components.RequestList.RequestItem.mediaerror": "No s'ha trobat {mediaType}", "components.RequestList.RequestItem.deleterequest": "Suprimeix la sol·licitud", "components.RequestList.RequestItem.cancelRequest": "Cancel·la la sol·licitud", "components.RequestCard.mediaerror": "No s'ha trobat {mediaType}", "components.RequestCard.deleterequest": "Suprimeix la sol·licitud", "components.NotificationTypeSelector.notificationTypes": "Tipus de notificacions", "components.Layout.VersionStatus.streamstable": "<PERSON><PERSON><PERSON><PERSON> (Estable)", "components.Layout.VersionStatus.streamdevelop": "<PERSON><PERSON><PERSON><PERSON> (Desenvolupament)", "components.Layout.VersionStatus.outofdate": "No està actualitzat", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {canvi} other {canvis}} posterior(s)", "components.UserList.usercreatedfailedexisting": "Un altre usuari ja utilitza l'adreça electrònica proporcionada.", "components.Settings.SonarrModal.enableSearch": "Activa la cerca automàtica", "components.Settings.RadarrModal.enableSearch": "Activa la cerca automàtica", "components.RequestModal.edit": "Edita la sol·licitud", "components.RequestList.RequestItem.editrequest": "Edita la sol·licitud", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "Predeterminat ({language})", "components.Settings.Notifications.toastTelegramTestFailed": "No s'ha pogut enviar la notificació de prova de Telegram.", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "No s'ha pogut enviar la notificació de prova de LunaSea.", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "No s'ha pogut desar la configuració de notificacions de LunaSea.", "components.DownloadBlock.estimatedtime": "{time} de temps estimat", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "La configuració de notificacions de Push Web s'ha desat correctament!", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "No s'ha pogut desar la configuració de notificacions de Push Web.", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "Web Push", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "Idioma de visualització", "components.Settings.webpush": "Web Push", "components.Settings.noDefault4kServer": "Cal marcar un servidor 4K de {serverType} com a predeterminat per permetre als usuaris enviar sol·licituds de {mediaType} en 4K.", "components.Settings.is4k": "4K", "components.Settings.SettingsUsers.newPlexLoginTip": "Permetre als usuaris de {mediaServerName} iniciar la sessió sense haver-los importat prèviament", "components.Settings.SettingsUsers.newPlexLogin": "Activa nou inici de sessió de {mediaServerName}", "components.Settings.Notifications.toastTelegramTestSuccess": "S'ha enviat la notificació de prova de Telegram!", "components.Settings.Notifications.toastTelegramTestSending": "S'està enviant la notificació de prova de Telegram…", "components.Settings.Notifications.toastEmailTestSuccess": "S'ha enviat una notificació de prova per correu electrònic!", "components.Settings.Notifications.toastEmailTestSending": "S'està enviant la notificació de prova per correu electrònic…", "components.Settings.Notifications.toastEmailTestFailed": "No s'ha pogut enviar la notificació de prova per correu electrònic.", "components.Settings.Notifications.toastDiscordTestSuccess": "S'ha enviat una notificació de prova de Discord!", "components.Settings.Notifications.toastDiscordTestSending": "S'està enviant la notificació de prova de Discord…", "components.Settings.Notifications.toastDiscordTestFailed": "No s'ha pogut enviar la notificació de prova de Discord.", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "S'ha enviat una notificació de prova de Webhook!", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "S'està enviant una notificació de prova de Webhook…", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "No s'ha pogut enviar la notificació de prova de Webhook.", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "La configuració de notificacions de Push Web s'ha desat correctament!", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "No s'ha pogut desar la configuració de notificacions de Push Web.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "S'ha enviat una notificació de prova de Push Web!", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "S'està enviant la notificació de prova de Push Web…", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "No s'ha pogut enviar la notificació de prova de Push Web.", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "Activa l'agent", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "S'ha enviat la notificació de la prova de Slack!", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "S'està enviant la notificació de prova de Slack…", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "No s'ha pogut enviar la notificació de prova de Slack.", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "S'ha enviat una notificació de prova de Pushover!", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "S'està enviant la notificació de prova de Pushover…", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "No s'ha pogut enviar la notificació de prova de Pushover.", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "S'ha enviat la notificació de prova Pushbullet!", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "S'està enviant la notificació de prova de Pushbullet…", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "No s'ha pogut enviar la notificació de prova Pushbullet.", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "URL del Webhook", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "Has de proporcionar un URL vàlid", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "S'ha enviat la notificació de prova de LunaSea!", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "S'està enviant la notificació de prova de LunaSea…", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "La configuració de les notificacions de LunaSea s'ha desat correctament!", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "Només és necessari si no s'utilitza el perfil <code>default</code>", "components.Settings.Notifications.NotificationsLunaSea.profileName": "Nom de perfil", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "Activa l'agent", "components.PermissionEdit.requestTvDescription": "Concedeix permís per sol·licitar sèries no 4K.", "components.PermissionEdit.requestTv": "Sol·licita sèries", "components.PermissionEdit.requestMoviesDescription": "Concedeix permís per sol·licitar pel·lícules no 4K.", "components.PermissionEdit.requestMovies": "Sol·liciteu pel·lícules", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "El vostre <LunaSeaLink>URL del webhook de notificació</LunaSeaLink> basat en l'usuari o el dispositiu", "components.UserList.localLoginDisabled": "El paràmetre <strong>Activa l'inici de sessió local</strong> està desactivat actualment.", "components.Settings.webAppUrlTip": "Opcional<PERSON>, dirigiu els usuaris a l'aplicació web del vostre servidor en lloc de l'aplicació web \"allotjada\"", "components.Settings.webAppUrl": "<WebAppLink>URL de l'aplicació web</WebAppLink>", "components.Settings.Notifications.encryptionTip": "En la majoria dels casos, TLS implícit utilitza el port 465 i STARTTLS utilitza el port 587", "components.Settings.Notifications.encryptionImplicitTls": "Utilitzeu TLS implícit", "components.Settings.Notifications.encryptionDefault": "Utilitzeu STARTTLS si està disponible", "components.Settings.Notifications.encryption": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.chatIdTip": "Inicieu un xat amb el bot, afegiu <GetIdBotLink>@get_id_bot</GetIdBotLink> i indiqueu l'ordre <code>/my_id</code>", "components.Settings.Notifications.botApiTip": "<CreateBotLink>Creeu un bot</CreateBotLink> per utilitzar-lo amb <PERSON><PERSON>rr", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "Per tal de rebre notificacions push web, <PERSON><PERSON>seerr s'ha de servir mitjan<PERSON>nt HTTPS.", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "Creeu una integració <WebhookLink>Webhook entrant</WebhookLink>", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "El vostre <UsersGroupsLink>identificador d'usuari o grup</UsersGroupsLink>", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicationRegistrationLink>Registreu una aplicació</ApplicationRegistrationLink> per utilitzar-la amb Je<PERSON>rr", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "Creeu un testimoni a partir de la <PushbulletSettingsLink>Configuració del compte</PushbulletSettingsLink>", "components.Settings.Notifications.webhookUrlTip": "Creeu una <DiscordWebhookLink>integració de webhook</DiscordWebhookLink> al vostre servidor", "components.Settings.Notifications.encryptionOpportunisticTls": "<PERSON><PERSON><PERSON>eu sempre STARTTLS", "components.Settings.Notifications.encryptionNone": "Cap", "components.Settings.SettingsUsers.localLoginTip": "Permetre als usuaris iniciar la sessió mitjançant la seva adreça de correu electrònic i contrasenya, en lloc de l'autenticació de Plex", "components.Settings.SettingsUsers.defaultPermissionsTip": "Permisos inicials assignats a usuaris nous", "components.RequestList.RequestItem.requesteddate": "Sol·licitat", "components.RequestCard.failedretry": "S'ha produït un error en tornar a demanar la sol·licitud.", "components.Settings.SettingsAbout.betawarning": "Aquest és un programari BETA. Algunes funcionalitats poden fallar. Informeu de qualsevol problema a GitHub!", "components.Settings.Notifications.validationTypes": "Heu de seleccionar com a mínim un tipus de notificació", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "Heu de seleccionar com a mínim un tipus de notificació", "components.Settings.Notifications.NotificationsSlack.validationTypes": "Heu de seleccionar com a mínim un tipus de notificació", "components.Settings.Notifications.NotificationsPushover.validationTypes": "Heu de seleccionar com a mínim un tipus de notificació", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "Heu de seleccionar com a mínim un tipus de notificació", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "Heu de seleccionar com a mínim un tipus de notificació", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{temporades} per {quotaDays} {dies}</quotaUnits>", "components.QuotaSelector.seasons": "{count, plural, one {temporada} other {temporades}}", "components.QuotaSelector.movies": "{count, plural, one {pel·lícula} other {pel·lícules}}", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{pel·lícules} per {quotaDays} {dies}</quotaUnits>", "components.QuotaSelector.days": "{count, plural, one {dia} other {dies}}", "components.NotificationTypeSelector.usermediarequestedDescription": "Rep notificacions quan altres usuaris envien sol·licituds de contingut noves que requereixen aprovació.", "components.NotificationTypeSelector.usermediafailedDescription": "Rep notificacions quan no es puguin afegir sol·licituds de contingut a Radarr o Sonarr.", "components.NotificationTypeSelector.usermediadeclinedDescription": "Rep notificacions quan es rebutgin les teves sol·licituds de contingut.", "components.NotificationTypeSelector.usermediaavailableDescription": "Rep notificacions quan les teves sol·licituds de contingut estiguin disponibles.", "components.NotificationTypeSelector.usermediaapprovedDescription": "Rep notificacions quan s'aprovin les teves sol·licituds de contingut.", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "Rep notificacions quan altres usuaris envien sol·licituds de contingut noves que s’aprovin automàticament.", "components.MovieDetails.showmore": "Mostra més", "components.MovieDetails.showless": "<PERSON><PERSON> menys", "components.Layout.LanguagePicker.displaylanguage": "Idioma de visualització", "components.StatusBadge.status": "{status}", "components.IssueDetails.IssueComment.areyousuredelete": "Esteu segur que voleu suprimir aquest comentari?", "components.IssueDetails.IssueComment.delete": "Suprimeix el comentari", "components.IssueDetails.IssueComment.edit": "Edita el comentari", "components.IssueDetails.IssueDescription.deleteissue": "Suprimeix la incidència", "components.IssueDetails.IssueDescription.description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.IssueComment.postedby": "Publicat per {username} el {relativeTime}", "components.IssueDetails.IssueComment.postedbyedited": "Publicat per {username} el {relativeTime} (Modificat)", "components.IssueDetails.IssueComment.validationComment": "Heu d'introduir un missatge", "components.IssueDetails.allepisodes": "<PERSON><PERSON> els episodis", "components.IssueDetails.comments": "<PERSON><PERSON><PERSON>", "components.IssueDetails.issuepagetitle": "Incidència", "components.IssueDetails.IssueDescription.edit": "Edita la descripció", "components.IssueDetails.allseasons": "Totes les temporades", "components.IssueDetails.closeissue": "Tanca la incidència", "components.IssueDetails.closeissueandcomment": "Tanca amb comentaris", "components.IssueDetails.deleteissue": "Suprimeix la incidència", "components.IssueDetails.leavecomment": "Comentari", "components.IssueDetails.deleteissueconfirm": "Esteu segur que voleu suprimir aquesta incidència?", "components.IssueDetails.episode": "E<PERSON>odi {episodeNumber}", "components.IssueDetails.lastupdated": "Última actualització", "components.IssueDetails.openinarr": "Obre a {arr}", "components.IssueDetails.toasteditdescriptionfailed": "S'ha produït un error en editar la descripció de la incidència.", "components.IssueDetails.toastissuedeletefailed": "S'ha produït un error en suprimir la incidència.", "components.IssueDetails.unknownissuetype": "Desconegut", "components.IssueList.IssueItem.openeduserdate": "{date} per {user}", "components.IssueDetails.nocomments": "Sense comentaris.", "components.IssueDetails.issuetype": "<PERSON><PERSON><PERSON>", "components.IssueDetails.openin4karr": "Obre en {arr} 4K", "components.IssueDetails.problemseason": "Temporada afectada", "components.IssueDetails.reopenissueandcomment": "Torna a obrir amb comentaris", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {Temporades}}", "components.IssueDetails.toastissuedeleted": "La incidència s'ha suprimit correctament!", "components.IssueDetails.toaststatusupdated": "L'estat de l'incidència s'ha actualitzat correctament!", "components.IssueList.IssueItem.issuestatus": "Estat", "components.IssueModal.CreateIssueModal.validationMessageRequired": "Heu de proporcionar una descripció", "components.IssueModal.CreateIssueModal.whatswrong": "Què passa?", "components.IssueModal.issueAudio": "<PERSON><PERSON><PERSON>", "components.IssueModal.issueOther": "Altre", "components.ManageSlideOver.manageModalClearMediaWarning": "* Això eliminarà de manera irreversible totes les dades de {mediaType}, incloses les sol·licituds. Si aquest element existeix a la vostra biblioteca {mediaServerName}, la informació dels continguts es recrearà durant la següent exploració.", "components.ManageSlideOver.downloadstatus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.toasteditdescriptionsuccess": "La descripció de l'incidència s'ha editat correctament!", "components.IssueList.IssueItem.issuetype": "<PERSON><PERSON><PERSON>", "components.IssueList.IssueItem.problemepisode": "Episodi afectat", "components.IssueDetails.problemepisode": "Episodi afectat", "components.IssueDetails.openedby": "#{issueId} oberta {relativeTime} per {username}", "components.IssueDetails.play4konplex": "Veure en 4K a {mediaServerName}", "components.IssueDetails.reopenissue": "Torna a obrir la incidència", "components.IssueDetails.season": "Temporada {seasonNumber}", "components.IssueList.IssueItem.unknownissuetype": "Desconegut", "components.IssueList.IssueItem.viewissue": "<PERSON><PERSON>e incidè<PERSON>", "components.IssueModal.CreateIssueModal.allepisodes": "<PERSON><PERSON> els episodis", "components.IssueModal.CreateIssueModal.allseasons": "Totes les temporades", "components.IssueDetails.playonplex": "<PERSON>eure a {mediaServerName}", "components.IssueList.IssueItem.opened": "Oberta", "components.IssueList.issues": "Incidències", "components.IssueModal.CreateIssueModal.reportissue": "Informar d'una incidència", "components.IssueModal.CreateIssueModal.season": "Temporada {seasonNumber}", "components.IssueModal.CreateIssueModal.submitissue": "Envia la incidència", "components.IssueModal.CreateIssueModal.toastFailedCreate": "S'ha produït un error en enviar la incidència.", "components.IssueList.showallissues": "Mostra tots les incidències", "components.IssueModal.CreateIssueModal.problemepisode": "Episodi afectat", "components.IssueModal.CreateIssueModal.providedetail": "Si us plau, proporcioneu una explicació detallada del problema que heu trobat.", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "L'informe de la incidència per a <strong>{title}</strong> s'ha enviat correctament!", "components.IssueModal.CreateIssueModal.problemseason": "Temporada afectada", "components.IssueModal.CreateIssueModal.episode": "E<PERSON>odi {episodeNumber}", "components.IssueList.sortModified": "Última modificació", "components.IssueList.sortAdded": "Més recent", "components.NotificationTypeSelector.issuecommentDescription": "Envia notificacions quan les incidències rebin comentaris nous.", "components.NotificationTypeSelector.issuecreatedDescription": "Envieu notificacions quan s'informin d'incidències.", "components.IssueModal.issueSubtitles": "Subtítol", "components.NotificationTypeSelector.issueresolvedDescription": "Envieu notificacions quan es resolguin les incidències.", "components.Layout.Sidebar.issues": "Incidències", "components.NotificationTypeSelector.adminissuecommentDescription": "Notifica'm quan altres usuaris facin comentaris sobre incidències.", "components.ManageSlideOver.tvshow": "sèries", "components.Settings.SettingsJobsCache.editJobSchedule": "Modifica la tasca programada", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "Freqüència nova", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "Cada {jobScheduleHours, plural, one {hora} other {{jobScheduleHours} hores}}", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "Cada {jobScheduleMinutes, plural, one {minut} other {{jobScheduleMinutes} minuts}}", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "<ApplicationRegistrationLink>Registreu una aplicació</ApplicationRegistrationLink> per utilitzar-la amb {applicationTitle}", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "El vostre <UsersGroupsLink>identificador d'usuari o grup </UsersGroupsLink> de 30 caràcters", "components.MovieDetails.streamingproviders": "Actualment s'està retransmetent", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "S'ha produït un error en desar la tasca programada.", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "Tasca programada editada correctament!", "components.IssueModal.issueVideo": "Vídeo", "components.NotificationTypeSelector.issuecomment": "Comentari de la incidència", "components.NotificationTypeSelector.issuecreated": "Incidència informada", "components.PermissionEdit.manageissuesDescription": "Doneu permís per gestionar incidències en continguts.", "components.IssueDetails.toaststatusupdatefailed": "Alguna cosa ha fallat en actualitzar l'estat de la incidència.", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "Testimoni d'accés", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "Creeu un testimoni des del vostra <PushbulletSettingsLink>configuració del compte</PushbulletSettingsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "La configuració de les notificacions Pushbullet no s'ha pogut desar.", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "La configuració de notificació Pushbullet s'ha desat correctament!", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "Testimoni API de l'aplicació", "components.IssueList.IssueItem.episodes": "{EpisodeCount, plural, one {<PERSON>pisodi} other {<PERSON>pisod<PERSON>}}", "components.IssueModal.CreateIssueModal.toastviewissue": "<PERSON><PERSON>e incidè<PERSON>", "components.ManageSlideOver.manageModalClearMedia": "Esborra les dades", "components.ManageSlideOver.manageModalNoRequests": "Sense sol·licituds.", "components.ManageSlideOver.manageModalRequests": "Sol·licituds", "components.ManageSlideOver.manageModalTitle": "Gestiona {mediaType}", "components.ManageSlideOver.mark4kavailable": "Marca com a disponible en 4K", "components.ManageSlideOver.markavailable": "Marca com a disponible", "components.ManageSlideOver.movie": "pel·lícula", "components.ManageSlideOver.openarr": "Obre a {arr}", "components.ManageSlideOver.openarr4k": "Obre en 4K {arr}", "components.NotificationTypeSelector.issueresolved": "Incidència resolta", "components.PermissionEdit.createissues": "Informa d'incidències", "components.PermissionEdit.viewissues": "Veure altres incidències", "components.PermissionEdit.viewissuesDescription": "Doneu permís per veure incidències de continguts informats per altres usuaris.", "components.PermissionEdit.createissuesDescription": "Doneu permís per informar incidències en continguts.", "components.PermissionEdit.manageissues": "Gestiona les incidències", "components.Settings.SettingsAbout.runningDevelop": "Esteu executant la branca <code>develop</code> d'<PERSON><PERSON><PERSON><PERSON>, que només es recomana per a aquells que contribueixen al desenvolupament o ajuden amb proves de nous desenvolupaments.", "components.TvDetails.streamingproviders": "Actualment s'està retransmetent", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "<PERSON><PERSON> d'usuari o grup", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "La configuració de notificacions Pushover no s'ha pogut desar.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "La configuració de notificació P<PERSON>over s'ha desat correctament!", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "Heu de proporcionar un testimoni d'accés", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "Heu de proporcionar un testimoni d'aplicació vàlid", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "Heu de proporcionar una clau d'usuari o grup vàlida", "i18n.open": "Oberta", "i18n.resolved": "Resol<PERSON>", "components.NotificationTypeSelector.userissuecommentDescription": "Notifica'm quan incidències reportades per mi rebin comentaris nous.", "components.NotificationTypeSelector.userissuecreatedDescription": "Notifica'm quan altres usuaris informen incidències.", "components.NotificationTypeSelector.userissueresolvedDescription": "Notifica'm quan es resolguin incidències reportades per mi.", "components.ManageSlideOver.manageModalIssues": "Incidències obertes", "components.IssueModal.CreateIssueModal.extras": "Extres", "components.NotificationTypeSelector.adminissuereopenedDescription": "Notifica'm quan es tornin a obrir incidències per altres usuaris.", "components.NotificationTypeSelector.adminissueresolvedDescription": "Notifica'm quan altres usuaris resolguin incidències.", "components.NotificationTypeSelector.issuereopened": "Incidències reobertes", "components.NotificationTypeSelector.issuereopenedDescription": "Notifica'm quan es tornin a obrir incidències.", "components.NotificationTypeSelector.userissuereopenedDescription": "Notifica'm quan es tornin a obrir incidències reportades per mi.", "components.RequestModal.requestmovies4k": "{count} {count, plural, one {Pel·lícula} other {Pel·lícules}} en 4K solicitada/es", "components.RequestModal.requestseasons4k": "{seasonCount} {seasonCount, plural, one {temporada} other {temporades}} solicitada/es", "components.RequestModal.selectmovies": "Selecciona les pel·lícules", "components.TvDetails.productioncountries": "{countryCount, plural, one {<PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}} de producció", "components.RequestModal.requestmovies": "{count} {count, plural, one {Pel·lícula} other {Pel·lícules}} solicitada/es", "components.MovieDetails.productioncountries": "{countryCount, plural, one {<PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}} de producció", "components.RequestModal.approve": "Aprova la sol·licitud", "components.RequestModal.requestApproved": "S'ha aprovat la sol·licitud de <strong>{title}</strong>!", "components.Settings.Notifications.enableMentions": "Activa les mencions", "components.Settings.RadarrModal.announced": "Anun<PERSON><PERSON>", "components.Settings.RadarrModal.inCinemas": "Als cinemes", "components.IssueDetails.commentplaceholder": "Afegeix un comentari…", "components.Settings.RadarrModal.released": "Alliberat", "components.Settings.Notifications.NotificationsGotify.agentenabled": "Activa l'agent", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "La configuració de notificacions de Gotify no s'ha pogut desar.", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "La configuració de notificacions de Gotify s'ha desat correctament!", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "Heu de proporcionar un testimoni d'aplicació", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "El <FindDiscordIdLink>número d'ID de diversos dígits</FindDiscordIdLink> associat al vostre compte d'usuari de Discord", "components.ManageSlideOver.manageModalAdvanced": "<PERSON><PERSON><PERSON><PERSON>", "components.ManageSlideOver.manageModalMedia": "Contingut", "components.ManageSlideOver.manageModalMedia4k": "Contingut 4K", "components.ManageSlideOver.alltime": "Tots els temps", "components.ManageSlideOver.markallseasons4kavailable": "Mar<PERSON><PERSON> totes les temporades com a disponibles en 4K", "components.ManageSlideOver.markallseasonsavailable": "<PERSON><PERSON><PERSON> totes les temporades com a disponibles", "components.ManageSlideOver.opentautulli": "<PERSON><PERSON>", "components.ManageSlideOver.pastdays": "Darrers {days, number} dies", "components.ManageSlideOver.playedby": "Reproduït per", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> {playCount, plural, one {reproducció} other {reproduccions}}", "components.Settings.Notifications.NotificationsGotify.url": "URL del servidor", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "S'ha enviat una notificació de prova de Gotify!", "components.Settings.Notifications.NotificationsGotify.token": "Testimoni d'aplica<PERSON>ó", "components.Settings.Notifications.NotificationsGotify.validationTypes": "<PERSON><PERSON> de seleccionar almenys un tipus de notificació", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "Heu de proporcionar un URL vàlid", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "L'URL no ha d'acabar amb una barra inclinada final", "components.Settings.tautulliApiKey": "Clau API", "components.Settings.validationUrlBaseLeadingSlash": "La base de l'URL ha de tenir una barra inclinada inicial", "components.Settings.validationUrlBaseTrailingSlash": "La base de l'URL no ha d'acabar amb una barra inclinada final", "components.Settings.validationUrlTrailingSlash": "L'URL no ha d'acabar amb una barra inclinada final", "i18n.import": "Importa", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "Etiqueta de canal", "components.Settings.externalUrl": "URL extern", "components.Settings.tautulliSettings": "Con<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.tautulliSettingsDescription": "Opcionalment podeu configurar del vostre servid<PERSON>. <PERSON><PERSON><PERSON>rr recupera les dades de l'historial de visualitzacions dels vostres continguts Plex de Tau<PERSON>lli.", "components.Settings.toastTautulliSettingsFailure": "Alguna cosa ha fallat en desar la configuració de Tau<PERSON>lli.", "components.Settings.toastTautulliSettingsSuccess": "La configuració de <PERSON> s'ha desat correctament!", "components.Settings.urlBase": "URL base", "components.Settings.validationApiKey": "Heu de proporcionar una clau API", "components.Settings.validationUrl": "Heu de proporcionar un URL vàlid", "components.UserList.newplexsigninenabled": "La configuració <strong>Activa el nou inici de sessió Plex</strong> està activada actualment. Els usuaris de Plex amb accés a la biblioteca no cal que s'importin per iniciar la sessió.", "components.UserProfile.recentlywatched": "Vist recentment", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "No s'ha pogut enviar la notificació de prova de Gotify.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "S'està enviant una notificació de prova de Gotify…", "components.Settings.SettingsAbout.appDataPath": "Directori de dades", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "Identificador d'usuari de Discord", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "Heu de proporcionar un identificador d'usuari de Discord vàlid", "i18n.importing": "S'està important…", "components.RequestBlock.languageprofile": "Perfil d'idioma", "components.AirDateBadge.airedrelative": "Emès {relativeTime}", "components.MovieDetails.digitalrelease": "Llançament digital", "components.MovieDetails.physicalrelease": "Alliberament físic", "components.PermissionEdit.viewrecent": "<PERSON><PERSON><PERSON> afegits recentment", "components.PermissionEdit.viewrecentDescription": "Concediu permís per veure la llista de contingut afegits recentment.", "components.Settings.deleteServer": "Suprimeix el servidor {serverType}", "components.StatusChecker.reloadApp": "Torna a carregar {applicationTitle}", "components.TitleCard.cleardata": "Esborra les dades", "i18n.restartRequired": "Cal reiniciar", "components.RequestList.RequestItem.tvdbid": "Identificador de TheTVDB", "components.TitleCard.tvdbid": "Identificador de TheTVDB", "components.RequestList.RequestItem.tmdbid": "Identificador TMDB", "components.Discover.DiscoverWatchlist.discoverwatchlist": "La vostra llista de seguiment de Plex", "components.Discover.plexwatchlist": "La vostra llista de seguiment de Plex", "components.PermissionEdit.autorequest": "Sol·licitud automàtica", "components.NotificationTypeSelector.mediaautorequested": "Sol·licitud enviada automàticament", "components.NotificationTypeSelector.mediaautorequestedDescription": "Rep notificacions quan s'enviïn automàticament sol·licituds de contingut multimèdia nous per als elements de la teva llista de seguiment de Plex.", "components.RequestCard.tmdbid": "Identificador TMDB", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "Sol·liciteu automàticament pel·lícules de la vostra <PlexWatchlistSupportLink>Llista de seguiment de Plex</PlexWatchlistSupportLink>", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "Sol·licitud automàtica de pel·lícules", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "Sol·licitud automàtica de sèries", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "Sol·liciteu sèries automàticament de la vostra <PlexWatchlistSupportLink>Llista de seguiment de Plex</PlexWatchlistSupportLink>", "components.PermissionEdit.autorequestSeries": "Sol·licitud automàtica de sèrie", "components.PermissionEdit.autorequestMovies": "Sol·licitud automàtica de pel·lícules", "components.PermissionEdit.autorequestMoviesDescription": "Concediu permís per enviar automàticament sol·licituds de pel·lícules que no siguin 4K mitjançant Plex Watchlist.", "components.PermissionEdit.autorequestSeriesDescription": "Concediu permís per enviar automàticament sol·licituds de sèries que no siguin 4K mitjançant Plex Watchlist.", "components.Settings.SettingsLogs.viewdetails": "<PERSON><PERSON><PERSON>", "components.TitleCard.tmdbid": "Identificador TMDB", "components.Discover.DiscoverWatchlist.watchlist": "Llista de seguiment de Plex", "components.MovieDetails.managemovie": "Gestiona la pel·lícula", "components.MovieDetails.theatricalrelease": "Estrena en cines", "components.PermissionEdit.autorequestDescription": "Concediu permís per enviar automàticament sol·licituds de contingut que no siguin 4K mitjançant llistes de seguiment de Plex.", "components.PermissionEdit.viewwatchlists": "Veure les llistes de seguiment de Plex", "components.PermissionEdit.viewwatchlistsDescription": "<PERSON><PERSON><PERSON> per<PERSON> per veure les llistes de seguiment de Plex d'altres usuaris.", "components.RequestCard.tvdbid": "Identificador de TheTVDB", "components.Settings.advancedTooltip": "La configuració incorrecta d'aquesta configuració pot provocar un malfuncionament", "components.Settings.experimentalTooltip": "L'activació d'aquesta configuració pot provocar un comportament inesperat de l'aplicació", "components.TvDetails.reportissue": "Informar d'un problema", "components.TitleCard.mediaerror": "No s'ha trobat {mediaType}", "components.UserProfile.plexwatchlist": "Llista de seguiment de Plex", "components.Layout.UserDropdown.requests": "Sol·licituds", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Sol·licituds de pel·lícules", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "Sol·licituds de sèries", "components.MovieDetails.reportissue": "Informa d'un problema", "components.StatusChecker.appUpdatedDescription": "Fes clic al botó següent per a tornar a carregar l'aplicació.", "components.StatusChecker.appUpdated": "Actualitzat {applicationTitle}", "components.StatusChecker.restartRequiredDescription": "Si us plau, reinicieu el servidor per aplicar la configuració actualitzada.", "components.AirDateBadge.airsrelative": "Emissió {relativeTime}", "components.MovieDetails.rtaudiencescore": "Puntuació d'audiència de Rotten Tomatoes", "components.MovieDetails.rtcriticsscore": "Tomàmetre Rotten Tomatoes", "components.MovieDetails.tmdbuserscore": "Puntuació d'usuaris de TMDB", "components.RequestBlock.approve": "Aprova la sol·licitud", "components.RequestBlock.decline": "Rebutja la sol·licitud", "components.RequestBlock.requestdate": "Data de sol·licitud", "components.RequestBlock.requestedby": "Sol·licitat per", "components.Settings.restartrequiredTooltip": "<PERSON><PERSON><PERSON><PERSON> s'ha de reiniciar perquè els canvis a aquesta configuració tinguin efecte", "components.StatusBadge.managemedia": "Gestiona {mediaType}", "components.TvDetails.Season.somethingwentwrong": "S'ha produït un error en recuperar les dades de la temporada.", "components.TvDetails.rtaudiencescore": "Puntuació d'audiència de Rotten Tomatoes", "components.TvDetails.rtcriticsscore": "Tomàmetre Rotten Tomatoes", "components.TvDetails.seasonstitle": "Temporades", "components.RequestCard.approverequest": "Aprova la sol·licitud", "components.RequestCard.cancelrequest": "Cancel·la la sol·licitud", "components.RequestCard.editrequest": "Edita la sol·licitud", "components.TvDetails.manageseries": "Gestiona les Sèries", "components.RequestBlock.delete": "Suprimeix la sol·licitud", "components.RequestBlock.edit": "Edita la sol·licitud", "components.RequestBlock.lastmodifiedby": "Última modificació per", "components.StatusBadge.playonplex": "Reprodueix a {mediaServerName}", "components.RequestCard.declinerequest": "Rebutja la sol·licitud", "components.StatusBadge.openinarr": "Obre a {arr}", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "Sincronització de la llista de seguiment de Plex", "components.TvDetails.seasonnumber": "Temporada {seasonNumber}", "components.TvDetails.episodeCount": "{episodeCount, plural, one {# Episodi} other {# Episodis}}", "components.TvDetails.tmdbuserscore": "Puntuació d'usuaris de TMDB", "components.TvDetails.status4k": "4K {status}", "components.StatusChecker.restartRequired": "Cal reiniciar el servidor", "components.Discover.emptywatchlist": "Els continguts afegits a la vostra <PlexWatchlistSupportLink>Llista de seguiment de Plex</PlexWatchlistSupportLink> apareixeran aquí.", "components.RequestModal.requestcollection4ktitle": "Sol·licitud de col·lecció en 4K", "components.RequestModal.SearchByNameModal.nomatches": "No hem pogut trobar cap coincidència per a aquesta sèrie.", "components.UserProfile.emptywatchlist": "Els continguts afegits a la vostra <PlexWatchlistSupportLink>Llista de seguiment de Plex</PlexWatchlistSupportLink> apareixeran aquí.", "components.RequestModal.requestseries4ktitle": "Sol·licitud de sèries en 4K", "components.RequestModal.requestcollectiontitle": "Sol·licitud de col·lecció", "components.RequestModal.requestmovie4ktitle": "Sol·licitud de pel·lícula en 4K", "components.RequestModal.requestmovietitle": "Sol·licitud de pel·lícula", "components.RequestModal.requestseriestitle": "Sol·licitud de sèries", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "Freqüència actual", "components.TvDetails.Season.noepisodes": "Llista d'episodis no disponible.", "components.Settings.SettingsJobsCache.image-cache-cleanup": "Neteja de la memòria cau d'imatges", "components.Settings.SettingsJobsCache.imagecache": "Memòria cau d'imatges", "components.Settings.SettingsJobsCache.imagecachecount": "Imatges a la memòria cau", "components.Settings.SettingsJobsCache.imagecachesize": "Mida total de la memòria cau", "components.Settings.SettingsJobsCache.imagecacheDescription": "Quan està activat a la configuració, <PERSON><PERSON><PERSON><PERSON> enviarà les imatges a la memòria cau de fonts externes preconfigurades. Les imatges emmagatzemades a la memòria cau es desen a la vostra carpeta de configuració. Podeu trobar els fitxers a <code>{appDataPath}/cache/images</code>.", "components.Discover.DiscoverMovieKeyword.keywordMovies": "Pel·lícules {keywordTitle}", "components.Discover.tmdbtvgenre": "Gènere de la sèrie TMDB", "components.Discover.tmdbtvkeyword": "Paraula clau de la sèrie TMDB", "components.Discover.CreateSlider.providetmdbkeywordid": "Proporciona un ID de paraula clau de TMDB", "components.Discover.CreateSlider.providetmdbsearch": "Proporciona una consulta de cerca", "components.Discover.CreateSlider.searchStudios": "Cercar per estudi…", "components.Discover.CreateSlider.slidernameplaceholder": "Nom del control lliscant", "components.Discover.CreateSlider.starttyping": "Començar a escriure per a cercar.", "components.Discover.DiscoverSliderEdit.enable": "Commutar la visibilitat", "components.Discover.DiscoverSliderEdit.remove": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.PlexWatchlistSlider.plexwatchlist": "La teva llista Plex", "components.Discover.RecentlyAddedSlider.recentlyAdded": "Afegit recentment", "components.Discover.CreateSlider.addSlider": "Afegir un control lliscant", "components.Discover.CreateSlider.addcustomslider": "<PERSON><PERSON>r un control lliscant personalitzat", "components.Discover.CreateSlider.addfail": "No s'ha pogut crear un control lliscant nou.", "components.Discover.CreateSlider.editSlider": "Editar el control lliscant", "components.Discover.CreateSlider.editfail": "No s'ha pogut editar el control lliscant.", "components.Discover.tmdbmoviegenre": "Gènere de pel·lícula TMDB", "components.Discover.tmdbsearch": "Cerca TMDB", "components.Discover.tmdbstudio": "Estudi TMDB", "components.Discover.CreateSlider.editsuccess": "S'ha editat el control lliscant i s'ha desat la configuració de la pàgina de Descobriu.", "components.Discover.CreateSlider.needresults": "Cal tenir almenys 1 resultat.", "components.Discover.CreateSlider.nooptions": "Sense resultats.", "components.Discover.CreateSlider.providetmdbgenreid": "Proporciona un ID de categoria TMDB", "components.Discover.CreateSlider.providetmdbnetwork": "Proporciona l'ID d'emissor TMDB", "components.Discover.CreateSlider.providetmdbstudio": "Proporciona l'ID d'estudi TMDB", "components.Discover.CreateSlider.searchGenres": "Cercar per gènere…", "components.Discover.CreateSlider.searchKeywords": "Cercar per paraules clau…", "components.Discover.DiscoverSliderEdit.deletefail": "No s'ha pogut suprimir el control lliscant.", "components.Discover.PlexWatchlistSlider.emptywatchlist": "El contingut afegit a la teva <PlexWatchlistSupportLink>llista de visualització de Plex</PlexWatchlistSupportLink> apareixerà aquí.", "components.Discover.CreateSlider.addsuccess": "S'ha creat un control lliscant nou i s'ha desat la configuració de personalització de Descobriu.", "components.Discover.CreateSlider.validationDatarequired": "Has de proporcionar un valor de dades.", "components.Discover.CreateSlider.validationTitlerequired": "Has de proporcionar un títol.", "components.Discover.DiscoverSliderEdit.deletesuccess": "Control lliscant suprimit amb èxit.", "components.Discover.createnewslider": "Crear un control lliscant nou", "components.Discover.DiscoverMovies.discovermovies": "Pel·lícules", "components.Discover.DiscoverMovies.sortPopularityAsc": "Popularitat ascendent", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {# filtre actiu} other {# filtres actius}}", "components.Discover.DiscoverMovies.sortPopularityDesc": "Popularitat descendent", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "Data de llançament ascendent", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "Data de llançament descendent", "components.Discover.DiscoverMovies.sortTitleAsc": "<PERSON><PERSON><PERSON><PERSON> (A-Z) ascendent", "components.Discover.DiscoverMovies.sortTitleDesc": "<PERSON><PERSON><PERSON><PERSON> (Z-A) descendent", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "Puntuació TMDB ascendent", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "Puntuació TMDB descendent", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {# filtre actiu} other {# filtres actius}}", "components.Discover.DiscoverTv.discovertv": "<PERSON><PERSON><PERSON>", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "Data de primera d'emissió ascendent", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "Data de primera d'emissió descendent", "components.Discover.DiscoverTv.sortPopularityAsc": "Popularitat ascendent", "components.Discover.DiscoverTv.sortPopularityDesc": "Popularitat descendent", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "Puntuació TMDB ascendent", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "Puntuació TMDB descendent", "components.Discover.networks": "Emissors", "components.Discover.resetwarning": "Restablir tots els controls lliscants al valor predeterminat. Això també suprimirà els controls lliscants personalitzats!", "components.Discover.tmdbmoviekeyword": "Paraula clau de pel·lícula TMDB", "components.Discover.tmdbnetwork": "Emissors TMDB", "components.Discover.FilterSlideover.tmdbuserscore": "Puntuació d'usuaris TMDB", "components.Discover.tvgenres": "<PERSON><PERSON><PERSON>", "components.Discover.DiscoverTvKeyword.keywordSeries": "S<PERSON><PERSON> {keywordTitle}", "components.Discover.moviegenres": "Gèneres de pel·lícules", "components.Discover.resetsuccess": "Restablerta la configuració personalitzada de Descobriu.", "components.Discover.studios": "Est<PERSON><PERSON>", "components.Discover.stopediting": "Parar l'edició", "components.Discover.updatefailed": "S'ha produït un error en actualitzar la configuració de personalitzada de Descobriu.", "components.Discover.updatesuccess": "S'ha actualitzat la configuració de personalitzada de Descobriu.", "components.Discover.FilterSlideover.releaseDate": "Data de publicació", "components.Discover.FilterSlideover.runtime": "Temps d'execució", "components.RequestList.RequestItem.unknowntitle": "Títol desconegut", "components.RequestCard.unknowntitle": "Títol desconegut", "components.Selector.nooptions": "Sense resultats.", "components.Discover.DiscoverTv.sortTitleAsc": "<PERSON><PERSON><PERSON><PERSON> (A-Z) ascendent", "components.Discover.DiscoverTv.sortTitleDesc": "<PERSON><PERSON><PERSON><PERSON> (Z-A) descendent", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {# filtre actiu} other {# filtres actius}}", "components.Discover.FilterSlideover.clearfilters": "<PERSON><PERSON><PERSON> els filtres actius", "components.Discover.FilterSlideover.filters": "Filtres", "components.Discover.FilterSlideover.firstAirDate": "Data primera d'emissió", "components.Discover.FilterSlideover.from": "<PERSON>", "components.Discover.FilterSlideover.genres": "<PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.keywords": "<PERSON><PERSON> clau", "components.Discover.FilterSlideover.originalLanguage": "Idioma original", "components.Discover.FilterSlideover.ratingText": "Puntuació entre {minValue} i {maxValue}", "components.Discover.FilterSlideover.runtimeText": "{minValue}-{maxValue} durada en minuts", "components.Discover.resetfailed": "S'ha produït un error en restablir la configuració de personalització de Descobriu.", "components.Discover.customizediscover": "<PERSON><PERSON><PERSON>", "components.Discover.resettodefault": "<PERSON><PERSON><PERSON><PERSON> al valor predeterminat", "components.Layout.Sidebar.browsemovies": "Pel·lícules", "components.Layout.Sidebar.browsetv": "<PERSON><PERSON><PERSON>", "components.Selector.searchGenres": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "components.DownloadBlock.formattedTitle": "{title}: <PERSON><PERSON><PERSON> {temporadaNumber} Episodi {episodeNumber}", "components.Discover.FilterSlideover.streamingservices": "Serveis en streaming", "components.Discover.FilterSlideover.studio": "Estudi", "components.Discover.FilterSlideover.to": "A", "components.Settings.SettingsMain.general": "General", "components.Settings.SettingsMain.generalsettings": "Configuració general", "components.Settings.SettingsMain.cacheImages": "Activar la memòria cau d'imatges", "components.Settings.SettingsMain.generalsettingsDescription": "Configuració global i predeterminada per a Jellyseerr.", "components.Settings.SettingsMain.hideAvailable": "Amagar el contingut disponible", "components.Settings.SettingsMain.apikey": "Clau API", "components.Settings.SettingsMain.applicationurl": "URL de l'aplicació", "components.Settings.SettingsMain.cacheImagesTip": "Memòria cau d'imatges d'origen extern (requereix una quantitat important d'espai en disc)", "components.Settings.SettingsJobsCache.editJobScheduleSelectorSeconds": "Cada {jobScheduleSeconds, plural, one {segon} other {{jobScheduleSeconds} segons}}", "components.Settings.SettingsMain.applicationTitle": "Títol de l'aplicació", "components.Selector.searchKeywords": "Cercar paraules clau…", "components.Selector.starttyping": "Començar a escriure per a cercar.", "components.Selector.searchStudios": "Cercar estudis…", "components.Selector.showless": "Mostrar menys", "components.Selector.showmore": "Mostrar més", "components.Settings.SettingsMain.locale": "Idioma de visualització", "components.Settings.SettingsMain.originallanguage": "Idioma a Descobriu", "components.Settings.SettingsMain.originallanguageTip": "Filtrar el contingut per idioma original", "components.Settings.SettingsMain.toastApiKeyFailure": "S'ha produït un error en generar una clau API nova.", "components.Settings.SettingsMain.toastApiKeySuccess": "La clau API s'ha generat correctament!", "components.Settings.SettingsMain.toastSettingsFailure": "S'ha produït un error en desar la configuració.", "components.Settings.SettingsMain.partialRequestsEnabled": "Permet sol·licituds parcials de sèries", "components.Settings.SettingsMain.toastSettingsSuccess": "La configuració s'ha desat correctament!", "components.Settings.SettingsMain.validationApplicationUrlTrailingSlash": "L'URL no ha d'acabar amb una barra inclinada final", "components.Settings.SettingsMain.validationApplicationTitle": "Has de proporcionar un títol d'aplicació", "components.Settings.SettingsMain.validationApplicationUrl": "Has de proporcionar un URL vàlid", "components.StatusBadge.seasonepisodenumber": "S{seasonNumber}E{episodeNumber}", "components.Settings.SettingsJobsCache.availability-sync": "Sincronització de disponibilitat de contingut", "components.Discover.tmdbmoviestreamingservices": "Serveis de transmissió de pel·lícules TMDB", "components.Discover.tmdbtvstreamingservices": "Serveis de transmissió de TV TMDB", "components.Layout.UserWarnings.emailRequired": "És requereix un n correu electrònic.", "components.Layout.UserWarnings.passwordRequired": "Es requereix una contrasenya.", "components.Login.description": "Com que és la primera vegada que inicieu sessió a {applicationName}, es necessita afegir un correu electrònic vàlid.", "components.Discover.FilterSlideover.tmdbuservotecount": "Recompte de vots d'usuaris de TMDB", "components.Discover.FilterSlideover.voteCount": "Nombre de vots entre {minValue} i {maxValue}", "components.Layout.UserWarnings.emailInvalid": "El correu electrònic no és vàlid.", "components.Login.credentialerror": "El nom d'usuari o la contrasenya són incorrectes."}