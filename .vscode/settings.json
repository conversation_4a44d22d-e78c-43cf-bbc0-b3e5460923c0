{"eslint.enable": true, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "typescript.tsdk": "node_modules/typescript/lib", "sqltools.connections": [{"previewLimit": 50, "driver": "SQLite", "name": "Local SQLite", "database": "./config/db/db.sqlite3"}], "editor.formatOnSave": true, "typescript.preferences.importModuleSpecifier": "non-relative", "files.associations": {"globals.css": "tailwindcss"}, "i18n-ally.localesPaths": ["src/i18n/locale"]}