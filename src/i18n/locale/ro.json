{"components.Discover.DiscoverStudio.studioMovies": "Filme de la {studio}", "components.AppDataWarning.dockerVolumeMissingDescription": "Montarea volumului <code>{appDataPath}</code> nu a fost configurată corespunzător. Toate datele vor fi șterse atunci când containerul este oprit sau repornit.", "components.CollectionDetails.numberofmovies": "{count} Filme", "components.CollectionDetails.overview": "Prezentare generală", "components.CollectionDetails.requestcollection": "Solicită Colecția", "components.CollectionDetails.requestcollection4k": "Solicită Colecția în 4K", "components.Discover.DiscoverMovieGenre.genreMovies": "{genul} Filme", "components.Discover.DiscoverMovieLanguage.languageMovies": "{language} Filme", "components.Discover.DiscoverNetwork.networkSeries": "Seriale de la {network}", "components.Discover.MovieGenreSlider.moviegenres": "<PERSON><PERSON>e", "components.Discover.NetworkSlider.networks": "<PERSON><PERSON><PERSON>", "components.Discover.TvGenreList.seriesgenres": "<PERSON><PERSON>", "components.IssueDetails.IssueDescription.edit": "Editați descrierea", "components.IssueDetails.IssueComment.validationComment": "Trebuie s<PERSON> introduceți un mesaj", "components.IssueDetails.IssueDescription.description": "Des<PERSON><PERSON><PERSON>", "components.IssueDetails.IssueComment.postedbyedited": "Postat {relativeTime} de {username} (Editat)", "components.Discover.DiscoverTvGenre.genreSeries": "Seriale de {genre}", "components.Discover.DiscoverTvLanguage.languageSeries": "Seriale în limba {language}", "components.Discover.MovieGenreList.moviegenres": "<PERSON><PERSON>e", "components.Discover.StudioSlider.studios": "<PERSON><PERSON>", "components.Discover.TvGenreSlider.tvgenres": "<PERSON><PERSON>", "components.Discover.discover": "Descoperă", "components.Discover.popularmovies": "Filme Populare", "components.Discover.populartv": "Seriale Populare", "components.Discover.recentlyAdded": "Adăugate Recent", "components.Discover.recentrequests": "Solicit<PERSON><PERSON>", "components.Discover.upcoming": "Filme Viitoare", "components.Discover.upcomingmovies": "Filme Viitoare", "components.Discover.upcomingtv": "Seriale Viitoare", "components.DownloadBlock.estimatedtime": "Estimat {time}", "components.IssueDetails.IssueComment.areyousuredelete": "Sunteți sigur că doriți să ștergeți acest comentariu?", "components.IssueDetails.IssueComment.delete": "Șterge comentariul", "components.IssueDetails.IssueComment.edit": "Editați comentariul", "components.IssueDetails.IssueComment.postedby": "Postat {relativeTime} de {username}", "components.IssueDetails.IssueDescription.deleteissue": "Ștergeți problema", "components.Discover.DiscoverWatchlist.discoverwatchlist": "Lista ta de Urmărire în Plex", "components.Discover.plexwatchlist": "Lista ta de Urmărire în Plex", "components.IssueDetails.allseasons": "Toate sezoanele", "components.IssueDetails.closeissue": "Înch<PERSON> problema", "components.IssueDetails.deleteissueconfirm": "Ești sigur că vrei să ștergi această problema?", "components.IssueDetails.commentplaceholder": "Adaugă un comentariu…", "components.IssueDetails.comments": "Comenta<PERSON><PERSON>", "components.IssueDetails.issuetype": "Tip", "components.IssueDetails.lastupdated": "Ultimul update", "components.IssueDetails.leavecomment": "Comentează", "components.Discover.DiscoverWatchlist.watchlist": "Listă de urmărire în Plex", "components.Discover.trending": "Tendințe", "components.IssueDetails.openinarr": "Deschide în {arr}", "components.IssueDetails.play4konplex": "Vizionează în 4K în Plex", "components.IssueDetails.playonplex": "Vizionează în Plex", "components.IssueDetails.problemepisode": "Episoade afectate", "components.IssueDetails.problemseason": "Sezoane afectate", "components.IssueDetails.reopenissue": "Redeschide problemă", "components.IssueDetails.reopenissueandcomment": "Redeschide cu comentariu", "components.IssueDetails.toasteditdescriptionfailed": "A intervenit o eroare în timpul editării descrierii problemei.", "components.IssueDetails.toasteditdescriptionsuccess": "Descrierea problemei editată cu succes!", "components.IssueDetails.toastissuedeleted": "Problemă ștearsă cu succes!", "components.IssueDetails.season": "Sezon {seasonNumber}", "components.IssueDetails.toastissuedeletefailed": "A intervenit o eroare în timpul ștergerii problemei.", "components.IssueDetails.toaststatusupdated": "Statusul problemei a fost actualizat cu succes!", "components.IssueDetails.toaststatusupdatefailed": "A intervenit o eroare în timpul actualizării statusului problemei.", "components.IssueDetails.unknownissuetype": "Necunoscut", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, un {Episod} alte {Episoade}}}", "components.IssueList.IssueItem.issuestatus": "Stare", "components.IssueList.IssueItem.opened": "<PERSON><PERSON><PERSON>", "components.IssueList.IssueItem.unknownissuetype": "Necunoscut", "components.IssueList.IssueItem.openeduserdate": "{date} de c<PERSON><PERSON> {user}", "components.IssueList.IssueItem.problemepisode": "Episodul afectat", "components.IssueList.IssueItem.viewissue": "<PERSON><PERSON><PERSON><PERSON> problema", "components.IssueModal.CreateIssueModal.problemepisode": "Episodul afectat", "components.IssueModal.CreateIssueModal.providedetail": "Vă rugăm să furnizați o explicație detaliată a problemei pe care ați întâmpinat-o.", "components.IssueModal.CreateIssueModal.problemseason": "Sezonul afectat", "components.IssueModal.CreateIssueModal.episode": "Episodul {episodeNumber}", "components.IssueModal.CreateIssueModal.extras": "Extra", "components.IssueModal.CreateIssueModal.season": "Sezonul {seasonNumber}", "components.IssueModal.CreateIssueModal.submitissue": "Trimiteți problema", "components.IssueModal.CreateIssueModal.toastFailedCreate": "Ceva nu a mers bine în timpul trimiterii problemei.", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "Raportul problemei pentru <strong>{title}</strong> a fost trimis cu succes!", "components.IssueModal.CreateIssueModal.whatswrong": "Ce nu e bine?", "components.IssueModal.issueAudio": "Audio", "components.IssueModal.issueOther": "Altele", "components.IssueModal.issueSubtitles": "Subtitrare", "components.IssueModal.issueVideo": "Video", "components.LanguageSelector.languageServerDefault": "Implicit ({language})", "components.LanguageSelector.originalLanguageDefault": "Toate limbile", "components.Layout.LanguagePicker.displaylanguage": "Limba de afișare", "components.Layout.Sidebar.dashboard": "Des<PERSON><PERSON><PERSON><PERSON>", "components.Layout.Sidebar.issues": "Probleme", "components.Layout.Sidebar.requests": "<PERSON><PERSON><PERSON><PERSON>", "components.Layout.Sidebar.settings": "<PERSON><PERSON><PERSON>", "components.Layout.Sidebar.users": "Util<PERSON><PERSON><PERSON>", "components.Layout.UserDropdown.myprofile": "Profil", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {commit} altul {commits}} în spate", "components.IssueDetails.allepisodes": "Toate epis<PERSON><PERSON><PERSON>", "components.IssueDetails.episode": "Episod {episodeNumber}", "components.IssueDetails.issuepagetitle": "Problemă", "components.IssueDetails.openedby": "#{issueId} deschis {relativeTime} de c<PERSON><PERSON> {username}", "components.IssueDetails.openin4karr": "Deschide în 4K {arr}", "components.AirDateBadge.airedrelative": "Difuzat la {relativeTime}", "components.AirDateBadge.airsrelative": "Se va difuza la {relativeTime}", "components.IssueList.issues": "Probleme", "components.IssueList.showallissues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> toate problemele", "components.IssueModal.CreateIssueModal.allseasons": "Toate sezoanele", "components.IssueModal.CreateIssueModal.reportissue": "Raportați o problemă", "components.IssueModal.CreateIssueModal.toastviewissue": "<PERSON><PERSON><PERSON><PERSON> problema", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "Solicitări de Seriale", "components.Layout.UserDropdown.requests": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueList.IssueItem.issuetype": "Tip", "components.IssueList.sortModified": "Ultima modificare", "components.Discover.emptywatchlist": "Media adăugată în <PlexWatchlistSupportLink>Lista de Urmărire Plex</PlexWatchlistSupportLink> va apărea aici.", "components.IssueDetails.closeissueandcomment": "Închide cu comentariu", "components.IssueDetails.deleteissue": "Șterge problema", "components.IssueDetails.nocomments": "<PERSON><PERSON><PERSON> comentariu.", "components.IssueModal.CreateIssueModal.allepisodes": "Toate epis<PERSON><PERSON><PERSON>", "components.IssueList.sortAdded": "<PERSON><PERSON> mai recente", "components.Layout.SearchInput.searchPlaceholder": "Căutați Filme și Seriale", "components.IssueModal.CreateIssueModal.validationMessageRequired": "Trebuie să furnizați o descriere", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Solicitări de Filme", "components.Layout.UserDropdown.settings": "<PERSON><PERSON><PERSON>", "components.Layout.UserDropdown.signout": "Deconectare", "components.Layout.VersionStatus.outofdate": "Expirat", "components.Login.loginerror": "A apărut o eroare în timp ce încercam să vă conectați.", "components.ManageSlideOver.manageModalAdvanced": "Avansat", "components.ManageSlideOver.movie": "film", "components.ManageSlideOver.openarr": "Deschide în {arr}", "components.NotificationTypeSelector.adminissueresolvedDescription": "Primiți notificări când problemele sunt rezolvate de alți utilizatori.", "components.NotificationTypeSelector.issuecomment": "<PERSON><PERSON><PERSON><PERSON>", "components.NotificationTypeSelector.issuereopenedDescription": "Trimiteți notificări când problemele sunt redeschise.", "components.MovieDetails.digitalrelease": "Lansare Digitală", "components.MovieDetails.originaltitle": "Titlu Original", "components.MovieDetails.overview": "Prezentare Generală", "components.MovieDetails.recommendations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.releasedate": "{releaseCount, plural, one {Data de Lansare} other {Date de Lansare}}", "components.MovieDetails.revenue": "<PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.runtime": "{minutes} minute", "components.MovieDetails.showless": "<PERSON><PERSON><PERSON>", "components.MovieDetails.studio": "{studioCount, plural, one {Studio} other {Studiouri}}", "components.MovieDetails.theatricalrelease": "Lansare la Cinema", "components.MovieDetails.viewfullcrew": "Vizualizați Echipa Completă", "components.MovieDetails.streamingproviders": "În Prezent se Difuzează pe", "components.NotificationTypeSelector.issuecommentDescription": "Trimiteți notificări când problemele primesc comentarii noi.", "components.NotificationTypeSelector.issuecreated": "Problemă Raportată", "components.NotificationTypeSelector.issueresolved": "Problemă Rezolvată", "components.NotificationTypeSelector.issueresolvedDescription": "Trimiteți notificări când <PERSON>ele sunt rezolvate.", "components.NotificationTypeSelector.mediaAutoApproved": "Solicitare Aprobată Automat", "components.Login.email": "Adresă de Mail", "components.Login.forgotpassword": "Ai uitat parola?", "components.ManageSlideOver.manageModalNoRequests": "<PERSON><PERSON> solicit<PERSON>.", "components.ManageSlideOver.manageModalTitle": "Administrează {mediaType}", "components.ManageSlideOver.manageModalRequests": "<PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.overviewunavailable": "Prezentare Generală Indisponibilă.", "components.NotificationTypeSelector.issuecreatedDescription": "Trimiteți notificări când sunt raportate probleme.", "components.NotificationTypeSelector.issuereopened": "Problemă Redeschisă", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {Sezon} other {Sezoane}}", "components.Login.signinwithplex": "Folosește contul tău Plex", "components.Login.validationemailrequired": "Trebuie să furnizați o adresă de e-mail validă", "components.ManageSlideOver.manageModalIssues": "Probleme Deschise", "components.ManageSlideOver.manageModalClearMedia": "<PERSON><PERSON><PERSON><PERSON>", "components.ManageSlideOver.manageModalMedia": "Media", "components.ManageSlideOver.manageModalMedia4k": "Media 4K", "components.ManageSlideOver.mark4kavailable": "Marcați ca Disponibil în 4K", "components.ManageSlideOver.markallseasons4kavailable": "Marcați Toate Sezoanele ca fiind Disponibile în 4K", "components.ManageSlideOver.markallseasonsavailable": "Marcați Toate Sezoanele ca Disponibile", "components.ManageSlideOver.markavailable": "Marcați ca Disponibil", "components.MovieDetails.originallanguage": "Limbă Originală", "components.MovieDetails.showmore": "<PERSON><PERSON><PERSON>", "components.MovieDetails.similar": "Tit<PERSON><PERSON>e", "components.MovieDetails.physicalrelease": "<PERSON><PERSON><PERSON>", "components.MovieDetails.managemovie": "Gestionați Filmul", "components.MovieDetails.reportissue": "Raportează o Problemă", "components.Login.password": "Pa<PERSON><PERSON>", "components.Login.signin": "Autentificare", "components.Login.signingin": "Se autentifică…", "components.Login.signinheader": "Autentifică-te pentru a continua", "components.Login.signinwithoverseerr": "Folosește contul tău pentru {applicationTitle}", "components.Login.validationpasswordrequired": "Trebuie să furnizați o parolă", "components.MovieDetails.markavailable": "Marcați ca Disponibil", "components.MovieDetails.rtaudiencescore": "Scorul Publicului pe Rotten Tomatoes", "components.MovieDetails.rtcriticsscore": "Tomatometru pe Rotten Tomatoes", "components.MovieDetails.tmdbuserscore": "Scor Utilizatori pe TMDB", "components.ManageSlideOver.pastdays": "Acum {days, number} Zile", "components.ManageSlideOver.opentautulli": "Deschide în <PERSON>", "components.ManageSlideOver.tvshow": "seriale", "components.MediaSlider.ShowMoreCard.seemore": "<PERSON><PERSON><PERSON>", "components.MovieDetails.MovieCast.fullcast": "Distribuție Completă", "components.MovieDetails.MovieCrew.fullcrew": "Echipaj Complet", "components.MovieDetails.budget": "Buget", "components.MovieDetails.cast": "Distribuție", "components.MovieDetails.mark4kavailable": "Marcați ca Disponibil în 4K", "components.NotificationTypeSelector.adminissuereopenedDescription": "Primiți notificări când problemele sunt redeschise de alți utilizatori.", "components.MovieDetails.watchtrailer": "Vizionați Trailerul", "components.NotificationTypeSelector.adminissuecommentDescription": "Primiți notificări când alți utilizatori comentează la probleme.", "components.ManageSlideOver.downloadstatus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.ManageSlideOver.openarr4k": "Deschide în {arr} 4k", "components.ManageSlideOver.manageModalClearMediaWarning": "* Aceasta va elimina ireversibil toate datele pentru {mediaType}, inclusiv orice solicitare. Dacă acest articol există în biblioteca dvs. Plex, informațiile media vor fi recreate în timpul următoarei scanări.", "components.NotificationTypeSelector.userissuereopenedDescription": "Primiți notificări când problemele pe care le-ați raportat sunt redeschise.", "components.PermissionEdit.advancedrequestDescription": "Acordați permisiunea de a modifica opțiunile avansate de solicitare media.", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "Trimiteți notificări atunci când utilizatorii trimit noi solicitări media care sunt aprobate automat.", "components.NotificationTypeSelector.mediaavailable": "Solicitare Disponibilă", "components.NotificationTypeSelector.mediaavailableDescription": "Trimiteți notificări când solicitările media devin disponibile.", "components.NotificationTypeSelector.mediaapproved": "Solicitare Aprobată", "components.NotificationTypeSelector.mediaapprovedDescription": "Trimiteți notificări când solicitările media sunt aprobate manual.", "components.NotificationTypeSelector.mediadeclined": "Solicitare Refuzată", "components.PermissionEdit.admin": "Admin", "components.NotificationTypeSelector.usermediaapprovedDescription": "Primiți notificări când solicitările dvs. media sunt aprobate.", "components.NotificationTypeSelector.usermediaavailableDescription": "Primiți notificări când solicitările dvs. media devin disponibile.", "components.PermissionEdit.adminDescription": "Acces de administrator complet. Ocolește toate celelalte verificări ale permisiunilor.", "components.PermissionEdit.advancedrequest": "Solicitări Avansate", "components.NotificationTypeSelector.mediaautorequested": "Solicitare Trimisă Automat", "components.NotificationTypeSelector.mediaautorequestedDescription": "Primiți notificări atunci când noi solicitări media sunt trimise automat pentru articole din Lista dvs. de Urmărire Plex.", "components.NotificationTypeSelector.mediafailed": "Procesarea Solicitării Eșuată", "components.NotificationTypeSelector.mediafailedDescription": "Trimiteți notificări atunci când solicitările media nu pot fi adăugate la Radarr sau Sonarr.", "components.NotificationTypeSelector.mediarequested": "Solicitarea Așteptă Aprobare", "components.NotificationTypeSelector.mediarequestedDescription": "Trimiteți notificări atunci când utilizatorii trimit noi solicitări media care necesită aprobare.", "components.NotificationTypeSelector.notificationTypes": "Tipuri de Notificări", "components.NotificationTypeSelector.userissuecommentDescription": "Primiți notificări când problemele pe care le-ați raportat primesc comentarii noi.", "components.NotificationTypeSelector.userissuecreatedDescription": "Primiți notificări când alți utilizatori raportează probleme.", "components.PermissionEdit.autoapprove": "<PERSON>obare Automată", "components.PermissionEdit.autoapprove4k": "Aprobare Automată 4K", "components.PermissionEdit.autoapprove4kDescription": "Acordați aprobare automată pentru toate solicitările media 4K.", "components.PermissionEdit.autoapprove4kMovies": "Aprobare Automată a Filmelor 4K", "components.PermissionEdit.autoapprove4kMoviesDescription": "Acordați aprobare automată pentru solicitările de filme 4K.", "components.PermissionEdit.autoapprove4kSeries": "Aprobare Automată a Serialelor 4K", "components.PermissionEdit.autoapproveDescription": "Acordați aprobare automată pentru toate solicitările media non-4K.", "components.PermissionEdit.autoapproveMovies": "Aprobare Automată a Filmelor", "components.NotificationTypeSelector.mediadeclinedDescription": "Trimiteți notificări atunci când solicitările media sunt refuzate.", "components.NotificationTypeSelector.usermediarequestedDescription": "Primiți notificări atunci când alți utilizatori trimit noi solicitări media care necesită aprobare.", "components.NotificationTypeSelector.usermediafailedDescription": "Primiți notificări când solicitările media nu pot fi adăugate la Radarr sau Sonarr.", "components.PermissionEdit.autoapproveSeriesDescription": "Acordați aprobare automată pentru solicitările de seriale non-4K.", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "Primiți notificări când alți utilizatori trimit solicitări media noi, care sunt aprobate automat.", "components.NotificationTypeSelector.usermediadeclinedDescription": "Primiți notificări când solicitările dvs. media sunt refuzate.", "components.PermissionEdit.autoapprove4kSeriesDescription": "Acordați aprobare automată pentru solicitările de seriale 4K.", "components.PermissionEdit.autoapproveMoviesDescription": "Acordați aprobare automată pentru solicitările de filme non-4K.", "components.PermissionEdit.autoapproveSeries": "Aprobare Automată a Serialelor", "components.PermissionEdit.autorequestDescription": "Acordați permisiunea de a trimite automat solicitări pentru conținut media non-4K prin Lista de Urmărire Plex.", "components.PermissionEdit.autorequest": "Solicitare Automată", "components.NotificationTypeSelector.userissueresolvedDescription": "Primiți notificări când problemele pe care le-ați raportat sunt rezolvate.", "components.PermissionEdit.autorequestMovies": "Solicitați Automat Filme", "components.PermissionEdit.autorequestMoviesDescription": "Acordați permisiunea de a trimite automat solicitări pentru filme non-4K prin Lista de Urmărire Plex.", "components.MovieDetails.productioncountries": "Produc<PERSON>ie {countryCount, plural, one {<PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON>}}", "components.PermissionEdit.request": "Solicitați", "components.PermissionEdit.request4kDescription": "Acordați permisiunea de a trimite solicitări pentru conținut media 4K.", "components.PermissionEdit.managerequestsDescription": "Acordați permisiunea de a gestiona solicitările media. Toate solicitările făcute de un utilizator cu această permisiune vor fi aprobate automat.", "components.PermissionEdit.request4k": "Solicitați 4K", "components.PermissionEdit.request4kTv": "Solicitați Seria TV în 4K", "components.Layout.VersionStatus.streamstable": "<PERSON><PERSON><PERSON><PERSON>", "components.PermissionEdit.request4kMoviesDescription": "Acordați permisiunea de a trimite solicitări pentru filme 4K.", "components.PermissionEdit.createissuesDescription": "Acordați permisiunea de a raporta probleme media.", "components.PermissionEdit.autorequestSeries": "Solicită Automat Seriale TV", "components.PermissionEdit.autorequestSeriesDescription": "Acordați permisiunea de a trimite automat solicitări pentru seriale tv non-4K prin Lista de Urmărire Plex.", "components.PermissionEdit.createissues": "Raportați Probleme", "components.PermissionEdit.manageissuesDescription": "Acordați permisiunea de a gestiona problemele media.", "components.PermissionEdit.managerequests": "Gestionați Solicitările", "components.PermissionEdit.request4kMovies": "Solicitați Filme 4K", "components.Layout.VersionStatus.streamdevelop": "<PERSON><PERSON><PERSON><PERSON> Dezvoltat", "components.PermissionEdit.manageissues": "Gestionarea Problemelor", "components.ManageSlideOver.playedby": "<PERSON><PERSON><PERSON>", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> {playCount, plural, one {rulare} other {rul<PERSON><PERSON>}}", "components.ManageSlideOver.alltime": "Dintotdeauna", "components.Discover.CreateSlider.addSlider": "<PERSON><PERSON><PERSON>", "components.Discover.CreateSlider.addcustomslider": "Crează Slider <PERSON>", "components.Discover.CreateSlider.addfail": "Nu s-a putut crea un nou slider.", "components.Discover.CreateSlider.addsuccess": "S-a creat un nou slider și s-au salvat setările de personalizare a descoperirii.", "components.Discover.CreateSlider.editSlider": "Modifică Slider", "components.Discover.CreateSlider.editfail": "Nu s-a putut modifica sliderul.", "components.Discover.CreateSlider.editsuccess": "Sliderul modificat și setările de personalizare pentru descoperire salvate.", "components.Discover.CreateSlider.needresults": "Trebuie să aveți cel puțin 1 rezultat.", "components.Discover.CreateSlider.nooptions": "Fără rezultate.", "components.Discover.CreateSlider.providetmdbgenreid": "Furnizați un ID de gen de la TMDB", "components.Discover.CreateSlider.providetmdbkeywordid": "Furnizați un ID de cuvânt cheie de la TMDB", "components.Discover.CreateSlider.providetmdbnetwork": "Furnizați ID-ul rețelei TMDB", "components.Discover.CreateSlider.providetmdbsearch": "Furnizați o interogare de căutare", "components.Discover.CreateSlider.providetmdbstudio": "Furnizați un ID de studio de pe TMDB", "components.Discover.CreateSlider.searchGenres": "Căutați genuri…", "components.Discover.CreateSlider.searchKeywords": "Căutați cuvinte cheie…", "components.Discover.CreateSlider.searchStudios": "Căuta<PERSON><PERSON> studiouri…", "components.Discover.CreateSlider.slidernameplaceholder": "<PERSON><PERSON> Slider", "components.Discover.PlexWatchlistSlider.emptywatchlist": "Media adăugată la <PlexWatchlistSupportLink>Lista de urmărire Plex</PlexWatchlistSupportLink> va apărea aici.", "components.Discover.DiscoverMovieKeyword.keywordMovies": "{keywordTitle} Filme", "components.Discover.DiscoverTvKeyword.keywordSeries": "Seriale care conțin {keywordTitle}", "components.Discover.networks": "<PERSON><PERSON><PERSON>", "components.Discover.resetfailed": "A apărut o eroare la resetarea setărilor de personalizare a descoperirii.", "components.Discover.resetsuccess": "Resetat cu succes setările de personalizare a descoperirii.", "components.Discover.resettodefault": "Resetare la valorile implicite", "components.Discover.CreateSlider.starttyping": "Începe s<PERSON>zi pentru a căuta.", "components.Discover.CreateSlider.validationTitlerequired": "Trebuie să furnizați un titlu.", "components.Discover.FilterSlideover.clearfilters": "Ștergeți Filtrele Active", "components.Discover.FilterSlideover.genres": "<PERSON><PERSON>", "components.Discover.resetwarning": "<PERSON><PERSON><PERSON><PERSON><PERSON> toate sliderele la valoarea implicită. Acest lucru va șterge și orice slider personalizat!", "components.Discover.DiscoverMovies.activefilters": "{count, plural, un {# Active Filter} alte {# Active Filters}}", "components.Discover.DiscoverMovies.discovermovies": "Filme", "components.Discover.DiscoverMovies.sortPopularityAsc": "Popularitate Crescătoare", "components.Discover.DiscoverMovies.sortPopularityDesc": "Popularitate Descrescătoare", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "Data Lansării <PERSON>", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "Data Lansării <PERSON>", "components.Discover.DiscoverMovies.sortTitleAsc": "Titlu (A-Z) Crescător", "components.Discover.DiscoverMovies.sortTitleDesc": "<PERSON><PERSON><PERSON> (Z-A) Descrescător", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "Evaluare TMDB Crescător", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "Evaluare TMDB Descrescător", "components.Discover.DiscoverSliderEdit.deletefail": "Sliderul nu a fost șters.", "components.Discover.DiscoverSliderEdit.deletesuccess": "Sliderul a fost șters.", "components.Discover.DiscoverSliderEdit.enable": "Comutați Vizibilitatea", "components.Discover.DiscoverSliderEdit.remove": "Elimină", "components.Discover.DiscoverTv.activefilters": "{count, plural, un {# Active Filter} alte {# Active Filters}}", "components.Discover.DiscoverTv.discovertv": "Seriale", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "Prima Dată de Difuzare Ascendent", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "Prima Dată de Difuzare Descendent", "components.Discover.DiscoverTv.sortPopularityAsc": "Popularitate Crescătoare", "components.Discover.DiscoverTv.sortPopularityDesc": "Popularitate Descrescătoare", "components.Discover.DiscoverTv.sortTitleAsc": "Titlu (A-Z) Crescător", "components.Discover.DiscoverTv.sortTitleDesc": "<PERSON><PERSON><PERSON> (Z-A) Descrescător", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "Evaluare TMDB Crescător", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "Evaluare TMDB Descrescător", "components.Discover.FilterSlideover.activefilters": "{count, plural, un {# Active Filter} alte {# Active Filters}}", "components.Discover.FilterSlideover.filters": "Filtre", "components.Discover.FilterSlideover.firstAirDate": "Prima Dată de Difuzare", "components.Discover.FilterSlideover.from": "De <PERSON>", "components.Discover.FilterSlideover.keywords": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.originalLanguage": "Limbă Originală", "components.Discover.FilterSlideover.ratingText": "<PERSON><PERSON><PERSON><PERSON> {minValue} și {maxValue}", "components.Discover.FilterSlideover.releaseDate": "<PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.runtime": "<PERSON><PERSON>", "components.Discover.FilterSlideover.runtimeText": "{minValue}-{maxValue} minute de rulare", "components.Discover.FilterSlideover.studio": "Studio", "components.Discover.FilterSlideover.tmdbuserscore": "Scorul Utilizatorilor pe TMDB", "components.Discover.customizediscover": "Personalizați Discoperirea", "components.Discover.moviegenres": "Genuri Film", "components.Discover.stopediting": "Opriți Modificarea", "components.Discover.createnewslider": "Creați un Nou Slider", "components.Discover.FilterSlideover.streamingservices": "Servicii de Streaming", "components.Discover.FilterSlideover.to": "La", "components.Discover.PlexWatchlistSlider.plexwatchlist": "Listă Urmărite pe Plex", "components.Discover.RecentlyAddedSlider.recentlyAdded": "Recent Adăugate", "components.Discover.studios": "<PERSON><PERSON>", "components.Discover.CreateSlider.validationDatarequired": "Trebuie să furnizați o valoare a datelor.", "components.Layout.Sidebar.browsetv": "Seriale", "components.Layout.Sidebar.browsemovies": "Filme", "components.Discover.tmdbmoviekeyword": "Cuvânt cheie al filmului de pe TMDB", "components.Discover.FilterSlideover.tmdbuservotecount": "Numărul de voturi ale utilizatorilor pe TMDB", "components.Discover.FilterSlideover.voteCount": "<PERSON><PERSON><PERSON><PERSON> de voturi <PERSON> {minValue} și {maxValue}", "components.Discover.tmdbnetwork": "Rețea de pe TMDB", "components.Discover.tmdbsearch": "Căutare pe TMDB", "components.Discover.tmdbmoviegenre": "Gen Film de pe TMDB", "components.Discover.tmdbmoviestreamingservices": "Servicii Streaming Filme de pe TMDB", "components.Discover.tmdbtvgenre": "Gen Seriale de pe TMDB", "components.Selector.searchStudios": "Caută studiouri…", "components.TvDetails.network": "{networkCount, plural, one {<PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON>}}", "components.RequestModal.AdvancedRequester.animenote": "* Acest serial este un anime.", "components.DownloadBlock.formattedTitle": "{title}: <PERSON><PERSON> {seasonNumber} Episod {episodeNumber}", "components.Discover.tvgenres": "<PERSON><PERSON>", "components.Discover.tmdbtvkeyword": "Cuvânt Cheie Seriale de pe TMDB", "components.Discover.tmdbtvstreamingservices": "Servicii Streaming TV de pe TMDB", "components.PermissionEdit.requestTv": "Solicită Seria", "components.UserProfile.UserSettings.UserGeneralSettings.region": "Regiune Descoperire", "components.Discover.updatefailed": "A apărut o eroare la actualizarea setărilor de personalizare a descoperirii.", "components.Discover.updatesuccess": "Setări de personalizare a descoperirii actualizate.", "components.Discover.tmdbstudio": "Studio de pe TMDB", "components.PermissionEdit.requestDescription": "Acordați permisiunea de a face cereri pentru seriale non-4K.", "components.MovieDetails.imdbuserscore": "Scor Utilizatori IMDB", "components.PermissionEdit.request4kTvDescription": "Acordați permisiunea de a face cereri pentru seriale 4K.", "components.PermissionEdit.requestMovies": "Solicită Filme", "components.PermissionEdit.requestTvDescription": "Acordați permisiunea de a trimite solicitări pentru seriale non-4K.", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "Limbă Descoperi<PERSON>", "components.Settings.SettingsMain.originallanguage": "Limbă Descoperi<PERSON>", "components.PermissionEdit.usersDescription": "Acordați permisiunea de a gestiona utilizatorii. Utilizatorii cu această permisiune nu pot modifica utilizatorii cu sau acorda privilegiul de administrator.", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{movies} per {quotaDays} {days}</quotaUnits>", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.RequestBlock.seasons": "{seasonCount, plural, un {Season} alte {Seasons}}", "components.PermissionEdit.requestMoviesDescription": "Acordați permisiunea de a trimite solicitări pentru filme non-4K.", "components.PermissionEdit.viewissuesDescription": "Acordați permisiunea de a vizualiza problemele media raportate de alți utilizatori.", "components.PermissionEdit.viewwatchlistsDescription": "Acordați permisiunea de a vizualiza listele de urmărire Plex ale altor utilizatori.", "components.QuotaSelector.seasons": "{count, plural, un {season} alte {seasons}}", "components.RequestBlock.requestoverrides": "Înlocuiri Solicitate", "components.PersonDetails.ascharacter": "ca {character}", "components.PermissionEdit.viewrecentDescription": "Acordați permisiunea de a vizualiza lista cu conținut media adăugat recent.", "components.PermissionEdit.viewrequests": "Vizualizați Solicitările", "components.PermissionEdit.viewrecent": "<PERSON><PERSON><PERSON>", "components.PermissionEdit.viewrequestsDescription": "Acordați permisiunea de a vizualiza solicitările media trimise de alți utilizatori.", "components.RequestBlock.rootfolder": "<PERSON><PERSON>", "components.PermissionEdit.viewwatchlists": "Vizualizați Listele de Urmărire Plex", "components.PersonDetails.appearsin": "Aparențe", "components.PersonDetails.crewmember": "Echipa", "components.PersonDetails.birthdate": "<PERSON><PERSON><PERSON><PERSON> {birthdate}", "components.QuotaSelector.movies": "{count, plural, un {movie} alte {movies}}", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{seasons} per {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.unlimited": "Nelimitat", "components.RegionSelector.regionDefault": "Toate <PERSON><PERSON><PERSON>", "components.RequestBlock.languageprofile": "Limba de Profil", "components.RequestBlock.server": "Server Destinatar", "components.PermissionEdit.users": "Gestionare Utilizatori", "components.PermissionEdit.viewissues": "Vede<PERSON><PERSON>ele", "components.QuotaSelector.days": "{count, plural, o {day} alte {days}}", "components.PersonDetails.alsoknownas": "Cunoscut și ca: {names}", "components.RegionSelector.regionServerDefault": "Mod Implicit ({region})", "components.RequestBlock.delete": "Șterge Solicitarea", "components.RequestBlock.edit": "Modifică Solicitarea", "components.RequestBlock.approve": "Aprobă Solicitarea", "components.RequestBlock.decline": "Respinge Solicitarea", "components.RequestBlock.requestedby": "Solicitat de", "components.RequestButton.approve4krequests": "Aprobă {requestCount, plural, o {4K Request} alte {{requestCount} 4K Requests}}", "components.RequestBlock.lastmodifiedby": "Ultima Dată Modificat de", "components.RequestBlock.profilechanged": "Profil <PERSON>", "components.RequestBlock.requestdate": "Dată <PERSON>", "components.Layout.UserWarnings.emailRequired": "Este necesara o adresa de email.", "components.Layout.UserWarnings.passwordRequired": "Este necesara o parola.", "components.Login.credentialerror": "Numele de utilizator sau parola sunt incorecte.", "components.Login.emailtooltip": "Nu este necesar ca adresa ta sa fie asociata cu instanta ta {mediaServerName} .", "components.Login.save": "Adauga", "components.Login.signinwithjellyfin": "Foloseste-ti contul de {mediaServerName}", "components.Login.title": "Adauga email", "components.Login.username": "Nume utilizator", "components.Login.validationEmailFormat": "Adresa email invalida", "components.Login.validationemailformat": "Necesar email valid", "components.Login.validationhostformat": "Necesar URL valid", "components.Login.validationhostrequired": "{mediaServerName} URL necesar", "components.Login.validationusernamerequired": "Nume utilizator necesar", "components.MovieDetails.downloadstatus": "Status descarcare", "components.MovieDetails.openradarr": "Deschide film in Radarr", "components.MovieDetails.openradarr4k": "Deschide film in 4K Radarr", "components.MovieDetails.play": "Ruleaza pe {mediaServerName}", "components.MovieDetails.play4k": "Ruleaza 4k pe {mediaServerName}", "components.RequestButton.declinerequest": "Refuzați Solicitare", "components.RequestButton.declinerequest4k": "Refuzați Solicitare 4k", "components.Layout.UserWarnings.emailInvalid": "Adresa de email este invalida.", "components.Login.description": "Deoarece este prima ta authentificare in {applicationName}, este necesara sa introduci o adresa de email valida.", "components.Login.initialsigningin": "Se conecteaza…", "components.Login.saving": "Se adauga…", "components.RequestButton.approverequest": "<PERSON><PERSON><PERSON> cerere", "components.RequestButton.approverequest4k": "Aproba cerere 4k", "components.Login.initialsignin": "Conecteaza-te", "components.ManageSlideOver.removearr4k": "Elimina din 4K {arr}", "components.ManageSlideOver.manageModalRemoveMediaWarning": "* Acesta va elimina ireversibil {mediaType} din {arr}, incluzand toate fisierele asociate.", "components.Login.validationEmailRequired": "Trebuie sa introduci o adresa email", "components.ManageSlideOver.removearr": "Elimina din {arr}", "components.Login.invalidurlerror": "Conectarea la serverul {mediaServerName} nu a fost posibilă.", "components.RequestModal.AdvancedRequester.advancedoptions": "Avansat", "components.MovieDetails.removefromwatchlist": "Eliminați din lista de favorite", "components.RequestModal.QuotaDisplay.allowedRequests": "Vi se permite să solicitați <strong>{limit}</strong> {type} la <strong>{days}</strong> zile.", "components.RequestModal.approve": "Aprobați Solicitare", "components.RequestModal.errorediting": "Ceva nu a funcționat în cursul editării solicitării.", "components.RequestModal.selectseason": "Selectați Sezoane", "components.Settings.Notifications.NotificationsGotify.validationTypes": "Trebuie să selectați cel puțin un tip de notificare", "components.Login.validationHostnameRequired": "Trebuie să introduceți un hostname sau o adresă IP valide", "components.RequestModal.AdvancedRequester.languageprofile": "<PERSON><PERSON>", "components.RequestModal.QuotaDisplay.requiredquota": "Trebuie să aveți cel puțin <strong>{seasons}</strong> {seasons, plural, one {season request} other {season requests}} r<PERSON><PERSON>e pentru a putea trimite o solicitare pentru această serie.", "components.RequestModal.requestmovies": "Solicita<PERSON>i {count} {count, plural, one {Movie} alte {Movies}}", "components.ResetPassword.confirmpassword": "Confirmați Parola", "components.ResetPassword.requestresetlinksuccessmessage": "Un link de resetare a parolei va fi trimis la adresa de e-mail furnizată dacă aceasta este asociată cu un utilizator valid.", "components.ResetPassword.validationpasswordminchars": "Parola este prea scurtă;ar trebui să aibă minim 8 caractere", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "Setările de notificare Pushbullet nu au reușit să fie salvate.", "components.MovieDetails.watchlistDeleted": "<strong>{title}</strong> Eliminat cu succes din lista de favorite!", "components.RequestButton.decline4krequests": "Refuza<PERSON>i {requestCount, plural, one {4K Request} alte {{requestCount} 4K Requests}}", "components.RequestButton.declinerequests": "Refuza<PERSON>i {requestCount, plural, one {Request} alte {{requestCount} Requests}}", "components.RequestList.RequestItem.failedretry": "Ceva nu a funcționat în cursul reintroducerii solicitării.", "components.RequestModal.SearchByNameModal.notvdbiddescription": "Nu am reușit să găsim automat un rezultat pentru aceast serial. Vă rugăm să selectați mai jos rezultatul corect.", "components.RequestModal.edit": "Editați Solicitare", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "Setările de notificare Gotify nu au reușit să fie salvate.", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "Setările de notificare LunaSea au fost salvate cu success!", "components.RequestModal.QuotaDisplay.quotaLinkUser": "Puteți vizualiza un rezumat al limitelor de solicitare ale acestui utilizator pe pagina sa <ProfileLink>profile</ProfileLink>.", "components.Selector.showmore": "Afișeaz<PERSON> mai mult", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "Testul de notificare LunaSea a fost trimis!", "components.RequestCard.tmdbid": "ID TMDB", "components.RequestCard.tvdbid": "ID TheTVDB", "components.ResetPassword.email": "<PERSON><PERSON><PERSON>", "components.Login.adminerror": "Trebuie să utilizați un cont de administrator pentru a vă conecta.", "components.MovieDetails.watchlistSuccess": "<strong>{title}</strong> ad<PERSON>ugat cu succes la lista de favorite!", "components.Login.back": "Înapoi", "components.Login.enablessl": "Utilizați SSL", "components.Login.hostname": "{mediaServerName} URL", "components.Login.port": "Port", "components.Login.servertype": "Tip Server", "components.Login.urlBase": "Bază URL", "components.Login.validationPortRequired": "Trebuie s<PERSON> introduceți un număr de port valid", "components.Login.validationUrlBaseLeadingSlash": "Baza URL trebuie să aibă la început o bară oblică", "components.Login.validationUrlBaseTrailingSlash": "Baza URL nu trebuie să se termine cu o bară oblică", "components.Login.validationUrlTrailingSlash": "URL-ul nu trebuie să se termine cu o bară oblică", "components.Login.validationservertyperequired": "Selectați un tip de server", "components.MovieDetails.addtowatchlist": "Adăugați la lista de favorite", "components.MovieDetails.watchlistError": "Ceva nu a funcționat, încercați din nou.", "components.RequestButton.approverequests": "Aprobați {requestCount, plural, one {Request} other {{requestCount} Requests}}", "components.RequestButton.requestmore": "Solicitați mai multe", "components.RequestButton.requestmore4k": "Solicitați mai multe în 4K", "components.RequestButton.viewrequest": "Vizualizați Solicitarea", "components.RequestCard.approverequest": "Aprobați Solicitarea", "components.RequestCard.cancelrequest": "Anulați Solicitarea", "components.RequestCard.declinerequest": "Refuzați Solicitarea", "components.RequestButton.viewrequest4k": "Vizualizați Solicitare 4k", "components.RequestCard.deleterequest": "Ștergeți Solicitarea", "components.RequestCard.editrequest": "Editați Solicitarea", "components.RequestCard.failedretry": "Ceva nu a funcționat în cursul reintroducerii solicitării.", "components.RequestCard.mediaerror": "Nu a fost găsit", "components.RequestCard.seasons": "{seasonCount, plural, one {Season} alte {Seasons}}", "components.RequestCard.unknowntitle": "Titlu Necunoscut", "components.RequestList.RequestItem.cancelRequest": "Anulați Solicitarea", "components.RequestList.RequestItem.deleterequest": "Ștergeți Solicitarea", "components.RequestList.RequestItem.editrequest": "Editați Solicitarea", "components.RequestList.RequestItem.mediaerror": "{mediaType} Nu a fost găsit", "components.RequestList.RequestItem.modified": "Modificat", "components.RequestList.RequestItem.modifieduserdate": "{date} de la {user}", "components.RequestList.RequestItem.profileName": "Profil", "components.RequestList.RequestItem.requested": "Solicitat", "components.RequestList.RequestItem.requesteddate": "Solicitat", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {Season} alte {Seasons}}", "components.RequestList.RequestItem.tmdbid": "ID TMDB", "components.RequestList.RequestItem.tvdbid": "ID TheTVDB", "components.RequestList.RequestItem.unknowntitle": "Titlu Necunoscut", "components.RequestList.requests": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestList.showallrequests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> toate Sol<PERSON><PERSON><PERSON><PERSON>", "components.RequestList.sortAdded": "<PERSON><PERSON>", "components.RequestList.sortModified": "Ultima Modificare", "components.RequestModal.AdvancedRequester.default": "{name} (<PERSON><PERSON><PERSON>)", "components.RequestModal.AdvancedRequester.destinationserver": "Server de destinație", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.RequestModal.AdvancedRequester.notagoptions": "<PERSON><PERSON><PERSON><PERSON> etiche<PERSON>.", "components.RequestModal.AdvancedRequester.qualityprofile": "Profil <PERSON>", "components.RequestModal.AdvancedRequester.requestas": "Solicitați ca", "components.RequestModal.AdvancedRequester.rootfolder": "Folder Root", "components.RequestModal.AdvancedRequester.selecttags": "Selectați etichete", "components.RequestModal.AdvancedRequester.tags": "Etichete", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "Acest utilizator poate solicita <strong>{limit}</strong> {type} la <strong>{days}</strong> zile.", "components.RequestModal.QuotaDisplay.movie": "film", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {movie} alte {movies}}", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "Nu au mai rămas suficiente solicitări de sezon", "components.RequestModal.QuotaDisplay.quotaLink": "Puteți vizualiza un rezumat al limitelor solicitărilor dvs. pe <ProfileLink>pagina de profil</ProfileLink>.", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {No} alte {<strong>#</strong>}} {type} {remaining, plural, one {request} alte {requests}} r<PERSON>mase", "components.RequestModal.QuotaDisplay.requiredquotaUser": "Acest utilizator trebuie să aibă cel puțin<strong>{seasons}</strong> {seasons, plural, one {season request} alte {season requests}}r<PERSON><PERSON>e pentru a putea trimite o solicitare pentru această serie.", "components.RequestModal.QuotaDisplay.season": "sezon", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {season} alte {seasons}}", "components.RequestModal.SearchByNameModal.nomatches": "Nu am putut găsi un rezultat pentru aceast serial.", "components.RequestModal.alreadyrequested": "Solicitat Deja", "components.RequestModal.autoapproval": "<PERSON>obare Automată", "components.RequestModal.cancel": "An<PERSON><PERSON><PERSON> Solicitare", "components.RequestModal.numberofepisodes": "# de Episoade", "components.RequestModal.pending4krequest": "Solicitare 4K în așteptare", "components.RequestModal.pendingapproval": "Solicitarea dvs. este în curs de aprobare.", "components.RequestModal.pendingrequest": "Solicitare în așteptare", "components.RequestModal.requestApproved": "Solicit<PERSON>a pentru <strong>{title}</strong> a fost aprobată!", "components.RequestModal.requestCancel": "Solicit<PERSON>a pentru <strong>{title}</strong> a fost anulată.", "components.RequestModal.requestSuccess": "<strong>{title}</strong> a fost aprobat cu success!", "components.RequestModal.requestadmin": "Această solicitare va fi aprobată automat.", "components.RequestModal.requestcancelled": "Solicit<PERSON>a pentru <strong>{title}</strong> anula<PERSON><PERSON>.", "components.RequestModal.requestcollection4ktitle": "Solicitare Colecție în 4K", "components.RequestModal.requestcollectiontitle": "Solicitați Colecție", "components.RequestModal.requestedited": "Solicit<PERSON>a pentru <strong>{title}</strong> editată cu success!", "components.RequestModal.requesterror": "Ceva nu a funcționat în cursul trimiterii solicitării.", "components.RequestModal.requestfrom": "Solicitarea lui {username} este în curs de aprobare.", "components.RequestModal.requestmovie4ktitle": "Solicitați Filmul în 4K", "components.RequestModal.requestmovies4k": "Solicitati {count} {count, plural, one {Movie} late {Movies}} în 4K", "components.RequestModal.requestmovietitle": "Solicitați Film", "components.RequestModal.requestseries4ktitle": "Solicitați Serial în 4K", "components.RequestModal.requestseriestitle": "Solicitați Serial", "components.RequestModal.season": "Sezon", "components.RequestModal.seasonnumber": "Sezonul {number}", "components.RequestModal.selectmovies": "Selectați FIlm(e)", "components.ResetPassword.emailresetlink": "Link Recuperare Email", "components.ResetPassword.gobacklogin": "Reveniți la pagina de înscriere", "components.ResetPassword.password": "Pa<PERSON><PERSON>", "components.ResetPassword.passwordreset": "Resetare <PERSON>", "components.ResetPassword.resetpassword": "Resetați parola", "components.ResetPassword.resetpasswordsuccessmessage": "<PERSON><PERSON><PERSON> resetată cu success!", "components.ResetPassword.validationemailrequired": "Trebuie să furnizați o adresă de e-mail validă", "components.ResetPassword.validationpasswordmatch": "Parolele trebuie să fie identice", "components.ResetPassword.validationpasswordrequired": "Trebuie să furnizați o parolă", "components.Search.search": "<PERSON><PERSON><PERSON><PERSON>", "components.Search.searchresults": "Rezultate Căutare", "components.Selector.canceled": "<PERSON><PERSON><PERSON>", "components.Selector.ended": "Terminat", "components.Selector.inProduction": "In Productie", "components.Selector.nooptions": "Fără rezultate.", "components.Selector.pilot": "Pilot", "components.Selector.planned": "Planificat", "components.Selector.returningSeries": "Serial care revine", "components.Selector.searchGenres": "Selectați genurile…", "components.Selector.searchKeywords": "Cauta<PERSON><PERSON> cuvinte cheie…", "components.Selector.searchStatus": "Selectați status...", "components.Selector.showless": "Afișează mai puțin", "components.Settings.Notifications.NotificationsGotify.agentenabled": "Activați Agentul", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Setările de notificare Gotify au fost salvate cu success!", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "Setările de notificare Gotify nu au reușit să fie trimise.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Trimitere test notificare Gotify…", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Testul de notificare Gotify a fost trimis!", "components.Settings.Notifications.NotificationsGotify.token": "Token Aplicație", "components.Settings.Notifications.NotificationsGotify.url": "Server URL", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "Trebuie să furnizați un token de aplicație", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "Trebuie să furnizați un URL valid", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "URL-ul nu trebuie să se termine cu o bară oblică", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "Activați Agentul", "components.Settings.Notifications.NotificationsLunaSea.profileName": "Nume Profil", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "Necesar doar dacă nu este folosit profilul <code>default</code>", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "Setările de notificare LunaSea nu au reușit să fie salvate.", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "Setările de notificare LunaSea nu au reușit să fie trimise.", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "Trimitere test notificare LunaSea…", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "Trebuie să selectați cel puțin un tip de notificare", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "Trebuie să furnizați un URL valid", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "URL Webhook", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "Token Acces", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "Etichetă Canal", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Setările de notificare Pushbullet au fost salvate cu success!", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "Trimitere test notificare Pushbullet…", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "Activați Agentul", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Testul de notificare Pushbullet a fost trimis!", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "Trebuie să furnizați un token de acces", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "Trebuie să selectați cel puțin un tip de notificare", "components.Settings.Notifications.NotificationsPushover.accessToken": "Token API Aplicație", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicationRegistrationLink>Înregistrează o aplicație</ApplicationRegistrationLink> pentru a fi utilizată cu Je<PERSON>rr", "components.Settings.Notifications.NotificationsPushover.agentenabled": "Activați Agentul", "components.Settings.Notifications.NotificationsPushover.deviceDefault": "Dispozitiv Implicit", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "Setările de notificare Pushover nu au reușit să fie salvate.", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Setările de notificare Pushover au fost salvate cu success!", "components.Settings.Notifications.NotificationsPushover.sound": "Sunet Notificare", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "Trimitere test notificare Pushover…", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Testul de notificare Pushover a fost trimis!", "components.Settings.Notifications.NotificationsPushover.userToken": "Cheie Utilizator sau Group", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "Trebuie să furnizați un token de acces valid", "components.Settings.Notifications.NotificationsPushover.validationTypes": "Trebuie să selectați cel puțin un tip de notificare", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "Trebuie să furnizați o cheie de user sau group validă", "components.Settings.Notifications.NotificationsSlack.agentenabled": "Activați Agentul", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "Setările de notificare Slack nu au reușit să fie salvate.", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Setările de notificare Slack au fost salvate cu success!", "components.Discover.FilterSlideover.status": "Status"}