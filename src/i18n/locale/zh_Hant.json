{"components.Settings.Notifications.webhookUrl": "Webhook 網址", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "Webhook 網址", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "Webhook 網址", "components.Settings.SonarrModal.apiKey": "應用程式密鑰", "components.Settings.RadarrModal.apiKey": "應用程式密鑰", "components.Settings.Notifications.senderName": "發件人名稱", "components.Settings.Notifications.emailsender": "發件人電子郵件地址", "components.Settings.Notifications.authUser": "SMTP 使用者", "components.Settings.Notifications.authPass": "SMTP 密碼", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "啟用通知", "components.Settings.Notifications.agentenabled": "啟用通知", "components.Settings.Notifications.NotificationsSlack.agentenabled": "啟用通知", "components.Settings.Notifications.NotificationsPushover.agentenabled": "啟用通知", "components.Settings.Notifications.validationSmtpPortRequired": "請輸入有效的通訊埠", "components.Settings.Notifications.validationSmtpHostRequired": "請輸入有效的主機名稱或 IP 位址", "components.Settings.Notifications.smtpPort": "SMTP 通訊埠", "components.Settings.Notifications.smtpHost": "SMTP 主機", "i18n.partiallyavailable": "部分可觀看", "i18n.unavailable": "不可觀看", "components.StatusBadge.status4k": "4K 版{status}", "components.Setup.welcome": "歡迎來到 <PERSON><PERSON><PERSON>rr", "components.TvDetails.TvCast.fullseriescast": "演員陣容", "components.TvDetails.TvCrew.fullseriescrew": "製作群", "components.TvDetails.originallanguage": "原始語言", "components.TvDetails.network": "電視網", "components.TvDetails.recommendations": "推薦", "i18n.pending": "待處理", "i18n.failed": "失敗", "i18n.close": "關閉", "i18n.cancel": "取消", "i18n.request": "提出請求", "i18n.requested": "已經有請求", "i18n.retry": "重試", "pages.returnHome": "返回首頁", "components.TvDetails.viewfullcrew": "檢視完整製作群", "components.TvDetails.overview": "概要", "components.Settings.SettingsAbout.Releases.viewongithub": "在 GitHub 上查看", "components.Settings.SettingsAbout.Releases.viewchangelog": "查看變更日誌", "components.UserList.validationpasswordminchars": "密碼必須至少包含八個字符", "components.UserList.userlist": "使用者清單", "components.Setup.finish": "完成設定", "components.Setup.continue": "繼續", "components.Setup.configureservices": "設定伺服器", "components.Settings.sonarrsettings": "Sonarr 設定", "components.Settings.radarrsettings": "Radarr 設定", "components.Settings.menuPlexSettings": "Plex", "components.UserList.importfromplexerror": "匯入 Plex 使用者時出了點問題。", "components.UserList.importfromplex": "匯入 Plex 使用者", "components.UserList.importedfromplex": "匯入 <strong>{userCount}</strong> 個 Plex 使用者成功！", "components.UserList.localuser": "本地使用者", "components.UserList.creating": "創建中…", "components.UserList.createlocaluser": "建立本地使用者", "components.UserList.autogeneratepassword": "自動生成密碼", "i18n.tvshows": "影集", "pages.oops": "哎呀", "components.TvDetails.firstAirDate": "原始播出日期", "i18n.delete": "刪除", "i18n.declined": "已拒絕", "i18n.decline": "拒絕", "components.MovieDetails.cast": "演員陣容", "components.TvDetails.cast": "演員陣容", "i18n.available": "可觀看", "i18n.approved": "獲批准", "i18n.approve": "批准", "components.TvDetails.anime": "動漫", "i18n.processing": "處理中", "i18n.deleting": "刪除中…", "components.Setup.finishing": "完成設定中…", "components.Settings.SonarrModal.loadingrootfolders": "載入中…", "components.Settings.SonarrModal.loadingprofiles": "載入中…", "components.Settings.RadarrModal.loadingrootfolders": "載入中…", "components.Settings.RadarrModal.loadingprofiles": "載入中…", "components.Search.searchresults": "搜尋結果", "components.RequestModal.seasonnumber": "第 {number} 季", "components.RequestModal.season": "季數", "components.RequestModal.numberofepisodes": "集數", "components.RequestModal.cancel": "取消請求", "components.RequestList.sortModified": "最後修改", "components.RequestList.sortAdded": "最新", "components.RequestList.showallrequests": "查看所有請求", "components.RequestList.requests": "請求", "components.RequestList.RequestItem.seasons": "季數", "components.RequestList.RequestItem.failedretry": "重試提出請求時出了點問題。", "components.RequestCard.seasons": "季數", "components.RequestButton.viewrequest4k": "查看 4K 請求", "components.RequestButton.viewrequest": "查看請求", "components.RequestButton.requestmore4k": "再提出 4K 請求", "components.RequestButton.requestmore": "提出更多季數的請求", "components.RequestButton.declinerequests": "拒絕{requestCount, plural, one {請求} other {{requestCount} 個請求}}", "components.RequestButton.declinerequest4k": "拒絕 4K 請求", "components.RequestButton.declinerequest": "拒絕請求", "components.RequestButton.decline4krequests": "拒絕 {requestCount, plural, one {4K 請求} other {{requestCount} 個 4K 請求}}", "components.RequestButton.approverequests": "批准{requestCount, plural, one {請求} other { {requestCount} 個請求}}", "components.RequestButton.approverequest4k": "批准 4K 請求", "components.RequestButton.approverequest": "批准請求", "components.RequestButton.approve4krequests": "批准{requestCount, plural, one { 4K 請求} other { {requestCount} 個 4K 請求}}", "components.RequestBlock.seasons": "季數", "components.PersonDetails.crewmember": "製作群成員", "components.NotificationTypeSelector.mediarequested": "請求待批准", "components.NotificationTypeSelector.mediafailed": "請求處理失敗", "components.NotificationTypeSelector.mediaapproved": "請求獲批准", "components.NotificationTypeSelector.mediaavailableDescription": "當請求的媒體可觀看時發送通知。", "components.NotificationTypeSelector.mediaapprovedDescription": "當請求獲手動批准時發送通知。", "components.NotificationTypeSelector.mediaavailable": "請求可觀看", "components.MovieDetails.watchtrailer": "觀看預告片", "components.MovieDetails.viewfullcrew": "檢視完整製作群", "components.MovieDetails.studio": "製作公司", "components.MovieDetails.similar": "類似", "components.MovieDetails.runtime": "{minutes} 分鐘", "components.MovieDetails.revenue": "收入", "components.MovieDetails.releasedate": "上映日期", "components.MovieDetails.recommendations": "推薦", "components.Layout.Sidebar.dashboard": "探索", "components.MovieDetails.overview": "概要", "components.MovieDetails.originallanguage": "原始語言", "components.MovieDetails.budget": "電影成本", "components.MovieDetails.MovieCrew.fullcrew": "製作群", "components.MovieDetails.MovieCast.fullcast": "演員陣容", "components.Login.validationpasswordrequired": "請輸入您的密碼", "components.Login.validationemailrequired": "請輸入有效的電子郵件地址", "components.Login.signinwithoverseerr": "使用您的 {applicationTitle} 帳戶", "components.Login.password": "密碼", "components.Login.loginerror": "登入時出了點問題。", "components.Login.email": "電子郵件地址", "components.Layout.UserDropdown.signout": "登出", "components.Layout.Sidebar.users": "使用者", "components.Layout.Sidebar.settings": "設定", "components.Layout.Sidebar.requests": "請求", "components.Layout.SearchInput.searchPlaceholder": "搜尋電影、影集、人物…", "components.Discover.upcomingmovies": "即將上映的電影", "components.Discover.upcoming": "即將上映的電影", "components.Discover.trending": "趨勢", "components.Discover.recentlyAdded": "最近新增", "components.Discover.recentrequests": "最新請求", "components.Discover.populartv": "熱門影集", "components.Discover.popularmovies": "熱門電影", "components.CollectionDetails.requestcollection": "提出電影系列請求", "components.CollectionDetails.overview": "概要", "components.UserList.userdeleteerror": "刪除使用者時出了點問題。", "components.UserList.userdeleted": "使用者刪除成功！", "components.UserList.usercreatedsuccess": "建立新使用者成功！", "components.UserList.usercreatedfailed": "建立新使用者時出了點問題。", "components.UserList.user": "使用者", "components.UserList.totalrequests": "請求數", "components.UserList.plexuser": "Plex 使用者", "components.UserList.email": "電子郵件地址", "components.UserList.deleteuser": "刪除使用者", "components.UserList.role": "角色", "components.UserList.password": "密碼", "i18n.movies": "電影", "components.Setup.signinMessage": "首先，請使用您的 Plex 帳戶登入", "components.CollectionDetails.numberofmovies": "{count} 部電影", "components.UserList.admin": "管理員", "components.Settings.SonarrModal.baseUrl": "網站根目錄", "components.Settings.RadarrModal.baseUrl": "網站根目錄", "components.Settings.notrunning": "未運行", "components.Settings.activeProfile": "目前的品質設定", "components.Settings.notificationsettings": "通知設定", "components.Settings.default4k": "設定 4K 為預設分辨率", "components.Settings.currentlibrary": "當前媒體庫： {name}", "components.Settings.SonarrModal.seasonfolders": "季數檔案夾", "components.Settings.SettingsAbout.overseerrinformation": "關於 <PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.gettingsupport": "支援", "components.Settings.manualscan": "媒體庫手動掃描", "components.Settings.plexlibraries": "Plex 媒體庫", "components.Settings.menuGeneralSettings": "一般", "components.Settings.SettingsAbout.totalrequests": "請求數", "components.Settings.SettingsAbout.totalmedia": "媒體數", "components.Settings.SettingsAbout.helppaycoffee": "請開發者喝咖啡", "components.Settings.SettingsAbout.githubdiscussions": "GitHub 討論區", "components.Settings.startscan": "執行掃描", "components.Settings.cancelscan": "取消掃描", "components.Settings.RadarrModal.toastRadarrTestSuccess": "Radarr 伺服器連線成功！", "components.Settings.SonarrModal.testFirstRootFolders": "請先測試連線", "components.Settings.SonarrModal.testFirstQualityProfiles": "請先測試連線", "components.Settings.RadarrModal.testFirstRootFolders": "請先測試連線", "components.Settings.RadarrModal.testFirstQualityProfiles": "請先測試連線", "components.Settings.SonarrModal.defaultserver": "預設伺服器", "components.Settings.deleteserverconfirm": "確定要刪除這個伺服器嗎？", "components.Settings.addradarr": "新增 Radarr 伺服器", "components.Settings.addsonarr": "新增 Sonarr 伺服器", "components.Settings.SonarrModal.server4k": "4K 伺服器", "components.Settings.SettingsAbout.supportoverseerr": "支持 <PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.validationProfileRequired": "請設定品質", "components.Settings.RadarrModal.validationProfileRequired": "請設定品質", "components.Settings.SonarrModal.selectQualityProfile": "設定品質", "components.Settings.RadarrModal.selectQualityProfile": "設定品質", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "請設定最低狀態", "components.Settings.RadarrModal.selectMinimumAvailability": "設定最低狀態", "components.Settings.SonarrModal.validationRootFolderRequired": "請設定根目錄", "components.Settings.SonarrModal.selectRootFolder": "設定根目錄", "components.Settings.RadarrModal.selectRootFolder": "設定根目錄", "components.Settings.RadarrModal.validationRootFolderRequired": "請設定根目錄", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Slack 通知設定儲存成功！", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "Webhook 通知設定儲存成功！", "components.Settings.Notifications.discordsettingssaved": "Discord 通知設定儲存成功！", "components.Settings.Notifications.emailsettingssaved": "電子郵件通知設定儲存成功！", "components.Settings.Notifications.chatId": "聊天室 ID", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "Pushover 通知設定儲存失敗。", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "Slack 通知設定儲存失敗。", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "Webhook 通知設定儲存失敗。", "components.Settings.Notifications.discordsettingsfailed": "Discord 通知設定儲存失敗。", "components.Settings.Notifications.emailsettingsfailed": "電子郵件通知設定儲存失敗。", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "幫助", "components.Settings.SonarrModal.animerootfolder": "動漫根目錄", "components.Settings.SonarrModal.rootfolder": "根目錄", "components.Settings.RadarrModal.rootfolder": "根目錄", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "重設為預設", "components.Settings.Notifications.NotificationsWebhook.customJson": "JSON 有效負載", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "JSON 有效負載重設為預設負載成功！", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "請輸入有效的使用者或群組令牌", "components.Settings.menuJobs": "作業和快取", "components.UserList.deleteconfirm": "確定要刪除這個使用者嗎？此使用者的所有儲存資料將被清除。", "components.Settings.SettingsAbout.Releases.releasedataMissing": "無法獲取軟體版本資料。", "components.UserList.passwordinfodescription": "設定應用程式網址以及啟用電子郵件通知，才能自動生成密碼。", "components.Settings.Notifications.validationBotAPIRequired": "請輸入機器人授權令牌", "components.Settings.Notifications.botAPI": "Bot 機器人授權令牌", "components.Settings.menuServices": "伺服器", "components.Settings.address": "網址", "components.Settings.ssl": "SSL", "components.Settings.SonarrModal.ssl": "使用安全通訊協定（SSL）", "components.Settings.RadarrModal.ssl": "使用安全通訊協定（SSL）", "components.Settings.port": "通訊埠", "components.Settings.SonarrModal.port": "通訊埠", "components.Settings.RadarrModal.port": "通訊埠", "components.Settings.Notifications.NotificationsPushover.userToken": "使用者或群組令牌", "components.Settings.Notifications.NotificationsPushover.accessToken": "應用程式 API 令牌", "components.Settings.menuNotifications": "通知", "components.Settings.menuLogs": "日誌", "components.Settings.menuAbout": "關於 <PERSON><PERSON><PERSON><PERSON>", "components.Settings.default": "預設", "components.Settings.SettingsAbout.version": "軟體版本", "components.Settings.SettingsAbout.Releases.latestversion": "最新版本", "components.Settings.SettingsAbout.Releases.currentversion": "目前的版本", "components.Settings.SettingsAbout.timezone": "時區", "components.Settings.SettingsAbout.documentation": "使用說明", "components.RequestModal.pending4krequest": "待處理的 4K 請求", "components.RequestModal.pendingrequest": "待處理的請求", "components.Settings.SettingsAbout.Releases.versionChangelog": "{version} 變更日誌", "components.Settings.SettingsAbout.Releases.releases": "軟體版本", "components.Settings.plexsettings": "Plex 設定", "components.RequestModal.selectseason": "請選擇季數", "components.RequestModal.requestseasons": "提出請求", "components.RequestModal.requestadmin": "此請求將自動獲批准。", "components.RequestModal.requestSuccess": "為 <strong>{title}</strong> 提出請求成功！", "components.RequestModal.requestCancel": "<strong>{title}</strong> 的請求已被取消。", "components.PersonDetails.appearsin": "演出", "components.PersonDetails.ascharacter": "飾演 {character}", "components.TvDetails.overviewunavailable": "沒有概要。", "components.MovieDetails.overviewunavailable": "沒有概要。", "components.TvDetails.watchtrailer": "觀看預告片", "components.TvDetails.showtype": "影集類型", "components.TvDetails.similar": "類似", "components.RequestModal.requestfrom": "{username} 的請求待處理。", "components.Settings.validationPortRequired": "請輸入有效的通訊埠", "components.Settings.validationHostnameRequired": "請輸入有效的主機名稱或 IP 位址", "components.Settings.SonarrModal.validationPortRequired": "請輸入有效的通訊埠", "components.Settings.SonarrModal.validationNameRequired": "請輸入伺服器名稱", "components.Settings.SonarrModal.validationHostnameRequired": "請輸入有效的主機名稱或 IP 位址", "components.Settings.RadarrModal.validationPortRequired": "請輸入有效的通訊埠", "components.Settings.SonarrModal.validationApiKeyRequired": "請輸入應用程式密鑰", "components.Settings.RadarrModal.validationNameRequired": "請輸入伺服器名稱", "components.Settings.RadarrModal.validationHostnameRequired": "請輸入有效的主機名稱或 IP 位址", "components.Settings.RadarrModal.validationApiKeyRequired": "請輸入應用程式密鑰", "components.Settings.Notifications.validationChatIdRequired": "請輸入有效的聊天室 ID", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "請輸入有效的 JSON 有效負載", "components.Settings.Notifications.NotificationsWebhook.authheader": "Authorization 頭欄位", "components.Settings.RadarrModal.minimumAvailability": "最低狀態", "components.Settings.Notifications.allowselfsigned": "允許自簽名證書", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "請輸入應用程式 API 令牌", "components.Settings.RadarrModal.hostname": "主機名稱或 IP 位址", "components.Settings.SonarrModal.hostname": "主機名稱或 IP 位址", "components.Settings.hostname": "主機名稱或 IP 位址", "components.Settings.RadarrModal.toastRadarrTestFailure": "Radarr 伺服器連線失敗。", "components.Settings.RadarrModal.server4k": "4K 伺服器", "components.Settings.SonarrModal.animequalityprofile": "動漫品質設定", "components.Settings.SonarrModal.qualityprofile": "品質設定", "components.Settings.RadarrModal.qualityprofile": "品質設定", "components.Settings.Notifications.telegramsettingsfailed": "Telegram 通知設定儲存失敗。", "components.Settings.Notifications.telegramsettingssaved": "Telegram 通知設定儲存成功！", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Pushover 通知設定儲存成功！", "components.UserList.created": "加入日期", "components.UserList.create": "建立", "components.Settings.SonarrModal.createsonarr": "新增 Sonarr 伺服器", "components.Settings.RadarrModal.createradarr": "新增 Radarr 伺服器", "components.Settings.SonarrModal.servername": "伺服器名稱", "components.Settings.SonarrModal.editsonarr": "編輯 Sonarr 伺服器", "components.Settings.SonarrModal.add": "新增伺服器", "components.Settings.RadarrModal.servername": "伺服器名稱", "components.Settings.RadarrModal.editradarr": "編輯 Radarr 伺服器", "components.Settings.RadarrModal.defaultserver": "預設伺服器", "components.Settings.RadarrModal.add": "新增伺服器", "components.RequestModal.requestcancelled": "<strong>{title}</strong> 的請求已被取消。", "components.RequestModal.AdvancedRequester.qualityprofile": "品質設定", "components.RequestModal.AdvancedRequester.animenote": "※這是個動漫影集。", "components.RequestModal.AdvancedRequester.advancedoptions": "進階選項", "components.RequestModal.AdvancedRequester.default": "{name}（預設）", "components.RequestModal.AdvancedRequester.destinationserver": "目標伺服器", "components.RequestBlock.server": "目標伺服器", "components.RequestModal.AdvancedRequester.rootfolder": "根目錄", "components.RequestBlock.rootfolder": "根目錄", "components.MediaSlider.ShowMoreCard.seemore": "更多", "components.Login.signinwithplex": "使用您的 Plex 帳戶", "components.Login.signinheader": "請先登入", "components.Login.signingin": "登入中…", "components.Login.signin": "登入", "components.Settings.SonarrModal.toastSonarrTestFailure": "Sonarr 伺服器連線失敗。", "components.Settings.serverpreset": "伺服器", "components.RequestModal.autoapproval": "自動批准", "components.PermissionEdit.autoapproveSeries": "影集自動批准", "components.PermissionEdit.autoapproveMovies": "電影自動批准", "components.PermissionEdit.autoapprove": "自動批准", "components.PermissionEdit.admin": "管理員", "components.Settings.toastPlexConnecting": "連線中…", "components.Settings.toastPlexRefresh": "載入中…", "components.Settings.serverpresetRefreshing": "載入中…", "components.Settings.SonarrModal.syncEnabled": "啟用掃描", "components.UserList.userssaved": "使用者權限儲存成功！", "components.Settings.SonarrModal.externalUrl": "外部網址", "components.Settings.RadarrModal.externalUrl": "外部網址", "components.RequestBlock.requestoverrides": "覆寫請求", "components.Settings.toastPlexConnectingSuccess": "Plex 伺服器連線成功！", "components.Settings.serverRemote": "遠端", "components.Settings.serverLocal": "本地", "components.MovieDetails.mark4kavailable": "標記 4K 版為可觀看", "components.MovieDetails.markavailable": "標記為可觀看", "components.Settings.RadarrModal.syncEnabled": "啟用掃描", "i18n.experimental": "實驗性", "components.UserList.bulkedit": "批次編輯", "i18n.edit": "編輯", "components.Settings.serverpresetManualMessage": "手動設定", "components.NotificationTypeSelector.mediadeclined": "請求拒絕", "components.PermissionEdit.users": "管理使用者", "components.PermissionEdit.request4kTv": "提出 4K 影集請求", "components.PermissionEdit.request4kMovies": "提出 4K 電影請求", "components.PermissionEdit.request4k": "提出 4K 請求", "components.PermissionEdit.request": "提出請求", "components.PermissionEdit.managerequests": "管理請求", "components.RequestBlock.profilechanged": "品質設定", "components.PermissionEdit.advancedrequest": "進階請求", "components.RequestModal.requestedited": "<strong>{title}</strong> 的請求編輯成功！", "components.RequestModal.errorediting": "編輯請求時出了點問題。", "components.RequestModal.requesterror": "提出請求時出了點問題。", "components.Settings.SettingsJobsCache.cachekeys": "鍵數", "components.Settings.SettingsJobsCache.cachevsize": "值儲存大小", "components.Settings.SettingsJobsCache.cacheksize": "鍵儲存大小", "components.Settings.SettingsJobsCache.cacheflushed": "{cachename} 快取清除成功！", "components.Settings.SettingsJobsCache.cachemisses": "失誤數", "components.Settings.SettingsJobsCache.cachehits": "擊中數", "components.Settings.SettingsJobsCache.cachename": "快取名稱", "components.Settings.SettingsJobsCache.runnow": "執行", "components.Settings.SettingsJobsCache.nextexecution": "下一次執行時間", "components.Settings.SettingsJobsCache.jobtype": "作業類型", "components.Settings.SettingsJobsCache.jobstarted": "{jobname} 已開始運行。", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname}已被取消。", "components.Settings.SettingsJobsCache.jobs": "作業", "components.Settings.SettingsJobsCache.jobname": "作業名稱", "components.Settings.SettingsJobsCache.flushcache": "清除快取", "components.Settings.SettingsJobsCache.canceljob": "取消作業", "components.Settings.SettingsJobsCache.cache": "快取", "components.Settings.SonarrModal.toastSonarrTestSuccess": "Sonarr 伺服器連線成功！", "components.Settings.SettingsJobsCache.command": "指令", "components.Settings.SettingsJobsCache.process": "程序", "components.Settings.SettingsAbout.preferredmethod": "首選", "i18n.advanced": "進階", "components.Settings.copied": "應用程式密鑰已複製到剪貼板。", "components.Settings.serverpresetLoad": "請點右邊的按鈕", "components.Settings.toastPlexRefreshSuccess": "獲取 Plex 伺服器列表成功！", "components.Settings.toastPlexRefreshFailure": "獲取 Plex 伺服器列表失敗。", "components.Settings.toastPlexConnectingFailure": "Plex 伺服器連線失敗。", "components.UserList.users": "使用者", "components.Search.search": "搜尋", "components.Setup.setup": "設定", "components.Discover.discover": "探索", "components.AppDataWarning.dockerVolumeMissingDescription": "您必須使用繫結掛載（bind mount）來繫結主機上的目錄跟 Docker 容器內的 <code>{appDataPath}</code> 目錄，才能儲存 Jellyseerr 的設定和數據。", "components.RequestModal.AdvancedRequester.requestas": "請求者", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "請刪除結尾斜線", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "請刪除結尾斜線", "components.Settings.SonarrModal.validationApplicationUrl": "請輸入有效的網址", "components.Settings.RadarrModal.validationApplicationUrl": "請輸入有效的網址", "components.PermissionEdit.viewrequests": "查看請求", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "請加前置斜線", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "請加前置斜線", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "請刪除結尾斜線", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "請刪除結尾斜線", "components.UserList.validationEmail": "請輸入有效的電子郵件地址", "components.Settings.Notifications.validationEmail": "請輸入有效的電子郵件地址", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "請輸入有效的網址", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "請輸入有效的網址", "components.ResetPassword.confirmpassword": "確認密碼", "components.ResetPassword.emailresetlink": "發送密碼重設電子郵件", "components.ResetPassword.email": "電子郵件地址", "components.ResetPassword.gobacklogin": "返回", "components.ResetPassword.resetpassword": "重設密碼", "components.ResetPassword.validationpasswordmatch": "密碼必須匹配", "components.ResetPassword.validationpasswordminchars": "密碼必須至少包含八個字符", "components.ResetPassword.validationemailrequired": "請輸入有效的電子郵件地址", "components.ResetPassword.validationpasswordrequired": "請輸入密碼", "components.ResetPassword.password": "密碼", "components.Login.forgotpassword": "忘記密碼？", "components.TvDetails.nextAirDate": "下一次播出日期", "components.NotificationTypeSelector.mediarequestedDescription": "當使用者提出需要管理員批准的請求時發送通知。", "components.NotificationTypeSelector.mediafailedDescription": "當 Radarr 或 Sonarr 處理請求失敗時發送通知。", "components.NotificationTypeSelector.mediadeclinedDescription": "當請求拒被絕時發送通知。", "components.PermissionEdit.request4kDescription": "授予提出 4K 媒體請求的權限。", "components.PermissionEdit.request4kMoviesDescription": "授予提出 4K 電影請求的權限。", "components.PermissionEdit.request4kTvDescription": "授予提出 4K 影集請求的權限。", "components.PermissionEdit.requestDescription": "授予提出非 4K 媒體請求的權限。", "components.PermissionEdit.viewrequestsDescription": "授予查看其他使用者提出的媒體請求的權限。", "components.Settings.SonarrModal.validationLanguageProfileRequired": "請設定語言", "components.Settings.SonarrModal.testFirstLanguageProfiles": "請先測試連線", "components.Settings.SonarrModal.selectLanguageProfile": "設定語言", "components.Settings.SonarrModal.loadinglanguageprofiles": "載入中…", "components.Settings.SonarrModal.languageprofile": "語言設定", "components.Settings.SonarrModal.animelanguageprofile": "動漫語言設定", "components.RequestModal.AdvancedRequester.languageprofile": "語言設定", "components.PermissionEdit.usersDescription": "授予管理使用者的權限。有此權限的使用者不能編輯管理員或授予管理員權限。", "components.PermissionEdit.autoapproveSeriesDescription": "自動批准非 4K 影集請求。", "components.PermissionEdit.autoapproveMoviesDescription": "自動批准非 4K 電影請求。", "components.PermissionEdit.autoapproveDescription": "自動批准所有非 4K 媒體請求。", "components.PermissionEdit.advancedrequestDescription": "授予使用進階媒體請求選項的權限。", "components.PermissionEdit.adminDescription": "授予最高權限；旁路所有權限檢查。", "components.PermissionEdit.managerequestsDescription": "授予管理媒體請求的權限，以及所有自動批准的權限。", "components.Settings.SettingsJobsCache.cacheDescription": "外部應用程式介面（external API）請求將存到快取記憶體，以減少 API 呼叫次數。", "components.Settings.librariesRemaining": "媒體庫剩餘數： {count}", "components.Settings.Notifications.sendSilentlyTip": "發送沒有警報音的通知", "components.Settings.Notifications.sendSilently": "無聲通知", "components.UserList.sortCreated": "加入日期", "components.UserList.sortDisplayName": "顯示名稱", "components.UserList.sortRequests": "請求數", "components.PermissionEdit.autoapprove4kSeriesDescription": "自動批准 4K 影集請求。", "components.PermissionEdit.autoapprove4kSeries": "4K 影集自動批准", "components.PermissionEdit.autoapprove4kMoviesDescription": "自動批准 4K 電影請求。", "components.PermissionEdit.autoapprove4kMovies": "4K 電影自動批准", "components.PermissionEdit.autoapprove4kDescription": "自動批准所有 4K 媒體請求。", "components.PermissionEdit.autoapprove4k": "自動批准 4K", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "權限設定儲存成功！", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "設定儲存成功！", "components.UserProfile.recentrequests": "最新請求", "components.UserProfile.UserSettings.UserPermissions.permissions": "權限設定", "components.UserProfile.UserSettings.menuPermissions": "權限", "components.UserProfile.UserSettings.menuNotifications": "通知", "components.UserProfile.UserSettings.menuGeneralSettings": "一般", "components.UserProfile.UserSettings.menuChangePass": "密碼", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "密碼必須至少包含八個字符", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "密碼必須匹配", "components.UserProfile.UserSettings.UserPasswordChange.password": "密碼設定", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "確認密碼", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "通知設定", "components.UserList.edituser": "編輯使用者權限", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "Pushbullet 通知設定儲存失敗。", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Pushbullet 通知設定儲存成功！", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "請輸入 API 令牌", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "使用者 ID", "components.UserProfile.ProfileHeader.profile": "顯示個人資料", "components.UserProfile.ProfileHeader.settings": "使用者設定", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "顯示名稱", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "一般設定", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "Plex 使用者", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "本地使用者", "components.UserList.userfail": "使用者權限儲存時出了點問題。", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "儲存設定時出了點問題。", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "儲存設定時出了點問題。", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "啟用通知", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "API 令牌", "components.Layout.UserDropdown.settings": "設定", "components.Layout.UserDropdown.myprofile": "個人資料", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "密碼必須匹配", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "目前的密碼", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "新密碼", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "請輸入當前的密碼", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "請輸入新密碼", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "密碼設定成功！", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "請輸入有效的使用者 ID", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "您的<FindDiscordIdLink>使用者 ID 號碼</FindDiscordIdLink>", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "重設密碼時出了點問題。", "components.RequestModal.SearchByNameModal.notvdbiddescription": "無法自動配對此影集的數據。 請在以下選擇正確的媒體項。", "components.CollectionDetails.requestcollection4k": "提出 4K 電影系列請求", "components.ResetPassword.requestresetlinksuccessmessage": "通過電子郵件發送了密碼重設鏈接。", "components.ResetPassword.resetpasswordsuccessmessage": "密碼重設成功！", "components.RegionSelector.regionDefault": "所有地區", "components.UserProfile.UserSettings.UserGeneralSettings.region": "「探索」地區", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "「探索」語言", "components.Discover.upcomingtv": "即將上映的影集", "components.Settings.webhook": "Webhook", "components.Settings.email": "電子郵件", "components.Settings.notificationAgentSettingsDescription": "設定通知類型和代理服務。", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "以地區可用性篩選結果", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "以原始語言篩選結果", "components.Settings.SettingsJobsCache.jobsDescription": "Jellyseerr 將定時運行以下的維護任務。手動執行工作不會影響它正常的行程。", "components.Settings.plexsettingsDescription": "關於 Plex 伺服器的設定。Jellyseerr 將定時執行媒體庫掃描。", "components.Settings.manualscanDescription": "在正常情況下，Jellyseerr 會每24小時掃描您的 Plex 媒體庫。最近新增的媒體將更頻繁掃描。設定新的 Plex 伺服器時，我們建議您執行一次手動掃描！", "components.RegionSelector.regionServerDefault": "預設設定（{region}）", "components.Settings.settingUpPlexDescription": "您可以手動輸入您的 Plex 伺服器資料，或從 <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink> 返回的數據做選擇。請點下拉式選單右邊的按鈕獲取伺服器列表。", "components.Settings.plexlibrariesDescription": "Jellyseerr 將掃描的媒體庫。", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "您無權設定此使用者的密碼。", "components.UserProfile.UserSettings.UserGeneralSettings.user": "使用者", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "使用者類型", "components.UserList.accounttype": "類型", "components.UserProfile.UserSettings.UserGeneralSettings.role": "角色", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "管理員", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "所有者", "components.UserList.owner": "所有者", "components.Settings.SettingsJobsCache.download-sync-reset": "下載狀態同步復位", "components.Settings.SettingsJobsCache.download-sync": "下載狀態同步", "components.Settings.SettingsJobsCache.unknownJob": "未知作業", "components.TvDetails.seasons": "{seasonCount} 季", "components.Discover.DiscoverTvGenre.genreSeries": "{genre}影集", "components.Discover.DiscoverNetwork.networkSeries": "{network} 影集", "components.Discover.DiscoverStudio.studioMovies": "{studio} 電影", "components.Discover.DiscoverMovieGenre.genreMovies": "{genre}電影", "i18n.loading": "載入中…", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "請輸入聊天室 ID", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "聊天室 ID", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "無聲通知", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "發送沒有警報音的通知", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "先<TelegramBotLink>建立一個聊天室</TelegramBotLink>以及把 <GetIdBotLink>@get_id_bot</GetIdBotLink> 加到聊天室，然後在聊天室裡發出 <code>/my_id</code> 命令", "components.Settings.Notifications.botUsername": "Bot 機器人名稱", "components.Discover.NetworkSlider.networks": "電視網", "components.Discover.StudioSlider.studios": "製作公司", "components.Settings.Notifications.validationUrl": "請輸入有效的網址", "components.Settings.Notifications.botAvatarUrl": "Bot 機器人頭像網址", "components.RequestList.RequestItem.modified": "最後修改者", "components.RequestList.RequestItem.modifieduserdate": "{user}（{date}）", "components.RequestList.RequestItem.requested": "請求者", "components.Settings.scanning": "同步中…", "components.Settings.scan": "媒體庫同步", "components.Settings.SettingsJobsCache.sonarr-scan": "Sonarr 掃描", "components.Settings.SettingsJobsCache.radarr-scan": "Radarr 掃描", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Plex 最近新增掃描", "components.Settings.SettingsJobsCache.plex-full-scan": "Plex 媒體庫掃描", "components.Discover.DiscoverTvLanguage.languageSeries": "{language}影集", "components.Discover.DiscoverMovieLanguage.languageMovies": "{language}電影", "components.UserProfile.ProfileHeader.userid": "使用者 ID：{userid}", "components.UserProfile.ProfileHeader.joindate": "加入日期：{joindate}", "components.Settings.SettingsUsers.localLogin": "允許本地登入", "components.Settings.SettingsUsers.defaultPermissions": "預設權限", "components.Settings.SettingsUsers.userSettingsDescription": "關於使用者的全域和預設設定。", "components.Settings.SettingsUsers.userSettings": "使用者設定", "components.Settings.menuUsers": "使用者", "components.Settings.SettingsUsers.toastSettingsSuccess": "使用者設定儲存成功！", "components.Settings.SettingsUsers.toastSettingsFailure": "儲存設定時出了點問題。", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "當使用者提出自動批准的請求時發送通知。", "components.NotificationTypeSelector.mediaAutoApproved": "請求自動批准", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "您不能編輯自己的權限。", "components.UserProfile.UserSettings.unauthorizedDescription": "您無權編輯此使用者的設定。", "components.Settings.Notifications.pgpPrivateKeyTip": "使用 <OpenPgpLink>OpenPGP</OpenPgpLink> 電子郵件加密與簽章", "components.Settings.Notifications.pgpPasswordTip": "使用 <OpenPgpLink>OpenPGP</OpenPgpLink> 電子郵件加密與簽章", "components.Settings.Notifications.pgpPassword": "PGP 解密密碼", "components.Settings.Notifications.pgpPrivateKey": "PGP 私鑰", "components.TvDetails.episodeRuntime": "劇集片長", "components.TvDetails.episodeRuntimeMinutes": "{runtime} 分鐘", "components.RequestModal.AdvancedRequester.folder": "{path}（{space}）", "components.Discover.TvGenreSlider.tvgenres": "影集類型", "components.Discover.MovieGenreSlider.moviegenres": "電影類型", "components.Discover.MovieGenreList.moviegenres": "電影類型", "components.Discover.TvGenreList.seriesgenres": "影集類型", "components.RequestModal.alreadyrequested": "已經有請求", "components.Settings.SettingsLogs.time": "時間戳", "components.Settings.SettingsLogs.resumeLogs": "恢復", "components.Settings.SettingsLogs.pauseLogs": "暫停", "components.Settings.SettingsLogs.filterWarn": "警告", "components.Settings.SettingsLogs.filterInfo": "訊息", "components.Settings.SettingsLogs.filterError": "錯誤", "components.Settings.SettingsLogs.filterDebug": "除錯", "components.Settings.SettingsLogs.label": "標籤", "components.Settings.SettingsLogs.level": "等級", "components.Settings.SettingsLogs.logs": "日誌", "components.Settings.SettingsLogs.showall": "查看所有日誌", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "通知", "components.Settings.SettingsUsers.users": "使用者", "pages.errormessagewithcode": "{statusCode}－{error}", "pages.somethingwentwrong": "出了點問題", "pages.serviceunavailable": "伺服器無法使用", "pages.pagenotfound": "頁面不存在", "pages.internalservererror": "內部伺服器錯誤", "i18n.usersettings": "使用者設定", "i18n.settings": "設定", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "重設密碼時出了點問題。您確定輸入的當前密碼是正確的嗎？", "components.ResetPassword.passwordreset": "密碼重設", "components.UserProfile.UserSettings.UserGeneralSettings.general": "一般", "components.Settings.services": "伺服器", "components.Settings.plex": "Plex", "components.Settings.notifications": "通知", "components.Settings.SettingsLogs.message": "訊息", "components.Settings.SettingsJobsCache.jobsandcache": "作業和快取", "components.Settings.SettingsAbout.about": "關於 <PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.logsDescription": "您也能直接查看 <code>stdout</code> 數據流或位置於 <code>{appDataPath}/logs/overseerr.log</code> 的日誌檔案。", "components.Settings.SettingsLogs.logDetails": "日誌詳細信息", "components.Settings.SettingsLogs.extraData": "附加數據", "components.Settings.SettingsLogs.copyToClipboard": "複製到剪貼板", "components.Settings.SettingsLogs.copiedLogMessage": "日誌訊息已複製到剪貼板。", "components.Settings.enablessl": "使用安全通訊協定（SSL）", "components.UserList.nouserstoimport": "沒有未匯入的 Plex 使用者。", "components.PersonDetails.birthdate": "{birthdate}－", "components.PersonDetails.lifespan": "{birthdate}－{deathdate}", "components.PersonDetails.alsoknownas": "別名：{names}", "i18n.delimitedlist": "{a}、{b}", "components.Settings.SettingsUsers.tvRequestLimitLabel": "影集請求全域限制", "components.Settings.SettingsUsers.movieRequestLimitLabel": "電影請求全域限制", "components.RequestModal.QuotaDisplay.seasonlimit": "季數", "components.RequestModal.QuotaDisplay.season": "影集季數", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {電影請求剩餘數不足} other {剩餘 <strong>#</strong> 個{type}請求}}", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "請求剩餘數不足", "components.RequestModal.QuotaDisplay.movielimit": "電影", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "此使用者每 <strong>{days}</strong> 天能提出 <strong>{limit}</strong> 個{type}請求。", "components.RequestModal.QuotaDisplay.allowedRequests": "您每 <strong>{days}</strong> 天能提出 <strong>{limit}</strong> 個{type}請求。", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "影集請求限制", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "電影請求限制", "components.UserProfile.movierequests": "電影請求", "components.UserProfile.limit": "{limit} 之 {remaining}", "components.UserProfile.pastdays": "{type}（前 {days} 天）", "components.UserProfile.requestsperdays": "剩餘 {limit}", "components.QuotaSelector.unlimited": "無限", "components.RequestModal.QuotaDisplay.movie": "電影", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "覆寫全域限制", "components.UserProfile.unlimited": "無限", "components.UserProfile.totalrequests": "請求總數", "components.UserProfile.seriesrequest": "影集請求", "i18n.view": "檢視", "i18n.tvshow": "影集", "i18n.testing": "測試中…", "i18n.test": "測試", "i18n.status": "狀態", "i18n.showingresults": "<strong>{from}</strong>－<strong>{to}</strong> 列（共 <strong>{total}</strong> 列）", "i18n.saving": "儲存中…", "i18n.save": "儲存變更", "i18n.resultsperpage": "每頁顯示 {pageSize} 列", "i18n.requesting": "提出請求中…", "i18n.request4k": "提出 4K 請求", "i18n.previous": "上一頁", "i18n.notrequested": "沒有請求", "i18n.noresults": "沒有結果。", "i18n.next": "下一頁", "i18n.movie": "電影", "i18n.canceling": "取消中…", "i18n.back": "返回", "i18n.all": "所有", "i18n.areyousure": "確定嗎？", "components.RequestModal.QuotaDisplay.requiredquotaUser": "此使用者的影集請求數量必須至少剩餘 <strong>{seasons}</strong> 個季數才能提出此影集請求。", "components.RequestModal.QuotaDisplay.requiredquota": "您的影集請求數量必須至少剩餘 <strong>{seasons}</strong> 個季數才能提出此影集請求。", "components.TvDetails.originaltitle": "原始標題", "components.MovieDetails.originaltitle": "原始標題", "components.RequestModal.QuotaDisplay.quotaLinkUser": "訪問此使用者的<ProfileLink>個人資料頁面</ProfileLink>以查看使用者的請求限制 。", "components.RequestModal.QuotaDisplay.quotaLink": "訪問您的<ProfileLink>個人資料頁面</ProfileLink>以查看您的請求限制 。", "components.LanguageSelector.languageServerDefault": "預設設定（{language}）", "components.LanguageSelector.originalLanguageDefault": "所有語言", "components.RequestModal.AdvancedRequester.selecttags": "設定標籤", "components.Settings.SonarrModal.selecttags": "設定標籤", "components.Settings.RadarrModal.selecttags": "設定標籤", "components.Settings.SonarrModal.notagoptions": "沒有標籤。", "components.Settings.RadarrModal.notagoptions": "沒有標籤。", "components.RequestModal.AdvancedRequester.notagoptions": "沒有標籤。", "components.Settings.SonarrModal.edit4ksonarr": "編輯 4K Sonarr 伺服器", "components.Settings.RadarrModal.edit4kradarr": "編輯 4K Radarr 伺服器", "components.Settings.RadarrModal.create4kradarr": "新增 4K Radarr 伺服器", "components.Settings.SonarrModal.create4ksonarr": "新增 4K Sonarr 伺服器", "components.Settings.SonarrModal.default4kserver": "預設 4K 伺服器", "components.Settings.RadarrModal.default4kserver": "預設 4K 伺服器", "components.Settings.SonarrModal.testFirstTags": "請先測試連線", "components.Settings.RadarrModal.testFirstTags": "請先測試連線", "components.Settings.SonarrModal.loadingTags": "載入中…", "components.Settings.SonarrModal.animeTags": "動漫標籤", "components.Settings.SonarrModal.tags": "標籤", "components.Settings.RadarrModal.tags": "標籤", "components.RequestModal.AdvancedRequester.tags": "標籤", "components.Settings.RadarrModal.loadingTags": "載入中…", "components.RequestList.RequestItem.mediaerror": "找不到{mediaType}", "components.RequestCard.mediaerror": "找不到{mediaType}", "components.RequestList.RequestItem.deleterequest": "刪除請求", "components.RequestCard.deleterequest": "刪除請求", "components.Settings.Notifications.botUsernameTip": "允許使用者也把機器人加到自己的聊天室以及設定自己的通知", "components.RequestModal.pendingapproval": "您的請求正在等待管理員批准。", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "Telegram 通知設定儲存失敗。", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "電子郵件通知設定儲存失敗。", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "Discord 通知設定儲存失敗。", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "Telegram 通知設定儲存成功！", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "電子郵件通知設定儲存成功！", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Discord 通知設定儲存成功！", "components.UserProfile.UserSettings.UserNotificationSettings.email": "電子郵件", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "使用 <OpenPgpLink>OpenPGP</OpenPgpLink> 電子郵件加密", "components.Settings.Notifications.validationPgpPassword": "請輸入 PGP 解密密碼", "components.Settings.Notifications.validationPgpPrivateKey": "請輸入有效的 PGP 私鑰", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "請輸入有效的 PGP 公鑰", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "PGP 公鑰", "components.RequestList.RequestItem.cancelRequest": "取消請求", "components.NotificationTypeSelector.notificationTypes": "通知類型", "components.Layout.VersionStatus.commitsbehind": "落後 {commitsBehind} 個提交", "components.Layout.VersionStatus.outofdate": "非最新版本", "components.Layout.VersionStatus.streamstable": "<PERSON><PERSON><PERSON><PERSON> 穩定版", "components.Layout.VersionStatus.streamdevelop": "<PERSON><PERSON><PERSON><PERSON> 開發版", "components.Settings.SettingsAbout.outofdate": "非最新版本", "components.Settings.SettingsAbout.uptodate": "最新", "components.Settings.noDefaultNon4kServer": "如果您只有一個 {serverType} 伺服器，請勿把它設定為 4K 伺服器。", "components.Settings.noDefaultServer": "您必須至少指定一個預設 {serverType} 伺服器，才能處理{mediaType}請求。", "components.Settings.serviceSettingsDescription": "關於 {serverType} 伺服器的設定。{serverType} 伺服器數沒有最大值限制，但您只能指定兩個預設伺服器（一個非 4K、一個 4K）。", "components.Settings.mediaTypeSeries": "影集", "components.Settings.mediaTypeMovie": "電影", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "此使用者的帳戶目前沒有設密碼。若在以下設定密碼，此使用者就能使用「本地登入」。", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "您的帳戶目前沒有設密碼。若在以下設定密碼，您就能使用「本地登入」。", "components.UserList.autogeneratepasswordTip": "通過電子郵件發送伺服器生成的密碼給使用者", "i18n.retrying": "重試中…", "components.Settings.serverSecure": "SSL", "components.UserList.usercreatedfailedexisting": "您提供的電子郵件地址已由其他使用者使用。", "components.RequestModal.edit": "編輯請求", "components.RequestList.RequestItem.editrequest": "編輯請求", "components.Settings.RadarrModal.enableSearch": "啟用自動搜尋", "components.Settings.SonarrModal.enableSearch": "啟用自動搜尋", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "網路推送", "components.Settings.webpush": "網路推送", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "網路推送通知設定儲存成功！", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "網路推送通知設定儲存失敗。", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "顯示語言", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "啟用通知", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "LunaSea 通知設定儲存成功！", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "LunaSea 通知設定儲存失敗。", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "Webhook 網址", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "請輸入有效的網址", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "啟用通知", "components.Settings.is4k": "4K", "components.Settings.Notifications.toastEmailTestSuccess": "電子郵件測試通知已發送！", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "網路推送測試通知已發送！", "components.Settings.Notifications.toastTelegramTestSuccess": "Telegram 測試通知已發送！", "components.Settings.Notifications.toastDiscordTestSuccess": "Discord 測試通知已發送！", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "Slack 測試通知已發送！", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Pushover 測試通知已發送！", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Pushbullet 測試通知已發送！", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "LunaSea 測試通知已發送！", "components.Settings.noDefault4kServer": "您必須指定一個 4K {serverType} 伺服器為預設，才能處理 4K 的{mediaType}請求。", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "不使用 <code>default</code> 預設設定檔才必須輸入", "components.Settings.Notifications.NotificationsLunaSea.profileName": "設定檔名稱", "components.Settings.Notifications.toastTelegramTestSending": "發送 Telegram 測試通知中…", "components.Settings.Notifications.toastEmailTestSending": "發送電子郵件測試通知中…", "components.Settings.Notifications.toastDiscordTestSending": "發送 Discord 測試通知中…", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "發送 webhook 測試通知中…", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "發送網路推送測試通知中…", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "發送 Slack 測試通知中…", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "發送 Pushover 測試通知中…", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "發送 Pushbullet 測試通知中…", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "發送 LunaSea 測試通知中…", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "網路推送測試通知發送失敗。", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "Webhook 測試通知發送失敗。", "components.Settings.Notifications.toastEmailTestFailed": "電子郵件測試通知發送失敗。", "components.Settings.Notifications.toastTelegramTestFailed": "Telegram 測試通知發送失敗。", "components.Settings.Notifications.toastDiscordTestFailed": "Discord 測試通知發送失敗。", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "Slack 測試通知發送失敗。", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "Pushover 測試通知發送失敗。", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "Pushbullet 測試通知發送失敗。", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "LunaSea 測試通知發送失敗。", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "Webhook 測試通知已發送！", "components.Settings.SettingsUsers.newPlexLoginTip": "讓還沒匯入的 Plex 使用者登入", "components.Settings.SettingsUsers.newPlexLogin": "允許新的 Plex 登入", "components.PermissionEdit.requestTv": "提出影集請求", "components.PermissionEdit.requestMovies": "提出電影請求", "components.PermissionEdit.requestMoviesDescription": "授予提出非 4K 電影請求的權限。", "components.PermissionEdit.requestTvDescription": "授予提出非 4K 影集請求的權限。", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "預設設定（{language}）", "components.DownloadBlock.estimatedtime": "預計：{time}", "components.Settings.Notifications.encryptionTip": "TLS 通常會使用通訊埠 465，而 STARTTLS 通常會使用通訊埠 587", "components.Settings.Notifications.encryptionDefault": "盡可能使用 STARTTLS", "components.Settings.Notifications.encryptionImplicitTls": "使用傳輸層安全標準（TLS）", "components.Settings.Notifications.encryptionOpportunisticTls": "始終使用 STARTTLS", "components.Settings.Notifications.encryptionNone": "不使用加密", "components.Settings.Notifications.encryption": "加密方式", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "您 30 個字符的<UsersGroupsLink>使用者或群組識別碼</UsersGroupsLink>", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "從您的<PushbulletSettingsLink>帳號設定</PushbulletSettingsLink>取得 API 令牌", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "建立一個 Jellyseerr 專用的<ApplicationRegistrationLink>應用程式</ApplicationRegistrationLink>", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "創建一個「<WebhookLink>Incoming Webhook</WebhookLink>」整合", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "使用者或設備通知的 <LunaSeaLink>webhook 網址</LunaSeaLink>", "components.Settings.Notifications.webhookUrlTip": "在您的伺服器裡建立一個 <DiscordWebhookLink>webhook</DiscordWebhookLink>", "components.Settings.Notifications.botApiTip": "建立一個 Jellyseerr 專用的<CreateBotLink>機器人</CreateBotLink>", "components.Settings.Notifications.chatIdTip": "先與您的機器人建立一個聊天室以及把 <GetIdBotLink>@get_id_bot</GetIdBotLink> 也加到聊天室，然後在聊天室裡發出 <code>/my_id</code> 命令", "components.Settings.webAppUrlTip": "使用伺服器的網路應用代替「託管」的網路應用", "components.Settings.webAppUrl": "<WebAppLink>網路應用</WebAppLink>網址", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "Jellyseerr 必須通過 HTTPS 投放才能使用網路推送通知。", "components.UserList.localLoginDisabled": "<strong>允許本地登入</strong>的設定目前被禁用。", "components.RequestList.RequestItem.requesteddate": "請求日期", "components.RequestCard.failedretry": "重試提出請求時出了點問題。", "components.Settings.SettingsUsers.localLoginTip": "讓使用者使用電子郵件地址和密碼登入", "components.Settings.SettingsUsers.defaultPermissionsTip": "授予給新使用者的權限", "components.QuotaSelector.days": "天", "components.QuotaSelector.seasons": "季", "components.QuotaSelector.movies": "部電影", "components.QuotaSelector.movieRequests": "<quotaUnits>每 {quotaDays} {days} </quotaUnits>{quotaLimit}<quotaUnits> {movies}</quotaUnits>", "components.QuotaSelector.tvRequests": "<quotaUnits>每 {quotaDays} {days} </quotaUnits>{quotaLimit}<quotaUnits> {seasons}</quotaUnits>", "components.Settings.Notifications.validationTypes": "請選擇通知類型", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "請選擇通知類型", "components.Settings.Notifications.NotificationsSlack.validationTypes": "請選擇通知類型", "components.Settings.Notifications.NotificationsPushover.validationTypes": "請選擇通知類型", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "請選擇通知類型", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "請選擇通知類型", "components.NotificationTypeSelector.usermediarequestedDescription": "當其他使用者提出需要管理員批准的請求時取得通知。", "components.NotificationTypeSelector.usermediafailedDescription": "當 Radarr 或 Sonarr 處理請求失敗時取得通知。", "components.NotificationTypeSelector.usermediadeclinedDescription": "當您的請求被拒絕時取得通知。", "components.NotificationTypeSelector.usermediaavailableDescription": "當您請求的媒體可觀看時取得通知。", "components.NotificationTypeSelector.usermediaapprovedDescription": "當您的請求獲批准時取得通知。", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "當其他使用者提出自動獲批准的請求時取得通知。", "components.Settings.SettingsAbout.betawarning": "這是測試版軟體，有些功能可能損壞或不穩定。請在 GitHub 上回報問題！", "components.Layout.LanguagePicker.displaylanguage": "顯示語言", "components.MovieDetails.showmore": "顯示更多", "components.MovieDetails.showless": "顯示更少", "components.TvDetails.streamingproviders": "目前的流媒體服務", "components.MovieDetails.streamingproviders": "目前的流媒體服務", "components.Settings.SettingsJobsCache.editJobSchedule": "編輯作業", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "每 {jobScheduleHours} 小時", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "新的頻率", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "每 {jobScheduleMinutes} 分鐘", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "儲存作業設定時出了點問題。", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "作業編輯成功！", "components.Settings.SettingsAbout.runningDevelop": "您正在使用 Jellyseerr 的 <code>develop</code> 開發版。我們只建議開發者和協助測試的人員使用。", "components.StatusBadge.status": "{status}", "components.IssueDetails.season": "第 {seasonNumber} 季", "components.IssueList.IssueItem.problemepisode": "有問題的集數", "components.IssueDetails.problemseason": "有問題的季數", "components.IssueModal.CreateIssueModal.season": "第 {seasonNumber} 季", "components.Layout.Sidebar.issues": "問題", "components.IssueDetails.deleteissue": "刪除問題", "components.IssueDetails.issuetype": "類型", "components.IssueDetails.allseasons": "所有季數", "components.IssueDetails.IssueDescription.description": "說明", "components.IssueDetails.IssueDescription.edit": "編輯說明", "components.IssueDetails.IssueDescription.deleteissue": "刪除問題", "components.IssueDetails.deleteissueconfirm": "確定要刪除這個問題嗎？", "components.IssueDetails.issuepagetitle": "問題", "components.IssueDetails.lastupdated": "最後更新時間", "components.IssueDetails.unknownissuetype": "不明", "components.IssueList.issues": "問題", "components.IssueList.IssueItem.viewissue": "查看問題", "components.IssueModal.CreateIssueModal.episode": "第 {episodeNumber} 集", "components.IssueModal.CreateIssueModal.problemepisode": "有問題的集數", "components.IssueModal.CreateIssueModal.problemseason": "有問題的季數", "components.IssueModal.CreateIssueModal.toastviewissue": "查看問題", "components.ManageSlideOver.manageModalClearMedia": "清除儲存資料", "components.ManageSlideOver.manageModalRequests": "請求", "components.ManageSlideOver.manageModalNoRequests": "沒有請求。", "components.ManageSlideOver.openarr": "開啟 {arr} 伺服器", "components.ManageSlideOver.openarr4k": "開啟 4K {arr} 伺服器", "components.ManageSlideOver.movie": "電影", "components.ManageSlideOver.markavailable": "標記為可觀看", "components.IssueModal.issueAudio": "音訊", "components.ManageSlideOver.downloadstatus": "下載狀態", "components.IssueModal.CreateIssueModal.allepisodes": "所有集數", "components.ManageSlideOver.manageModalClearMediaWarning": "※這將會刪除包括使用者請求在內所有有關此{mediaType}的資料。如果這{mediaType}存在於您的 {mediaServerName} 伺服器，資料將會在媒體庫掃描時重新建立。", "components.ManageSlideOver.mark4kavailable": "標記 4K 版為可觀看", "components.IssueModal.issueSubtitles": "字幕", "components.IssueModal.issueOther": "其他", "components.ManageSlideOver.tvshow": "影集", "components.ManageSlideOver.manageModalTitle": "管理{mediaType}", "components.IssueModal.issueVideo": "影片", "components.IssueList.IssueItem.issuetype": "類型", "components.IssueDetails.problemepisode": "有問題的集數", "components.IssueList.IssueItem.unknownissuetype": "不明", "components.IssueList.IssueItem.issuestatus": "狀態", "components.IssueModal.CreateIssueModal.allseasons": "所有季數", "components.IssueDetails.allepisodes": "所有集數", "components.IssueDetails.episode": "第 {episodeNumber} 集", "components.PermissionEdit.viewissues": "查看問題", "components.PermissionEdit.manageissuesDescription": "授予管理媒體問題的權限。", "components.PermissionEdit.viewissuesDescription": "授予查看其他使用者提出的媒體問題的權限。", "components.PermissionEdit.manageissues": "管理問題", "components.IssueDetails.comments": "評論", "components.IssueDetails.nocomments": "沒有評論。", "components.IssueDetails.openedby": "#{issueId} 由 {username} {relativeTime}報告", "components.IssueDetails.toastissuedeleted": "問題刪除成功！", "components.IssueDetails.toaststatusupdated": "問題狀態編輯成功！", "components.IssueDetails.toastissuedeletefailed": "刪除問題狀態時出了點問題。", "components.IssueList.IssueItem.openeduserdate": "{user}（{date}）", "components.IssueModal.CreateIssueModal.providedetail": "請詳細解釋您遇到的問題。", "components.IssueModal.CreateIssueModal.submitissue": "報告問題", "components.IssueModal.CreateIssueModal.toastFailedCreate": "提出問題報告時出了點問題。", "components.NotificationTypeSelector.userissueresolvedDescription": "當您報告的問題解決時取得通知。", "components.PermissionEdit.createissues": "報告問題", "components.PermissionEdit.createissuesDescription": "授予報告媒體問題的權限。", "components.IssueDetails.leavecomment": "發表評論", "components.IssueDetails.toasteditdescriptionfailed": "編輯問題說明時出了點問題。", "components.IssueList.showallissues": "查看所有問題", "components.IssueDetails.toaststatusupdatefailed": "編輯問題狀態時出了點問題。", "components.IssueModal.CreateIssueModal.reportissue": "報告問題", "components.IssueList.IssueItem.opened": "報告者", "components.IssueModal.CreateIssueModal.whatswrong": "請解釋您遇到的問題。", "components.IssueModal.CreateIssueModal.validationMessageRequired": "請輸入問題說明", "components.NotificationTypeSelector.issueresolved": "問題解決", "components.NotificationTypeSelector.issuecreated": "問題報告", "components.NotificationTypeSelector.issuecreatedDescription": "當使用者報告問題時發送通知。", "components.NotificationTypeSelector.issuecomment": "問題評論", "components.NotificationTypeSelector.adminissuecommentDescription": "當其他使用者加新評論時取得通知。", "components.NotificationTypeSelector.issuecommentDescription": "當問題有新評論時發送通知。", "components.NotificationTypeSelector.issueresolvedDescription": "當問題解決時發送通知。", "components.IssueDetails.openinarr": "開啟 {arr} 伺服器", "i18n.resolved": "已解決", "i18n.open": "未解決", "components.IssueList.sortAdded": "最新", "components.IssueList.sortModified": "最後修改", "components.IssueDetails.toasteditdescriptionsuccess": "問題說明編輯成功！", "components.NotificationTypeSelector.userissuecreatedDescription": "當其他使用者報告問題時取得通知。", "components.IssueDetails.IssueComment.delete": "刪除評論", "components.IssueDetails.IssueComment.edit": "編輯評論", "components.IssueDetails.IssueComment.validationComment": "請輸入評論", "components.IssueDetails.IssueComment.areyousuredelete": "確定要刪除這個評論嗎？", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "為 <strong>{title}</strong> 報告問題成功！", "components.NotificationTypeSelector.userissuecommentDescription": "當您報告的問題有新評論時取得通知。", "components.IssueDetails.IssueComment.postedby": "由 {username} {relativeTime}發表", "components.IssueDetails.IssueComment.postedbyedited": "由 {username} {relativeTime}發表（已編輯）", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "請輸入 API 令牌", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "請輸入應用程式 API 令牌", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "Pushbullet 通知設定儲存失敗。", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "應用程式 API 令牌", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "Pushover 通知設定儲存成功！", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "API 令牌", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "您 30 個字符的<UsersGroupsLink>使用者或群組識別碼</UsersGroupsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "Pushover 通知設定儲存失敗。", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "從您的<PushbulletSettingsLink>帳號設定</PushbulletSettingsLink>取得 API 令牌", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "Pushbullet 通知設定儲存成功！", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "建立一個 {applicationTitle} 專用的<ApplicationRegistrationLink>應用程式</ApplicationRegistrationLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "使用者或群組令牌", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "請輸入有效的使用者或群組令牌", "components.IssueDetails.openin4karr": "開啟 4K {arr} 伺服器", "components.IssueDetails.play4konplex": "在 Plex 上觀看 4K 版", "components.IssueDetails.playonplex": "在 Plex 上觀看", "components.IssueList.IssueItem.episodes": "集數", "components.IssueList.IssueItem.seasons": "季數", "components.IssueDetails.closeissue": "關閉問題", "components.IssueDetails.closeissueandcomment": "發表評論及關閉問題", "components.IssueDetails.reopenissueandcomment": "發表評論及重新開啟問題", "components.IssueDetails.reopenissue": "重新開啟問題", "components.ManageSlideOver.manageModalIssues": "未解決問題", "components.IssueModal.CreateIssueModal.extras": "特輯", "components.NotificationTypeSelector.issuereopened": "問題重新開啟", "components.NotificationTypeSelector.adminissuereopenedDescription": "當其他使用者重新開啟問題時取得通知。", "components.NotificationTypeSelector.adminissueresolvedDescription": "當其他使用者解決問題時取得通知。", "components.NotificationTypeSelector.issuereopenedDescription": "當問題重新開啟時發送通知。", "components.NotificationTypeSelector.userissuereopenedDescription": "當您報告的問題重新開啟時取得通知。", "components.RequestModal.requestmovies4k": "提出 4K 請求", "components.RequestModal.requestseasons4k": "提出 4K 請求", "components.RequestModal.requestmovies": "提出請求", "components.MovieDetails.productioncountries": "製作國家", "components.RequestModal.selectmovies": "請選擇電影", "components.TvDetails.productioncountries": "製作國家", "components.IssueDetails.commentplaceholder": "發表評論…", "components.RequestModal.requestApproved": "<strong>{title}</strong> 的請求已獲批准。", "components.RequestModal.approve": "批准請求", "components.Settings.RadarrModal.inCinemas": "已上映", "components.Settings.RadarrModal.released": "已發佈", "components.Settings.RadarrModal.announced": "已公佈", "components.Settings.Notifications.enableMentions": "允許提及", "components.Settings.Notifications.NotificationsGotify.agentenabled": "啟用通知", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Gotify 測試通知已發送！", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "請刪除結尾斜線", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "Gotify 通知設定儲存失敗。", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Gotify 通知設定儲存成功！", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "Gotify 測試通知發送失敗。", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "發送 Gotify 測試通知中…", "components.Settings.Notifications.NotificationsGotify.token": "應用程式令牌", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "請輸入應用程式令牌", "components.Settings.Notifications.NotificationsGotify.url": "伺服器網址", "components.Settings.Notifications.NotificationsGotify.validationTypes": "請選擇通知類型", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "請輸入有效的網址", "i18n.import": "匯入", "i18n.importing": "匯入中…", "components.ManageSlideOver.manageModalMedia": "媒體", "components.ManageSlideOver.manageModalMedia4k": "4K 媒體", "components.ManageSlideOver.manageModalAdvanced": "進階", "components.ManageSlideOver.alltime": "歷史", "components.ManageSlideOver.opentautulli": "開啟 Tautulli 伺服器", "components.ManageSlideOver.pastdays": "過去 {days, number} 天", "components.ManageSlideOver.playedby": "觀看者", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> 次", "components.Settings.validationUrlTrailingSlash": "請刪除結尾斜線", "components.Settings.externalUrl": "外部網址", "components.Settings.toastTautulliSettingsSuccess": "Tautulli 設定儲存成功！", "components.ManageSlideOver.markallseasons4kavailable": "標記 4K 版的所有季數為可觀看", "components.ManageSlideOver.markallseasonsavailable": "標記所有季數為可觀看", "components.Settings.tautulliSettings": "Tautulli 設定", "components.Settings.tautulliApiKey": "應用程式密鑰", "components.Settings.validationUrlBaseTrailingSlash": "請刪除結尾斜線", "components.UserProfile.recentlywatched": "最近觀看", "components.Settings.toastTautulliSettingsFailure": "儲存 Tautulli 設定時出了點問題。", "components.Settings.urlBase": "網站根目錄", "components.Settings.validationApiKey": "請輸入應用程式密鑰", "components.Settings.validationUrlBaseLeadingSlash": "請加前置斜線", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "頻道標籤", "components.Settings.validationUrl": "請輸入有效的網址", "components.UserList.newplexsigninenabled": "<strong>允許新的 Plex 登入</strong> 的設定目前已啟用。還沒匯入的 Plex 使用者也能登入。", "components.Settings.tautulliSettingsDescription": "關於 Tautulli 伺服器的設定。Jellyseerr 會從 Tautulli 獲取 Plex 媒體的觀看歷史記錄。", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "Discord 使用者 ID", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "您的<FindDiscordIdLink>Discord 使用者 ID 號碼</FindDiscordIdLink>", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "請輸入有效的 Discord 使用者 ID", "components.Settings.SettingsAbout.appDataPath": "數據目錄", "components.RequestBlock.languageprofile": "語言設定", "components.StatusChecker.appUpdated": "{applicationTitle} 軟體已更新", "components.StatusChecker.reloadApp": "刷新頁面", "components.StatusChecker.restartRequired": "必須重新啟動伺服器", "i18n.restartRequired": "必須重新啟動伺服器", "components.Settings.deleteServer": "刪除 {serverType} 伺服器", "components.StatusChecker.appUpdatedDescription": "請點擊以下的按鈕刷新頁面。", "components.StatusChecker.restartRequiredDescription": "請重新啟動伺服器以應用設定的變更。", "components.MovieDetails.physicalrelease": "實體光碟", "components.MovieDetails.theatricalrelease": "影院", "components.MovieDetails.digitalrelease": "數字版本", "components.PermissionEdit.viewrecent": "查看最近新增", "components.PermissionEdit.viewrecentDescription": "授予查看最近新增的媒體的權限。", "components.RequestList.RequestItem.tmdbid": "TMDB ID", "components.TitleCard.mediaerror": "找不到{mediaType}", "components.TitleCard.tvdbid": "TheTVDB ID", "components.RequestCard.tvdbid": "TheTVDB ID", "components.TitleCard.cleardata": "清除儲存資料", "components.TitleCard.tmdbid": "TMDB ID", "components.RequestCard.tmdbid": "TMDB ID", "components.RequestList.RequestItem.tvdbid": "TheTVDB ID", "components.Discover.plexwatchlist": "您的 Plex 關注列表", "components.PermissionEdit.autorequestMovies": "自動提出電影請求", "components.PermissionEdit.autorequestSeries": "自動提出影集請求", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "Plex 關注列表同步", "components.PermissionEdit.autorequest": "自動提出請求", "components.Discover.DiscoverWatchlist.discoverwatchlist": "您的 Plex 關注列表", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "自動提出電影請求", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "自動提出影集請求", "components.NotificationTypeSelector.mediaautorequested": "請求自動提出", "components.PermissionEdit.autorequestMoviesDescription": "授予從 Plex 關注列表中自動提出非 4K 電影請求的權限。", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "從您的 <PlexWatchlistSupportLink>Plex 關注列表</PlexWatchlistSupportLink>中自動提出電影請求", "components.NotificationTypeSelector.mediaautorequestedDescription": "當您的 Plex 關注列表中的媒體自動提出請求時取得通知。", "components.PermissionEdit.autorequestDescription": "授予從 Plex 關注列表中自動提出非 4K 媒體請求的權限。", "components.PermissionEdit.autorequestSeriesDescription": "授予從 Plex 關注列表中自動提出非 4K 影集請求的權限。", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "從您的 <PlexWatchlistSupportLink>Plex 關注列表</PlexWatchlistSupportLink>中自動提出影集請求", "components.Settings.SettingsLogs.viewdetails": "查看詳細信息", "components.TvDetails.reportissue": "報告問題", "components.MovieDetails.managemovie": "管理電影", "components.Discover.DiscoverWatchlist.watchlist": "Plex 關注列表", "components.UserProfile.plexwatchlist": "Plex 關注列表", "components.MovieDetails.reportissue": "報告問題", "components.PermissionEdit.viewwatchlists": "查看 Plex 關注列表", "components.PermissionEdit.viewwatchlistsDescription": "授予查看其他使用者的 Plex 關注列表的權限。", "components.TvDetails.manageseries": "管理影集", "components.Settings.restartrequiredTooltip": "Jellyseerr 必須重新啟動才能應用設定的變更", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "電影請求", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "影集請求", "components.Layout.UserDropdown.requests": "請求", "components.TvDetails.seasonstitle": "季數", "components.RequestBlock.decline": "拒絕請求", "components.RequestBlock.edit": "編輯請求", "components.RequestBlock.lastmodifiedby": "最後修改者", "components.RequestBlock.requestdate": "請求日期", "components.RequestCard.approverequest": "批准請求", "components.RequestCard.cancelrequest": "取消請求", "components.RequestCard.declinerequest": "拒絕請求", "components.TvDetails.seasonnumber": "第 {seasonNumber} 季", "components.RequestBlock.approve": "批准請求", "components.RequestBlock.delete": "刪除請求", "components.RequestCard.editrequest": "編輯請求", "components.RequestBlock.requestedby": "請求者", "components.StatusBadge.playonplex": "在 {mediaServerName} 上觀看", "components.StatusBadge.managemedia": "管理{mediaType}", "components.StatusBadge.openinarr": "開啟 {arr} 伺服器", "components.TvDetails.status4k": "4K 版{status}", "components.TvDetails.episodeCount": "{episodeCount} 集", "components.AirDateBadge.airedrelative": "{relativeTime}播出", "components.MovieDetails.rtaudiencescore": "爛番茄觀眾評分", "components.TvDetails.Season.somethingwentwrong": "檢索數據時出了點問題。", "components.TvDetails.tmdbuserscore": "TMDB 使用者評分", "components.TvDetails.rtcriticsscore": "爛番茄專業評分", "components.AirDateBadge.airsrelative": "{relativeTime}播出", "components.MovieDetails.tmdbuserscore": "TMDB 使用者評分", "components.MovieDetails.rtcriticsscore": "爛番茄專業評分", "components.TvDetails.rtaudiencescore": "爛番茄觀眾評分", "components.RequestModal.requestmovietitle": "提出電影請求", "components.RequestModal.requestmovie4ktitle": "提出 4K 電影請求", "components.RequestModal.requestcollection4ktitle": "提出 4K 電影系列請求", "components.RequestModal.requestseriestitle": "提出影集請求", "components.RequestModal.requestseries4ktitle": "提出 4K 影集請求", "components.RequestModal.requestcollectiontitle": "提出電影系列請求", "components.RequestModal.SearchByNameModal.nomatches": "找不到此影集的數據。", "components.UserProfile.emptywatchlist": "您的 <PlexWatchlistSupportLink>Plex 關注列表</PlexWatchlistSupportLink>中的媒體會顯示在這裡。", "components.Discover.emptywatchlist": "您的 <PlexWatchlistSupportLink>Plex 關注列表</PlexWatchlistSupportLink>中的媒體會顯示在這裡。", "components.Settings.advancedTooltip": "錯誤的設定可能會破壞應用程式功能", "components.Settings.experimentalTooltip": "啟用此設定可能會出現意外的應用程式行為", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "目前的頻率", "components.TvDetails.Season.noepisodes": "没有剧集列表。", "components.Discover.CreateSlider.providetmdbgenreid": "提供一個 TMDB 類型 ID", "components.Discover.CreateSlider.nooptions": "沒有結果。", "components.Discover.CreateSlider.providetmdbkeywordid": "提供一個 TMDB 關鍵字 ID", "components.Discover.CreateSlider.needresults": "您必須至少擁有一個結果。", "components.Discover.FilterSlideover.tmdbuserscore": "TMDB 使用者評分", "components.Discover.tmdbsearch": "TMDB 搜尋", "components.Discover.CreateSlider.providetmdbsearch": "提供一個搜尋詞", "components.Discover.CreateSlider.searchStudios": "搜尋工作室…", "components.Discover.CreateSlider.providetmdbnetwork": "提供一個 TMDB 電視網 ID", "components.Discover.CreateSlider.providetmdbstudio": "提供一個 TMDB 工作室 ID", "components.Discover.CreateSlider.searchGenres": "搜尋種類…", "components.Discover.CreateSlider.searchKeywords": "搜尋關鍵字…", "components.Discover.DiscoverTv.discovertv": "影集", "components.Discover.FilterSlideover.studio": "工作室", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "TMDB 評分降序排列", "components.Discover.DiscoverTv.sortTitleDesc": "標題字母 (Z-A) 降序排列", "components.Discover.FilterSlideover.firstAirDate": "首集播映日期", "components.Discover.FilterSlideover.releaseDate": "發行日期", "components.Discover.FilterSlideover.to": "到", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "TMDB 評分升序排列", "components.Discover.DiscoverMovies.discovermovies": "電影", "components.Discover.DiscoverMovies.sortPopularityAsc": "人氣升序排列", "components.Discover.DiscoverMovies.sortPopularityDesc": "人氣降序排列", "components.Discover.FilterSlideover.from": "從", "components.Discover.FilterSlideover.genres": "種類", "components.Discover.FilterSlideover.keywords": "關鍵字", "components.Discover.FilterSlideover.runtime": "播放時間", "components.Discover.DiscoverMovies.sortTitleDesc": "標題字母 (Z-A) 降序排列", "components.Discover.FilterSlideover.originalLanguage": "原始語言", "components.Discover.customizediscover": "客製化「探索」", "components.Discover.CreateSlider.addSlider": "新增模塊", "components.Discover.CreateSlider.addcustomslider": "建立客製化模塊", "components.Discover.CreateSlider.addfail": "新建模塊失敗。", "components.Discover.FilterSlideover.ratingText": "介於 {minValue} 和 {maxValue} 之間的評分", "components.Discover.CreateSlider.addsuccess": "成功建立新模塊並儲存客製化設定。", "components.Discover.DiscoverTv.sortPopularityAsc": "人氣升序排列", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "TMDB 評分升序排列", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "最早播映日期升序排列", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "最早播映日期降序排列", "components.Discover.DiscoverTv.sortPopularityDesc": "人氣降序排列", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "TMDB 評分降序排列", "components.Discover.DiscoverSliderEdit.deletefail": "刪除模塊失敗。", "components.Discover.CreateSlider.editfail": "編輯模塊失敗。", "components.DownloadBlock.formattedTitle": "{title}: 第{seasonNumber}季第{episodeNumber}集", "components.Discover.CreateSlider.editSlider": "編輯模塊", "components.Discover.CreateSlider.editsuccess": "成功編輯模塊並儲存「探索」客製化設定。", "components.Discover.DiscoverSliderEdit.deletesuccess": "成功刪除模塊。", "components.Discover.createnewslider": "新建模塊", "components.Settings.SettingsJobsCache.imagecacheDescription": "當啟用此設定，Jellyseerr 將會代理並快取來自預設外部來源的影像。快取的影像將會儲存到您的設定資料夾中。您可以在<code>{appDataPath}/cache/images</code>找到這些檔案。", "components.Settings.SettingsJobsCache.imagecachecount": "快取的影像", "components.Discover.PlexWatchlistSlider.emptywatchlist": "新增到您 <PlexWatchlistSupportLink>Plex 觀看清單</PlexWatchlistSupportLink> 的媒體將會在這裡顯示。", "components.Discover.resettodefault": "重置為預設", "components.Discover.tmdbtvgenre": "TMDB 影集類型", "components.Layout.Sidebar.browsemovies": "電影", "components.Selector.searchKeywords": "搜尋關鍵字…", "components.Selector.showmore": "顯示更多", "components.Layout.Sidebar.browsetv": "影集", "components.Selector.searchGenres": "選擇種類…", "components.Selector.searchStudios": "搜尋工作室…", "components.Selector.nooptions": "沒有結果。", "components.Selector.showless": "檢視較少", "components.Selector.starttyping": "輸入文字以開始搜尋。", "components.Settings.SettingsJobsCache.imagecache": "影像快取", "components.Settings.SettingsJobsCache.image-cache-cleanup": "清理影像快取", "components.Settings.SettingsMain.hideAvailable": "隱藏可觀看的媒體", "components.Settings.SettingsMain.locale": "顯示語言", "components.Discover.resetsuccess": "成功重設「探索」客製化設定。", "components.Settings.SettingsMain.toastApiKeySuccess": "成功產生 API 密鑰！", "components.Settings.SettingsMain.toastSettingsFailure": "儲存設定時發生問題。", "components.Settings.SettingsMain.toastSettingsSuccess": "設定儲存成功！", "components.Settings.SettingsMain.validationApplicationUrl": "請輸入有效的網址", "components.Settings.SettingsMain.validationApplicationTitle": "請輸入應用程式名稱", "components.Settings.SettingsMain.validationApplicationUrlTrailingSlash": "請刪除結尾斜線", "components.Discover.DiscoverMovieKeyword.keywordMovies": "{keywordTitle} 電影", "components.Discover.tmdbtvkeyword": "TMDB 影集關鍵字", "components.Discover.tvgenres": "影集種類", "components.Settings.SettingsMain.apikey": "API 密鑰", "components.Settings.SettingsMain.applicationTitle": "應用程式名稱", "components.Discover.CreateSlider.slidernameplaceholder": "模塊名稱", "components.Discover.CreateSlider.starttyping": "輸入文字以搜尋。", "components.Discover.CreateSlider.validationDatarequired": "您必須提供一個資料值。", "components.Discover.CreateSlider.validationTitlerequired": "您必須提供一個標題。", "components.Discover.DiscoverSliderEdit.enable": "切換顯示", "components.Discover.DiscoverSliderEdit.remove": "移除", "components.Discover.DiscoverMovies.sortTitleAsc": "標題字母 (A-Z) 升序排列", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "發行日期升序排列", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "發行日期降序排列", "components.Discover.DiscoverTvKeyword.keywordSeries": "{keywordTitle} 影集", "components.Discover.DiscoverTv.sortTitleAsc": "標題字母 (A-Z) 升序排列", "components.Discover.FilterSlideover.clearfilters": "清除現行過濾器", "components.Discover.FilterSlideover.filters": "過濾器", "components.Discover.FilterSlideover.runtimeText": "{minValue}-{maxValue} 分鐘的播放時間", "components.Discover.PlexWatchlistSlider.plexwatchlist": "您的 Plex 觀看清單", "components.Discover.RecentlyAddedSlider.recentlyAdded": "最近新增", "components.Discover.moviegenres": "電影種類", "components.Discover.networks": "電視網", "components.Discover.resetfailed": "重設「探索」客製化設定時發生錯誤。", "components.Discover.resetwarning": "所有模塊已重置為預設。這將同時刪除所有客製模塊！", "components.Discover.stopediting": "停止編輯", "components.Discover.studios": "工作室", "components.Discover.tmdbmoviegenre": "TMDB 電影種類", "components.Discover.tmdbmoviekeyword": "TMDB 電影關鍵字", "components.Discover.tmdbnetwork": "TMDB 電視網", "components.Discover.tmdbstudio": "TMDB 工作室", "components.Discover.updatefailed": "更新「探索」客製化設定時發生錯誤。", "components.Discover.updatesuccess": "已更新「探索」客製化設定。", "components.RequestCard.unknowntitle": "未知標題", "components.RequestList.RequestItem.unknowntitle": "未知標題", "components.Settings.SettingsMain.general": "一般", "components.Settings.SettingsMain.generalsettings": "一般設定", "components.Settings.SettingsMain.generalsettingsDescription": "Jellyseerr 的全域與預設設定。", "components.Settings.SettingsMain.originallanguage": "「探索」語言", "components.Settings.SettingsMain.originallanguageTip": "根據原始語言過濾內容", "components.Settings.SettingsMain.partialRequestsEnabled": "允許不完整的影集請求", "components.Settings.SettingsMain.toastApiKeyFailure": "產生 API 密鑰時發生問題。", "components.StatusBadge.seasonepisodenumber": "S{seasonNumber}E{episodeNumber}", "components.Discover.FilterSlideover.streamingservices": "串流服務", "components.Settings.SettingsJobsCache.imagecachesize": "總快取大小", "components.Settings.SettingsMain.applicationurl": "應用程式網址", "components.Settings.SettingsMain.cacheImages": "啟用影像快取", "components.Settings.SettingsMain.cacheImagesTip": "快取外部來源影像（需要大量磁碟空間）", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {# 個使用中的篩選項目} other {# 個使用中的篩選項目}}", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {# 個使用中的篩選項目} other {# 個使用中的篩選項目}}", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {# 個使用中的篩選項目} other {# 個使用中的篩選項目}}", "components.Discover.FilterSlideover.tmdbuservotecount": "TMDB 用戶評分數", "components.Discover.FilterSlideover.voteCount": "在 {minValue} 和 {maxValue} 之間的評分數", "components.Discover.tmdbmoviestreamingservices": "TMDB 電影串流服務", "components.Settings.Notifications.NotificationsPushover.deviceDefault": "預設裝置", "components.Settings.Notifications.NotificationsPushover.sound": "通知提示聲", "components.Discover.tmdbtvstreamingservices": "TMDB 電視串流服務", "components.Settings.SettingsJobsCache.editJobScheduleSelectorSeconds": "每 {jobScheduleSeconds} 秒", "components.Settings.SettingsJobsCache.availability-sync": "同步媒體可用性", "components.Settings.SonarrModal.seriesType": "劇集類型", "components.Settings.SonarrModal.tagRequests": "標記請求", "components.Settings.SonarrModal.tagRequestsInfo": "自動新增帶有請求者的用户 ID 和顯示名稱的附加標籤", "components.Settings.RadarrModal.tagRequests": "標籤請求", "components.Settings.RadarrModal.tagRequestsInfo": "自動新增帶有請求者的用户 ID 和顯示名稱的附加標籤", "components.UserProfile.UserSettings.UserNotificationSettings.sound": "通知提示聲", "i18n.collection": "合集", "components.MovieDetails.imdbuserscore": "IMDB 用戶評分", "components.UserProfile.UserSettings.UserNotificationSettings.deviceDefault": "預設裝置", "components.Settings.SonarrModal.animeSeriesType": "動漫劇集類型"}