@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    min-height: calc(100% + env(safe-area-inset-top));
    padding: env(safe-area-inset-top) env(safe-area-inset-right)
      calc(4rem + env(safe-area-inset-bottom)) env(safe-area-inset-left);
    scrollbar-width: thin;
    scrollbar-color: #4b5563 #1f2937;
  }

  html:hover {
    scrollbar-color: #6b7280 #1f2937;
  }

  /* WebKit scrollbar styles */
  html::-webkit-scrollbar {
    width: 10px;
  }

  html::-webkit-scrollbar-track {
    background: #1f2937;
  }

  html::-webkit-scrollbar-thumb {
    background-color: #4b5563;
  }

  html:hover::-webkit-scrollbar-thumb {
    background-color: #6b7280;
  }

  @media (min-width: 640px) {
    html {
      padding-bottom: env(safe-area-inset-bottom);
    }
  }

  body {
    @apply bg-gray-900;
    -webkit-overflow-scrolling: touch;
  }

  code {
    @apply rounded-md bg-gray-800 px-2 py-1;
  }

  input[type='search']::-webkit-search-cancel-button {
    -webkit-appearance: none;
  }
}

@layer components {
  .searchbar {
    padding-top: env(safe-area-inset-top);
    height: calc(4rem + env(safe-area-inset-top));
  }

  .sidebar {
    @apply border-r border-gray-700;
    padding-top: env(safe-area-inset-top);
    padding-left: env(safe-area-inset-left);
    background: linear-gradient(180deg, rgba(31, 41, 55, 1) 0%, #131928 100%);
  }

  .slideover {
    padding-top: calc(0.75rem + env(safe-area-inset-top)) !important;
    padding-bottom: calc(0.75rem + env(safe-area-inset-top)) !important;
  }

  .sidebar-close-button {
    top: env(safe-area-inset-top);
  }

  .server-type-button {
    @apply rounded-md border border-gray-500 bg-gray-700 px-4  py-2 text-white transition duration-150 ease-in-out hover:bg-gray-500;
  }
  .jellyfin-server svg {
    @apply h-6 w-6;
  }
  .emby-server svg {
    @apply h-7 w-7;
  }

  ul.cards-vertical,
  ul.cards-horizontal {
    @apply grid gap-4;
  }

  ul.cards-vertical {
    grid-template-columns: repeat(auto-fill, minmax(9.375rem, 1fr));
  }

  ul.cards-horizontal {
    grid-template-columns: repeat(auto-fill, minmax(16.5rem, 1fr));
  }

  .provider-icons {
    grid-template-columns: repeat(auto-fill, minmax(3.5rem, 1fr));
  }

  .slider-header {
    @apply relative mt-6 mb-4 flex;
  }

  .slider-title {
    @apply inline-flex items-center text-xl font-bold leading-7 text-gray-300 sm:truncate sm:text-2xl sm:leading-9;
  }

  a.slider-title {
    @apply transition duration-300 hover:text-white;
  }

  a.slider-title svg {
    @apply ml-2 h-6 w-6;
  }

  .media-page {
    @apply relative -mx-4 bg-cover bg-center px-4;
    margin-top: calc(-4rem - env(safe-area-inset-top));
    padding-top: calc(4rem + env(safe-area-inset-top));
  }

  .media-page-bg-image {
    @apply absolute inset-0 h-full w-full;
    z-index: -10;
  }

  .media-header {
    @apply flex flex-col items-center pt-4 xl:flex-row xl:items-end;
  }

  .media-poster {
    @apply w-32 overflow-hidden rounded shadow md:w-44 md:rounded-lg md:shadow-2xl xl:mr-4 xl:w-52;
  }

  .media-status {
    @apply mb-2 space-x-2;
  }

  .media-title {
    @apply mt-4 flex flex-1 flex-col text-center text-white xl:mr-4 xl:mt-0 xl:text-left;
  }

  .media-title > h1 {
    @apply text-2xl font-bold xl:text-4xl;
  }

  h1 .media-year {
    @apply text-2xl;
  }

  .media-attributes {
    @apply mt-1 flex flex-wrap items-center justify-center space-x-1 text-xs text-gray-300 sm:text-sm xl:mt-0 xl:justify-start xl:text-base;
  }

  .media-attributes a {
    @apply transition duration-300 hover:text-white hover:underline;
  }

  .media-actions {
    @apply relative mt-4 flex flex-shrink-0 flex-wrap items-center justify-center sm:flex-nowrap sm:justify-end xl:mt-0;
  }

  .media-actions > * {
    @apply mb-3 sm:mb-0;
  }

  .media-overview {
    @apply flex flex-col pt-8 pb-4 text-white lg:flex-row;
  }

  .media-overview-left {
    @apply flex-1 lg:mr-8;
  }

  .tagline {
    @apply mb-4 text-xl italic text-gray-400 lg:text-2xl;
  }

  .media-overview h2 {
    @apply text-xl font-bold text-gray-300 sm:text-2xl;
  }

  .media-overview p {
    @apply pt-2 text-sm text-gray-400 sm:text-base;
  }

  ul.media-crew {
    @apply mt-6 grid grid-cols-2 gap-6 sm:grid-cols-3;
  }

  ul.media-crew > li {
    @apply col-span-1 flex flex-col font-bold text-gray-300;
  }

  a.crew-name,
  .media-fact-value a,
  .media-fact-value button {
    @apply font-normal text-gray-400 transition duration-300 hover:text-gray-100 hover:underline;
  }

  .media-overview-right {
    @apply mt-8 w-full lg:mt-0 lg:w-80;
  }

  .media-facts {
    @apply rounded-lg border border-gray-700 bg-gray-900 text-sm font-bold text-gray-300 shadow;
  }

  .media-fact {
    @apply flex justify-between border-b border-gray-700 px-4 py-2 last:border-b-0;
  }

  .media-fact-value {
    @apply ml-2 text-right text-sm font-normal text-gray-400;
  }

  .media-ratings {
    @apply flex items-center justify-center space-x-5 border-b border-gray-700 px-4 py-2 font-medium last:border-b-0;
  }

  .media-rating {
    @apply flex items-center space-x-1;
  }

  .error-message {
    @apply relative top-0 bottom-0 left-0 right-0 flex h-screen flex-col items-center justify-center text-center text-gray-300;
  }

  .heading {
    @apply text-2xl font-bold leading-8 text-gray-100;
  }

  .description {
    @apply mt-1 max-w-4xl text-sm leading-5 text-gray-400;
  }

  img.avatar-sm {
    @apply mr-1 h-5 w-5 scale-100 transform-gpu rounded-full transition duration-300 group-hover:scale-105;
  }

  .card-field {
    @apply flex items-center truncate py-0.5 text-sm sm:py-1;
  }

  .card-field-name {
    @apply mr-2 font-bold;
  }

  .card-field a {
    @apply transition duration-300 hover:text-white hover:underline;
  }

  .section {
    @apply mt-6 mb-10 text-white;
  }

  .form-row {
    @apply mt-6 max-w-6xl sm:mt-5 sm:grid sm:grid-cols-3 sm:items-start sm:gap-4;
  }

  .form-input-area {
    @apply text-sm text-white sm:col-span-2;
  }

  .form-input-field {
    @apply flex max-w-xl rounded-md shadow-sm;
  }

  .actions {
    @apply mt-8 border-t border-gray-700 pt-5 text-white;
  }

  label,
  .group-label {
    @apply mb-1 block text-sm font-bold leading-5 text-gray-400;
  }

  label.checkbox-label {
    @apply sm:mt-1;
  }

  label.text-label {
    @apply sm:mt-2;
  }

  label a {
    @apply text-gray-100 transition duration-300 hover:text-white hover:underline;
  }

  .label-required {
    @apply ml-1 text-red-500;
  }

  .label-tip {
    @apply block font-medium text-gray-500;
  }

  button,
  input,
  select,
  textarea {
    @apply disabled:cursor-not-allowed;
  }

  input[type='checkbox'] {
    @apply h-6 w-6 rounded-md text-indigo-600 transition duration-150 ease-in-out;
  }

  input[type='text'],
  input[type='password'],
  select,
  textarea {
    @apply block w-full min-w-0 flex-1 rounded-md border border-gray-500 bg-gray-700 text-white transition duration-150 ease-in-out sm:text-sm sm:leading-5;
  }

  input.rounded-l-only,
  select.rounded-l-only,
  textarea.rounded-l-only {
    @apply rounded-r-none;
  }

  input.rounded-r-only,
  select.rounded-r-only,
  textarea.rounded-r-only {
    @apply rounded-l-none;
  }

  input.short {
    @apply w-20;
  }

  select.short {
    @apply w-min;
  }

  button > span {
    @apply whitespace-nowrap;
  }

  button.input-action {
    @apply relative -ml-px inline-flex items-center border border-gray-500 bg-indigo-600 bg-opacity-80 px-3 py-2 text-sm font-medium leading-5 text-white last:rounded-r-md sm:px-3.5;
  }

  button.input-action[disabled] {
    filter: grayscale(100%);
  }

  button.input-action:not([disabled]) {
    @apply transition duration-150 ease-in-out hover:bg-opacity-100 active:bg-gray-100 active:text-gray-700;
  }

  .button-md :where(svg),
  button.input-action svg {
    @apply ml-2 mr-2 h-5 w-5 first:ml-0 last:mr-0;
  }

  .button-sm svg {
    @apply ml-1.5 mr-1.5 h-4 w-4 first:ml-0 last:mr-0;
  }

  svg.icon-md {
    @apply h-5 w-5;
  }

  svg.icon-sm {
    @apply h-4 w-4;
  }

  .protocol {
    @apply inline-flex cursor-default items-center rounded-l-md border border-r-0 border-gray-500 bg-gray-600 px-3 text-gray-100 sm:text-sm;
  }

  .error {
    @apply mt-2 text-sm text-red-500;
  }

  .form-group {
    @apply mt-6 text-white;
  }

  .toast {
    width: 360px;
  }

  .react-select-container {
    @apply w-full;
  }

  .react-select-container:has(+ .input-action) .react-select__control {
    @apply rounded-r-none border border-gray-500 bg-gray-700 text-white hover:border-gray-500;
  }

  .react-select-container .react-select__control {
    @apply rounded-md border border-gray-500 bg-gray-700 text-white hover:border-gray-500;
  }

  .react-select-container-dark .react-select__control {
    @apply border border-gray-700 bg-gray-800;
  }

  .react-select-container .react-select__control--is-focused {
    @apply rounded-md border border-gray-500 bg-gray-700 text-white shadow;
  }

  .react-select-container-dark .react-select__control--is-focused {
    @apply border-gray-600 bg-gray-800;
  }

  .react-select-container .react-select__menu {
    @apply bg-gray-700 text-gray-300;
  }

  .react-select-container-dark .react-select__menu {
    @apply bg-gray-800;
  }

  .react-select-container .react-select__option--is-focused {
    @apply bg-gray-600 text-white;
  }

  .react-select-container-dark .react-select__option--is-focused {
    @apply bg-gray-700;
  }

  .react-select-container .react-select__indicator-separator {
    @apply bg-gray-500;
  }

  .react-select-container .react-select__indicator {
    @apply text-gray-500;
  }

  .react-select-container .react-select__placeholder {
    @apply text-gray-400;
  }

  .react-select-container .react-select__multi-value {
    @apply rounded-md border border-gray-500 bg-gray-800;
  }

  .react-select-container .react-select__multi-value__label {
    @apply text-white;
  }

  .react-select-container .react-select__multi-value__remove {
    @apply cursor-pointer rounded-r-md hover:bg-red-700 hover:text-red-100;
  }

  .react-select-container .react-select__input {
    @apply border-none text-base shadow-sm ring-0;
  }

  .react-select-container .react-select__input input:focus {
    @apply border-none;
    box-shadow: none;
  }

  .react-select-container .react-select__input-container {
    @apply text-white;
  }

  .react-select-container .react-select__single-value {
    @apply text-sm text-gray-100;
  }

  .react-select-container .react-select__placeholder {
    @apply text-sm text-gray-500;
  }

  .datepicker-wrapper > button {
    @apply top-0;
  }

  .datepicker-wrapper > div {
    @apply fixed left-0 right-0 w-full px-4 md:w-auto;
  }

  .datepicker-wrapper > div > div:nth-child(2) > div {
    @apply !flex-col;
  }

  .datepicker-wrapper > div > div:nth-child(2) > div > div > div {
    @apply !w-full !min-w-full;
  }

  .datepicker-wrapper > div > div:first-child {
    @apply hidden;
  }

  input[type='range']::-webkit-slider-thumb {
    @apply rounded-full border-0 bg-indigo-500;
    pointer-events: all;
    width: 16px;
    height: 16px;
    -webkit-appearance: none;
  }

  input[type='range']::-moz-range-thumb {
    @apply rounded-full border-0 bg-indigo-500;
    pointer-events: all;
    width: 16px;
    height: 16px;
    -webkit-appearance: none;
  }
}

@layer utilities {
  .absolute-top-shift {
    top: calc(-4rem - env(safe-area-inset-top));
  }

  .absolute-bottom-shift {
    bottom: calc(5rem + env(safe-area-inset-bottom));
  }

  .safe-shift-edit-menu {
    bottom: calc(54px + env(safe-area-inset-bottom));
  }

  .padding-bottom-safe {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .min-h-screen-shift {
    min-height: calc(100vh + env(safe-area-inset-top));
  }

  /* Used for animating height */
  .extra-max-height {
    max-height: 100rem;
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .hide-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .text-overseerr {
    @apply bg-gradient-to-br from-indigo-400 to-purple-400 bg-clip-text text-transparent;
  }

  @media all and (display-mode: browser) {
    .pwa-only {
      @apply hidden;
    }
  }

  .extra-bottom-space {
    height: calc(4rem + env(safe-area-inset-bottom));
  }
}

.ptr--ptr {
  box-shadow: initial !important;
  position: absolute !important;
  z-index: 30 !important;
}

.ptr--refresh {
  overflow: visible !important;
  z-index: 30 !important;
}

.ptr--pull {
  z-index: 30 !important;
}

.ptr--box {
  margin-bottom: -13px !important;
}
