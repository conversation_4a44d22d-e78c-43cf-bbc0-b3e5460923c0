name: ✨ Feature Request
description: Suggest an idea
labels: ['enhancement', 'awaiting triage']
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this feature request!

        Please note that we use GitHub issues exclusively for bug reports and feature requests. For support requests, please use our other support channels to get help.
  - type: textarea
    id: description
    attributes:
      label: Description
      description: Is your feature request related to a problem? If so, please provide a clear and concise description of the problem; e.g., "I'm always frustrated when [...]."
    validations:
      required: true
  - type: textarea
    id: desired-behavior
    attributes:
      label: Desired Behavior
      description: Provide a clear and concise description of what you want to happen.
    validations:
      required: true
  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Provide any additional information or screenshots that may be relevant or helpful.
  - type: checkboxes
    id: terms
    attributes:
      label: Code of Conduct
      description: By submitting this issue, you agree to follow our [Code of Conduct](https://github.com/fallenbagel/jellyseerr/blob/develop/CODE_OF_CONDUCT.md)
      options:
        - label: I agree to follow <PERSON><PERSON><PERSON><PERSON>'s Code of Conduct
          required: true
