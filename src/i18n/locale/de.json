{"components.AirDateBadge.airedrelative": "Ausgestrahlt {relativeTime}", "components.AirDateBadge.airsrelative": "Ausstrahlung {relativeTime}", "components.AppDataWarning.dockerVolumeMissingDescription": "Die <code>{appDataPath}</code> Volume Einbindung wurde nicht korrekt konfiguriert. Alle Daten werden gelöscht, wenn dieser Container gestoppt oder neugestartet wird.", "components.CollectionDetails.numberofmovies": "{count} Filme", "components.CollectionDetails.overview": "Übersicht", "components.CollectionDetails.requestcollection": "Sammlung anfragen", "components.CollectionDetails.requestcollection4k": "Sammlung in 4K anfragen", "components.Discover.DiscoverMovieGenre.genreMovies": "{genre}-Filme", "components.Discover.DiscoverMovieLanguage.languageMovies": "Filme auf {language}", "components.Discover.DiscoverNetwork.networkSeries": "{network}-Serien", "components.Discover.DiscoverStudio.studioMovies": "{studio}-Filme", "components.Discover.DiscoverTvGenre.genreSeries": "{genre}-Serien", "components.Discover.DiscoverTvLanguage.languageSeries": "Serien auf {language}", "components.Discover.DiscoverWatchlist.discoverwatchlist": "<PERSON><PERSON>", "components.Discover.DiscoverWatchlist.watchlist": "Plex Me<PERSON>e", "components.Discover.MovieGenreList.moviegenres": "Film-Genres", "components.Discover.MovieGenreSlider.moviegenres": "Film-Genres", "components.Discover.NetworkSlider.networks": "Sender", "components.Discover.StudioSlider.studios": "Filmstudio", "components.Discover.TvGenreList.seriesgenres": "Serien-Genres", "components.Discover.TvGenreSlider.tvgenres": "Serien-Genres", "components.Discover.discover": "Entdecken", "components.Discover.emptywatchlist": "Hier erscheinen deine zur <PlexWatchlistSupportLink>Plex Merkliste</PlexWatchlistSupportLink> hinzugefügten Medien.", "components.Discover.plexwatchlist": "<PERSON><PERSON>", "components.Discover.RecentlyAddedSlider.recentlyAdded": "<PERSON><PERSON><PERSON><PERSON> hinzugefügt", "components.Discover.popularmovies": "Beliebte Filme", "components.Discover.populartv": "Beliebte Serien", "components.Discover.recentlyAdded": "<PERSON><PERSON><PERSON><PERSON> hinzugefügt", "components.Discover.recentrequests": "Bisherige <PERSON>", "components.Discover.trending": "Trends", "components.Discover.upcoming": "Demnächst erscheinende Filme", "components.Discover.upcomingmovies": "Demnächst erscheinende Filme", "components.Discover.upcomingtv": "Demnächst erscheinende Serien", "components.DownloadBlock.estimatedtime": "Geschätzte {time}", "components.DownloadBlock.formattedTitle": "{title}: <PERSON><PERSON> {seasonNumber} Episode {episodeNumber}", "components.IssueDetails.IssueComment.areyousuredelete": "<PERSON><PERSON> dieser Kommentar wirklich gelöscht werden?", "components.IssueDetails.IssueComment.delete": "Kommentar löschen", "components.IssueDetails.IssueComment.edit": "Kommentar bearbeiten", "components.IssueDetails.IssueComment.postedby": "Gepostet {relativeTime} von {username}", "components.IssueDetails.IssueComment.postedbyedited": "Gepostet {relativeTime} von {username} (Bearbeitet)", "components.IssueDetails.IssueComment.validationComment": "Du musst eine Nachricht e<PERSON>ben", "components.IssueDetails.IssueDescription.deleteissue": "Problem löschen", "components.IssueDetails.IssueDescription.description": "Beschreibung", "components.IssueDetails.IssueDescription.edit": "Beschreibung bearbeiten", "components.IssueDetails.allepisodes": "Alle Folgen", "components.IssueDetails.allseasons": "Alle Staffeln", "components.IssueDetails.closeissue": "Problem schließen", "components.IssueDetails.closeissueandcomment": "Schließen mit Kommentar", "components.IssueDetails.commentplaceholder": "Kommentar hinzufügen…", "components.IssueDetails.comments": "Kommentare", "components.IssueDetails.deleteissue": "Problem löschen", "components.IssueDetails.deleteissueconfirm": "<PERSON><PERSON> dieses Problem wirklich gelöscht werden?", "components.IssueDetails.episode": "Folge {episodeNumber}", "components.IssueDetails.issuepagetitle": "Problem", "components.IssueDetails.issuetype": "Art", "components.IssueDetails.lastupdated": "Letzte Aktualisierung", "components.IssueDetails.leavecomment": "Kommentar", "components.IssueDetails.nocomments": "<PERSON><PERSON>.", "components.IssueDetails.openedby": "#{issueId} geöffnet {relativeTime} von {username}", "components.IssueDetails.openin4karr": "In {arr} 4K <PERSON><PERSON>", "components.IssueDetails.openinarr": "In {arr} <PERSON><PERSON><PERSON>", "components.IssueDetails.play4konplex": "Auf {mediaServerName} in 4K abspielen", "components.IssueDetails.playonplex": "Auf {mediaServerName} abspielen", "components.IssueDetails.problemepisode": "Betroffene Folge", "components.IssueDetails.problemseason": "Betroffene Staffeln", "components.IssueDetails.reopenissue": "Problem erneut <PERSON>", "components.IssueDetails.reopenissueandcomment": "Mit Kommentar wieder ö<PERSON>nen", "components.IssueDetails.season": "Staffel {seasonNumber}", "components.IssueDetails.toasteditdescriptionfailed": "Beim Bear<PERSON>ten der Problembeschreibung ist ein Fehler aufgetreten.", "components.IssueDetails.toasteditdescriptionsuccess": "Problembeschreibung erfolgreich bearbeitet!", "components.IssueDetails.toastissuedeleted": "Problem erfolgreich gelöscht!", "components.IssueDetails.toastissuedeletefailed": "Beim Löschen des Problems ist ein Fehler aufgetreten.", "components.IssueDetails.toaststatusupdated": "Problemstatus erfolgreich aktualisiert!", "components.IssueDetails.toaststatusupdatefailed": "Beim Aktualisieren des Problemstatus ist ein Fehler aufgetreten.", "components.IssueDetails.unknownissuetype": "Unbekannt", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {<PERSON>ol<PERSON>} other {Folgen}}", "components.IssueList.IssueItem.issuestatus": "Status", "components.IssueList.IssueItem.issuetype": "<PERSON><PERSON>", "components.IssueList.IssueItem.opened": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.IssueList.IssueItem.openeduserdate": "{date} von {user}", "components.IssueList.IssueItem.problemepisode": "Betroffene Folge", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {<PERSON><PERSON>} other {<PERSON>el<PERSON>}}", "components.IssueList.IssueItem.unknownissuetype": "Unbekannt", "components.IssueList.IssueItem.viewissue": "Problem anzeigen", "components.IssueList.issues": "Probleme", "components.IssueList.showallissues": "Alle Probleme anzeigen", "components.IssueList.sortAdded": "Neueste", "components.IssueList.sortModified": "Zuletzt geändert", "components.IssueModal.CreateIssueModal.allepisodes": "Alle Folgen", "components.IssueModal.CreateIssueModal.allseasons": "Alle Staffeln", "components.IssueModal.CreateIssueModal.episode": "Folgen {episodeNumber}", "components.IssueModal.CreateIssueModal.extras": "Extras", "components.IssueModal.CreateIssueModal.problemepisode": "Betroffene Folge", "components.IssueModal.CreateIssueModal.problemseason": "Betroffene Staffel", "components.IssueModal.CreateIssueModal.providedetail": "Gib eine detaillierte Erklärung des Problems an.", "components.IssueModal.CreateIssueModal.reportissue": "Ein Problem melden", "components.IssueModal.CreateIssueModal.season": "Staffel {seasonNumber}", "components.IssueModal.CreateIssueModal.submitissue": "Problem melden", "components.IssueModal.CreateIssueModal.toastFailedCreate": "Beim Senden des Problems ist ein Fehler aufgetreten.", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "Problembericht für <strong>{title}</strong> erfolgreich übermittelt!", "components.IssueModal.CreateIssueModal.toastviewissue": "<PERSON> ansehen", "components.IssueModal.CreateIssueModal.validationMessageRequired": "Du musst eine Beschreibung eingeben", "components.IssueModal.CreateIssueModal.whatswrong": "Was ist das Problem?", "components.IssueModal.issueAudio": "Ton", "components.IssueModal.issueOther": "<PERSON><PERSON>", "components.IssueModal.issueSubtitles": "Untertitel", "components.IssueModal.issueVideo": "Video", "components.LanguageSelector.languageServerDefault": "Standard ({language})", "components.LanguageSelector.originalLanguageDefault": "Alle Sprachen", "components.Layout.LanguagePicker.displaylanguage": "Anzeigesprache", "components.Layout.SearchInput.searchPlaceholder": "Nach Filmen und Serien suchen", "components.Layout.Sidebar.dashboard": "Entdecken", "components.Layout.Sidebar.issues": "Probleme", "components.Layout.Sidebar.requests": "Anfragen", "components.Layout.Sidebar.settings": "Einstellungen", "components.Layout.Sidebar.users": "<PERSON><PERSON><PERSON>", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Film-Anfragen", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "Serien-Anfragen", "components.Layout.UserDropdown.myprofile": "Profil", "components.Layout.UserDropdown.requests": "Anfragen", "components.Layout.UserDropdown.settings": "Einstellungen", "components.Layout.UserDropdown.signout": "Abmelden", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {Version} other {Versionen}} hinterher", "components.Layout.VersionStatus.outofdate": "<PERSON><PERSON><PERSON>", "components.Layout.VersionStatus.streamdevelop": "<PERSON><PERSON><PERSON><PERSON>twicklung", "components.Layout.VersionStatus.streamstable": "Jellyseerr stabil", "components.Login.email": "E-Mail Adresse", "components.Login.forgotpassword": "Passwort vergessen?", "components.Login.loginerror": "<PERSON><PERSON> ist etwas schief gelaufen.", "components.Login.password": "Passwort", "components.Login.signin": "Anmelden", "components.Login.signingin": "Anmelden…", "components.Login.signinheader": "Anmelden um fortzufahren", "components.Login.signinwithoverseerr": "Verwende dein {applicationTitle}-Konto", "components.Login.signinwithplex": "Benutze dein Plex-Konto", "components.Login.validationemailrequired": "Du musst eine gültige E-Mail Adresse angeben", "components.Login.validationpasswordrequired": "Du musst ein Passwort angeben", "components.ManageSlideOver.alltime": "Gesamte Zeit", "components.ManageSlideOver.downloadstatus": "Downloads", "components.ManageSlideOver.manageModalAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.ManageSlideOver.manageModalClearMedia": "Daten löschen", "components.ManageSlideOver.manageModalClearMediaWarning": "* Dadurch werden alle Daten für diesen {mediaType} unwiderruflich entfernt, einschließlich aller Anfragen. Wenn dieses Element in deiner {mediaServerName}-Bibliothek existiert, werden die Medieninformationen beim nächsten Scan neu erstellt.", "components.ManageSlideOver.manageModalIssues": "Offene Probleme", "components.ManageSlideOver.manageModalMedia": "Medien", "components.ManageSlideOver.manageModalMedia4k": "4K Medien", "components.ManageSlideOver.manageModalNoRequests": "<PERSON><PERSON>.", "components.ManageSlideOver.manageModalRequests": "Anfragen", "components.ManageSlideOver.manageModalTitle": "{mediaType} verwalten", "components.ManageSlideOver.mark4kavailable": "Als in 4K verfügbar markieren", "components.ManageSlideOver.markallseasons4kavailable": "Alle Staffeln als in 4K verfügbar markieren", "components.ManageSlideOver.markallseasonsavailable": "Alle Staffeln als verfügbar markieren", "components.ManageSlideOver.markavailable": "Als verfügbar markieren", "components.ManageSlideOver.movie": "Film", "components.ManageSlideOver.openarr": "In {arr} <PERSON><PERSON><PERSON>", "components.ManageSlideOver.openarr4k": "In 4K {arr} <PERSON><PERSON><PERSON>", "components.ManageSlideOver.opentautulli": "In Tautulli öffnen", "components.ManageSlideOver.pastdays": "Vergangene {days, number} Tage", "components.ManageSlideOver.playedby": "<PERSON>b<PERSON><PERSON><PERSON> von", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> {playCount, plural, one {abgespielt} other {abgespielt}}", "components.ManageSlideOver.tvshow": "Serie", "components.MediaSlider.ShowMoreCard.seemore": "Mehr anschauen", "components.MovieDetails.MovieCast.fullcast": "Komplette Besetzung", "components.MovieDetails.MovieCrew.fullcrew": "Komplette Crew", "components.MovieDetails.budget": "Budget", "components.MovieDetails.cast": "Besetzung", "components.MovieDetails.digitalrelease": "Digitale Veröffentlichung", "components.MovieDetails.managemovie": "Film verwalten", "components.MovieDetails.mark4kavailable": "4K als verfügbar markieren", "components.MovieDetails.markavailable": "Als verfügbar markieren", "components.MovieDetails.originallanguage": "Originalsprache", "components.MovieDetails.originaltitle": "Originaltitel", "components.MovieDetails.overview": "Übersicht", "components.MovieDetails.overviewunavailable": "Übersicht nicht verfügbar.", "components.MovieDetails.physicalrelease": "DVD/Bluray-Veröffentlichung", "components.MovieDetails.productioncountries": "Produktions {countryCount, plural, one {Land} other {Länder}}", "components.MovieDetails.recommendations": "Empfehlungen", "components.MovieDetails.releasedate": "{releaseCount, plural, one {Veröffentlichungstermin} other {Veröffentlichungstermine}}", "components.MovieDetails.reportissue": "Problem melden", "components.MovieDetails.revenue": "Einnahmen", "components.MovieDetails.rtaudiencescore": "Rotten Tomatoes Publikumswertung", "components.MovieDetails.rtcriticsscore": "Rotten Tomatoes Tomatometer", "components.MovieDetails.runtime": "{minutes} Minuten", "components.MovieDetails.showless": "<PERSON><PERSON>", "components.MovieDetails.showmore": "Mehr Anzeigen", "components.MovieDetails.similar": "Ähnliche Titel", "components.MovieDetails.streamingproviders": "Streamt derzeit auf", "components.MovieDetails.studio": "{studioCount, plural, one {Studio} other {Studios}}", "components.MovieDetails.theatricalrelease": "Kinostart", "components.MovieDetails.tmdbuserscore": "TMDB-Nutzerwertung", "components.MovieDetails.viewfullcrew": "Komplette Crew anzeigen", "components.MovieDetails.watchtrailer": "<PERSON>er <PERSON><PERSON><PERSON>", "components.NotificationTypeSelector.adminissuecommentDescription": "Sende eine Benachrichtigung, wenn andere Benutzer Kommentare zu Problemen abgeben.", "components.NotificationTypeSelector.adminissuereopenedDescription": "Sende eine Benachrichtigung, wenn Probleme von anderen Benutzern wieder geöffnet werden.", "components.NotificationTypeSelector.adminissueresolvedDescription": "Sende eine Benachrichtigung, wenn andere Benutzer Kommentare zu Themen abgeben.", "components.NotificationTypeSelector.issuecomment": "Problem Kommentar", "components.NotificationTypeSelector.issuecommentDescription": "Sende eine Benachrichtigungen, wenn Probleme neue Kommentare erhalten.", "components.NotificationTypeSelector.issuecreated": "Problem gemeldet", "components.NotificationTypeSelector.issuecreatedDescription": "Senden eine Benachrichtigungen, wenn Probleme gemeldet werden.", "components.NotificationTypeSelector.issuereopened": "Problem wiederer<PERSON><PERSON><PERSON>", "components.NotificationTypeSelector.issuereopenedDescription": "Sende eine Benachrichtigung, wenn Probleme wieder geöffnet werden.", "components.NotificationTypeSelector.issueresolved": "<PERSON> gelöst", "components.NotificationTypeSelector.issueresolvedDescription": "Senden Benachrichtigungen, wenn Probleme gelöst sind.", "components.NotificationTypeSelector.mediaAutoApproved": "Anfrage automatisch genehmigt", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "Sende eine Benachrichtigung, wenn das angeforderte Medium automatisch genehmigt wird.", "components.NotificationTypeSelector.mediaapproved": "<PERSON><PERSON><PERSON>", "components.NotificationTypeSelector.mediaapprovedDescription": "Sende Benachrichtigungen, wenn angeforderte Medien manuell genehmigt wurden.", "components.NotificationTypeSelector.mediaautorequested": "Anfrage automatisch übermittelt", "components.NotificationTypeSelector.mediaautorequestedDescription": "Erhalten eine Benachrichtigung, wenn neue Medienanfragen für Objekte auf deiner Merkliste automatisch übermittelt werden.", "components.NotificationTypeSelector.mediaavailable": "Anfrage verfügbar", "components.NotificationTypeSelector.mediaavailableDescription": "Sendet Benachrichtigungen, wenn angeforderte Medien verfügbar werden.", "components.NotificationTypeSelector.mediadeclined": "<PERSON><PERSON><PERSON> ab<PERSON>", "components.NotificationTypeSelector.mediadeclinedDescription": "Sende eine Benachrichtigungen, wenn Medienanfragen abgelehnt wurden.", "components.NotificationTypeSelector.mediafailed": "Anfrageverarbeitung fehlgeschlagen", "components.NotificationTypeSelector.mediafailedDescription": "Sende Benachrichtigungen, wenn angeforderte Medien nicht zu Radarr oder Sonarr hinzugefügt werden konnten.", "components.NotificationTypeSelector.mediarequested": "Anfrage in Bearbeitung", "components.NotificationTypeSelector.mediarequestedDescription": "Sende Benachrichtigungen, wenn neue Medien angefordert wurden und auf Genehmigung warten.", "components.NotificationTypeSelector.notificationTypes": "Benachrichtigungstypen", "components.NotificationTypeSelector.userissuecommentDescription": "Sende eine Benachrichtigung, wenn dein Problem neue Kommentare erhält.", "components.NotificationTypeSelector.userissuecreatedDescription": "<PERSON>sen dich benachrichtigen, wenn andere Benutzer Probleme melden.", "components.NotificationTypeSelector.userissuereopenedDescription": "Sende eine Benachrichtigung, wenn die von dir gemeldeten Probleme wieder geöffnet werden.", "components.NotificationTypeSelector.userissueresolvedDescription": "Sende eine Benachrichtigung, wenn dein Problem gelöst wurde.", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "<PERSON><PERSON> ben<PERSON>, wenn andere Nutzer Medien anfordern, welche automatisch angenommen werden.", "components.NotificationTypeSelector.usermediaapprovedDescription": "Werde ben<PERSON><PERSON>, wenn deine Medienanfrage angenommen wurde.", "components.NotificationTypeSelector.usermediaavailableDescription": "Sende eine Benachrichtigung, wenn deine Medienanfragen verfügbar sind.", "components.NotificationTypeSelector.usermediadeclinedDescription": "Werde ben<PERSON><PERSON>, wenn deine Medienanfrage abgelehnt wurde.", "components.NotificationTypeSelector.usermediafailedDescription": "Werde benachrich<PERSON>gt, wenn die angeforderten Medien bei der Hinzufügung zu Radarr oder Sonarr fehlschlagen.", "components.NotificationTypeSelector.usermediarequestedDescription": "Werde <PERSON>, wenn andere Nutzer eine Medie anfordern, welches eine Genehmigung erfordert.", "components.PermissionEdit.admin": "Admin", "components.PermissionEdit.adminDescription": "Voller Administratorzugriff. Umgeht alle anderen Rechteabfragen.", "components.PermissionEdit.advancedrequest": "Erweiterte Anfragen", "components.PermissionEdit.advancedrequestDescription": "Autorisierung zur Änderung der erweiterten Optionen für Medienanfragen.", "components.PermissionEdit.autoapprove": "Automatische Genehmigung", "components.PermissionEdit.autoapprove4k": "Automatische Genehmigung von 4K", "components.PermissionEdit.autoapprove4kDescription": "Autorisierung der automatischen Freigabe für alle 4K-Medienanfragen.", "components.PermissionEdit.autoapprove4kMovies": "Automatische Genehmigung von 4K Filmen", "components.PermissionEdit.autoapprove4kMoviesDescription": "Autorisierung der automatischen Freigabe von Anfragen für 4K-Filme.", "components.PermissionEdit.autoapprove4kSeries": "Automatische Genehmigung von 4K Serien", "components.PermissionEdit.autoapprove4kSeriesDescription": "Autorisierung der automatischen Freigabe von Anfragen für 4K-Serien.", "components.PermissionEdit.autoapproveDescription": "Autorisierung der automatischen Freigabe von nicht-4K Anfragen.", "components.PermissionEdit.autoapproveMovies": "Automatische Genehmigung von Filmen", "components.PermissionEdit.autoapproveMoviesDescription": "Autorisierung der automatischen Freigabe von Anfragen für nicht-4K-Filme.", "components.PermissionEdit.autoapproveSeries": "Automatische Genehmigung von Serien", "components.PermissionEdit.autoapproveSeriesDescription": "Autorisierung der automatischen Freigabe von Anfragen für nicht-4K-Serien.", "components.PermissionEdit.autorequest": "Automatische Anfrage aus Plex-Merkliste", "components.PermissionEdit.autorequestDescription": "Autorisierung zur automatischen Anfrage von Nicht-4K-Medien über die Plex Merkliste.", "components.PermissionEdit.autorequestMovies": "Filme automatisch anfragen", "components.PermissionEdit.autorequestMoviesDescription": "Autorisierung zur automatischen Anfrage von Nicht-4K-Medien über die Plex Merkliste.", "components.PermissionEdit.autorequestSeries": "Serien automatisch anfragen", "components.PermissionEdit.autorequestSeriesDescription": "Autorisierung der automatischen Anfrage von Nicht-4K-Serien über die Plex Merkliste.", "components.PermissionEdit.createissues": "<PERSON><PERSON> melden", "components.PermissionEdit.createissuesDescription": "Autorisierung zur Meldung von Medienproblemen.", "components.PermissionEdit.manageissues": "<PERSON><PERSON> ver<PERSON><PERSON>", "components.PermissionEdit.manageissuesDescription": "Autorisierung zur Verwaltung von Medienproblemen.", "components.PermissionEdit.managerequests": "<PERSON><PERSON><PERSON> verwalten", "components.PermissionEdit.managerequestsDescription": "Autorisierung zur Verwaltung von Medienanfragen. <PERSON><PERSON> Anfragen, die von einem Benutzer mit dieser Berechtigung gestellt werden, werden automatisch genehmigt.", "components.PermissionEdit.request": "<PERSON><PERSON><PERSON> senden", "components.PermissionEdit.request4k": "4K anfragen", "components.PermissionEdit.request4kDescription": "Autorisierung zur Anfrage von Medien in 4K.", "components.PermissionEdit.request4kMovies": "4K Filme anfragen", "components.PermissionEdit.request4kMoviesDescription": "Autorisierung zur Übermittlung von Anfragen für 4K-Filme.", "components.PermissionEdit.request4kTv": "4K Serien anfragen", "components.PermissionEdit.request4kTvDescription": "Autorisierung zur Übermittlung von Anfragen für 4K-Serien.", "components.PermissionEdit.requestDescription": "Autorisierung zur Übermittlung von Anfragen für nicht-4K Medien.", "components.PermissionEdit.requestMovies": "Filme anfragen", "components.PermissionEdit.requestMoviesDescription": "Autorisierung zur Übermittlung von Anfragen für nicht-4K-Filme.", "components.PermissionEdit.requestTv": "Serien anfragen", "components.PermissionEdit.requestTvDescription": "Autorisierung zur Übermittlung von Anfragen für nicht-4K-Serien.", "components.PermissionEdit.users": "<PERSON><PERSON><PERSON> ver<PERSON><PERSON>", "components.PermissionEdit.usersDescription": "Autorisierung zur Benutzerverwaltung erteilen. Benutzer mit dieser Berechtigung können keine Benutzer mit Admin-Recht ändern oder das Admin-Recht gewähren.", "components.PermissionEdit.viewissues": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "components.PermissionEdit.viewissuesDescription": "Autorisierung zur Ansicht von Medienproblemen, die von anderen Benutzern gemeldet wurden.", "components.PermissionEdit.viewrecent": "<PERSON><PERSON><PERSON><PERSON> hinzugefügt anzeigen", "components.PermissionEdit.viewrecentDescription": "Autorisierung zur Anzeige der Liste der kürzlich hinzugefügten Medien.", "components.PermissionEdit.viewrequests": "Anfragen anzeigen", "components.PermissionEdit.viewrequestsDescription": "Autorisierung zur Anzeige der von anderen Benutzern eingereichten Medienanfragen.", "components.PermissionEdit.viewwatchlists": "{mediaServerName} Merklisten anzeigen", "components.PermissionEdit.viewwatchlistsDescription": "Autorisierung zur Anzeige von {mediaServerName} Merklisten anderer <PERSON>.", "components.PersonDetails.alsoknownas": "Auch bekannt unter: {names}", "components.PersonDetails.appearsin": "Auftritte", "components.PersonDetails.ascharacter": "als {character}", "components.PersonDetails.birthdate": "<PERSON><PERSON><PERSON><PERSON> am {birthdate}", "components.PersonDetails.crewmember": "Crew", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.QuotaSelector.days": "{count, plural, one {Tag} other {<PERSON><PERSON>}}", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{movies} pro {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.movies": "{count, plural, one {Film} other {<PERSON>e}}", "components.QuotaSelector.seasons": "{count, plural, one {<PERSON><PERSON>} other {<PERSON>el<PERSON>}}", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{seasons} pro {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.unlimited": "Unbegrenzt", "components.RegionSelector.regionDefault": "Alle Regionen", "components.RegionSelector.regionServerDefault": "Standard ({region})", "components.RequestBlock.approve": "<PERSON><PERSON><PERSON> genehmigen", "components.RequestBlock.decline": "<PERSON><PERSON><PERSON>", "components.RequestBlock.delete": "Anfrage löschen", "components.RequestBlock.edit": "<PERSON><PERSON><PERSON> bearbeiten", "components.RequestBlock.languageprofile": "Sprachprofil", "components.RequestBlock.lastmodifiedby": "Zuletzt ge<PERSON><PERSON><PERSON> von", "components.RequestBlock.profilechanged": "Qualitätsprofil", "components.RequestBlock.requestdate": "Anfrage-Datum", "components.RequestBlock.requestedby": "<PERSON><PERSON><PERSON><PERSON> von", "components.RequestBlock.requestoverrides": "Anfrage Überschreibungen", "components.RequestBlock.rootfolder": "Stammordner", "components.RequestBlock.seasons": "{seasonCount, plural, one {<PERSON><PERSON>} other {<PERSON>el<PERSON>}}", "components.RequestBlock.server": "Zielserver", "components.RequestButton.approve4krequests": "Genehmige {requestCount, plural, one {4K Anfrage} other {{requestCount} 4K Anfragen}}", "components.RequestButton.approverequest": "<PERSON><PERSON><PERSON> genehmigen", "components.RequestButton.approverequest4k": "4K Anfrage genehmigen", "components.RequestButton.approverequests": "Genehmige {requestCount, plural, one {An<PERSON>ge} other {{requestCount} Anfragen}}", "components.RequestButton.decline4krequests": "Lehne {requestCount, plural, one {4K Anfrage} other {{requestCount} 4K Anfragen}} ab", "components.RequestButton.declinerequest": "<PERSON><PERSON><PERSON>", "components.RequestButton.declinerequest4k": "4K Anfrage ablehnen", "components.RequestButton.declinerequests": "Lehne {requestCount, plural, one {An<PERSON>ge} other {{requestCount} Anfragen}} ab", "components.RequestButton.requestmore": "Mehr anfragen", "components.RequestButton.requestmore4k": "Mehr in 4K anfragen", "components.RequestButton.viewrequest": "An<PERSON>ge anzeigen", "components.RequestButton.viewrequest4k": "4K Anfrage anzeigen", "components.RequestCard.approverequest": "<PERSON><PERSON><PERSON> genehmigen", "components.RequestCard.cancelrequest": "<PERSON><PERSON><PERSON> abbrechen", "components.RequestCard.declinerequest": "<PERSON><PERSON><PERSON>", "components.RequestCard.deleterequest": "Anfrage löschen", "components.RequestCard.editrequest": "<PERSON><PERSON><PERSON> bearbeiten", "components.RequestCard.failedretry": "<PERSON><PERSON> erneuten Versuch die Anfrage zu senden ist ein Fehler aufgetreten.", "components.RequestCard.mediaerror": "{mediaType} wurde nicht gefunden", "components.RequestCard.seasons": "{seasonCount, plural, one {<PERSON><PERSON>} other {<PERSON>el<PERSON>}}", "components.RequestCard.tmdbid": "TMDB-ID", "components.RequestCard.tvdbid": "TheTVDB-ID", "components.RequestCard.unknowntitle": "Unbekannter Titel", "components.RequestList.RequestItem.cancelRequest": "<PERSON><PERSON><PERSON> abbrechen", "components.RequestList.RequestItem.deleterequest": "Anfrage löschen", "components.RequestList.RequestItem.editrequest": "<PERSON><PERSON><PERSON> bearbeiten", "components.RequestList.RequestItem.failedretry": "<PERSON><PERSON> der Anfrage ist etwas schief gelaufen.", "components.RequestList.RequestItem.mediaerror": "{mediaType} wurde nicht gefunden", "components.RequestList.RequestItem.modified": "G<PERSON><PERSON>ndert", "components.RequestList.RequestItem.modifieduserdate": "{date} von {user}", "components.RequestList.RequestItem.requested": "Ang<PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.requesteddate": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {<PERSON><PERSON>} other {<PERSON>el<PERSON>}}", "components.RequestList.RequestItem.tmdbid": "TMDB-ID", "components.RequestList.RequestItem.tvdbid": "TheTVDB-ID", "components.RequestList.RequestItem.unknowntitle": "Unbekannter Titel", "components.RequestList.requests": "Anfragen", "components.RequestList.showallrequests": "Zeige alle Anfragen", "components.RequestList.sortAdded": "Zuletzt angefragt", "components.RequestList.sortModified": "Zuletzt geändert", "components.RequestModal.AdvancedRequester.advancedoptions": "Erweiterte Einstellungen", "components.RequestModal.AdvancedRequester.animenote": "* Diese Serie ist ein Anime.", "components.RequestModal.AdvancedRequester.default": "{name} (Standard)", "components.RequestModal.AdvancedRequester.destinationserver": "Zielserver", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.RequestModal.AdvancedRequester.languageprofile": "Sprachprofil", "components.RequestModal.AdvancedRequester.notagoptions": "Keine Tags.", "components.RequestModal.AdvancedRequester.qualityprofile": "Qualitätsprofil", "components.RequestModal.AdvancedRequester.requestas": "<PERSON><PERSON><PERSON> als", "components.RequestModal.AdvancedRequester.rootfolder": "Stammordner", "components.RequestModal.AdvancedRequester.selecttags": "Wähle Tags aus", "components.RequestModal.AdvancedRequester.tags": "Tags", "components.RequestModal.QuotaDisplay.allowedRequests": "<PERSON> darfst <strong>{limit}</strong> {type} Anfragen alle <strong>{days}</strong> <PERSON>e machen.", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "<PERSON><PERSON> darf <strong>{limit}</strong> {type} Anfragen alle <strong>{days}</strong> Tage machen.", "components.RequestModal.QuotaDisplay.movie": "Filme", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {Film} other {Filme}}", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "Es sind nicht genügend Staffelanfragen verbleibend", "components.RequestModal.QuotaDisplay.quotaLink": "Du kannst eine Zusammenfassung deiner Anfragenlimits auf deiner <ProfileLink>profile page</ProfileLink> an<PERSON><PERSON>.", "components.RequestModal.QuotaDisplay.quotaLinkUser": "Du kannst eine Zusammenfassung der Anfragenlimits dieses Benutzers auf seiner <ProfileLink>profile page</ProfileLink> an<PERSON><PERSON>.", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {<PERSON><PERSON>} other {<strong>#</strong>}} {type} {remaining, plural, one {Anfrage} other {Anfragen}} verbleibend", "components.RequestModal.QuotaDisplay.requiredquota": "Du musst mindestens <strong>{seasons}</strong> {seasons, plural, one {Staffel Anfrage} other {Staffel Anfragen}} verbleibend haben, um eine Anfrage für diese Serie einzureichen.", "components.RequestModal.QuotaDisplay.requiredquotaUser": "<PERSON><PERSON>utzer muss mindestens <strong>{seasons}</strong> {seasons, plural, one {Staffel Anfrage} other {Staffel Anfragen}} verbleibend haben, um eine Anfrage für diese Serie einzureichen.", "components.RequestModal.QuotaDisplay.season": "Staffeln", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {<PERSON><PERSON>} other {<PERSON>el<PERSON>}}", "components.RequestModal.SearchByNameModal.nomatches": "Wir konnten keine Übereinstimmung für diese Serie finden.", "components.RequestModal.SearchByNameModal.notvdbiddescription": "Wir konnten diese Serie nicht automatisch zuordnen. Bitte wähle unten eine korrekte Übereinstimmung aus.", "components.RequestModal.alreadyrequested": "Bereits Angefragt", "components.RequestModal.approve": "<PERSON><PERSON><PERSON> genehmigen", "components.RequestModal.autoapproval": "Automatische Genehmigung", "components.RequestModal.cancel": "<PERSON><PERSON><PERSON> abbrechen", "components.RequestModal.edit": "<PERSON><PERSON><PERSON> bearbeiten", "components.RequestModal.errorediting": "Beim bearbeiten der Anfrage ist etwas schief gelaufen.", "components.RequestModal.numberofepisodes": "Anzahl der Folgen", "components.RequestModal.pending4krequest": "Ausstehende 4K Anfrage", "components.RequestModal.pendingapproval": "<PERSON><PERSON> Anfrage steht noch aus.", "components.RequestModal.pendingrequest": "Ausstehende Anfrage", "components.RequestModal.requestApproved": "<PERSON><PERSON><PERSON> für <strong>{title}</strong> genehmigt!", "components.RequestModal.requestCancel": "<PERSON><PERSON><PERSON> für <strong>{title}</strong> abgebrochen.", "components.RequestModal.requestSuccess": "<strong>{title}</strong> erfolgreich angefragt!", "components.RequestModal.requestadmin": "Diese Anfrage wird automatisch genehmigt.", "components.RequestModal.requestcancelled": "<PERSON><PERSON><PERSON> für <strong>{title}</strong> abgebrochen.", "components.RequestModal.requestcollection4ktitle": "Sammlung in 4k anfragen", "components.RequestModal.requestcollectiontitle": "Sammlung anfragen", "components.RequestModal.requestedited": "An<PERSON><PERSON> für <strong>{title}</strong> erfolgreich bearbeitet!", "components.RequestModal.requesterror": "Beim Senden der Anfragen ist etwas schief gelaufen.", "components.RequestModal.requestfrom": "<PERSON> Anfrage von {username} muss noch genehmigt werden.", "components.RequestModal.requestmovie4ktitle": "Film in 4k anfragen", "components.RequestModal.requestmovies": "Anfrage {count} {count, plural, one {Film} other {Filme}}", "components.RequestModal.requestmovies4k": "Anfrage {count} {count, plural, one {Film} other {Filme}} in 4K", "components.RequestModal.requestmovietitle": "Film anfragen", "components.RequestModal.requestseasons": "{seasonCount} {seasonCount, plural, one {<PERSON><PERSON>} other {Staffeln}} anfragen", "components.RequestModal.requestseasons4k": "Anfrage {seasonCount} {seasonCount, plural, one {Serie} other {Serien}} in 4K", "components.RequestModal.requestseries4ktitle": "Serie in 4k anfragen", "components.RequestModal.requestseriestitle": "Serie anfragen", "components.RequestModal.season": "Staffel", "components.RequestModal.seasonnumber": "Staffel {number}", "components.RequestModal.selectmovies": "Wähle Film(e)", "components.RequestModal.selectseason": "Staffel(n) Auswählen", "components.ResetPassword.confirmpassword": "Passwort bestätigen", "components.ResetPassword.email": "E-Mail Adresse", "components.ResetPassword.emailresetlink": "Wiederherstellungs-Link per E-Mail senden", "components.ResetPassword.gobacklogin": "Zurück zur Anmeldeseite", "components.ResetPassword.password": "Passwort", "components.ResetPassword.passwordreset": "Passwort zurücksetzen", "components.ResetPassword.requestresetlinksuccessmessage": "Ein Link zum Zurücksetzen des Passworts wird an die angegebene E-Mail Adresse gesendet, wenn sie einem gültigen Benutzer zugeordnet ist.", "components.ResetPassword.resetpassword": "Passwort zurücksetzen", "components.ResetPassword.resetpasswordsuccessmessage": "Passwort wurde erfolgreich zurückgesetzt!", "components.ResetPassword.validationemailrequired": "Du musst eine gültige E-Mail Adresse angeben", "components.ResetPassword.validationpasswordmatch": "Passwörter müssen übereinstimmen", "components.ResetPassword.validationpasswordminchars": "Passwort ist zu kurz; es sollte mindestens 8 Zeichen lang sein", "components.ResetPassword.validationpasswordrequired": "Du musst ein Passwort angeben", "components.Search.search": "<PERSON><PERSON>", "components.Search.searchresults": "Suchergebnisse", "components.Settings.Notifications.NotificationsGotify.agentenabled": "Agent aktivieren", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "Die Gotify-Benachrichtigungseinstellungen konnten nicht gespeichert werden.", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Gotify Benachrichtigungseinstellungen erfolgreich gespeichert!", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "Gotify-Testbenachrichtigung konnte nicht gesendet werden.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Versende Gotify-Testbenachrichtigung…", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Gotify-Testbenachrichtigung gesendet!", "components.Settings.Notifications.NotificationsGotify.token": "Anwendungs-Token", "components.Settings.Notifications.NotificationsGotify.url": "Server-URL", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "Es muss ein Anwendung<PERSON>-Token angegeben werden", "components.Settings.Notifications.NotificationsGotify.validationTypes": "Es muss mindestens eine Benachrichtigungsart ausgewählt werden", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "Es muss eine gültige URL angegeben werden", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "URL darf nicht mit einem abschließenden Schrägstrich enden", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "Dienst aktivieren", "components.Settings.Notifications.NotificationsLunaSea.profileName": "Profil Name", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "Wird nur benötigt wenn <code>default</code> Profil nicht verwendet wird", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "LunaSea Benachrichtigungseinstellungen konnten nicht gespeichert werden.", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "LunaSea Benachrichtigungseinstellungen wurden gespeichert!", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "LunaSea Test Benachrichtigung fehlgeschlagen.", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "LunaSea Test Benachrichtigung wird gesendet…", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "LunaSea Test Benachrichtigung gesendet!", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "Sie müssen mindestens einen Benachrichtigungstypen auswählen", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "G<PERSON>en sie eine gültige URL an", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "Webhook URL", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "<PERSON><PERSON> oder Geräte basierende <LunaSeaLink>Benachrichtigungs-Webhook URL</LunaSeaLink>", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "Zugangstoken", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "<PERSON><PERSON><PERSON><PERSON> einen <PERSON> in Ihren <PushbulletSettingsLink>Account Einstellungen</PushbulletSettingsLink>", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "Agent aktivieren", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "Channel Tag", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "Pushbullet-Benachrichtigungseinstellungen konnten nicht gespeichert werden.", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Pushbullet-Benachrichtigungseinstellungen erfolgreich gespeichert!", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "Pushbullet Test Benachrichtigung fehlgeschlagen.", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "Pushbullet Test Benachrichtigung wird gesendet…", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Pushbullet Test Benachrichtigung gesendet!", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "Du musst ein Zugangstoken angeben", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "Sie müssen mindestens einen Benachrichtigungstypen auswählen", "components.Settings.Notifications.NotificationsPushover.accessToken": "Anwendungs API-Token", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicationRegistrationLink>Registriere eine Anwendung</ApplicationRegistrationLink> , um diese mit Jellyseerr benutzen zu können", "components.Settings.Notifications.NotificationsPushover.agentenabled": "Agent aktivieren", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "Pushover-Benachrichtigungseinstellungen konnten nicht gespeichert werden.", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Pushover-Benachrichtigungseinstellungen erfolgreich gespeichert!", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "Pushover Test Benachrichtigung fehlgeschlagen.", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "Pushover Test Benachrichtigung wird gesendet…", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Pushover Test Benachrichtigung gesendet!", "components.Settings.Notifications.NotificationsPushover.userToken": "Benutzer- oder Gruppenschlüssel", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "Ihr 30-stelliger <UsersGroupsLink>Nutzer oder Gruppen Identifikator</UsersGroupsLink>", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "Du musst ein gültiges Anwendungstoken angeben", "components.Settings.Notifications.NotificationsPushover.validationTypes": "Du musst mindestens einen Benachrichtigungstypen auswählen", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "Du musst einen gültigen Benutzer-/Gruppenschlüssel angeben", "components.Settings.Notifications.NotificationsSlack.agentenabled": "Agent aktivieren", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "Slack-Benachrichtigungseinstellungen konnten nicht gespeichert werden.", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Slack-Benachrichtigungseinstellungen erfolgreich gespeichert!", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "Slack Test Benachrichtigung fehlgeschlagen.", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "Slack Test Benachrichtigung wird gesendet…", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "Slack Test Benachrichtigung gesendet!", "components.Settings.Notifications.NotificationsSlack.validationTypes": "Du musst mindestens einen Benachrichtigungstypen auswählen", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "Du musst eine gültige URL angeben", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "Webhook URL", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "<PERSON><PERSON><PERSON> e<PERSON> <WebhookLink>Eingehende Webhook</WebhookLink> integration", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "Agent aktivieren", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "<PERSON><PERSON><PERSON><PERSON> muss via HTTPS bereitgestellt werden, um Web-Push Benachrichtigungen empfangen zu können.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "Web push Test Benachrichtigung fehlgeschlagen.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "Web push Test Benachrichtigung wird gesendet…", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "Web push Test Benachrichtigung gesendet!", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "Web push Benachrichtigungseinstellungen konnten nicht gespeichert werden.", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "Web push Benachrichtigungseinstellungen erfolgreich gespeichert!", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "Dienst aktivieren", "components.Settings.Notifications.NotificationsWebhook.authheader": "Autorisierungsüberschrift", "components.Settings.Notifications.NotificationsWebhook.customJson": "JSON-Inhalt", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "Auf Standard zurücksetzen", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "JSON-Inhalt erfolgreich zurückgesetzt!", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "<PERSON><PERSON><PERSON> zu Vorlagenvariablen", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "Webhook Test Benachrichtigung konnte nicht gesendet werden.", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "Webhook Test Benachrichtigung wird gesendet…", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "Webhook Test Benachrichtigung gesendet!", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "Du musst einen gültigen JSON-Inhalt angeben", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "Du musst mindestens einen Benachrichtigungstypen auswählen", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "Du musst eine gültige URL angeben", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "Webhook-URL", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "Webhook-Benachrichtigungseinstellungen konnten nicht gespeichert werden.", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "Webhook-Benachrichtigungseinstellungen erfolgreich gespeichert!", "components.Settings.Notifications.agentenabled": "Agent aktivieren", "components.Settings.Notifications.allowselfsigned": "Selbstsignierte Zertifikate erlauben", "components.Settings.Notifications.authPass": "SMTP-Passwort", "components.Settings.Notifications.authUser": "SMTP-Benutzername", "components.Settings.Notifications.botAPI": "Bot-Autorisierungstoken", "components.Settings.Notifications.botApiTip": "<CreateBotLink><PERSON><PERSON> erstellen</CreateBotLink> für die Verwendung mit Jellyseerr", "components.Settings.Notifications.botAvatarUrl": "Bot Avatar URL", "components.Settings.Notifications.botUsername": "<PERSON><PERSON>", "components.Settings.Notifications.botUsernameTip": "Benutz<PERSON> erlauben, einen Chat mit dem Bot zu starten und ihre eigenen Benachrichtigungen konfigurieren", "components.Settings.Notifications.chatId": "Chat-ID", "components.Settings.Notifications.chatIdTip": "<PERSON>e einen Chat mit dem Bot, füge <GetIdBotLink>@get_id_bot</GetIdBotLink> hinzu, und erteile den <code>/my_id</code> Be<PERSON>hl", "components.Settings.Notifications.discordsettingsfailed": "Discord-Benachrichtigungseinstellungen konnten nicht gespeichert werden.", "components.Settings.Notifications.discordsettingssaved": "Discord-Benachrichtigungseinstellungen erfolgreich gespeichert!", "components.Settings.Notifications.emailsender": "Absenderad<PERSON>e", "components.Settings.Notifications.emailsettingsfailed": "E-Mail-Benachrichtigungseinstellungen konnten nicht gespeichert werden.", "components.Settings.Notifications.emailsettingssaved": "E-Mail-Benachrichtigungseinstellungen erfolgreich gespeichert!", "components.Settings.Notifications.enableMentions": "Erwähnungen aktivieren", "components.Settings.Notifications.encryption": "Verschlüsselungsmethode", "components.Settings.Notifications.encryptionDefault": "Verwende STARTTLS wenn verfügbar", "components.Settings.Notifications.encryptionImplicitTls": "Benutze Implizit TLS", "components.Settings.Notifications.encryptionNone": "<PERSON><PERSON>", "components.Settings.Notifications.encryptionOpportunisticTls": "STARTTLS immer verwenden", "components.Settings.Notifications.encryptionTip": "Im Regelfall verwendet Implicit TLS Port 465 und STARTTLS Port 587", "components.Settings.Notifications.pgpPassword": "PGP Passwort", "components.Settings.Notifications.pgpPasswordTip": "Signiere verschlüsselte E-Mail-Nachrichten mit <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.pgpPrivateKey": "PGP Privater Schlüssel", "components.Settings.Notifications.pgpPrivateKeyTip": "Signiere verschlüsselte E-Mail-Nachrichten mit <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.sendSilently": "<PERSON>e stumm", "components.Settings.Notifications.sendSilentlyTip": "Sende Benachrichtigungen ohne Ton", "components.Settings.Notifications.senderName": "Absendername", "components.Settings.Notifications.smtpHost": "SMTP-Host", "components.Settings.Notifications.smtpPort": "SMTP-Port", "components.Settings.Notifications.telegramsettingsfailed": "Telegram-Benachrichtigungseinstellungen konnten nicht gespeichert werden.", "components.Settings.Notifications.telegramsettingssaved": "Telegram-Benachrichtigungseinstellungen erfolgreich gespeichert!", "components.Settings.Notifications.toastDiscordTestFailed": "Discord Test Benachrichtigung fehlgeschlagen.", "components.Settings.Notifications.toastDiscordTestSending": "Discord Test Benachrichtigung wird gesendet…", "components.Settings.Notifications.toastDiscordTestSuccess": "Discord Test Benachrichtigung gesendet!", "components.Settings.Notifications.toastEmailTestFailed": "E-Mail Test Benachrichtigung fehlgeschlagen.", "components.Settings.Notifications.toastEmailTestSending": "Email Test Benachrichtigung wird gesendet…", "components.Settings.Notifications.toastEmailTestSuccess": "Email Test Benachrichtigung gesendet!", "components.Settings.Notifications.toastTelegramTestFailed": "Telegram Test Benachrichtigung fehlgeschlagen.", "components.Settings.Notifications.toastTelegramTestSending": "Telegram Test Benachrichtigung wird gesendet…", "components.Settings.Notifications.toastTelegramTestSuccess": "Telegram Test Benachrichtigung gesendet!", "components.Settings.Notifications.validationBotAPIRequired": "Du musst ein Bot-Autorisierungstoken angeben", "components.Settings.Notifications.validationChatIdRequired": "Du musst eine gültige Chat-ID angeben", "components.Settings.Notifications.validationEmail": "Du musst eine gültige E-Mail-Adresse angeben", "components.Settings.Notifications.validationPgpPassword": "Ein PGP-Passwort muss angeben werden", "components.Settings.Notifications.validationPgpPrivateKey": "Ein gültiger privater PGP-Schlüssel muss angeben werden", "components.Settings.Notifications.validationSmtpHostRequired": "Du musst einen gültigen Hostnamen oder IP-Adresse angeben", "components.Settings.Notifications.validationSmtpPortRequired": "Du musst einen gültigen Port angeben", "components.Settings.Notifications.validationTypes": "Es muss mindestens ein Benachrichtigungstyp ausgewählt werden", "components.Settings.Notifications.validationUrl": "Du musst eine gültige URL angeben", "components.Settings.Notifications.webhookUrl": "Webhook-URL", "components.Settings.Notifications.webhookUrlTip": "<PERSON><PERSON><PERSON> e<PERSON> <DiscordWebhookLink>webhook Integration</DiscordWebhookLink> auf dem Server", "components.Settings.RadarrModal.add": "Server hinz<PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.announced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.apiKey": "API-Schlüssel", "components.Settings.RadarrModal.baseUrl": "Basis-URL", "components.Settings.RadarrModal.create4kradarr": "Neuen 4K Radarr Server hinzufügen", "components.Settings.RadarrModal.createradarr": "Neuen Radarr Server hinzufügen", "components.Settings.RadarrModal.default4kserver": "Standard 4K Server", "components.Settings.RadarrModal.defaultserver": "Standardserver", "components.Settings.RadarrModal.edit4kradarr": "4K Radarr Server bearbeiten", "components.Settings.RadarrModal.editradarr": "Radarr Server bearbeiten", "components.Settings.RadarrModal.enableSearch": "Automatische Suche aktivieren", "components.Settings.RadarrModal.externalUrl": "Externe URL", "components.Settings.RadarrModal.hostname": "Hostname oder IP-Adresse", "components.Settings.RadarrModal.inCinemas": "<PERSON><PERSON>", "components.Settings.RadarrModal.loadingTags": "Lade Tags…", "components.Settings.RadarrModal.loadingprofiles": "Qualitätsprofile werden geladen…", "components.Settings.RadarrModal.loadingrootfolders": "Stammordner werden geladen…", "components.Settings.RadarrModal.minimumAvailability": "Mindestverfügbarkeit", "components.Settings.RadarrModal.notagoptions": "Keine Tags.", "components.Settings.RadarrModal.port": "Port", "components.Settings.RadarrModal.qualityprofile": "Qualitätsprofil", "components.Settings.RadarrModal.released": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.rootfolder": "Stammordner", "components.Settings.RadarrModal.selectMinimumAvailability": "Wähle die Mindestverfügbarkeit", "components.Settings.RadarrModal.selectQualityProfile": "Wähle Qualitätsprofil", "components.Settings.RadarrModal.selectRootFolder": "W<PERSON><PERSON>e Stammordner", "components.Settings.RadarrModal.selecttags": "Tags auswählen", "components.Settings.RadarrModal.server4k": "4K-Server", "components.Settings.RadarrModal.servername": "Servername", "components.Settings.RadarrModal.ssl": "SSL aktivieren", "components.Settings.RadarrModal.syncEnabled": "Scannen aktivieren", "components.Settings.RadarrModal.tags": "Tags", "components.Settings.RadarrModal.testFirstQualityProfiles": "Teste die Verbindung, um Qualitätsprofile zu laden", "components.Settings.RadarrModal.testFirstRootFolders": "Teste die Verbindung, um Stammordner zu laden", "components.Settings.RadarrModal.testFirstTags": "<PERSON><PERSON> Verbindu<PERSON>, um <PERSON><PERSON> zu laden", "components.Settings.RadarrModal.toastRadarrTestFailure": "Verbin<PERSON><PERSON> zu Radarr fehlgeschlagen.", "components.Settings.RadarrModal.toastRadarrTestSuccess": "Radarr-Verbindung erfolgreich hergestellt!", "components.Settings.RadarrModal.validationApiKeyRequired": "Du musst einen API-Schlüssel angeben", "components.Settings.RadarrModal.validationApplicationUrl": "Du musst eine gültige URL angeben", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "Die URL darf nicht mit einem abschließenden Schrägstrich enden", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "Die URL-Basis muss einen vorangestellten Schrägstrich enthalten", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "Die Basis-URL darf nicht mit einem Schrägstrich enden", "components.Settings.RadarrModal.validationHostnameRequired": "Es muss ein gültiger Hostname oder IP-Adresse angegeben werden", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "Du musst eine Mindestverfügbarkeit auswählen", "components.Settings.RadarrModal.validationNameRequired": "Du musst einen Servernamen angeben", "components.Settings.RadarrModal.validationPortRequired": "Du musst einen Port angeben", "components.Settings.RadarrModal.validationProfileRequired": "Du musst ein Qualitätsprofil auswählen", "components.Settings.RadarrModal.validationRootFolderRequired": "Du musst einen Stammordner auswählen", "components.Settings.SettingsAbout.Releases.currentversion": "Aktuell", "components.Settings.SettingsAbout.Releases.latestversion": "Neuste", "components.Settings.SettingsAbout.Releases.releasedataMissing": "Informationen der Version nicht verfügbar.", "components.Settings.SettingsAbout.Releases.releases": "Veröffentlichungen", "components.Settings.SettingsAbout.Releases.versionChangelog": "Änderungsprotokoll {version}", "components.Settings.SettingsAbout.Releases.viewchangelog": "Änderungsprotokoll anzeigen", "components.Settings.SettingsAbout.Releases.viewongithub": "Auf GitHub anzeigen", "components.Settings.SettingsAbout.about": "<PERSON><PERSON>", "components.Settings.SettingsAbout.appDataPath": "Datenverzeichnis", "components.Settings.SettingsAbout.betawarning": "Das ist eine BETA Software. Einige Funktionen könnten nicht richtig/stabil funktionieren. Bitte sämtliche Fehler auf GitHub melden!", "components.Settings.SettingsAbout.documentation": "Dokumentation", "components.Settings.SettingsAbout.gettingsupport": "<PERSON><PERSON><PERSON> erhalten", "components.Settings.SettingsAbout.githubdiscussions": "GitHub-Diskussionen", "components.Settings.SettingsAbout.helppaycoffee": "Unterstütze das Projekt mit einem Kaffee", "components.Settings.SettingsAbout.outofdate": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.overseerrinformation": "<PERSON><PERSON>", "components.Settings.SettingsAbout.preferredmethod": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.runningDevelop": "<PERSON>e benutzen den Branch<code>develop</code> <PERSON>, welcher nur für Entwickler, bzw. \"Bleeding-Edge\" Tests empfohlen wird.", "components.Settings.SettingsAbout.supportoverseerr": "Unterstütze Overseerr", "components.Settings.SettingsAbout.timezone": "Zeitzone", "components.Settings.SettingsAbout.totalmedia": "Medien insgesamt", "components.Settings.SettingsAbout.totalrequests": "Anfragen insgesamt", "components.Settings.SettingsAbout.uptodate": "Auf dem neusten Stand", "components.Settings.SettingsAbout.version": "Version", "components.Settings.SettingsJobsCache.cache": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.cacheDescription": "<PERSON>ur Leistungsoptimierung und um unnötige Anfragen zu minimieren, speichert Jellyseerr Anfragen zwischen.", "components.Settings.SettingsJobsCache.cacheflushed": "{cachename} <PERSON><PERSON> geleert.", "components.Settings.SettingsJobsCache.cachehits": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachekeys": "Schlüssel insgesamt", "components.Settings.SettingsJobsCache.cacheksize": "Schlüsselgröße", "components.Settings.SettingsJobsCache.cachemisses": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachename": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachevsize": "Wertgröße", "components.Settings.SettingsJobsCache.canceljob": "Aufgabe abbrechen", "components.Settings.SettingsJobsCache.command": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.download-sync": "Download Synchronisierung", "components.Settings.SettingsJobsCache.download-sync-reset": "Download Synchronisierung Zurücksetzen", "components.Settings.SettingsJobsCache.editJobSchedule": "<PERSON>", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "Aktuelle Häufigkeit", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "Häufigkeit", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "Alle {jobScheduleHours, plural, one {Stunde} other {{jobScheduleHours} Stunden}}", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "Alle {jobScheduleMinutes, plural, one {Minute} other {{jobScheduleMinutes} Minuten}}", "components.Settings.SettingsJobsCache.flushcache": "<PERSON><PERSON> le<PERSON>n", "components.Settings.SettingsJobsCache.image-cache-cleanup": "Bild-Cache-Bereinigung", "components.Settings.SettingsJobsCache.imagecache": "Bild-<PERSON>ache", "components.Settings.SettingsJobsCache.imagecacheDescription": "<PERSON>n diese Einstellung aktiviert ist, wird <PERSON><PERSON><PERSON>rr Bilder aus vorkonfigurierten externen Quellen im Proxy-Cache zwischenspeichern. Bilder im Zwischenspeicher werden in deinem Konfigurationsordner gespeichert: <code>{appDataPath}/cache/images</code>.", "components.Settings.SettingsJobsCache.imagecachecount": "Bilder im Cache", "components.Settings.SettingsJobsCache.imagecachesize": "Gesamtgröße des Caches", "components.Settings.SettingsJobsCache.jellyfin-recently-added-scan": "Scan der zuletzt hinzugefügten Jellyfin Medien", "components.Settings.SettingsJobsCache.jellyfin-full-scan": "Vollständiger Jellyfin Bibliotheken Scan", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "<PERSON><PERSON> des Auftrags ging etwas schief.", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "Auftrag erfolgreich bearbeitet!", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname} abgebrochen.", "components.Settings.SettingsJobsCache.jobname": "Aufgabenname", "components.Settings.SettingsJobsCache.jobs": "Aufgaben", "components.Settings.SettingsJobsCache.jobsDescription": "Jellyseerr führt bestimmte Wartungsaufgaben als regulär geplante Aufgaben durch. Diese können allerdings auch manuell ausgeführt werden, ohne dabei den regulären Zeitplan abzuändern.", "components.Settings.SettingsJobsCache.jobsandcache": "Aufgaben und Cache", "components.Settings.SettingsJobsCache.jobstarted": "{jobname} gestartet.", "components.Settings.SettingsJobsCache.jobtype": "Art", "components.Settings.SettingsJobsCache.nextexecution": "Nächste Ausführung", "components.Settings.SettingsJobsCache.plex-full-scan": "Vollständiger Plex Bibliotheken Scan", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Scan der zuletzt hinzugefügten Plex Medien", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "Plex Merklisten Sync", "components.Settings.SettingsJobsCache.process": "Prozess", "components.Settings.SettingsJobsCache.radarr-scan": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.runnow": "Jetzt ausführen", "components.Settings.SettingsJobsCache.sonarr-scan": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.unknownJob": "Unbekannte Aufgabe", "components.Settings.SettingsLogs.copiedLogMessage": "Protokollnachricht in die Zwischenablage kopiert.", "components.Settings.SettingsLogs.copyToClipboard": "In Zwischenablage kopieren", "components.Settings.SettingsLogs.extraData": "Zusätzliche Daten", "components.Settings.SettingsLogs.filterDebug": "Fehlersuche", "components.Settings.SettingsLogs.filterError": "<PERSON><PERSON>", "components.Settings.SettingsLogs.filterInfo": "Infos", "components.Settings.SettingsLogs.filterWarn": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.label": "Bezeichnung", "components.Settings.SettingsLogs.level": "Schweregrad", "components.Settings.SettingsLogs.logDetails": "Protokolldetails", "components.Settings.SettingsLogs.logs": "Protokolle", "components.Settings.SettingsLogs.logsDescription": "Du kannst diese Protokolle auch direkt über <code>stdout</code> oder in <code>{appDataPath}/logs/jellyseerr.log</code> anzeigen.", "components.Settings.SettingsLogs.message": "Nachricht", "components.Settings.SettingsLogs.pauseLogs": "Pause", "components.Settings.SettingsLogs.resumeLogs": "Fortsetzen", "components.Settings.SettingsLogs.showall": "Alle Protokolle anzeigen", "components.Settings.SettingsLogs.time": "Zeitstempel", "components.Settings.SettingsLogs.viewdetails": "Details anzeigen", "components.Settings.SettingsUsers.defaultPermissions": "Standardberechtigungen", "components.Settings.SettingsUsers.defaultPermissionsTip": "Initiale Berechtigungen neuem Nutzer zugewiesen", "components.Settings.SettingsUsers.localLogin": "Lokale Anmeldung aktivieren", "components.Settings.SettingsUsers.localLoginTip": "<PERSON><PERSON>er dürfen sich mit ihrer E-Mail-Adresse und Passwort anmelden", "components.Settings.SettingsUsers.movieRequestLimitLabel": "Globales Filmanfragen-Limit", "components.Settings.SettingsUsers.newPlexLogin": "Aktiviere neue {mediaServerName} Anmeldung", "components.Settings.SettingsUsers.newPlexLoginTip": "Erlaube {mediaServerName} Nutzer Log-In, ohne diese zuerst importieren zu müssen", "components.Settings.SettingsUsers.toastSettingsFailure": "<PERSON>im <PERSON>ichern der Einstellungen ist ein Fehler aufgetreten.", "components.Settings.SettingsUsers.toastSettingsSuccess": "Benutzereinstellungen erfolgreich gespeichert!", "components.Settings.SettingsUsers.tvRequestLimitLabel": "Globales Serienanfragenlimit", "components.Settings.SettingsUsers.userSettings": "Benutzereinstellungen", "components.Settings.SettingsUsers.userSettingsDescription": "Globale und Standardbenutzereinstellungen konfigurieren.", "components.Settings.SettingsUsers.users": "<PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.add": "Server hinz<PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.animeTags": "Anime Tags", "components.Settings.SonarrModal.animelanguageprofile": "Anime-Sprachprofil", "components.Settings.SonarrModal.animequalityprofile": "Animequalitätsprofil", "components.Settings.SonarrModal.animerootfolder": "Animestammverzeichnis", "components.Settings.SonarrModal.apiKey": "API-Schlüssel", "components.Settings.SonarrModal.baseUrl": "Basis-URL", "components.Settings.SonarrModal.create4ksonarr": "Neuen 4K Sonarr Server hinzufügen", "components.Settings.SonarrModal.createsonarr": "Neuen Sonarr-Server hinzuf<PERSON>", "components.Settings.SonarrModal.default4kserver": "Standard 4K Server", "components.Settings.SonarrModal.defaultserver": "Standardserver", "components.Settings.SonarrModal.edit4ksonarr": "4K Sonarr Server bearbeiten", "components.Settings.SonarrModal.editsonarr": "Sonarr Server bearbeiten", "components.Settings.SonarrModal.enableSearch": "Automatische Suche aktivieren", "components.Settings.SonarrModal.externalUrl": "Externe URL", "components.Settings.SonarrModal.hostname": "Hostname oder IP-Adresse", "components.Settings.SonarrModal.languageprofile": "Sprachprofil", "components.Settings.SonarrModal.loadingTags": "Lade Tags…", "components.Settings.SonarrModal.loadinglanguageprofiles": "Sprachprofile werden geladen…", "components.Settings.SonarrModal.loadingprofiles": "Qualitätsprofile werden geladen…", "components.Settings.SonarrModal.loadingrootfolders": "Stammordner werden geladen…", "components.Settings.SonarrModal.notagoptions": "Keine Tags.", "components.Settings.SonarrModal.port": "Port", "components.Settings.SonarrModal.qualityprofile": "Qualitätsprofil", "components.Settings.SonarrModal.rootfolder": "Stammordner", "components.Settings.SonarrModal.seasonfolders": "<PERSON><PERSON> Ordner", "components.Settings.SonarrModal.selectLanguageProfile": "<PERSON><PERSON>hle ein Sprachprofil aus", "components.Settings.SonarrModal.selectQualityProfile": "Wähle Qualitätsprofil", "components.Settings.SonarrModal.selectRootFolder": "W<PERSON><PERSON>e Stammordner", "components.Settings.SonarrModal.selecttags": "Wähle Tags", "components.Settings.SonarrModal.server4k": "4K-Server", "components.Settings.SonarrModal.servername": "Servername", "components.Settings.SonarrModal.ssl": "SSL aktivieren", "components.Settings.SonarrModal.syncEnabled": "Scannen aktivieren", "components.Settings.SonarrModal.tags": "Tags", "components.Settings.SonarrModal.testFirstLanguageProfiles": "Teste die Verbindung zum Laden von Sprachprofilen", "components.Settings.SonarrModal.testFirstQualityProfiles": "Teste die Verbindung, um Qualitätsprofile zu laden", "components.Settings.SonarrModal.testFirstRootFolders": "Teste die Verbindung, um Stammordner zu laden", "components.Settings.SonarrModal.testFirstTags": "<PERSON><PERSON> Verbindu<PERSON>, um <PERSON><PERSON> zu laden", "components.Settings.SonarrModal.toastSonarrTestFailure": "Verbin<PERSON><PERSON> zu Sonarr fehlgeschlagen.", "components.Settings.SonarrModal.toastSonarrTestSuccess": "Sonarr-Verbindung erfolgreich hergestellt!", "components.Settings.SonarrModal.validationApiKeyRequired": "Du musst einen API-Schlüssel angeben", "components.Settings.SonarrModal.validationApplicationUrl": "Du musst eine gültige URL angeben", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "Die URL darf nicht mit einem abschließenden Schrägstrich enden", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "Die Basis-URL muss einen führenden Schrägstrich haben", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "Die Basis-URL darf nicht mit einem abschließenden Schrägstrich enden", "components.Settings.SonarrModal.validationHostnameRequired": "Du musst einen Hostnamen oder IP-Adresse angeben", "components.Settings.SonarrModal.validationLanguageProfileRequired": "Du musst ein Qualitätsprofil auswählen", "components.Settings.SonarrModal.validationNameRequired": "Du musst einen Servernamen angeben", "components.Settings.SonarrModal.validationPortRequired": "Du musst einen Port angeben", "components.Settings.SonarrModal.validationProfileRequired": "Du musst ein Qualitätsprofil auswählen", "components.Settings.SonarrModal.validationRootFolderRequired": "Du musst einen Stammordner auswählen", "components.Settings.activeProfile": "Aktives Profil", "components.Settings.addradarr": "Radarr Server hinzufügen", "components.Settings.address": "<PERSON><PERSON><PERSON>", "components.Settings.addsonarr": "Sonarr Server hinzufügen", "components.Settings.advancedTooltip": "Bei falscher Konfiguration dieser Einstellung, kann dies zu einer Funktionsstörung führen", "components.Settings.cancelscan": "Durchsuchung abbrechen", "components.Settings.copied": "API-Schlüssel in die Zwischenablage kopiert.", "components.Settings.currentlibrary": "Aktuelle Bibliothek: {name}", "components.Settings.default": "Standard<PERSON><PERSON><PERSON><PERSON>", "components.Settings.default4k": "Standard-4K", "components.Settings.deleteServer": "{serverType} Server löschen", "components.Settings.deleteserverconfirm": "<PERSON><PERSON> du sicher, dass du diesen Server löschen möchtest?", "components.Settings.email": "E-Mail", "components.Settings.enablessl": "SSL aktivieren", "components.Settings.experimentalTooltip": "Die Aktivierung dieser Einstellung kann zu einem unerwarteten Verhalten der Anwendung führen", "components.Settings.externalUrl": "Externe URL", "components.Settings.hostname": "Hostname oder IP-Adresse", "components.Settings.is4k": "4K", "components.Settings.librariesRemaining": "Verbleibende Bibliotheken: {count}", "components.Settings.manualscan": "Manuelle Bibliotheksdurchsuchung", "components.Settings.manualscanDescription": "Normalerweise wird dies nur einmal alle 24 Stunden ausgeführt. Jellyseerr überprüft die kürzlich hinzugefügten Plex-Server aggressiver. Falls du Plex zum ersten Mal konfigurierst, wird <PERSON><PERSON><PERSON><PERSON>, ein<PERSON><PERSON> eine manuelle, komplette Bibliotheksdurchsuchung anzustoßen!", "components.Settings.mediaTypeMovie": "Film", "components.Settings.mediaTypeSeries": "Serie", "components.Settings.menuAbout": "<PERSON><PERSON>", "components.Settings.menuGeneralSettings": "Allgemein", "components.Settings.menuJobs": "Aufgaben und Cache", "components.Settings.menuLogs": "Protokolle", "components.Settings.menuNotifications": "Benachrichtigungen", "components.Settings.menuPlexSettings": "Plex", "components.Settings.menuServices": "<PERSON><PERSON><PERSON>", "components.Settings.menuUsers": "<PERSON><PERSON><PERSON>", "components.Settings.noDefault4kServer": "Ein 4K {serverType} Server muss als <PERSON>art markiert werden um Nutzern zu ermöglichen 4K {mediaType} anfragen zu senden.", "components.Settings.noDefaultNon4kServer": "Wenn du nur einen einzigen {serverType}-Server für Nicht-4K- und 4K-Inhalte hast (oder wenn du nur 4K-Inhalte herunterlädst), solltest du den {serverType}-Server <strong>NICHT</strong> als 4K-Server festgelegen.", "components.Settings.noDefaultServer": "Mindestens ein {serverType}-Server muss als Standard markiert sein, damit {mediaType}-Anfragen verarbeitet werden können.", "components.Settings.notificationAgentSettingsDescription": "Konfiguriere und aktiviere Benachrichtigungsagenten.", "components.Settings.notifications": "Benachrichtigungen", "components.Settings.notificationsettings": "Benachrichtigungseinstellungen", "components.Settings.notrunning": "Nicht aktiv", "components.Settings.plex": "Plex", "components.Settings.plexlibraries": "Plex Bibliotheken", "components.Settings.plexlibrariesDescription": "Die Bibliotheken, welche Jellyseerr nach Titeln durchsucht. Richte deine Plex Verbindungseinstellungen ein und speichere sie. Sollten keine auf<PERSON><PERSON> sein, klicke auf die Schaltfläche weiter unten.", "components.Settings.plexsettings": "Plex Einstellungen", "components.Settings.plexsettingsDescription": "Konfiguriere die Einstellungen deines Plex Servers. Jellyseerr durchsucht deine Plex Bibliotheken zur Feststellung der verfügbaren Inhalte.", "components.Settings.port": "Port", "components.Settings.radarrsettings": "Radarr Einstellungen", "components.Settings.restartrequiredTooltip": "<PERSON><PERSON><PERSON><PERSON> muss neu gestartet werden, damit Änderungen angewendet werden können", "components.Settings.scan": "Bibliotheken synchronisieren", "components.Settings.scanning": "Synchronisieren…", "components.Settings.serverLocal": "lokal", "components.Settings.serverRemote": "entfernt", "components.Settings.serverSecure": "<PERSON><PERSON>", "components.Settings.serverpreset": "Server", "components.Settings.serverpresetLoad": "<PERSON>licke auf die Schaltfläche, um verfügbare Server zu laden", "components.Settings.serverpresetManualMessage": "<PERSON><PERSON> Konfiguration", "components.Settings.serverpresetRefreshing": "Rufe Server ab…", "components.Settings.serviceSettingsDescription": "Konfiguriere unten deine {serverType}-Server. <PERSON> kannst mehrere {serverType}-Server verbinden, aber nur zwei davon können als Standard markiert werden (ein Nicht-4K- und ein 4K-Server). Administratoren können den Server überschreiben, auf dem neue Anfragen vor der Genehmigung verarbeitet werden.", "components.Settings.services": "<PERSON><PERSON><PERSON>", "components.Settings.settingUpPlexDescription": "Um Plex e<PERSON>zu<PERSON>, können die Daten manuell eintragen oder einen Server ausgewählt werden, welcher von <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink> abgerufen wurde. Drück den Knopf rechts neben dem Dropdown-Menü, um die Liste der verfügbaren Server abzurufen.", "components.Settings.sonarrsettings": "<PERSON><PERSON><PERSON>", "components.Settings.ssl": "SSL", "components.Settings.startscan": "Durchsuchung starten", "components.Settings.tautulliApiKey": "API-Schlüssel", "components.Settings.tautulliSettings": "<PERSON><PERSON><PERSON>", "components.Settings.tautulliSettingsDescription": "Optionale Einstellungen für den Tautulli-Server konfigurieren. <PERSON><PERSON><PERSON>rr holt die Überwachungsdaten für Ihre Plex-Medien von <PERSON>.", "components.Settings.toastPlexConnecting": "Versuche mit Plex zu verbinden…", "components.Settings.toastPlexConnectingFailure": "Verbindung zu Plex fehlgeschlagen.", "components.Settings.toastPlexConnectingSuccess": "Plex Verbindung erfolgreich hergestellt!", "components.Settings.toastPlexRefresh": "Abrufen der Serverliste von <PERSON>lex…", "components.Settings.toastPlexRefreshFailure": "Fehler beim Abrufen der Plex Serverliste.", "components.Settings.toastPlexRefreshSuccess": "Plex Serverliste erfolgreich abgerufen!", "components.Settings.toastTautulliSettingsFailure": "Beim Speichern der Tautulli Einstellungen ist etwas schief gegangen.", "components.Settings.toastTautulliSettingsSuccess": "Tautulli Einstellungen erfolgreich gespeichert!", "components.Settings.urlBase": "URL-Basis", "components.Settings.validationApiKey": "Die Angabe eines API-Schlüssels ist erforderlich", "components.Settings.validationHostnameRequired": "Ein gültiger Hostnamen oder IP-Adresse muss angeben werden", "components.Settings.validationPortRequired": "Du musst einen gültigen Port angeben", "components.Settings.validationUrl": "Die Angabe einer gültigen URL ist erforderlich", "components.Settings.validationUrlBaseLeadingSlash": "Die URL-Basis muss einen Schrägstrich enthalten", "components.Settings.validationUrlBaseTrailingSlash": "Die URL-Basis darf nicht mit einem Schrägstrich enden", "components.Settings.validationUrlTrailingSlash": "URL darf nicht mit einem Schrägstrich enden", "components.Settings.webAppUrl": "<WebAppLink>Web App</WebAppLink> URL", "components.Settings.webAppUrlTip": "Leite Benutzer optional zur Web-App auf deinen Server statt zur „gehosteten“ Web-App weiter", "components.Settings.webhook": "Webhook", "components.Settings.webpush": "Web Push", "components.Setup.configureservices": "Dienste konfigurieren", "components.Setup.continue": "Fortfahren", "components.Setup.finish": "Konfiguration beenden", "components.Setup.finishing": "Fertigstellung…", "components.Setup.setup": "Einrichtung", "components.Setup.signinMessage": "<PERSON>de dich zunächst an", "components.Setup.welcome": "<PERSON><PERSON>mme<PERSON> bei <PERSON>", "components.StatusBadge.managemedia": "{mediaType} verwalten", "components.StatusBadge.openinarr": "In {arr} <PERSON><PERSON><PERSON>", "components.StatusBadge.playonplex": "Auf {mediaServerName} abspielen", "components.StatusBadge.seasonepisodenumber": "S{seasonNumber}F{episodeNumber}", "components.StatusBadge.status": "{status}", "components.StatusBadge.status4k": "4K {status}", "components.StatusChecker.appUpdated": "{applicationTitle} aktualisiert", "components.StatusChecker.appUpdatedDescription": "<PERSON><PERSON>e bitte auf die Schaltfläche unten, um die Anwendung neu zu laden.", "components.StatusChecker.reloadApp": "{applicationTitle} neu laden", "components.StatusChecker.restartRequired": "Server <PERSON><PERSON><PERSON><PERSON>", "components.StatusChecker.restartRequiredDescription": "Starte bitte den Server neu, um die aktualisierten Einstellungen zu übernehmen.", "components.TitleCard.cleardata": "Daten löschen", "components.TitleCard.mediaerror": "{mediaType} wurde nicht gefunden", "components.TitleCard.tmdbid": "TMDB-ID", "components.TitleCard.tvdbid": "TheTVDB-ID", "components.TvDetails.Season.noepisodes": "Liste der Episoden nicht verfügbar.", "components.TvDetails.Season.somethingwentwrong": "<PERSON><PERSON> der Staffel ist etwas schief gelaufen.", "components.TvDetails.TvCast.fullseriescast": "Komplette Serien Besetzung", "components.TvDetails.TvCrew.fullseriescrew": "Komplette Serien-Crew", "components.TvDetails.anime": "Anime", "components.TvDetails.cast": "Besetzung", "components.TvDetails.episodeCount": "{episodeCount, plural, one {# Episode} other {# Episoden}}", "components.TvDetails.episodeRuntime": "Episodenlaufzeit", "components.TvDetails.episodeRuntimeMinutes": "{runtime} Minuten", "components.TvDetails.firstAirDate": "Erstausstrahlung", "components.TvDetails.manageseries": "Serie verwalten", "components.TvDetails.network": "{networkCount, plural, one {Netzwerk} other {Netzwerke}}", "components.TvDetails.nextAirDate": "Nächstes Sendedatum", "components.TvDetails.originallanguage": "Originalsprache", "components.TvDetails.originaltitle": "Originaltitel", "components.TvDetails.overview": "Übersicht", "components.TvDetails.overviewunavailable": "Übersicht nicht verfügbar.", "components.TvDetails.productioncountries": "Produktions {countryCount, plural, one {Land} other {Länder}}", "components.TvDetails.recommendations": "Empfehlungen", "components.TvDetails.reportissue": "Problem melden", "components.TvDetails.rtaudiencescore": "Rotten Tomatoes Publikumswertung", "components.TvDetails.rtcriticsscore": "Rotten Tomatoes Tomatometer", "components.TvDetails.seasonnumber": "Staffel {seasonNumber}", "components.TvDetails.seasons": "{seasonCount, plural, one {# <PERSON><PERSON>} other {# <PERSON><PERSON><PERSON>}}", "components.TvDetails.seasonstitle": "Staffeln", "components.TvDetails.showtype": "Serientyp", "components.TvDetails.similar": "Ähnliche Serien", "components.TvDetails.status4k": "4K {status}", "components.TvDetails.streamingproviders": "Streamt derzeit auf", "components.TvDetails.tmdbuserscore": "TMDB-Nutzerwertung", "components.TvDetails.viewfullcrew": "Komplette Crew anzeigen", "components.TvDetails.watchtrailer": "<PERSON>er <PERSON><PERSON><PERSON>", "components.UserList.accounttype": "Art", "components.UserList.admin": "Admin", "components.UserList.autogeneratepassword": "Passwort automatisch generieren", "components.UserList.autogeneratepasswordTip": "Sende ein vom Server generiertes Kennwort per E-Mail an den Benutzer", "components.UserList.bulkedit": "Ausgewählte bearbeiten", "components.UserList.create": "<PERSON><PERSON><PERSON><PERSON>", "components.UserList.created": "Beigetreten", "components.UserList.createlocaluser": "Lokalen Benutzer erstellen", "components.UserList.creating": "<PERSON><PERSON><PERSON>…", "components.UserList.deleteconfirm": "Möchtest du diesen Benutzer wirklich löschen? Alle seine Anfragendaten werden dauerhaft entfernt.", "components.UserList.deleteuser": "Benutzer löschen", "components.UserList.edituser": "Benutzerberechtigungen Bearbeiten", "components.UserList.email": "E-Mail Adresse", "components.UserList.importedfromplex": "<strong>{userCount}</strong> Plex {userCount, Plural, one {<PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON>}} erfolgreich importiert!", "components.UserList.importfrommediaserver": "{mediaServerName}-Benutzer importieren", "components.UserList.importfromplex": "Plex Benutzer importieren", "components.UserList.importfromplexerror": "<PERSON><PERSON> Importieren von Plex Benutzern ist etwas schief gelaufen.", "components.UserList.localLoginDisabled": "Die Einstellung <strong>Lokale Anmeldung aktivieren</strong> ist derzeit deaktiviert.", "components.UserList.localuser": "<PERSON><PERSON><PERSON>", "components.UserList.newplexsigninenabled": "Die Einstellung <strong>Aktiviere neuen Plex Log-In</strong> ist derzeit aktiviert. Plex-Benutzer mit Bibliothekszugang müssen nicht importiert werden, um sich anmelden zu können.", "components.UserList.nouserstoimport": "<PERSON>s gibt keine zu importierenden Plex Benutzer.", "components.UserList.owner": "<PERSON><PERSON><PERSON><PERSON>", "components.UserList.password": "Passwort", "components.UserList.passwordinfodescription": "Konfiguriere eine Anwendungs-URL und aktiviere E-Mail-Benachrichtigungen, um die automatische Kennwortgenerierung zu ermöglichen.", "components.UserList.plexuser": "Plex<PERSON><PERSON><PERSON><PERSON>", "components.UserList.role": "<PERSON><PERSON>", "components.UserList.sortCreated": "Beitrittsdatum", "components.UserList.sortDisplayName": "Anzeigename", "components.UserList.sortRequests": "An<PERSON><PERSON> der Anfragen", "components.UserList.totalrequests": "Anfragen", "components.UserList.user": "<PERSON><PERSON><PERSON>", "components.UserList.usercreatedfailed": "<PERSON><PERSON> des Benutzers ist etwas schief gelaufen.", "i18n.edit": "<PERSON><PERSON><PERSON>", "i18n.experimental": "Experimentell", "components.UserList.userssaved": "Benutzerberechtigungen erfolgreich gespeichert!", "i18n.advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserList.validationEmail": "E-Mail <PERSON><PERSON><PERSON>", "components.UserList.users": "<PERSON><PERSON><PERSON>", "components.UserProfile.recentrequests": "Bisherige <PERSON>", "components.UserProfile.UserSettings.menuPermissions": "Berechtigungen", "components.UserProfile.UserSettings.menuNotifications": "Benachrichtigungen", "components.UserProfile.UserSettings.menuGeneralSettings": "Allgemein", "components.UserProfile.UserSettings.menuChangePass": "Passwort", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "Berechtigungen erfolgreich gespeichert!", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "Beim Speichern der Einstellungen ist etwas schief gelaufen.", "components.UserProfile.UserSettings.UserPermissions.permissions": "Berechtigungen", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "Passwort ist zu kurz; es sollte mindestens 8 Zeichen lang sein", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "Du musst ein neues Passwort angeben", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "Du musst dein aktuelles Passwort angeben", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "Das Passwort muss übereinstimmen", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "Du musst das neue Passwort bestätigen", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "Passwort erfolgreich geändert!", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "<PERSON>im S<PERSON>ichern des Passworts ist ein Fehler aufgetreten.", "components.UserProfile.UserSettings.UserPasswordChange.password": "Passwort ändern", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "Neues Passwort", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "Aktuelles Passwort", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "Passwort bestätigen", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "Benachrichtigungseinstellungen", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "Benutzer-ID", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "Einstellungen erfolgreich gespeichert!", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "Beim Speichern der Einstellungen ist etwas schief gelaufen.", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "Allgemeine Einstellungen", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "Anzeigename", "components.UserProfile.ProfileHeader.settings": "Einstellungen bearbeiten", "components.UserProfile.ProfileHeader.profile": "<PERSON><PERSON> anzeigen", "components.UserList.userfail": "<PERSON>im Speichern der Benutzerberechtigungen ist ein Fehler aufgetreten.", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "Du musst eine gültige Benutzer-ID angeben", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "Die <FindDiscordIdLink>ID Nummer</FindDiscordIdLink> für dein Benutzerkonto", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "Filtere Inhalte nach regionaler Verfügbarkeit", "components.UserProfile.UserSettings.UserGeneralSettings.region": "Region Entdecken", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "Filtere Inhalte nach Originalsprache", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "Sprache des Bereiches \"Entdecken\"", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "<PERSON>e haben keine Berechtigung, das Kennwort dieses Benutzers zu ändern.", "components.UserProfile.UserSettings.UserGeneralSettings.user": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.role": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "Admin", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "Kontotyp", "i18n.loading": "<PERSON>de…", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "Du musst eine gültige Chat-ID angeben", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink>Starte einen Chat</TelegramBotLink>, füge <GetIdBotLink>@get_id_bot</GetIdBotLink> hinzu, und führe den Befehl <code>/my_id</code> aus", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "Chat-ID", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "Sende Benachrichtigungen ohne Ton", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "<PERSON><PERSON><PERSON> senden", "components.UserProfile.ProfileHeader.userid": "Benutzer-ID: {userid}", "components.UserProfile.ProfileHeader.joindate": "Mit<PERSON>ed seit dem {joindate}", "components.UserProfile.UserSettings.unauthorizedDescription": "Du hast keine Berechtigung, die Einstellungen dieses Benutzers zu ändern.", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "Du kannst deine eigenen Berechtigungen nicht ändern.", "pages.errormessagewithcode": "{statusCode} - {error}", "pages.somethingwentwrong": "Etwas ist schief gelaufen", "pages.serviceunavailable": "Dienst nicht verfügbar", "pages.pagenotfound": "Seite nicht gefunden", "pages.internalservererror": "<PERSON><PERSON>", "i18n.usersettings": "Benutzereinstellungen", "i18n.settings": "Einstellungen", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "<PERSON>im Speichern des Passworts ist ein Fehler aufgetreten. Wurde dein aktuelles Passwort korrekt eingegeben?", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "Benachrichtigungen", "components.UserProfile.UserSettings.UserGeneralSettings.general": "Allgemein", "i18n.delimitedlist": "{a}, {b}", "i18n.view": "Anzeigen", "i18n.tvshow": "Serie", "i18n.testing": "<PERSON>en…", "i18n.test": "Test", "i18n.status": "Status", "i18n.showingresults": "Zeige <strong>{from}</strong> bis <strong>{to}</strong> von <strong>{total}</strong> <PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.saving": "Speichern…", "i18n.save": "Änderungen speichern", "i18n.retrying": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "i18n.resultsperpage": "Zeige {pageSize} Ergebnisse pro Seite", "i18n.requesting": "<PERSON><PERSON><PERSON>…", "i18n.request4k": "In 4K anfragen", "i18n.previous": "Zurück", "i18n.notrequested": "<PERSON><PERSON> Angefragt", "i18n.noresults": "<PERSON><PERSON>.", "i18n.next": "<PERSON><PERSON>", "i18n.movie": "Film", "i18n.canceling": "Abbrechen…", "i18n.back": "Zurück", "i18n.areyousure": "Bist du sicher?", "i18n.all": "Alle", "components.UserProfile.totalrequests": "Anfragen insgesamt", "components.UserProfile.seriesrequest": "Serienanfragen", "components.UserProfile.pastdays": "{type} (vergangene {days} Tage)", "components.UserProfile.movierequests": "Filmanfragen", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "Für dein Konto ist derzeit kein Passwort festgelegt. Konfiguriere unten ein Passwort, um die Anmeldung als \"lokaler Benutzer\" mit deiner E-Mail-Adresse zu aktivieren.", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "<PERSON><PERSON><PERSON> dieses Benutzerkonto ist derzeit kein Kennwort festgelegt. Konfiguriere unten ein Kennwort, damit sich dieses Konto als \"lokaler Benutzer\" anmelden kann", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "Du musst einen gültigen öffentlichen PGP-Schlüssel angeben", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "Telegram-Benachrichtigungseinstellungen erfolgreich gespeichert!", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "Die Einstellungen für die Telegram-Benachrichtigung konnten nicht gespeichert werden.", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "Verschlüssele E-Mail-Nachrichten mit <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "Öffentlicher PGP-Schlüssel", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "E-Mail-Benachrichtigungseinstellungen erfolgreich gespeichert!", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "E-Mail-Benachrichtigungseinstellungen konnten nicht gespeichert werden.", "components.UserProfile.UserSettings.UserNotificationSettings.email": "E-Mail", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Discord-Benachrichtigungseinstellungen erfolgreich gespeichert!", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "Die Einstellungen für die Discord-Benachrichtigung konnten nicht gespeichert werden.", "components.UserProfile.unlimited": "Unbegrenzt", "components.UserProfile.requestsperdays": "{limit} verbleibend", "components.UserProfile.limit": "{remaining} von {limit}", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "Serienanfragenlimit", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "Filmanfragenlimit", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "Überschreibe globales Limit", "components.UserList.usercreatedfailedexisting": "Die angegebene E-Mail Adresse wird bereits von einem anderen Benutzer verwendet.", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "Standard ({language})", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "Web Push", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "Anzeigesprache", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "Discord <PERSON> ID", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "Die <FindDiscordIdLink>mehrstellige ID-Nummer</FindDiscordIdLink> deines Discord-Accounts", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "Filme automatisch aus Plex-Merkliste anfragen", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "Automatisch Filme aus deiner <PlexWatchlistSupportLink>Plex Merkliste</PlexWatchlistSupportLink> anfordern", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "Serien automatisch aus Plex-Merkliste anfragen", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "Automatisch Serien aus deiner <PlexWatchlistSupportLink>Plex Merkliste</PlexWatchlistSupportLink> anfragen", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "Du musst eine gültige Discord Benutzer ID angeben", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "Zugangs-Token", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "<PERSON><PERSON><PERSON> ein Token aus deinen <PushbulletSettingsLink>Kontoeinstellungen</PushbulletSettingsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "Die Einstellungen für Pushbullet-Benachrichtigungen konnten nicht gespeichert werden.", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "Pushbullet-Benachrichtigungseinstellungen erfolgreich gespeichert!", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "Anwendungs API-Token", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "<ApplicationRegistrationLink>Registriere eine Anwendung</ApplicationRegistrationLink> zur Verwendung mit {applicationTitle}", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "Benutzer- oder Gruppenschlüssel", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "Die 30-stellige <UsersGroupsLink>Benutzer- oder Gruppenkennung</UsersGroupsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "Die Einstellungen für die Pushover-Benachrichtigung konnten nicht gespeichert werden.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "Pushover-Benachrichtigungseinstellungen erfolgreich gespeichert!", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "Ein Zugriffstoken muss angegeben werden", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "Du musst einen gültigen Anwendungs-Token angeben", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "Du musst einen gültigen Benutzer- oder Gruppenschlüssel angeben", "i18n.resolved": "<PERSON><PERSON><PERSON><PERSON>", "i18n.importing": "Importieren…", "i18n.import": "Importieren", "components.UserProfile.recentlywatched": "<PERSON><PERSON><PERSON><PERSON> angesehen", "i18n.restartRequired": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.emptywatchlist": "Hier erscheinen deine zur <PlexWatchlistSupportLink>Plex Merkliste</PlexWatchlistSupportLink> hinzugefügte Medien.", "components.UserProfile.plexwatchlist": "Plex Me<PERSON>e", "components.Discover.DiscoverTvKeyword.keywordSeries": "{keywordTitle} Serien", "components.Discover.moviegenres": "Film Genre", "components.Discover.studios": "Studios", "components.Discover.tmdbmoviegenre": "TMDB Film Genre", "components.Discover.tmdbtvgenre": "TMDB Serien Genre", "components.Discover.tmdbtvkeyword": "TMDB Serien Stichwort", "components.Discover.tvgenres": "Serien Genre", "components.Settings.SettingsMain.apikey": "API-Schlüssel", "components.Settings.SettingsMain.applicationTitle": "Anwendungstitel", "components.Settings.SettingsMain.general": "Allgemein", "components.Settings.SettingsMain.generalsettings": "Allgemeine Einstellungen", "components.Settings.SettingsMain.locale": "Anzeigesprache", "components.Settings.SettingsMain.hideAvailable": "Verfügbare Medien ausblenden", "components.Settings.SettingsMain.toastApiKeySuccess": "Neuer API Schlüssel erfolgreich generiert!", "components.Settings.SettingsMain.validationApplicationUrl": "Du musst eine valide URL angeben", "components.Settings.SettingsMain.validationApplicationUrlTrailingSlash": "Die URL darf nicht mit einem Slash \"/\" enden", "components.Discover.DiscoverMovieKeyword.keywordMovies": "{keywordTitle} Filme", "components.Discover.PlexWatchlistSlider.plexwatchlist": "<PERSON><PERSON>", "components.Discover.tmdbsearch": "TMDB Suche", "components.Settings.SettingsMain.toastApiKeyFailure": "Etwas ist schiefgelaufen während der Generierung eines neuen API Schlüssels.", "components.Settings.SettingsMain.toastSettingsSuccess": "Einstellungen erfolgreich gespeichert!", "components.Discover.tmdbmoviekeyword": "TMDB Film Stichwort", "components.Settings.SettingsMain.validationApplicationTitle": "Du musst einen Anwendungstitel angeben", "components.Discover.PlexWatchlistSlider.emptywatchlist": "Medien in deiner <PlexWatchlistSupportLink>Plex Merkliste</PlexWatchlistSupportLink> er<PERSON><PERSON> hier.", "components.Settings.SettingsMain.cacheImagesTip": "Cache extern gehostete Bilder (erfordert eine beträchtliche Menge an Speicherplatz)", "components.Discover.networks": "Sender", "components.Discover.tmdbstudio": "TMDB Studio", "components.Settings.SettingsMain.applicationurl": "Anwendung URL", "components.Settings.SettingsMain.cacheImages": "Bild-Caching aktivieren", "components.Settings.SettingsMain.generalsettingsDescription": "Globale- und Standardeinstellungen für Jellyseerr konfigurieren.", "components.Settings.SettingsMain.originallanguage": "Sprache des Bereiches \"Entdecken\"", "components.Settings.SettingsMain.partialRequestsEnabled": "Teilweise Serienanfragen zulassen", "components.Settings.SettingsMain.toastSettingsFailure": "<PERSON>im <PERSON>ichern der Einstellungen ist ein Fehler aufgetreten.", "components.Discover.tmdbnetwork": "TMDB Sender", "components.Settings.SettingsMain.originallanguageTip": "Inhalt nach Originalsprache filtern", "components.Discover.CreateSlider.addSlider": "<PERSON><PERSON><PERSON>", "components.Discover.CreateSlider.addcustomslider": "Benutzerdefinierten Slider erstellen", "components.Discover.CreateSlider.addfail": "<PERSON><PERSON><PERSON> Slider konnte nicht erstellt werden.", "components.Discover.CreateSlider.addsuccess": "Ein neuer Slider wurde erstellt und die Einstellungen wurden gespeichert.", "components.Discover.CreateSlider.editSlider": "Slider bearbeiten", "components.Discover.CreateSlider.editfail": "<PERSON>lide<PERSON> konnte nicht bearbeitet werden.", "components.Discover.CreateSlider.editsuccess": "Slider bearbeitet und Einstellung gespeichert.", "components.Discover.CreateSlider.needresults": "Es muss mindestens 1 Ergebnis vorhanden sein.", "components.Layout.Sidebar.browsemovies": "Filme", "components.Layout.Sidebar.browsetv": "Serien", "components.Discover.CreateSlider.nooptions": "<PERSON><PERSON>.", "components.Discover.CreateSlider.providetmdbgenreid": "Hinterlege eine TMDB Genre ID", "components.Discover.CreateSlider.providetmdbkeywordid": "Hinterlege eine TMDB Keyword ID", "components.Discover.CreateSlider.providetmdbnetwork": "Hinterlege eine TMDB Netzwerk ID", "components.Discover.CreateSlider.providetmdbsearch": "Geben Si<PERSON> eine Suchanfrage an", "components.Discover.CreateSlider.validationTitlerequired": "Du musst einen T<PERSON>l e<PERSON>ben.", "components.Discover.DiscoverSliderEdit.remove": "Entfernen", "components.Discover.DiscoverSliderEdit.deletefail": "<PERSON><PERSON><PERSON> konnte nicht gelöscht werden.", "components.Discover.DiscoverSliderEdit.deletesuccess": "<PERSON><PERSON><PERSON> er<PERSON><PERSON><PERSON><PERSON><PERSON> entfernt.", "components.Discover.DiscoverMovies.discovermovies": "Filme", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "Erscheinungsdatum (aufsteigend)", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "Erscheinungsdatum (absteigend)", "components.Discover.DiscoverMovies.sortTitleAsc": "Titel (A-Z) (aufsteigend)", "components.Discover.DiscoverMovies.sortTitleDesc": "Titel (Z-A) (absteigend)", "components.Discover.DiscoverTv.discovertv": "Serien", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "Erstausstrahlung (absteigend)", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "Erstausstrahlung (aufsteigend)", "components.Discover.DiscoverTv.sortPopularityAsc": "Beliebtheit (aufsteigend)", "components.Discover.DiscoverTv.sortPopularityDesc": "Beliebtheit (absteigend)", "components.Discover.CreateSlider.slidernameplaceholder": "Name <PERSON>", "components.Settings.SettingsJobsCache.availability-sync": "Medienverfügbarkeit Sync", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {# Aktiver Filter} other {# Aktive Filter}}", "components.Discover.FilterSlideover.originalLanguage": "Originalsprache", "components.Settings.SettingsJobsCache.editJobScheduleSelectorSeconds": "Alle {jobScheduleSeconds, plural, one {Sekunde} other {{jobScheduleSeconds} Sekunden}}", "components.Discover.updatefailed": "Bei der Aktualisierung der Entdecken-Einstellungen ist ein Fehler aufgetreten.", "components.Selector.searchGenres": "Genres auswählen…", "components.Discover.updatesuccess": "Die Einstellungen für die Entdecken-Anpassung wurden aktualisiert.", "components.Selector.showmore": "<PERSON><PERSON> anzeigen", "components.Selector.starttyping": "Start der Suche durch Tippen.", "components.Selector.showless": "<PERSON><PERSON> anzeigen", "components.Discover.CreateSlider.providetmdbstudio": "TMDB Studio ID angeben", "components.Discover.CreateSlider.searchGenres": "Genres suchen…", "components.Discover.CreateSlider.searchKeywords": "Stichwörter suchen…", "components.Discover.CreateSlider.searchStudios": "Studios suchen…", "components.Discover.CreateSlider.starttyping": "Start der Suche durch Tippen.", "components.Discover.CreateSlider.validationDatarequired": "Du musst einen Datenwert angeben.", "components.Discover.DiscoverSliderEdit.enable": "Sichtbarkeit umschalten", "components.Discover.customizediscover": "Entdecken anpassen", "components.Discover.resetfailed": "<PERSON><PERSON>zen der Entdecken-Einstellungen ist etwas schief gelaufen.", "components.Discover.resetsuccess": "Die Entdecken-Einstellungen wurden erfolgreich zurückgesetzt.", "components.Discover.stopediting": "Bearbeitung stoppen", "components.Discover.resettodefault": "Zurücksetzen auf Standard", "components.Discover.resetwarning": "Setzt alle Slider auf die Standardwerte zurück. Dadurch werden auch alle benutzerdefinierten Slider gelöscht!", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {# Aktiver Filter} other {# Aktive Filter}}", "components.Discover.DiscoverMovies.sortPopularityAsc": "Beliebtheit (aufsteigend)", "components.Discover.DiscoverMovies.sortPopularityDesc": "Beliebtheit (absteigend)", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "TMDB-Bewertung (aufsteigend)", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "TMDB-Bewertung (absteigend)", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {# Aktiver Filter} other {# Aktive Filter}}", "components.Discover.DiscoverTv.sortTitleAsc": "Titel (A-Z) (aufsteigend)", "components.Discover.DiscoverTv.sortTitleDesc": "Titel (Z-A) (absteigend)", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "TMDB-Bewertung (aufsteigend)", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "TMDB-Bewertung (absteigend)", "components.Discover.FilterSlideover.clearfilters": "Aktive Filter löschen", "components.Discover.FilterSlideover.filters": "Filter", "components.Discover.FilterSlideover.firstAirDate": "Datum der Erstausstrahlung", "components.Discover.FilterSlideover.from": "Vom", "components.Discover.FilterSlideover.genres": "Genres", "components.Discover.FilterSlideover.keywords": "Stichwörter", "components.Discover.FilterSlideover.ratingText": "Bewertungen zwischen {minValue} und {maxValue}", "components.Discover.FilterSlideover.releaseDate": "Erscheinungsdatum", "components.Discover.FilterSlideover.runtime": "Laufzeit", "components.Discover.FilterSlideover.runtimeText": "{minValue}-{maxValue} Minuten Laufzeit", "components.Discover.FilterSlideover.tmdbuserscore": "TMDB-Benutzerbewertung", "components.Discover.FilterSlideover.to": "Bis", "components.Discover.createnewslider": "<PERSON>euen Slider <PERSON>", "components.Discover.FilterSlideover.studio": "Studio", "components.Discover.FilterSlideover.streamingservices": "Streaming-Dienste", "components.Selector.nooptions": "<PERSON><PERSON>.", "components.Selector.searchKeywords": "Stichwörter suchen…", "components.Selector.searchStudios": "Studios suchen…", "components.Discover.tmdbmoviestreamingservices": "TMDB Film-Streaming-Dienste", "components.Discover.tmdbtvstreamingservices": "TMDB TV-Streaming-Dienste", "i18n.collection": "<PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.tmdbuservotecount": "Anzahl an TMDB-Benutzerbewertungen", "components.Settings.RadarrModal.tagRequestsInfo": "Füge automatisch ein Tag hinzu mit der ID und dem Namen des anfordernden Nutzers", "components.MovieDetails.imdbuserscore": "IMDB Nutzer Bewertung", "components.Settings.SonarrModal.tagRequests": "<PERSON>", "components.Discover.FilterSlideover.voteCount": "<PERSON><PERSON>hl Abstimmungen zwischen {minValue} und {maxValue}", "components.Settings.SonarrModal.tagRequestsInfo": "Füge automatisch einen zusätzlichen Tag mit der ID & Namen des anfordernden Nutzers", "components.Layout.UserWarnings.passwordRequired": "Ein Passwort ist erforderlich.", "components.Login.description": "Da du dich zum ersten Mal bei {applicationName} anmel<PERSON>t, musst du eine gültige E-Mail-Adresse angeben.", "components.Layout.UserWarnings.emailRequired": "E-Mail Adresse ist erforderlich.", "components.Layout.UserWarnings.emailInvalid": "E-Mail Adresse ist nicht gültig.", "components.Login.credentialerror": "Der Benutzername oder das Passwort ist falsch.", "components.Login.emailtooltip": "Die Adresse muss nicht mit Ihrer {mediaServerName}-Instanz verbunden sein.", "components.Login.initialsignin": "<PERSON><PERSON><PERSON><PERSON>", "components.Login.initialsigningin": "<PERSON><PERSON><PERSON><PERSON>…", "components.Login.save": "Hinzufügen", "components.Login.saving": "Hinzufügen…", "components.Login.signinwithjellyfin": "Verwende dein {mediaServerName} Konto", "components.Login.title": "E-Mail hinzufügen", "components.Login.username": "<PERSON><PERSON><PERSON><PERSON>", "components.Login.validationEmailFormat": "Ungültige E-Mail", "components.Login.validationEmailRequired": "Du musst eine E-Mail angeben", "components.Login.validationemailformat": "Gültige E-Mail erforderlich", "components.Login.validationhostformat": "Gültige URL erforderlich", "components.Login.validationhostrequired": "{mediaServerName} U<PERSON>", "components.Login.validationusernamerequired": "<PERSON><PERSON><PERSON><PERSON>", "components.ManageSlideOver.removearr": "Aus {arr} entfernen", "components.ManageSlideOver.removearr4k": "Aus 4K {arr} entfernen", "components.MovieDetails.downloadstatus": "Download-Status", "components.MovieDetails.openradarr4k": "Film in 4K Radarr öffnen", "components.MovieDetails.play": "Wiedergabe auf {mediaServerName}", "components.MovieDetails.play4k": "4K abspielen auf {mediaServerName}", "components.Settings.SonarrModal.animeSeriesType": "Anime-Serien Typ", "components.Settings.jellyfinSettings": "{mediaServerName} Einstellungen", "components.Settings.jellyfinSettingsSuccess": "{mediaServerName} Einstellungen erfolgreich gespeichert!", "components.Settings.jellyfinlibraries": "{mediaServerName} Bibliotheken", "components.Settings.jellyfinsettings": "{mediaServerName} Einstellungen", "components.Settings.jellyfinsettingsDescription": "Konfiguriere die Einstellungen für deinen {mediaServerName} Server. {mediaServerName} scannt deine {mediaServerName} Bibliotheken, um zu sehen, welche Inhalte verfügbar sind.", "components.Settings.manualscanJellyfin": "<PERSON><PERSON>n der Bibliothek", "components.Settings.menuJellyfinSettings": "{mediaServerName}", "components.Settings.saving": "Speichern…", "components.Settings.syncing": "Synchronisierung", "components.Settings.timeout": "Zeitüberschreitung", "components.Setup.signin": "Anmelden", "components.Setup.signinWithJellyfin": "Gib deine <PERSON> Daten ein", "components.Setup.signinWithPlex": "Gib deine Plex Daten ein", "components.TitleCard.watchlistDeleted": "<strong>{title}</strong> Erfolgreich aus der Merkliste entfernt!", "components.TitleCard.watchlistError": "Ein Fehler ist aufgetreten. Bitte versuche es erneut.", "components.TitleCard.watchlistSuccess": "<strong>{title}</strong> erfolgreich zur Merkliste hinzugefügt!", "components.TvDetails.play": "Wiedergabe auf {mediaServerName}", "components.TvDetails.play4k": "4K abspielen auf {mediaServerName}", "components.UserList.importfromJellyfin": "Import<PERSON><PERSON> von {mediaServerName} Benutzern", "components.UserList.mediaServerUser": "{mediaServerName} <PERSON><PERSON><PERSON>", "components.UserList.noJellyfinuserstoimport": "<PERSON><PERSON> gibt keine {mediaServerName} Benutzer zu importieren.", "components.UserList.userdeleted": "Benutzer erfolgreich gelöscht!", "components.UserList.userdeleteerror": "Beim Löschen des Benutzers ist etwas schief gelaufen.", "components.UserList.userlist": "Benutzerliste", "components.UserProfile.UserSettings.UserGeneralSettings.email": "E-Mail", "components.UserProfile.UserSettings.UserGeneralSettings.mediaServerUser": "{mediaServerName} <PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.saving": "Speichern…", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "Web-Push-Benachrichtigungseinstellungen erfolgreich gespeichert!", "i18n.close": "Schließen", "i18n.decline": "<PERSON><PERSON><PERSON><PERSON>", "i18n.declined": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.delete": "Löschen", "i18n.deleting": "<PERSON><PERSON><PERSON>…", "i18n.failed": "Fehlgeschlagen", "i18n.movies": "Filme", "i18n.open": "<PERSON>en", "i18n.pending": "<PERSON><PERSON><PERSON><PERSON>", "i18n.processing": "Verarbeitung", "i18n.request": "<PERSON><PERSON><PERSON> senden", "i18n.requested": "Ang<PERSON><PERSON><PERSON>", "i18n.retry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.tvshows": "Serie", "i18n.unavailable": "Nicht verfügbar", "pages.oops": "<PERSON><PERSON><PERSON>", "components.MovieDetails.openradarr": "Film in Radarr öffnen", "components.Settings.Notifications.NotificationsPushover.deviceDefault": "Gerätestandard", "components.Settings.RadarrModal.tagRequests": "Tag-Anfragen", "components.Settings.SettingsAbout.supportjellyseerr": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.jellyfinSettingsDescription": "Konfiguriere optional die internen und externen Endpunkte für deinen {mediaServerName} Server. In den meisten Fällen ist die externe URL eine andere als die interne URL. Für die Anmeldung bei {mediaServerName} kann auch eine benutzerdefinierte URL zum Zurücksetzen des Passworts festgelegt werden, falls du auf eine andere Seite zum Zurücksetzen des Passworts umleiten möchtest. Du kannst auch selber einen API Key für Jellyfin anlegen, was bisher automatisch geschah.", "components.Settings.jellyfinSettingsFailure": "<PERSON><PERSON> der Einstellungen von {mediaServerName} ist ein Fehler aufgetreten.", "components.Settings.manualscanDescriptionJellyfin": "Normalerweise wird dieser Vorgang nur einmal alle 24 Stunden durchgeführt. Jellyseerr wird die kürzlich hinzugefügten Bibliotheken deines {mediaServerName} Servers aggressiver überprüfen. <PERSON><PERSON> dies das erste Mal ist, dass du Jellyseerr konfigurierst, wird ein einmaliger vollständiger manueller Bibliotheks-Scan empfohlen!", "components.Settings.save": "Änderungen speichern", "components.Settings.Notifications.userEmailRequired": "Benutzer E-Mail erforderlich", "components.Settings.Notifications.NotificationsPushover.sound": "Benachrichtigungston", "components.Settings.SonarrModal.seriesType": "TV-Serie Typ", "components.Settings.jellyfinlibrariesDescription": "Die Bibliotheken {mediaServerName} werden nach Titeln durchsucht. Klicke auf die Schaltfläche unten, wenn keine Bibliotheken aufgelistet sind.", "components.UserList.importfromJellyfinerror": "<PERSON><PERSON> von {mediaServerName} Benutzern ist etwas schief gelaufen.", "components.Settings.syncJellyfin": "Bibliotheken synchronisieren", "components.Setup.configuremediaserver": "Medienserver konfigurieren", "components.UserProfile.UserSettings.UserGeneralSettings.save": "Änderungen speichern", "i18n.available": "Verfügbar", "i18n.cancel": "Abbrechen", "components.TitleCard.addToWatchList": "Zur Merkliste hinzufügen", "components.TitleCard.watchlistCancel": "<PERSON><PERSON><PERSON><PERSON> für <strong>{title}</strong> entfernt.", "components.UserList.usercreatedsuccess": "Benutzer erfolgreich angelegt!", "components.ManageSlideOver.manageModalRemoveMediaWarning": "* <PERSON><PERSON><PERSON> wird dieser {mediaType} unwiderruflich aus {arr} entfernt, einschließlich aller Dateien.", "components.UserList.importedfromJellyfin": "<strong>{userCount}</strong> {mediaServerName} {userCount, plural, one {<PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON>}} erfolgreich importiert!", "components.UserList.validationpasswordminchars": "Das Passwort ist zu kurz; es sollte mindestens 8 <PERSON>eichen lang sein", "components.UserProfile.UserSettings.UserNotificationSettings.deviceDefault": "Gerätestandard", "i18n.approve": "<PERSON><PERSON><PERSON><PERSON>", "i18n.partiallyavailable": "Teilweise verfügbar", "components.UserList.newJellyfinsigninenabled": "Die Einstellung <strong>Aktiviere neuen {mediaServerName} Sign-In</strong> ist derzeit aktiviert. {mediaServerName}-<PERSON>utzer mit Bibliothekszugang müssen nicht importiert werden, um sich anmelden zu können.", "components.UserProfile.UserSettings.UserNotificationSettings.sound": "Benachrichtigungston", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "Die Einstellungen für Web-Push-Benachrichtigungen konnten nicht gespeichert werden.", "components.UserProfile.localWatchlist": "<PERSON><PERSON><PERSON><PERSON> {username}", "i18n.approved": "<PERSON><PERSON><PERSON><PERSON>", "pages.returnHome": "Zurück zur Startseite", "components.Discover.FilterSlideover.status": "Status", "components.UserList.username": "<PERSON><PERSON><PERSON><PERSON>", "components.Login.adminerror": "Du musst einen Adminaccount für den Zugang benutzen.", "components.MovieDetails.watchlistError": "<PERSON>s ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.", "components.RequestList.RequestItem.profileName": "Profil", "components.Selector.searchStatus": "Status auswählen...", "components.Settings.invalidurlerror": "Es kann keine Verbindung zu {mediaServerName} hergestellt werden.", "components.Settings.jellyfinSyncFailedGenericError": "Es trat ein unbekannter Fehler während der Bibliothekssynchronisation auf", "components.UserList.validationUsername": "Du musst einen Benutzernamen angeben", "components.UserProfile.UserSettings.UserGeneralSettings.validationemailrequired": "E-Mail <PERSON><PERSON><PERSON>", "components.Login.invalidurlerror": "Es kann keine Verbindung zu {mediaServerName} hergestellt werden.", "components.MovieDetails.removefromwatchlist": "Von der Merkliste entfernen", "components.TvDetails.watchlistDeleted": "<strong>{title}</strong> erfolgreich aus der Merkliste entfernt!", "components.Login.back": "Zurück", "components.Login.servertype": "Servertyp", "components.Login.validationHostnameRequired": "Du musst eine gültige IP-Adresse oder einen gültigen Hostnamen angeben", "components.Login.validationPortRequired": "Du musst einen gültigen Port angeben", "components.Login.validationUrlBaseLeadingSlash": "Der URL muss ein Slash vorangestellt sein", "components.Login.validationUrlBaseTrailingSlash": "Die URL-Ba<PERSON> darf nicht auf einem Slash enden", "components.Login.validationUrlTrailingSlash": "Die URL darf nicht auf einem Slash enden", "components.Login.validationservertyperequired": "Bitte wähle einen Servertyp", "components.MovieDetails.addtowatchlist": "Zur Merkliste hinzufügen", "components.MovieDetails.watchlistDeleted": "<strong>{title}</strong> erfolgreich aus der Merkliste entfernt!", "components.MovieDetails.watchlistSuccess": "<strong>{title}</strong> erfolgreich zur Merkliste hinzugefügt!", "components.Selector.canceled": "Abgebrochen", "components.Selector.ended": "<PERSON><PERSON>", "components.Selector.inProduction": "In Produktion", "components.Selector.pilot": "Pilotfolge", "components.Selector.planned": "<PERSON><PERSON><PERSON>", "components.Selector.returningSeries": "Wiederkehrende Serie", "components.Setup.back": "Zurückgehen", "components.Setup.configemby": "<PERSON>by konfigurieren", "components.Setup.configjellyfin": "<PERSON><PERSON><PERSON> kon<PERSON>gu<PERSON>en", "components.Setup.configplex": "Plex konfigurieren", "components.Setup.servertype": "Servertyp auswählen", "components.Setup.signinWithEmby": "Emby Daten eintragen", "components.Setup.subtitle": "<PERSON>g <PERSON>, indem du einen Medienserver auswählst", "components.StatusBadge.seasonnumber": "S{seasonNumber}", "components.TvDetails.addtowatchlist": "Zur Merkliste hinzufügen", "components.TvDetails.removefromwatchlist": "Von der Merkliste entfernen", "components.TvDetails.watchlistError": "Ein Fehler ist aufgetreten. Bitte versuche es erneut.", "components.TvDetails.watchlistSuccess": "<strong>{title}</strong> erfolgreich zur Merkliste hinzugefügt!", "components.UserProfile.UserSettings.UserGeneralSettings.validationemailformat": "Gültige E-Mail Adresse benö<PERSON>gt", "components.Login.hostname": "{mediaServerName} URL", "components.Login.port": "Port", "components.Login.urlBase": "URL-Basis", "components.Login.enablessl": "Benutze SSL", "components.Settings.jellyfinForgotPasswordUrl": "Passwort vergessen URL", "components.Settings.jellyfinSyncFailedAutomaticGroupedFolders": "Eine benutzerdefinierte Authentifizierung mit automatischer Bibliotheksbündelung wird nicht unterstützt", "components.Settings.jellyfinSyncFailedNoLibrariesFound": "<PERSON>s wurden keine Bibliotheken gefunden", "components.Settings.scanbackground": "Der Scanvorgang wird im Hintergrund ausgeführt. Sie können in der Zwischenzeit den Einrichtungsprozess fortsetzen.", "components.Blacklist.blacklistdate": "Datum", "components.PermissionEdit.viewblacklistedItems": "Medien auf der Sperrliste anzeigen.", "components.Settings.SettingsMain.discoverRegion": "Region entdecken", "components.Blacklist.blacklistNotFoundError": "<strong>{title}</strong> ist nicht auf der Sperrliste.", "components.PermissionEdit.manageblacklist": "<PERSON><PERSON><PERSON><PERSON><PERSON> verwalten", "components.Settings.SettingsJobsCache.plex-refresh-token": "Plex Refresh Token", "components.UserProfile.UserSettings.UserGeneralSettings.discoverRegion": "Region entdecken", "i18n.blacklistDuplicateError": "<strong>{title}</strong> wurde bereits auf die Sperrliste gesetzt.", "components.Settings.Notifications.validationWebhookRoleId": "Du musst eine gültige Discord Rollen-ID angeben", "components.Settings.Notifications.webhookRoleIdTip": "Die Rollen ID, die in der Webhook Nachricht erwähnt werden soll. <PERSON><PERSON>, um Erwähnungen zu deaktivieren", "i18n.addToBlacklist": "Zur Sperrliste hinzufügen", "components.PermissionEdit.blacklistedItemsDescription": "Autorisierung zum Sperren von Medien.", "components.Settings.SettingsMain.streamingRegion": "Streaming Region", "i18n.removeFromBlacklistSuccess": "<strong>{title}</strong> wurde er<PERSON><PERSON><PERSON><PERSON><PERSON> von der Sperrliste entfernt.", "components.UserProfile.UserSettings.UserGeneralSettings.streamingRegion": "Streaming Region", "components.UserProfile.UserSettings.UserGeneralSettings.streamingRegionTip": "Streaming Seiten nach regionaler Verfügbarkeit anzeigen", "components.Blacklist.blacklistedby": "{date} durch {user}", "components.Blacklist.mediaName": "Name", "components.Blacklist.mediaTmdbId": "tmdb Id", "components.Blacklist.mediaType": "<PERSON><PERSON>", "component.BlacklistBlock.blacklistdate": "S<PERSON>rda<PERSON>", "component.BlacklistBlock.blacklistedby": "<PERSON><PERSON><PERSON><PERSON> durch", "components.Blacklist.blacklistSettingsDescription": "Medien auf der Sperrliste verwalten.", "components.Blacklist.blacklistsettings": "Sperrlisteneinstellungen", "component.BlacklistModal.blacklisting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Layout.Sidebar.blacklist": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.PermissionEdit.manageblacklistDescription": "Erlaubnis zur Verwaltung von Medien auf der Sperrliste erteilen.", "components.PermissionEdit.blacklistedItems": "Medien sperren.", "components.PermissionEdit.viewblacklistedItemsDescription": "Erlaubnis zum Anzeigen von Medien auf der Sperrliste erteilen.", "components.RequestList.RequestItem.removearr": "<PERSON> {arr} ent<PERSON><PERSON>", "components.RequestList.sortDirection": "Sortierrichtung umschalten", "components.Settings.Notifications.webhookRoleId": "Benachrichtigung Rollen ID", "components.Settings.SettingsJobsCache.usersavatars": "Avatare der Nutzer", "components.Settings.SettingsMain.discoverRegionTip": "Inhalte nach regionaler Verfügbarkeit filtern", "components.Settings.SettingsMain.streamingRegionTip": "Streaming Seiten nach regionaler Verfügbarkeit anzeigen", "components.Settings.apiKey": "API-Schlüssel", "components.Settings.tip": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.discoverRegionTip": "Inhalte nach regionaler Verfügbarkeit filtern", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailureEmail": "Diese E-Mail ist bereits vergeben!", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailureEmailEmpty": "Ein anderer Benutzer hat bereits diesen Benutzernamen. Sie müssen eine E-Mail festlegen", "i18n.blacklist": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.blacklistError": "Etwas ist schief gelaufen, versuche es noch einmal.", "i18n.blacklistSuccess": "<strong>{title}</strong> wurde erfolgreich auf die Sperrliste gesetzt.", "i18n.blacklisted": "<PERSON><PERSON><PERSON><PERSON>", "i18n.removefromBlacklist": "Von der Sperrliste entfernen", "i18n.specials": "Besonderheiten", "components.Settings.SettingsNetwork.trustProxyTip": "Erlaube Jellyseerr die Client-IP-Adressen hinter einem Proxy zu registrieren", "components.DiscoverTvUpcoming.upcomingtv": "Demnächst erscheinende Serien", "components.Login.loginwithapp": "Einloggen bei {appName}", "components.Settings.OverrideRuleModal.rootfolder": "Stammverzeichnis", "components.UserProfile.UserSettings.menuLinkedAccounts": "Verknüpfte Konten", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.deleteFailed": "Verknüpftes Konto kann nicht gelöscht werden.", "components.UserProfile.UserSettings.LinkJellyfinModal.usernameRequired": "<PERSON><PERSON> müssen einen Benutzernamen angeben", "components.Setup.librarieserror": "Validierung fehlgeschlagen. Bitte schalte die Bibliotheken erneut um, um fortzufahren.", "components.Settings.SettingsNetwork.proxyBypassFilterTip": "Verwen<PERSON> ',' als Trennzeichen und '*.' als Platzhalter für Subdomains", "components.Settings.OverrideRuleModal.settingsDescription": "G<PERSON>t an, welche Einstellungen geändert werden, wenn die oben genannten Bedingungen erfüllt sind.", "components.Settings.SettingsUsers.mediaServerLogin": "Aktiviere {mediaServerName} Anmeldung", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.plexErrorExists": "<PERSON>ses <PERSON> ist bereits mit einem Plex Benutzer verknüpft", "components.UserProfile.UserSettings.LinkJellyfinModal.description": "<PERSON><PERSON><PERSON> Sie Ihre {mediaServerName}-Anmeldeinformationen ein, um Ihr Konto mit {applicationName} zu verknüpfen.", "components.Settings.SettingsNetwork.networkDisclaimer": "Anstelle dieser Einstellungen sollten Netzwerkparameter aus Ihrem Container/System verwendet werden. Weitere Informationen finden Sie in den {docs}.", "components.Selector.searchUsers": "Benutzer auswählen…", "components.Settings.overrideRules": "Override-Regeln", "components.Settings.Notifications.messageThreadId": "Thread-/Themen-ID", "components.Settings.OverrideRuleModal.conditions": "Bedingungen", "components.Settings.OverrideRuleTile.settings": "Einstellungen", "components.Login.noadminerror": "<PERSON><PERSON><PERSON><PERSON> auf dem Server gefunden.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.plexErrorUnauthorized": "<PERSON><PERSON> Ihren Anmeldeinformationen kann keine Verbindung zu Plex hergestellt werden", "components.UserProfile.UserSettings.LinkJellyfinModal.errorUnknown": "Ein unbekannter Fehler ist aufgetreten", "components.Settings.addrule": "Neue Override-Regel", "components.Login.orsigninwith": "Oder anmelden mit", "components.Settings.OverrideRuleModal.create": "Regel erstellen", "components.Settings.OverrideRuleModal.createrule": "Neue Override-Regel", "components.Settings.OverrideRuleModal.editrule": "Bearbeite Override-Regel", "components.Settings.OverrideRuleModal.genres": "Genre", "components.Settings.OverrideRuleModal.keywords": "Schlüsselwörter", "components.Settings.OverrideRuleModal.languages": "<PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleModal.notagoptions": "Keine Tags.", "components.Settings.OverrideRuleModal.ruleCreated": "Override-<PERSON>el erfolgreich erstellt!", "components.Settings.OverrideRuleModal.ruleUpdated": "Override-Regel erfolgreich bearbeitet!", "components.Settings.OverrideRuleModal.selectRootFolder": "Stammverzeichnis wählen", "components.Settings.OverrideRuleModal.selectService": "Service auswählen", "components.Settings.OverrideRuleModal.selecttags": "Tags auswählen", "components.Settings.OverrideRuleTile.users": "<PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleTile.tags": "Tags", "components.Settings.OverrideRuleTile.rootfolder": "Stammverzeichnis", "components.Settings.OverrideRuleTile.language": "<PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleTile.keywords": "Schlüsselwörter", "components.Settings.OverrideRuleTile.genre": "Genre", "components.Settings.OverrideRuleTile.conditions": "Bedingungen", "components.Settings.OverrideRuleModal.users": "<PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleModal.tags": "Tags", "components.Settings.OverrideRuleModal.settings": "Einstellungen", "components.Settings.OverrideRuleModal.serviceDescription": "Wende diese Regel auf den ausgewählten Dienst an.", "components.Settings.OverrideRuleModal.service": "<PERSON><PERSON>", "components.Settings.SettingsMain.enableSpecialEpisodes": "<PERSON><PERSON><PERSON> zu Spezial-<PERSON>n zulassen", "components.Settings.SettingsNetwork.advancedNetworkSettings": "Erweiterte Netzwerkeinstellungen", "components.Settings.SettingsNetwork.csrfProtectionHoverTip": "Aktivieren Sie diese Einstellung NICHT, wenn Sie nicht wissen, was <PERSON><PERSON> tun!", "components.Settings.SettingsNetwork.docs": "Dokumentation/Hilfe", "components.Settings.SettingsNetwork.networksettings": "Netzwerkeinstellungen", "components.Settings.SettingsNetwork.networksettingsDescription": "Konfiguriere die Netzwerkeinstellungen deiner Jellyseerr-Instanz.", "components.Settings.SettingsNetwork.toastSettingsSuccess": "Einstellungen erfolgreich gespeichert!", "components.Settings.SettingsNetwork.trustProxy": "Aktiviere Proxy-Unterstützung", "components.Settings.SettingsNetwork.validationProxyPort": "Sie müssen einen gültigen Port angeben", "components.Settings.SettingsUsers.atLeastOneAuth": "Es muss mindestens eine Authentifizierungsmethode ausgewählt werden.", "components.Settings.SettingsUsers.loginMethods": "Anmeldemethoden", "components.Settings.SettingsUsers.loginMethodsTip": "Anmeldemethoden für Benutzer konfigurieren.", "components.Settings.SettingsUsers.mediaServerLoginTip": "<PERSON>utz<PERSON> erlauben, sich mit ihrem {mediaServerName}-<PERSON><PERSON> anzu<PERSON>den", "components.Settings.SettingsNetwork.csrfProtection": "Aktiviere CSRF-Schutz", "components.Settings.SettingsNetwork.csrfProtectionTip": "Externen API-Zugriff auf schreibgeschützt setzen (erfordert HTTPS)", "components.Settings.SettingsNetwork.forceIpv4First": "Erzwinge IPv4-Auflösung zuerst", "components.Settings.SettingsNetwork.forceIpv4FirstTip": "<PERSON><PERSON><PERSON><PERSON>, dass Je<PERSON>seerr zuerst IPv4-<PERSON><PERSON><PERSON> anstelle von IPv6 auflöst", "components.Settings.SettingsNetwork.toastSettingsFailure": "<PERSON>im <PERSON>ichern der Einstellungen ist ein Fehler aufgetreten.", "components.Settings.SettingsNetwork.proxyUser": "Proxy <PERSON>", "components.Settings.SettingsNetwork.proxySsl": "Benutze SSL für Proxy", "components.Settings.SettingsNetwork.proxyPort": "Proxy Port", "components.Settings.SettingsNetwork.proxyPassword": "Proxy Passwort", "components.Settings.SettingsNetwork.proxyHostname": "Proxy Hostname", "components.Settings.SettingsNetwork.proxyEnabled": "HTTP(S) Proxy", "components.Settings.SettingsNetwork.proxyBypassLocalAddresses": "Proxy für lokale Adressen umgehen", "components.Settings.SettingsNetwork.proxyBypassFilter": "Vom Proxy ignorierte Adressen", "components.Settings.SettingsNetwork.network": "Netzwerk", "components.Settings.menuNetwork": "Netzwerk", "components.UserProfile.UserSettings.LinkJellyfinModal.username": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.LinkJellyfinModal.title": "{mediaServerName}-<PERSON>nto verknüpfen", "components.UserProfile.UserSettings.LinkJellyfinModal.saving": "Hinzufügen…", "components.UserProfile.UserSettings.LinkJellyfinModal.passwordRequired": "<PERSON>e müssen ein Passwort angeben", "components.UserProfile.UserSettings.LinkJellyfinModal.password": "Passwort", "components.UserProfile.UserSettings.LinkJellyfinModal.errorUnauthorized": "Mit Ihren Anmeldeinformationen kann keine Verbindung zu {mediaServerName} hergestellt werden", "components.UserProfile.UserSettings.LinkJellyfinModal.errorExists": "<PERSON><PERSON> ist bereits mit einem {applicationName}-Benutzer verknüpft", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.noPermissionDescription": "<PERSON>e sind nicht berechtigt, die verknüpften Konten dieses Benutzers zu ändern.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.noLinkedAccounts": "Sie haben keine externen Konten mit Ihrem Konto verknüpft.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.linkedAccountsHint": "Diese externen Konten sind mit Ihrem {applicationName}-Konto verknüpft.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.linkedAccounts": "Verknüpfte Konten", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.errorUnknown": "Ein unbekannter Fehler ist aufgetreten", "components.Settings.overrideRulesDescription": "Überschreibungsregeln ermöglichen es dir, Eigenschaften festzulegen, die ersetzt werden, wenn eine Anforderung der Regel entspricht.", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramMessageThreadId": "Die Thread/Themen ID muss eine positive volle Zahl sein", "components.UserProfile.UserSettings.UserNotificationSettings.telegramMessageThreadId": "Thread/Themen ID", "components.UserProfile.UserSettings.UserNotificationSettings.telegramMessageThreadIdTip": "Wenn in deinem Gruppenchat Themen aktiviert sind, kannst du hier die ID des Threads/Themas angeben", "components.Settings.Notifications.validationMessageThreadId": "Die Thread-/Themen-<PERSON> muss eine positive volle Zahl sein", "components.Settings.Notifications.messageThreadIdTip": "Wenn in deinem Gruppenchat Themen aktiviert sind, kannst du hier die ID des Threads/Themas angeben", "components.Settings.OverrideRuleModal.qualityprofile": "Qualitätsprofil", "components.Settings.OverrideRuleModal.selectQualityProfile": "Qualitätsprofil auswählen", "components.Settings.OverrideRuleTile.qualityprofile": "Qualitätsprofil", "components.Settings.OverrideRuleModal.conditionsDescription": "Gib Bedingungen an, bevor die Parameteränderungen angewendet werden. <PERSON><PERSON>ld muss validiert werden, damit die Regeln angewendet werden (UND-Betrieb). Ein Feld gilt als verifiziert, wenn eine dieser Eigenschaften übereinstimmt (ODER Betrieb).", "components.UserProfile.UserSettings.LinkJellyfinModal.save": "Link"}