{"components.Discover.DiscoverTv.sortTmdbRatingAsc": "TMDB Рейтинг Възходящ", "components.Discover.CreateSlider.editsuccess": "Редактиран плъзгач и запазени настройки за откриване.", "components.CollectionDetails.numberofmovies": "{count} Филми", "components.Discover.CreateSlider.slidernameplaceholder": "Име на плъзгач", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "Възходяща дата на първо излъчване", "components.AppDataWarning.dockerVolumeMissingDescription": "Монтирането на дялът с път <code>{appDataPath}</code> не е конфигуриран правилно. Всички данни ще бъдат изчистени, когато контейнерът бъде спрян или рестартиран.", "components.Discover.DiscoverTvKeyword.keywordSeries": "{keywordTitle} Сериали", "components.Discover.DiscoverMovies.sortPopularityDesc": "Популярност низходяща", "components.AirDateBadge.airsrelative": "Излъчва се {relativeTime}", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "TMDB Рейтинг Низходящ", "components.CollectionDetails.overview": "Преглед", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {# Активен филтър} other {# Активни филтри}}", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "TMDB Рейтинг Възходящ", "components.Discover.DiscoverTvGenre.genreSeries": "{genre} Сериали", "components.AirDateBadge.airedrelative": "Излъчен {relativeTime}", "components.Discover.CreateSlider.searchStudios": "Търсете студия…", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "Дата на издаване в низходящ ред", "components.Discover.CreateSlider.providetmdbnetwork": "Предоставете TMDB ID на мрежа", "components.Discover.CreateSlider.addfail": "Провали се създаването на нов плъзгач.", "components.CollectionDetails.requestcollection": "Заявка на колекция", "components.Discover.DiscoverMovieGenre.genreMovies": "{genre} Филми", "components.Discover.DiscoverMovieLanguage.languageMovies": "{language} Филми", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {# Активен филтър} other {# Активни филтри}}", "components.Discover.DiscoverTvLanguage.languageSeries": "{language} Сериали", "components.Discover.DiscoverMovies.sortPopularityAsc": "Популярност възходяща", "components.Discover.CreateSlider.needresults": "Трябва да имате поне 1 резултат.", "components.Discover.CreateSlider.addcustomslider": "Създай персонализиран плъзгач", "components.Discover.DiscoverTv.sortPopularityAsc": "Популярност възходяща", "components.Discover.CreateSlider.editSlider": "Редактир<PERSON>й плъзгач", "components.Discover.DiscoverTv.sortTitleAsc": "Заглавие (А-Я) Възходящо", "components.Discover.CreateSlider.validationDatarequired": "Трябва да предоставите стойност на данните.", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "Низходяща дата на първо излъчване", "components.Discover.DiscoverTv.discovertv": "Сериали", "components.Discover.DiscoverSliderEdit.deletefail": "Провали се изтриване на плъзгач.", "components.Discover.CreateSlider.providetmdbstudio": "Предоставете TMDB ID на Студио", "components.Discover.DiscoverMovies.sortTitleDesc": "Заглавие (А-Я) Низходящо", "components.Discover.DiscoverStudio.studioMovies": "{studio} Филми", "components.Discover.DiscoverTv.sortPopularityDesc": "Популярност низходяща", "components.Discover.CreateSlider.searchGenres": "Търсете жанрове…", "components.Discover.CreateSlider.editfail": "Провали се редакцията на слайдера.", "components.Discover.CreateSlider.starttyping": "Започнете въвеждане на търсене.", "components.Discover.DiscoverSliderEdit.enable": "Превключване на видимостта", "components.Discover.CreateSlider.addSlider": "Добави плъзгач", "components.CollectionDetails.requestcollection4k": "Заявка на колекция в 4К", "components.Discover.CreateSlider.providetmdbsearch": "Въведете заявка за търсене", "components.Discover.DiscoverNetwork.networkSeries": "{network} Сериали", "components.Discover.CreateSlider.providetmdbkeywordid": "Предоставете ID на TMDB ключова дума", "components.Discover.DiscoverMovieKeyword.keywordMovies": "{keywordTitle} Филми", "components.Discover.CreateSlider.validationTitlerequired": "Трябва да предоставите заглавие.", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "Дата на издаване Възходящ ред", "components.Discover.CreateSlider.nooptions": "Няма резултати.", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "TMDB Рейтинг Низходящ", "components.Discover.CreateSlider.searchKeywords": "Търсете ключови думи…", "components.Discover.CreateSlider.addsuccess": "Създаден е нов плъзгач и са запазени настройките за Открийте.", "components.Discover.DiscoverSliderEdit.deletesuccess": "Успешно изтрит плъзгач.", "components.Discover.DiscoverMovies.discovermovies": "Филми", "components.Discover.DiscoverMovies.sortTitleAsc": "Заглавие (А-Я) Възходящо", "components.Discover.CreateSlider.providetmdbgenreid": "Предоставете TMDB ID на жанра", "components.Discover.DiscoverTv.sortTitleDesc": "Заглавие (А-Я) Низходящо", "components.Discover.DiscoverSliderEdit.remove": "Премахване", "components.Layout.Sidebar.browsemovies": "Филми", "components.IssueList.IssueItem.issuetype": "Тип", "components.IssueList.IssueItem.unknownissuetype": "Неизвестен", "components.IssueList.IssueItem.issuestatus": "Статус", "components.Login.signinheader": "Впишете се, за да продължите", "components.IssueDetails.allseasons": "Всички сезони", "components.IssueDetails.IssueDescription.edit": "Редактирай описание", "components.IssueDetails.IssueComment.areyousuredelete": "Сигурни ли сте, че искате да изтриете този коментар?", "components.IssueList.IssueItem.problemepisode": "Засегнат епизод", "components.IssueDetails.toasteditdescriptionfailed": "Нещо се обърка при редактиране на описанието на проблема.", "components.Discover.updatesuccess": "Актуализирани настройки за персонализиране на Откриване.", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {Сезон} other {Сезони}}", "components.IssueDetails.problemepisode": "Засегнат епизод", "components.IssueModal.CreateIssueModal.problemseason": "Засегнат сезон", "components.Layout.VersionStatus.streamdevelop": "Overseerr Развоен", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Заявки за филми", "components.IssueDetails.issuepagetitle": "Проблем", "components.IssueList.showallissues": "Покажи всички проблеми", "components.IssueDetails.toasteditdescriptionsuccess": "Описанието на проблема е редактирано успешно!", "components.Layout.UserDropdown.signout": "Отписване", "components.IssueModal.CreateIssueModal.reportissue": "Докладвайте за проблем", "components.Layout.UserDropdown.myprofile": "Профил", "components.Login.email": "<PERSON><PERSON><PERSON><PERSON><PERSON> адрес", "components.IssueDetails.IssueDescription.deleteissue": "Изтриване на проблем", "components.IssueModal.CreateIssueModal.submitissue": "Изпратете проблем", "components.IssueDetails.episode": "Епизод {episodeNumber}", "components.IssueDetails.unknownissuetype": "Неизвестен", "components.Layout.Sidebar.issues": "Проблеми", "components.IssueList.IssueItem.openeduserdate": "{date} от {user}", "components.Login.signinwithoverseerr": "Използвайте своя акаунт {applicationTitle}", "components.Discover.updatefailed": "Нещо се обърка при актуализирането на настройките за Откриване.", "components.Layout.Sidebar.requests": "Заявки", "components.Layout.Sidebar.settings": "Настройки", "components.IssueList.IssueItem.opened": "Отворен", "components.IssueDetails.problemseason": "Засегнат сезон", "components.Layout.UserDropdown.requests": "Заявки", "components.Login.loginerror": "Нещо се обърка при опит за влизане.", "components.IssueList.sortAdded": "Последни", "components.IssueDetails.IssueDescription.description": "Описание", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "Заявки за сериали", "components.LanguageSelector.languageServerDefault": "По подразбиране ({language})", "components.IssueModal.issueSubtitles": "Субтитри", "components.IssueModal.CreateIssueModal.problemepisode": "Засегнат епизод", "components.IssueDetails.toastissuedeletefailed": "Нещо се обърка при изтриването на проблема.", "components.IssueDetails.openin4karr": "Отвори в 4K {arr}", "components.IssueDetails.play4konplex": "Пусни в 4K в Plex", "components.IssueModal.CreateIssueModal.toastFailedCreate": "Нещо се обърка при изпращането на проблема.", "components.IssueDetails.IssueComment.validationComment": "Трябва да въведете съобщение", "components.IssueDetails.IssueComment.postedbyedited": "Публикуван {relativeTime} от {username} (Редактира<PERSON>)", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "Докладът за проблем <strong>{title}</strong> е изпратен успешно!", "components.IssueDetails.IssueComment.delete": "Изтрий коментар", "components.IssueDetails.openedby": "#{issueId} е оворен {relativeTime} от {username}", "components.IssueDetails.nocomments": "Няма коментари.", "components.IssueDetails.allepisodes": "Всички епизоди", "components.Login.signingin": "Вписване…", "components.Layout.SearchInput.searchPlaceholder": "Търсене на Филми§ТВ", "components.IssueDetails.season": "Сезон {seasonNumber}", "components.IssueDetails.IssueComment.postedby": "Публикуван {relativeTime} от {username}", "components.IssueDetails.reopenissueandcomment": "Отвори отново с коментар", "components.IssueDetails.toaststatusupdatefailed": "Нещо се обърка при актуализиране на състоянието на проблема.", "components.IssueList.issues": "Проблеми", "components.IssueModal.CreateIssueModal.validationMessageRequired": "Трябва да предоставите описание", "components.IssueDetails.reopenissue": "Отвори отново проблема", "components.IssueDetails.closeissueandcomment": "Приключване с коментар", "components.IssueDetails.deleteissue": "Изтрий проблем", "components.IssueModal.CreateIssueModal.allseasons": "Всички сезони", "components.IssueModal.CreateIssueModal.season": "Сезон {seasonNumber}", "components.DownloadBlock.formattedTitle": "{title}: <PERSON><PERSON><PERSON><PERSON><PERSON> {seasonNumber} Епизод {episodeNumber}", "components.IssueDetails.IssueComment.edit": "Редакти<PERSON><PERSON>й коментар", "components.IssueModal.issueVideo": "Видео", "components.Layout.Sidebar.users": "Потребители", "components.Layout.UserDropdown.settings": "Настройки", "components.Login.password": "Парола", "components.IssueModal.CreateIssueModal.toastviewissue": "Виж проблема", "components.Layout.VersionStatus.outofdate": "Оста<PERSON><PERSON>л", "components.IssueList.IssueItem.viewissue": "Виж проблема", "components.IssueDetails.comments": "Коментари", "components.Login.forgotpassword": "Забравена парола?", "components.Layout.Sidebar.dashboard": "Открийте", "components.IssueModal.CreateIssueModal.extras": "Екстри", "components.IssueDetails.toastissuedeleted": "Проблемът е изтрит успешно!", "components.DownloadBlock.estimatedtime": "Приблизително {time}", "components.IssueModal.CreateIssueModal.whatswrong": "Какво не е наред?", "components.IssueDetails.toaststatusupdated": "Състоянието на проблема е актуализирано успешно!", "components.Login.signin": "Впиши се", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {Епизод} other {Епизоди}}", "components.IssueDetails.lastupdated": "Последно опреснен", "components.IssueModal.CreateIssueModal.allepisodes": "Всички епизоди", "components.Layout.LanguagePicker.displaylanguage": "Език на дисплея", "components.Layout.Sidebar.browsetv": "Сериали", "components.IssueDetails.commentplaceholder": "Добави коментар …", "components.IssueList.sortModified": "Последно редактирани", "components.IssueDetails.openinarr": "Отвори в {arr}", "components.IssueModal.CreateIssueModal.providedetail": "Моля, дайте подробно обяснение на проблема, който сте срещнали.", "components.IssueDetails.closeissue": "Приключване на проблема", "components.LanguageSelector.originalLanguageDefault": "Всички езици", "components.IssueModal.issueOther": "Друго", "components.IssueModal.CreateIssueModal.episode": "Епизод {episodeNumber}", "components.IssueDetails.issuetype": "Тип", "components.IssueDetails.leavecomment": "Коментар", "components.IssueDetails.playonplex": "Пусни в Plex", "components.Layout.VersionStatus.streamstable": "Overseerr Стабилен", "components.IssueModal.issueAudio": "Аудио", "components.IssueDetails.deleteissueconfirm": "Сигурни ли сте, че искате да изтриете този проблем?", "components.PermissionEdit.request": "Заявка", "components.Discover.moviegenres": "Филмови жанрове", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.RequestList.RequestItem.modified": "Изменен", "components.MovieDetails.digitalrelease": "Цифрово издание", "components.Settings.RadarrModal.announced": "Обявен", "components.Settings.SettingsAbout.Releases.latestversion": "Най-нов", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {No} други {<strong>#</strong>}} {type} {remaining, plural, one {заявка} other {заявки}} оставащи", "components.PersonDetails.alsoknownas": "Също известен като: {names}", "components.Settings.Notifications.toastEmailTestSuccess": "Известието за тест чрез имейл е изпратено!", "components.Discover.FilterSlideover.studio": "Студио", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "Настройките за известяване на Gotify не успяха да бъдат запазени.", "components.RequestModal.requestseries4ktitle": "Заявка на сериали в 4К", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "Трябва да предоставите токен за достъп", "components.Settings.SettingsAbout.Releases.currentversion": "Текущ", "components.Discover.PlexWatchlistSlider.emptywatchlist": "Мултимедията, добавена към вашия <PlexWatchlistSupportLink>списък за гледане в Plex</PlexWatchlistSupportLink>, ще се появи тук.", "components.Settings.Notifications.toastTelegramTestSuccess": "Известието за тест към Telegram е изпратено!", "components.Settings.SettingsJobsCache.cachemisses": "Пропуски", "components.NotificationTypeSelector.usermediaavailableDescription": "Получавайте известие, когато заявките ви за медии станат налични.", "components.Discover.RecentlyAddedSlider.recentlyAdded": "Наскоро добавен", "components.Settings.Notifications.NotificationsPushover.sound": "Звук за известяване", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "Настройките за известяване чрез Webhook не успяха да бъдат запазени.", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "Трябва да предоставите валиден URL адрес", "components.Settings.Notifications.pgpPrivateKey": "Ча<PERSON><PERSON><PERSON>н ключ за PGP", "components.PermissionEdit.requestTvDescription": "Дайте разрешение за изпращане на заявки за не-4K сериали.", "components.PermissionEdit.autoapproveSeriesDescription": "Гарантиране на автоматично одобрение за заявки на 4K филми.", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "Трябва да предоставите валиден токен за приложение", "components.Settings.RadarrModal.baseUrl": "Базов URL адрес", "components.Discover.FilterSlideover.keywords": "Ключови думи", "components.Discover.tvgenres": "Жанрове сериали", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "Webhook URL", "components.RequestModal.SearchByNameModal.nomatches": "Не успяхме да намерим съвпадение за този сериал.", "components.PermissionEdit.autorequest": "Автоматична заявка", "components.Settings.Notifications.encryptionOpportunisticTls": "Винаги използвайте STARTTLS", "components.Discover.FilterSlideover.ratingText": "Оценки между {minValue} и {maxValue}", "components.PermissionEdit.autoapproveSeries": "Автоматично одобряване на сериали", "components.RequestButton.approverequests": "Одобряване {requestCount, plural, one {заявка} other {{requestCount} заявки}}", "components.PersonDetails.crewmember": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestButton.requestmore4k": "Заявете повече в 4К", "components.PersonDetails.ascharacter": "като {character}", "components.Settings.Notifications.validationChatIdRequired": "Трябва да предоставите валиден chat ID", "components.ManageSlideOver.downloadstatus": "Изтегляния", "components.Settings.Notifications.enableMentions": "Активиране на споменаванията", "components.Settings.Notifications.validationPgpPassword": "Трябва да предоставите валидна PGP парола", "components.Settings.SettingsAbout.timezone": "Време зона", "components.Settings.RadarrModal.toastRadarrTestSuccess": "Връзката към Radarr е установена успешно!", "components.Discover.resetwarning": "Нулирайте всички плъзгачи по подразбиране. Това също ще изтрие всички персонализирани плъзгачи!", "components.RequestList.RequestItem.editrequest": "Редакция на заявка", "components.Settings.RadarrModal.ssl": "Използвай SSL", "components.MovieDetails.showless": "Покажи по-малко", "components.PermissionEdit.manageissuesDescription": "Дайте разрешение за управление на медийни проблеми.", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "Активиране на агент", "components.RequestModal.AdvancedRequester.qualityprofile": "Профил качество", "components.NotificationTypeSelector.issuecommentDescription": "Изпращайте известия, когато проблемите получат нови коментари.", "components.Settings.RadarrModal.testFirstTags": "Тествайте връзката за зареждане на тагове", "components.Settings.Notifications.sendSilently": "Изпрати безшумно", "components.ResetPassword.validationpasswordmatch": "Паролите трябва да съвпадат", "components.RequestList.requests": "Заявки", "components.RequestModal.QuotaDisplay.season": "сезон", "components.PermissionEdit.managerequests": "Управление на заявки", "components.ResetPassword.emailresetlink": "Връзка за възстановяване на имейл", "components.NotificationTypeSelector.userissuereopenedDescription": "Получавайте известие, когато проблемите, за които сте докладвали, бъдат отворени отново.", "components.NotificationTypeSelector.usermediafailedDescription": "Получавайте известия, когато медийните заявки не успеят да бъдат добавени към Radarr или Sonarr.", "components.RequestButton.approverequest4k": "Одобряване на 4K заявка", "components.PermissionEdit.manageissues": "Управление на проблемите", "components.Settings.Notifications.validationBotAPIRequired": "Трябва да предоставите токен за оторизация на бота", "components.RequestCard.tmdbid": "TMDB ID", "components.Settings.Notifications.authUser": "SMTP потребител", "components.PermissionEdit.viewrequests": "Преглед на заявките", "components.RequestCard.failedretry": "Нещо се обърка при повторен опит за заявка.", "components.PermissionEdit.requestMovies": "Заявка за филми", "components.RequestModal.QuotaDisplay.quotaLinkUser": "Можете да прегледате обобщение на ограниченията на заявки от потребителя на неговата <ProfileLink>профилна страница</ProfileLink>.", "components.Discover.StudioSlider.studios": "Студия", "components.ManageSlideOver.manageModalRequests": "Заявки", "components.NotificationTypeSelector.issuecreatedDescription": "Изпращайте известия при докладване на проблеми.", "components.NotificationTypeSelector.mediaavailableDescription": "Изпращайте известия, когато медийните заявки станат налични.", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "Вашият базиран на потребител или устройство <LunaSeaLink>URL адрес за webhook за известия</LunaSeaLink>", "components.RequestModal.requestmovie4ktitle": "Заявете филм в 4K", "components.RequestModal.requestSuccess": "<strong>{title}</strong> е заявен успешно!", "components.Settings.Notifications.webhookUrlTip": "Създайте <DiscordWebhookLink>интегриране на webhook</DiscordWebhookLink> във вашия сървър", "components.Settings.RadarrModal.selecttags": "Изберете етикети", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "Неуспешно изпращане на тестово известие към Pushbullet.", "components.Discover.tmdbmoviekeyword": "TMDB Ключови думи във филма", "components.RequestButton.declinerequest": "Отказ на заявка", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicationRegistrationLink>Регистриране на приложение</ApplicationRegistrationLink> за използване с Overseerr", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Известието към Pushover тест е изпратено!", "components.Settings.Notifications.emailsender": "Адрес на изпращача", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "Трябва да предоставите токен за приложение", "components.Settings.SettingsAbout.Releases.viewchangelog": "Преглед на Дневник на промените", "components.RequestCard.seasons": "{seasonCount, plural, one {Сезон} other {Сезони}}", "components.Settings.RadarrModal.released": "Излъчен", "components.ManageSlideOver.movie": "филм", "components.Discover.stopediting": "Спрете редактирането", "components.RequestCard.declinerequest": "Отказ на заявка", "components.Settings.RadarrModal.port": "Порт", "components.PermissionEdit.request4kMoviesDescription": "Дайте разрешение за изпращане на заявки за 4K филми.", "components.RequestModal.AdvancedRequester.advancedoptions": "Разширено", "components.Discover.resetsuccess": "Успешно нулиране на настройките за персонализиране на откриването.", "components.Settings.RadarrModal.minimumAvailability": "Мини<PERSON><PERSON><PERSON>на наличност", "components.Settings.Notifications.agentenabled": "Активиране на агент", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "Неуспешно изпращане на тестово известие към LunaSea.", "components.Settings.SettingsAbout.Releases.releases": "Издания", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "Активиране на агент", "components.Settings.RadarrModal.validationApiKeyRequired": "Трябва да предоставите API ключ", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "Трябва да изберете минимална наличност", "components.RequestModal.requestseasons": "Заявете {seasonCount} {seasonCount, plural, one {сезон} other {сезони}}", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "Изпраща се тестово известие към Slack…", "components.RequestButton.viewrequest": "Преглед на заявката", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "Трябва да предоставите валиден URL адрес", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Настройките за известяване към Slack са запазени успешно!", "components.Settings.RadarrModal.validationProfileRequired": "Трябва да изберете профил за качество", "components.Settings.SettingsAbout.totalrequests": "Общо заявки", "components.ManageSlideOver.manageModalAdvanced": "Разширено", "components.Discover.customizediscover": "Персонализирайте Откриване", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Настройките за известяване към Gotify са запазени успешно!", "components.RequestModal.approve": "Одобряване на заявката", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Известието за тест към Gotify е изпратено!", "components.Discover.emptywatchlist": "Мултимедията, добавена към вашия <PlexWatchlistSupportLink>списък за гледане в Plex</PlexWatchlistSupportLink>, ще се появи тук.", "components.MovieDetails.runtime": "{minutes} минути", "components.Settings.Notifications.encryption": "Метод на криптиране", "components.Discover.populartv": "Популярни сериали", "components.Settings.SettingsJobsCache.cachekeys": "Общо ключове", "components.RequestModal.AdvancedRequester.notagoptions": "Без етикети.", "components.Settings.RadarrModal.testFirstRootFolders": "Тествайте връзката за зареждане на основните папки", "components.RequestModal.pendingapproval": "Вашата заявка чака одобрение.", "components.Settings.Notifications.NotificationsSlack.validationTypes": "Трябва да изберете поне един тип известие", "components.RequestBlock.rootfolder": "Основна папка", "components.Settings.SettingsAbout.preferredmethod": "Предпочитан", "components.Settings.RadarrModal.defaultserver": "Сървър по подразбиране", "components.MovieDetails.watchtrailer": "Гледайте трейлър", "components.NotificationTypeSelector.issuecomment": "Коментар на проблема", "components.RequestBlock.seasons": "{seasonCount, plural, one {Сезон} other {Сезони}}", "components.Settings.RadarrModal.selectMinimumAvailability": "Изберете минимална наличност", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "Настройките за известяване към LunaSea са запазени успешно!", "components.Selector.showmore": "Покажи повече", "components.Settings.RadarrModal.selectRootFolder": "Изберете главна папка", "components.RequestList.RequestItem.modifieduserdate": "{date} от {user}", "components.Settings.RadarrModal.rootfolder": "Основна папка", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "Изпраща се известие за тест чрез Web push…", "components.NotificationTypeSelector.mediaapprovedDescription": "Изпращайте известия, когато медийните заявки са одобрени ръчно.", "components.Settings.RadarrModal.loadingTags": "Етикетите се зареждат…", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "Създайте интеграция на <WebhookLink>входен Webhook</WebhookLink>", "components.PermissionEdit.autoapprove4k": "Автоматично одобрение 4К", "components.PermissionEdit.autoapproveMoviesDescription": "Гарантирано автоматично одобрение за заявки за не-4K филми.", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "Трябва да предоставите валиден потребителски или групов ключ", "components.Settings.SettingsAbout.Releases.versionChangelog": "{version} Дневник на промените", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "Изисква се само ако не използвате профила <code>по подразбиране</code>", "components.ManageSlideOver.manageModalMedia": "Медия", "components.NotificationTypeSelector.issueresolved": "Проблемът е решен", "components.MovieDetails.originaltitle": "Оригинално заглавие", "components.Discover.trending": "Тендеция", "components.RequestButton.declinerequests": "Decline {requestCount, plural, one {Заявка} other {{requestCount} Заявки}}", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "Създайте токен от вашите <PushbulletSettingsLink>Настройки на акаунта</PushbulletSettingsLink>", "components.MovieDetails.rtcriticsscore": "Rotten Tomatoes Tomatometer", "components.PermissionEdit.requestMoviesDescription": "Дайте разрешение за изпращане на заявки за не-4K филми.", "components.Settings.Notifications.toastDiscordTestSending": "Изпраща се тестово известие към Discord…", "components.RequestModal.AdvancedRequester.requestas": "Заявка като", "components.Settings.SettingsJobsCache.cachehits": "Хитове", "components.Settings.RadarrModal.inCinemas": "В кината", "components.Settings.Notifications.toastDiscordTestFailed": "Неуспешно изпращане на тестово известие чрез Telegram.", "components.ResetPassword.resetpasswordsuccessmessage": "Успешно нулиране на паролата!", "components.RequestModal.AdvancedRequester.animenote": "* Този сериал е аниме.", "components.RequestBlock.requestoverrides": "Охвърляне на заявката", "components.RequestCard.mediaerror": "{mediaType} не е намерен", "components.RequestModal.selectmovies": "Изберете филм(и)", "components.RequestModal.requestApproved": "Заявката за <strong>{title}</strong> е одобрена!", "components.Settings.RadarrModal.testFirstQualityProfiles": "Тествайте връзката, за да заредите профилите за качество", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "Изпраща се тестово известие към LunaSea…", "components.QuotaSelector.unlimited": "Неограничен", "components.ResetPassword.validationpasswordminchars": "Паролата е твърде кратка; трябва да съдържа минимум 8 знака", "components.Settings.RadarrModal.syncEnabled": "Активирайте сканирането", "components.RequestModal.numberofepisodes": "# брой епизоди", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "Настройките за известяване към Pushover не успяха да бъдат запазени.", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "Настройките за известяване чрез Web push са запазени успешно!", "components.MovieDetails.originallanguage": "Ориг<PERSON><PERSON><PERSON>ен език", "components.PermissionEdit.request4kTv": "Заявете 4K сериал", "components.Discover.FilterSlideover.clearfilters": "Изчистване на активните филтри", "components.RequestModal.edit": "Редакция на заявка", "components.RequestBlock.profilechanged": "Профил качество", "components.Settings.RadarrModal.create4kradarr": "Добавяне на нов 4K Radarr сървър", "components.Settings.Notifications.senderName": "Име на изпращача", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "Трябва да предоставите валиден URL адрес", "components.PermissionEdit.autoapprove4kMovies": "Автоматично одобряване на 4К филми", "components.ManageSlideOver.playedby": "Изигран от", "components.Settings.RadarrModal.default4kserver": "4K сървър по подразбиране", "components.Settings.RadarrModal.validationHostnameRequired": "Трябва да предоставите валидно име на хост или IP адрес", "components.Discover.tmdbtvkeyword": "TMDB Ключови думи в сериали", "components.Settings.SettingsAbout.documentation": "Документация", "components.RequestModal.season": "Сезон", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "Неуспешно изпращане на тестово известие чрез Web push.", "components.Discover.tmdbnetwork": "TMDB Мрежи", "components.ManageSlideOver.mark4kavailable": "Маркирайте като налично в 4K", "components.Settings.SettingsAbout.about": "Относно", "components.ManageSlideOver.tvshow": "серии", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "За да получава уеб насочени известия, Overseerr трябва да се работи през HTTPS.", "components.MovieDetails.cast": "В ролите", "components.PermissionEdit.viewissues": "Преглед на проблемите", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "Webhook URL", "components.NotificationTypeSelector.mediaautorequestedDescription": "Получавайте известия, когато автоматично се изпращат заявки за елементи от вашия списък за гледане в Plex.", "components.Discover.MovieGenreSlider.moviegenres": "Филмови жанрове", "components.PermissionEdit.viewrecent": "Преглед на наскоро добавените", "components.Discover.networks": "Мр<PERSON><PERSON><PERSON>", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "URL адресът не трябва да завършва с наклонена черта в края", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "Трябва да изберете поне един тип известие", "components.MovieDetails.budget": "<PERSON>ю<PERSON><PERSON><PERSON><PERSON>", "components.RequestList.showallrequests": "Покажи всички заявки", "components.Settings.Notifications.validationTypes": "Трябва да изберете поне един тип известие", "components.Selector.searchGenres": "Изберете жанрове…", "components.Settings.RadarrModal.servername": "Име на сървъра", "components.PermissionEdit.autoapprove4kDescription": "Гарантирано автоматично одобрение за заявки за 4K медии.", "components.RequestModal.requestmovies": "Заявка {count} {count, plural, one {филм} other {филми}}", "components.Settings.Notifications.validationSmtpHostRequired": "Трябва да предоставите валидно име на хост или IP адрес", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "Известието за тест към LunaSea е изпратено!", "components.RequestModal.requestedited": "Заявката за <strong>{title}</strong> е редактирана успешно!", "components.Discover.TvGenreSlider.tvgenres": "Жанрове сериали", "components.RequestModal.selectseason": "Изберете сезон(и)", "components.NotificationTypeSelector.adminissueresolvedDescription": "Получавайте известия, когато проблемите бъдат разрешени от други потребители.", "components.RequestList.RequestItem.tmdbid": "TMDB ID", "components.PermissionEdit.createissues": "Докладвайте проблеми", "components.RequestList.RequestItem.requesteddate": "Заявен", "components.Settings.Notifications.discordsettingssaved": "Настройките за известяване към Discord са запазени успешно!", "components.RequestModal.QuotaDisplay.movie": "филм", "components.Settings.RadarrModal.qualityprofile": "Профил качество", "components.Settings.RadarrModal.enableSearch": "Активирайте автоматичното търсене", "components.Settings.SettingsJobsCache.cacheDescription": "Overseerr кешира заявки към външни крайни точки на API, за да оптимизира производителността и да избегне извършването на ненужни извиквания на API.", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> {playCount, plural, one {гледа} other {гледат}}", "components.Discover.TvGenreList.seriesgenres": "Жанрове сериали", "components.RequestModal.requestcancelled": "Заявката за <strong>{title}</strong> е анулирана.", "components.Settings.SettingsJobsCache.cacheksize": "Размер на ключове", "components.MovieDetails.overviewunavailable": "Прегледът не е наличен.", "components.Settings.RadarrModal.notagoptions": "Без етикети.", "components.Login.signinwithplex": "Използвайте своя Plex акаунт", "components.MovieDetails.productioncountries": "Продукция на {countryCount, plural, one {държава} other {държави}}", "components.Settings.RadarrModal.toastRadarrTestFailure": "Неуспешно свързване с Radarr.", "components.Settings.SettingsAbout.outofdate": "Оста<PERSON><PERSON>л", "components.MovieDetails.managemovie": "Управление на филм", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "JSON payload нулиран успешно!", "components.ManageSlideOver.manageModalMedia4k": "4К медия", "components.NotificationTypeSelector.adminissuereopenedDescription": "Получавайте известие, когато проблемите се отварят отново от други потребители.", "components.ResetPassword.password": "Парола", "components.ManageSlideOver.alltime": "Всички времена", "components.RequestBlock.delete": "Изтриване на заявка", "components.ResetPassword.validationemailrequired": "Трябва да предоставите валиден имейл адрес", "components.ManageSlideOver.manageModalNoRequests": "Няма заявки.", "components.Settings.Notifications.validationEmail": "Трябва да предоставите валиден имейл адрес", "components.RequestModal.requestmovies4k": "Заявка {count} {count, plural, one {филм} other {филми}} в 4К", "components.Discover.NetworkSlider.networks": "Мр<PERSON><PERSON><PERSON>", "components.PermissionEdit.autorequestSeries": "Автоматична заявка на сериали", "components.MovieDetails.showmore": "Покажи повече", "components.PermissionEdit.viewrequestsDescription": "Дайте разрешение за преглед на заявки за медии, изпратени от други потребители.", "components.Settings.Notifications.encryptionNone": "Нищо", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "Базовият URL адрес не трябва да завършва с наклонена черта в края", "components.ManageSlideOver.openarr4k": "Отвори в 4K {arr}", "components.Discover.FilterSlideover.streamingservices": "Услуги за стрийминг", "components.RequestBlock.lastmodifiedby": "Последна промяна от", "components.MovieDetails.releasedate": "{releaseCount, plural, one {Дата на излизане} other {Дати на излизане}}", "components.Discover.popularmovies": "Популярни филми", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{филми} за {quotaDays} {дни}</quotaUnits>", "components.ResetPassword.requestresetlinksuccessmessage": "Връзката за повторно задаване на парола ще ви бъде изпратена на предоставения имейл адрес, ако е свързан с валиден потребител.", "components.RequestBlock.approve": "Одобряване на заявката", "components.RegionSelector.regionDefault": "Всички региони", "components.RequestCard.editrequest": "Редакция на заявка", "components.Selector.searchStudios": "Търсете студия…", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {# Активен филтър} other {# Активни филтри}}", "components.Settings.SettingsAbout.gettingsupport": "Получаване на поддръжка", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Настройките за известяване към Pushbullet са запазени успешно!", "components.Settings.Notifications.NotificationsGotify.validationTypes": "Трябва да изберете поне един тип известие", "components.Discover.upcomingmovies": "Предстоящи филми", "components.ManageSlideOver.manageModalIssues": "Отворени въпроси", "components.Settings.Notifications.telegramsettingssaved": "Настройките за известяване към Telegram са запазени успешно!", "components.RequestButton.viewrequest4k": "Преглед на 4К заявка", "components.Settings.RadarrModal.edit4kradarr": "Редактирай 4К Radarr сървър", "components.PermissionEdit.request4k": "Заявка 4K", "components.RequestModal.QuotaDisplay.quotaLink": "Можете да прегледате обобщение на ограниченията на вашите заявки на вашата <ProfileLink>профилна страница</ProfileLink>.", "components.Discover.plexwatchlist": "Вашият Plex списък за гледане", "components.ResetPassword.confirmpassword": "Потвърди парола", "components.Discover.FilterSlideover.tmdbuserscore": "TMDB потребителска оценка", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "Етикет на канала", "components.Settings.RadarrModal.createradarr": "Добавяне на нов Radarr сървър", "components.NotificationTypeSelector.mediaapproved": "Заявката е одобрена", "components.RequestModal.requestcollection4ktitle": "Заявка на колекция в 4К", "components.Discover.tmdbtvgenre": "TMDB Жанр сериали", "components.MovieDetails.theatricalrelease": "Излъчване в кината", "components.Settings.RadarrModal.validationApplicationUrl": "Трябва да предоставите валиден URL адрес", "components.Settings.Notifications.chatId": "Чат ID", "components.MovieDetails.overview": "Преглед", "components.Settings.SettingsJobsCache.availability-sync": "Синхронизиране наличността на медиите", "components.Discover.tmdbstudio": "TMDB Студио", "components.Discover.upcoming": "Предстоящи филми", "components.ManageSlideOver.pastdays": "Последните {days, number} дни", "components.MovieDetails.rtaudiencescore": "Резултат от публиката на Rotten Tomatoes", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {филм} other {филми}}", "components.RequestModal.requesterror": "Нещо се обърка при изпращане на заявката.", "components.Settings.Notifications.pgpPasswordTip": "Подписвайте шифровани имейл съобщения с помощта на <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.RequestList.RequestItem.failedretry": "Нещо се обърка при повторен опит за заявка.", "components.MovieDetails.imdbuserscore": "IMDB потребителска оценка", "components.RequestButton.decline4krequests": "Отхвърляне {requestCount, plural, one {заявка} other {{requestCount} заявки}}", "components.RequestButton.declinerequest4k": "Отказ на 4К заявка", "components.Settings.Notifications.NotificationsLunaSea.profileName": "Профилно име", "components.Settings.Notifications.NotificationsGotify.url": "URL адрес на сървъра", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "Трябва да изберете поне един тип известие", "components.NotificationTypeSelector.mediarequestedDescription": "Изпращайте известия, когато потребителите изпращат нови медийни заявки, които изискват одобрение.", "components.Discover.tmdbtvstreamingservices": "TMDB Услуги за стрийминг на сериали", "components.ResetPassword.gobacklogin": "Върнете се към страницата за вход", "components.ManageSlideOver.manageModalClearMediaWarning": "* Това ще премахне необратимо всички данни за този {mediaType}, включително всички заявки. Ако този елемент съществува във вашата Plex библиотека, медийната информация ще бъде отново създадена по време на следващото сканиране.", "components.Settings.Notifications.encryptionDefault": "Използвайте STARTTLS, ако има такъв", "components.Settings.SettingsAbout.uptodate": "Актуално", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "Настройките за известяване на LunaSea не успяха да бъдат запазени.", "components.Settings.Notifications.pgpPassword": "PGP Парола", "components.RequestModal.QuotaDisplay.requiredquotaUser": "Този потребител трябва да има най-малко <strong>{seasons}</strong> {seasons, plural, one {заявка за сезон} other {заявки за сезони}} оставащи, за да изпрати заявка за този сериал.", "components.Settings.Notifications.NotificationsWebhook.authheader": "Хедър за удостоверяване", "components.PermissionEdit.request4kTvDescription": "Дайте разрешение за изпращане на заявки за 4K сериали.", "components.ManageSlideOver.markavailable": "Маркирайте като наличен", "components.Selector.showless": "Покажи по-малко", "components.Discover.DiscoverWatchlist.discoverwatchlist": "Вашият Plex списък за гледане", "components.RequestList.RequestItem.unknowntitle": "Неизвестно заглавие", "components.Settings.SettingsAbout.totalmedia": "Общо медия", "components.RegionSelector.regionServerDefault": "По подразбиране ({region})", "components.PermissionEdit.request4kMovies": "Заявка за 4K филми", "components.RequestButton.approve4krequests": "Одобрете {requestCount, plural, one {4K заявка} other {{requestCount} 4K Заявки}}", "components.Discover.FilterSlideover.releaseDate": "Дата на излизане", "components.Settings.Notifications.webhookUrl": "Webhook URL", "components.RequestModal.errorediting": "Нещо се обърка при редактирането на заявката.", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "Известието за тест към Slack е изпратено!", "components.PermissionEdit.autoapprove4kSeries": "Автоматично одобрение на 4К сериали", "components.PermissionEdit.autorequestSeriesDescription": "Дайте разрешение за автоматично изпращане на заявки за не-4K серии чрез Plex Списък за гледане.", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "Активиране на агент", "components.MovieDetails.similar": "Подобни заглавия", "components.Settings.SettingsAbout.appDataPath": "Директория с данни", "components.Settings.Notifications.botUsername": "Бот потребителско име", "components.MovieDetails.mark4kavailable": "Маркирайте като налично в 4K", "components.RequestCard.approverequest": "Одобряване на заявката", "components.Discover.recentlyAdded": "Наскоро добавен", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "URL адреса трябва да има водеща наклонена черта", "components.Settings.RadarrModal.validationPortRequired": "Трябва да предоставите валиден номер на порт", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "Настройките за известяване към Slack не успяха да бъдат запазени.", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "Известието чрез Webhook тест е изпратено!", "components.RequestButton.approverequest": "Одобряване на заявката", "components.Settings.SettingsAbout.Releases.releasedataMissing": "Понастоящем няма налични данни за изданието.", "components.Settings.SettingsAbout.Releases.viewongithub": "Вижте в GitHub", "components.Settings.Notifications.toastTelegramTestSending": "Изпраща се тестово известие чрез Telegram…", "components.PermissionEdit.managerequestsDescription": "Дайте разрешение за управление на медийни заявки. Всички заявки, направени от потребител с това разрешение, ще бъдат автоматично одобрени.", "components.Settings.SettingsAbout.version": "Версия", "components.RequestModal.QuotaDisplay.allowedRequests": "Имате право да заявявате <strong>{limit}</strong> {type} на всеки <strong>{days}</strong> дни.", "components.PermissionEdit.autorequestMoviesDescription": "Дайте разрешение за автоматично изпращане на заявки за не-4K филми чрез Plex Списък за гледане.", "components.NotificationTypeSelector.usermediadeclinedDescription": "Получавайте известие, когато заявките ви за медия бъдат отхвърлени.", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {промяна} other {промени}} назад", "components.ResetPassword.resetpassword": "Нулиране на паролата ви", "components.Settings.Notifications.smtpHost": "SMTP Host", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "Изпращайте известия, когато потребителите изпращат нови медийни заявки, които се одобряват автоматично.", "components.Discover.FilterSlideover.runtime": "Времетраене", "components.Settings.SettingsAbout.githubdiscussions": "Дискусии в GitHub", "components.PermissionEdit.autoapprove": "Автоматично одобрение", "components.RequestCard.deleterequest": "Изтриване на заявка", "components.Settings.Notifications.emailsettingssaved": "Настройките за известяване по имейл са запазени успешно!", "components.RequestBlock.server": "Целеви сървър", "components.Discover.FilterSlideover.from": "От", "components.Settings.RadarrModal.add": "Добавете сървър", "components.Settings.Notifications.telegramsettingsfailed": "Настройките за известяване към Telegram не успяха да бъдат запазени.", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "Настройките за известяване чрез Webhook са запазени успешно!", "components.RequestModal.pending4krequest": "Изчакваща заявка за 4K", "components.PermissionEdit.viewwatchlists": "Вижте списъците за гледане от Plex", "components.RequestModal.AdvancedRequester.destinationserver": "Целеви сървър", "components.PermissionEdit.createissuesDescription": "Дайте разрешение за докладване на проблеми с медиите.", "components.Settings.Notifications.NotificationsPushover.userToken": "Потребителски или групов ключ", "components.RequestModal.AdvancedRequester.rootfolder": "Основна папка", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{сезона} за {quotaDays} {дни}</quotaUnits>", "components.NotificationTypeSelector.adminissuecommentDescription": "Получавайте известия, когато други потребители коментират проблеми.", "components.PermissionEdit.adminDescription": "Пълен администраторски достъп. Заобикаля всички други проверки на права.", "components.NotificationTypeSelector.mediadeclined": "Заявката е отхвърлена", "components.Settings.Notifications.emailsettingsfailed": "Настройките за известяване по имейл не успяха да бъдат запазени.", "components.PermissionEdit.viewrecentDescription": "Дайте разрешение за преглед на списъка с наскоро добавени медии.", "components.NotificationTypeSelector.usermediarequestedDescription": "Получавайте известия, когато други потребители изпращат нови медийни заявки, които изискват одобрение.", "components.Discover.studios": "Студия", "components.Settings.SettingsAbout.overseerrinformation": "Относно Overseerr", "components.Settings.RadarrModal.tagRequestsInfo": "Автоматично добавяне на допълнителен етикет с потребителското ID и показваното име на заявителя", "components.RequestBlock.requestdate": "Дата на заявка", "components.PermissionEdit.usersDescription": "Дайте разрешение за управление на потребители. Потребителите с това разрешение не могат да променят потребители с привилегии или да предоставят привилегия за администратор.", "components.Settings.Notifications.botAvatarUrl": "URL адрес на аватара на бота", "components.Settings.Notifications.discordsettingsfailed": "Настройките за известяване към Discord не успяха да бъдат запазени.", "components.Discover.tmdbmoviestreamingservices": "TMDB Услуги за стрийминг на филми", "components.Discover.FilterSlideover.to": "До", "components.Settings.SettingsAbout.helppaycoffee": "Помогнете, като купите едно кафе", "components.RequestList.sortAdded": "Най-скорошен", "components.Discover.resetfailed": "Нещо се обърка при нулирането на настройките за персонализиране на откриването.", "components.Settings.RadarrModal.validationRootFolderRequired": "Трябва да изберете основна папка", "components.Discover.createnewslider": "Създайте нов плъзгач", "components.Search.searchresults": "Резултати от търсенето", "components.Settings.Notifications.NotificationsPushover.agentenabled": "Активиране на агент", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "Неуспешно изпращане на тестово известие към Pushover.", "components.RequestModal.requestfrom": "Заявката на {username} чака одобрение.", "components.Discover.FilterSlideover.filters": "Филтри", "components.RequestCard.unknowntitle": "Неизвестно заглавие", "components.QuotaSelector.movies": "{count, plural, one {филм} other {филми}}", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.MovieDetails.tmdbuserscore": "TMDB потребителска оценка", "components.Settings.Notifications.botApiTip": "<CreateBotLink>Създай бот</CreateBotLink> за използване от Overseerr", "components.Discover.DiscoverWatchlist.watchlist": "Plex списък за гледане", "components.RequestModal.AdvancedRequester.tags": "Етикети", "components.RequestList.RequestItem.mediaerror": "{mediaType} не е намерен", "components.Discover.discover": "Открийте", "components.PermissionEdit.admin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.NotificationsPushover.validationTypes": "Трябва да изберете поне един тип известие", "components.Settings.Notifications.smtpPort": "SMTP Порт", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "Изпраща се известие за тест към Pushbullet…", "components.RequestList.RequestItem.cancelRequest": "Откажи заявката", "components.Settings.Notifications.NotificationsGotify.agentenabled": "Активиране на агент", "components.Settings.Notifications.toastEmailTestSending": "Изпраща се тестово известие към имейл…", "components.PermissionEdit.autoapproveDescription": "Гарантирано автоматично одобрение за заявки за не-4K медии.", "components.Settings.Notifications.encryptionTip": "В повечето случаи Implicit TLS използва порт 465, а STARTTLS използва порт 587", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "Трябва да предоставите валиден URL адрес", "components.RequestList.sortModified": "Последно променен", "components.NotificationTypeSelector.mediarequested": "Заявка в очакване на одобрение", "components.PermissionEdit.autoapproveMovies": "Автоматично одобряване на филми", "components.PermissionEdit.viewissuesDescription": "Дайте разрешение за преглед на медийни проблеми, докладвани от други потребители.", "components.Settings.Notifications.validationPgpPrivateKey": "Трябва да предоставите валиден PGP частен ключ", "components.RequestList.RequestItem.tvdbid": "Идентификатор за TheTVDB", "components.ManageSlideOver.markallseasonsavailable": "Маркирайте всички сезони като налични", "components.Settings.Notifications.botUsernameTip": "Позволете на потребителите също да започнат чат с вашия бот и да конфигурират свои собствени известия", "components.Settings.RadarrModal.loadingrootfolders": "Основните папки се зареждат…", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "Вашият 30-<PERSON><PERSON><PERSON><PERSON> <UsersGroupsLink>идентификатор на потребител или група</UsersGroupsLink>", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "Изпраща се известие за тест към Pushover…", "components.Settings.Notifications.NotificationsPushover.accessToken": "Токен за API към приложение", "components.PersonDetails.birthdate": "<PERSON>о<PERSON><PERSON><PERSON> {birthdate}", "components.PermissionEdit.requestTv": "Заявка на сериали", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "Токен за достъп", "components.Selector.starttyping": "Започнете въвеждане на търсене.", "components.NotificationTypeSelector.userissuecommentDescription": "Получавайте известия, когато проблемите, за които сте докладвали, получат нови коментари.", "components.Settings.Notifications.botAPI": "Токен за оторизация на бот", "components.Discover.tmdbsearch": "TMDB Търсене", "components.Settings.SettingsJobsCache.cachevsize": "Размер на стойността", "components.PermissionEdit.autorequestMovies": "Автоматично заявяване на филми", "components.RequestModal.requestadmin": "Тази заявка ще бъде одобрена автоматично.", "components.Discover.FilterSlideover.firstAirDate": "Първа дата за ефир", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "Неуспешно изпращане на тестово известие чрез Webhook.", "components.PermissionEdit.autoapprove4kMoviesDescription": "Гарантирано автоматично одобрение за заявки за 4K филми.", "components.PermissionEdit.request4kDescription": "Дайте разрешение за изпращане на заявки за 4K медии.", "components.RequestModal.cancel": "Откажи заявката", "components.PermissionEdit.advancedrequestDescription": "Дайте разрешение за промяна на разширените опции за заявка на медия.", "components.Selector.searchKeywords": "Търсете ключови думи…", "components.MovieDetails.MovieCast.fullcast": "Пълен актьорски състав", "components.Settings.SettingsAbout.runningDevelop": "Вие изпълнявате версия <code>develop</code> на Overseerr, която се препоръчва само за тези, които допринасят за разработката или помагат при тестване на последните версии.", "components.Settings.RadarrModal.externalUrl": "Външен URL адрес", "components.Settings.Notifications.NotificationsWebhook.customJson": "JSON съдържание", "components.RequestBlock.edit": "Редакция на заявка", "components.Discover.FilterSlideover.runtimeText": "{minValue}-{maxValue} минути времетраене", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "Неуспешно изпращане на тестово известие към Gotify.", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "Неуспешно изпращане на тестово известие към Slack.", "components.Settings.SettingsAbout.supportoverseerr": "Подкрепете Overseerr", "components.Discover.PlexWatchlistSlider.plexwatchlist": "Вашият Plex списък за гледане", "components.Selector.nooptions": "Няма резултати.", "components.RequestModal.AdvancedRequester.selecttags": "Изберете етикети", "components.Settings.RadarrModal.editradarr": "Редак<PERSON><PERSON><PERSON><PERSON><PERSON> Radarr сървър", "components.Settings.Notifications.sendSilentlyTip": "Изпращайте известия без звук", "components.ResetPassword.email": "<PERSON><PERSON><PERSON><PERSON><PERSON> адрес", "components.RequestBlock.languageprofile": "Езиков профил", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "Трябва да изберете поне един тип известие", "components.NotificationTypeSelector.userissuecreatedDescription": "Получавайте известия, когато други потребители съобщават за проблеми.", "components.Discover.FilterSlideover.genres": "Жанрове", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "Този потребител има право да заявяви <strong>{limit}</strong> {type} на всеки <strong>{days}</strong> дни.", "components.NotificationTypeSelector.mediafailed": "Неуспешна обработка на заявката", "components.Settings.Notifications.toastTelegramTestFailed": "Неуспешно изпращане на тестово известие чрез Telegram.", "components.RequestList.RequestItem.requested": "Заявен", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "Помощ за променливи на шаблони", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Настройките за известяване към Pushover са запазени успешно!", "components.ResetPassword.validationpasswordrequired": "Трябва да въведете парола", "components.Login.validationemailrequired": "Трябва да предоставите валиден имейл адрес", "components.PermissionEdit.requestDescription": "Дайте разрешение за изпращане на заявки за не-4K медии.", "components.Discover.FilterSlideover.originalLanguage": "Ориг<PERSON><PERSON><PERSON>ен език", "components.MovieDetails.recommendations": "Препоръки", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "Активиране на агент", "components.MovieDetails.markavailable": "Маркирайте като наличен", "components.RequestBlock.requestedby": "Поискано от", "components.Discover.resettodefault": "Нулиране до първоначално", "components.MovieDetails.reportissue": "Докладвайте за проблем", "components.RequestModal.autoapproval": "Автоматично одобрение", "components.Settings.Notifications.allowselfsigned": "Разрешаване на самоподписани сертификати", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Известието към Pushbullet тест е изпратено!", "components.MovieDetails.revenue": "Приходи", "components.MediaSlider.ShowMoreCard.seemore": "Виж повече", "components.NotificationTypeSelector.usermediaapprovedDescription": "Получавайте известие, когато заявките ви за медии бъдат одобрени.", "components.MovieDetails.studio": "{studioCount, plural, one {студио} other {студия}}", "components.PermissionEdit.advancedrequest": "Разширени заявки", "components.RequestBlock.decline": "Отказ на заявка", "components.PersonDetails.appearsin": "Изяви", "components.PermissionEdit.users": "Управление на потребителите", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Изпраща се тестово известие към Gotify…", "components.Search.search": "Търсене", "components.Settings.RadarrModal.loadingprofiles": "Зареждат се профилите за качество…", "components.Settings.Notifications.NotificationsGotify.token": "Токен за приложение", "components.PermissionEdit.viewwatchlistsDescription": "Дайте разрешение за преглед на списъците за гледане от Plex на други потребители.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "Известието чрез Web push тест е изпратено!", "components.ManageSlideOver.opentautulli": "Отвори в Tautulli", "components.MovieDetails.viewfullcrew": "Вижте целия екип", "components.Settings.RadarrModal.hostname": "Име на хост или IP адрес", "components.RequestModal.requestCancel": "Заявката за <strong>{title}</strong> е анулирана.", "components.Settings.Notifications.NotificationsPushover.deviceDefault": "Устройство по подразбиране", "components.RequestCard.tvdbid": "Идентификатор за TheTVDB", "components.Settings.Notifications.toastDiscordTestSuccess": "Известието за тест към Discord е изпратено!", "components.NotificationTypeSelector.mediafailedDescription": "Изпращайте известия, когато медийните заявки не могат да бъдат добавени към Radarr или Sonarr.", "components.RequestModal.requestmovietitle": "Заявка за филм", "components.RequestButton.requestmore": "Заявете повече", "components.NotificationTypeSelector.issuereopened": "Проблемът е отворен отново", "components.QuotaSelector.days": "{count, plural, one {ден} other {дни}}", "components.NotificationTypeSelector.mediaAutoApproved": "Заявката е автоматично одобрена", "components.Settings.RadarrModal.tagRequests": "Заявки за етикети", "components.Settings.Notifications.pgpPrivateKeyTip": "Подписвайте шифровани имейл съобщения с помощта на <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.RequestModal.QuotaDisplay.requiredquota": "Трябва да имате оставащи поне <strong>{seasons}</strong> {seasons, plural, one {заявка за сезон} other {заявки за сезони}}, за да изпратите заявка за този сериал.", "components.ManageSlideOver.markallseasons4kavailable": "Маркирайте всички сезони като налични в 4K", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "Получавайте известия, когато други потребители изпращат нови медийни заявки, които се одобряват автоматично.", "components.Settings.SettingsJobsCache.cacheflushed": "Кешът на {cachename} е изчистен.", "components.RequestModal.seasonnumber": "Сезон {number}", "components.Discover.FilterSlideover.tmdbuservotecount": "TMDB брой гласували потребители", "components.Discover.tmdbmoviegenre": "TMDB Филмови жанрове", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "Нулиране до първоначално", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "Не остават достатъчно заявки за сезона", "components.RequestModal.requestseasons4k": "Заявете {seasonCount} {seasonCount, plural, one {сезон} other {сезони}} в 4К", "components.RequestModal.pendingrequest": "Изчакваща заявка", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "Webhook URL", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "Изпраща се известие за тест чрез Webhook…", "components.NotificationTypeSelector.issuereopenedDescription": "Изпращайте известия, когато проблемите се отварят отново.", "components.Settings.Notifications.chatIdTip": "Започнете чат с вашия бот, добавете <GetIdBotLink>@get_id_bot</GetIdBotLink> и изпълнете командата <code>/my_id</code>", "components.Settings.Notifications.NotificationsSlack.agentenabled": "Активиране на агент", "components.QuotaSelector.seasons": "{count, plural, one {сезон} other {сезони}}", "components.RequestModal.requestseriestitle": "Заявка на сериали", "components.Settings.Notifications.encryptionImplicitTls": "Използвайте Implicit TLS", "components.RequestList.RequestItem.deleterequest": "Изтриване на заявка", "components.ManageSlideOver.manageModalTitle": "Управление на {mediaType}", "components.ResetPassword.passwordreset": "Нулиране на парола", "components.ManageSlideOver.openarr": "Отвори в {arr}", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {Сезон} other {Сезони}}", "components.RequestModal.SearchByNameModal.notvdbiddescription": "Не успяхме автоматично да съпоставим този сериал. Моля, изберете правилното съвпадение по-долу.", "components.PermissionEdit.autorequestDescription": "Дайте разрешение за автоматично изпращане на заявки за не-4K медии чрез Plex Списък за гледане.", "components.RequestModal.AdvancedRequester.default": "{name} (подразбиране)", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "Настройките за известяване чрез Web push не успяха да бъдат запазени.", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "Трябва да предоставите валиден JSON payload", "components.Discover.MovieGenreList.moviegenres": "Филмови жанрове", "components.RequestModal.requestcollectiontitle": "Заявка на колекция", "components.MovieDetails.MovieCrew.fullcrew": "Екипа", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {сезон} other {сезони}}", "components.NotificationTypeSelector.issueresolvedDescription": "Изпращайте известия, когато проблемите бъдат разрешени.", "components.RequestModal.AdvancedRequester.languageprofile": "Езиков профил", "components.Settings.Notifications.toastEmailTestFailed": "Неуспешно изпращане на тестово известие към имейл.", "components.Discover.FilterSlideover.voteCount": "Брой гласове между {minValue} и {maxValue}", "components.NotificationTypeSelector.issuecreated": "Проблемът е докладван", "components.RequestModal.alreadyrequested": "Вече е заявено", "components.NotificationTypeSelector.mediaavailable": "Налична заявка", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "Настройките за известяване към Pushbullet не успяха да бъдат запазени.", "components.PermissionEdit.autoapprove4kSeriesDescription": "Гарантирано автоматично одобрение за заявки за 4K сериали.", "components.Settings.Notifications.authPass": "SMTP Парола", "components.Discover.recentrequests": "Последни заявки", "components.Settings.RadarrModal.validationNameRequired": "Трябва да посочите име на сървъра", "components.Login.validationpasswordrequired": "Трябва да въведете парола", "components.Settings.RadarrModal.server4k": "4K сървър", "components.NotificationTypeSelector.mediadeclinedDescription": "Изпращайте известия, когато медийните заявки бъдат отхвърлени.", "components.Settings.Notifications.validationSmtpPortRequired": "Трябва да предоставите валиден номер на порт", "components.NotificationTypeSelector.mediaautorequested": "Заявката е изпратена автоматично", "components.ManageSlideOver.manageModalClearMedia": "Изчистване на данните", "components.Settings.RadarrModal.apiKey": "<PERSON> ключ", "components.MovieDetails.streamingproviders": "В момента се излъчва по", "components.NotificationTypeSelector.notificationTypes": "Видове известия", "components.Settings.SettingsAbout.betawarning": "Това е BETA софтуер. Функциите може да са повредени и/или нестабилни. Моля, докладвайте за проблеми в GitHub!", "components.Settings.RadarrModal.selectQualityProfile": "Изберете профил за качество", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "URL адресът не трябва да завършва с наклонена черта в края", "components.Settings.SettingsJobsCache.cache": "<PERSON><PERSON><PERSON>", "components.Discover.upcomingtv": "Предстоящи сериали", "components.Settings.RadarrModal.tags": "Етикети", "components.Settings.Notifications.validationUrl": "Трябва да предоставите валиден URL адрес", "components.Settings.SettingsJobsCache.cachename": "Кеш име", "components.RequestCard.cancelrequest": "Откажи заявката", "components.NotificationTypeSelector.userissueresolvedDescription": "Получавайте известие, когато проблемите, за които сте докладвали, бъдат разрешени.", "components.Settings.SettingsMain.hideAvailable": "Скриване на наличните медии", "components.Settings.SettingsLogs.logDetails": "Подробности за лог файл", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "Настройките са запазени успешно!", "components.UserList.sortRequests": "Брой заявки", "components.Settings.SonarrModal.edit4ksonarr": "Редактирай 4К Sonarr сървър", "components.Settings.SettingsJobsCache.imagecache": "Кеш изображения", "components.UserProfile.totalrequests": "Общо заявки", "components.UserProfile.UserSettings.UserGeneralSettings.role": "Роля", "components.StatusChecker.reloadApp": "Презареди {applicationTitle}", "components.Settings.toastTautulliSettingsSuccess": "Tautulli настройките са запазени успешно!", "components.Settings.default4k": "По подразбиране 4К", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "Всяка {jobScheduleMinutes, plural, one {минута} other {{jobScheduleMinutes} минути}}", "components.Settings.SettingsJobsCache.imagecachesize": "Общ размер на кеша", "components.Settings.SonarrModal.validationLanguageProfileRequired": "Трябва да изберете езиков профил", "components.Settings.SonarrModal.loadingTags": "Етикетите се зареждат…", "components.StatusChecker.restartRequiredDescription": "Моля, рестартирайте сървъра, за да приложите актуализираните настройки.", "components.Settings.SettingsJobsCache.jobtype": "Тип", "components.Settings.menuAbout": "Относно", "components.UserProfile.UserSettings.UserNotificationSettings.deviceDefault": "Устройство по подразбиране", "components.Settings.SettingsUsers.users": "Потребители", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "Нещо се обърка при запазване на настройките.", "components.Settings.toastPlexConnectingFailure": "Неуспешно свързване с Plex.", "components.TvDetails.rtcriticsscore": "Rotten Tomatoes Tomatometer", "components.TvDetails.episodeRuntime": "Времетраене на епизод", "i18n.all": "Всичко", "components.Settings.SettingsUsers.toastSettingsSuccess": "Потребителските настройки са запазени успешно!", "components.Settings.notificationsettings": "Настройки за известията", "components.Settings.SettingsLogs.logsDescription": "Можете също да видите тези лог файлове директно чрез <code>stdout</code> или в <code>{appDataPath}/logs/jellyseerr.log</code>.", "components.TvDetails.episodeCount": "{episodeCount, plural, one {# епизод} other {# епизоди}}", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "ID на потребител в Discord", "components.TvDetails.firstAirDate": "Първа дата за ефир", "pages.errormessagewithcode": "{statusCode} - {error}", "components.UserList.validationEmail": "Трябва да предоставите валиден имейл адрес", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "Потвърди парола", "components.Settings.SettingsLogs.logs": "Лог файлове", "i18n.tvshows": "Сериали", "i18n.notrequested": "Не е заявен", "i18n.saving": "Запазване…", "components.Settings.notifications": "Известия", "components.Settings.SettingsMain.toastSettingsSuccess": "Настройките са запазени успешно!", "components.UserList.edituser": "Редактиране на потребителски права", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "Настройките за известяване към Telegram са запазени успешно!", "components.TvDetails.TvCrew.fullseriescrew": "Пълен екип на сериала", "pages.somethingwentwrong": "Нещо се обърка", "components.Settings.radarrsettings": "Radarr настройки", "i18n.canceling": "Отменя се…", "components.Settings.SonarrModal.notagoptions": "Без етикети.", "components.Settings.SettingsJobsCache.image-cache-cleanup": "Почистване на кеша на изображенията", "components.UserProfile.UserSettings.menuPermissions": "Права", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "Автоматично заявяване на филми", "components.Settings.SonarrModal.toastSonarrTestSuccess": "Връзката към Sonarr е установена успешно!", "components.Settings.SettingsUsers.defaultPermissionsTip": "Първоначални права, присвоени на нови потребители", "i18n.deleting": "Изтриване…", "components.Settings.cancelscan": "Отказ от сканиране", "components.Settings.SettingsJobsCache.runnow": "Пусни сега", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "Настройките за известяване към Pushbullet са запазени успешно!", "components.Settings.SettingsLogs.filterWarn": "Внимание", "components.Settings.SettingsLogs.viewdetails": "Виж детайлите", "components.Settings.SonarrModal.validationApiKeyRequired": "Трябва да предоставите API ключ", "i18n.save": "Запазване на промените", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "Трябва да предоставите валиден PGP публичен ключ", "components.Settings.SonarrModal.add": "Добавете сървър", "components.Settings.toastPlexRefreshSuccess": "Списъкът със сървъри на Plex е извлечен успешно!", "components.Settings.serverpresetManualMessage": "Ръчна конфигурация", "components.UserList.autogeneratepasswordTip": "Изпратете генерирана от сървъра парола до потребителя", "components.Settings.SonarrModal.animerootfolder": "Основна папка за аниме", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "Език в Открийте", "components.TvDetails.similar": "Подобни сериали", "components.TvDetails.reportissue": "Докладвайте за проблем", "components.UserList.admin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "Чат ID", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "Настройките за известяване към Telegram не успяха да бъдат запазени.", "components.UserList.validationpasswordminchars": "Паролата е твърде кратка; трябва да съдържа минимум 8 знака", "components.TitleCard.tmdbid": "TMDB ID", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "Текуща честота", "components.UserList.userfail": "Нещо се обърка при запазване на потребителски права.", "i18n.retry": "Опитайте отново", "components.Settings.SettingsLogs.copyToClipboard": "Копирано в клипборда", "components.TvDetails.overviewunavailable": "Прегледът не е наличен.", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "Синхронизиране на Plex Списък за гледане", "components.UserList.createlocaluser": "Създайте локален потребител", "components.TvDetails.nextAirDate": "Следваща дата на излъчване", "components.UserList.sortDisplayName": "Показвано име", "components.Settings.SettingsLogs.filterInfo": "Информация", "i18n.view": "Преглед", "components.Settings.scan": "Синхронизиране на библиотеки", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "Заданието редактирана успешно!", "components.UserList.usercreatedsuccess": "Потребителят е създаден успешно!", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "Настройките за известяване по имейл са запазени успешно!", "components.Settings.SonarrModal.tagRequests": "Заявки за етикети", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "Паролите трябва да съвпадат", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "Токън за API към приложение", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "Трябва да предоставите валиден потребителски идентификатор (User ID) в Discord", "i18n.importing": "Импортиране.…", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "Настройките за известяване чрез Web push не успяха да бъдат запазени.", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "Автоматична заявка на сериали", "components.UserList.create": "Създавайте", "i18n.restartRequired": "Изисква се рестартиране", "components.Settings.tautulliSettingsDescription": "По желание конфигурирайте настройките за вашия сървър Tau<PERSON>. Overseerr извлича данни от хронологията на гледане за вашата Plex медия от Tautulli.", "components.Settings.copied": "Копиран API ключ в клипборда.", "i18n.request": "Заявка", "components.Settings.validationApiKey": "Трябва да предоставите API ключ", "components.Settings.SonarrModal.editsonarr": "Редак<PERSON><PERSON><PERSON><PERSON><PERSON> Sonarr сървър", "components.Settings.addradarr": "Добавяне на нов Radarr сървър", "components.Settings.notrunning": "Не работи", "components.Settings.urlBase": "Базов URL адрес", "components.Settings.SonarrModal.rootfolder": "Основна папка", "components.Settings.SonarrModal.apiKey": "<PERSON> ключ", "components.UserList.userssaved": "Потребителските права са запазени успешно!", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "Този потребителски акаунт в момента няма зададена парола. Конфигурирайте парола по-долу, за да позволите на този акаунт да влиза като „локален потребител“.", "components.Settings.mediaTypeMovie": "филм", "components.Settings.SettingsMain.locale": "Език на дисплея", "components.Settings.SettingsJobsCache.unknownJob": "Непозната задача", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Сканиране на наскоро добавени в Plex", "components.UserList.localuser": "Локален потребител", "components.UserProfile.UserSettings.UserGeneralSettings.region": "Регион за Открийте", "components.UserList.importfromplexerror": "Нещо се обърка при импортирането на потребители на Plex.", "components.Settings.SonarrModal.selectRootFolder": "Изберете главна папка", "i18n.delimitedlist": "{a}, {b}", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "Web Push", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "Известия", "components.Settings.SonarrModal.validationApplicationUrl": "Трябва да предоставите валиден URL адрес", "components.Settings.SonarrModal.seasonfolders": "Папки по сезони", "i18n.resultsperpage": "Показване на {pageSize} резултати на страница", "components.Settings.SettingsUsers.movieRequestLimitLabel": "Глоба<PERSON>ен лимит за заявки на филми", "components.Settings.advancedTooltip": "Неправилното конфигуриране на тази настройка може да доведе до нарушена функционалност", "components.UserList.newplexsigninenabled": "<strong>Локално влизане</strong> в момента е разрешено. Потребителите на Plex с достъп до библиотека не трябва да бъдат импортирани, за да влязат.", "components.UserList.deleteconfirm": "Сигурни ли сте, че искате да изтриете този потребител? Всички данни за техните заявки ще бъдат премахнати за постоянно.", "components.TvDetails.productioncountries": "Продукция на {countryCount, plural, one {държава} other {държави}}", "components.Settings.SettingsUsers.toastSettingsFailure": "Нещо се обърка при запазване на настройките.", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "Настройките за известяване по имейл не успяха да бъдат запазени.", "components.Settings.hostname": "Име на хост или IP адрес", "components.Settings.SonarrModal.tagRequestsInfo": "Автоматично добавяне на допълнителен етикет с потребителското ID и показваното име на заявителя", "components.TvDetails.Season.noepisodes": "Няма наличен списък с епизоди.", "components.Settings.serviceSettingsDescription": "Конфигурирайте вашия {serverType} сървър(и) по-долу. Можете да свържете множество {serverType} сървъри, но само два от тях могат да бъдат маркирани като стандартни (един не-4K и един 4K). Администраторите могат да заменят сървъра, използван за обработка на нови заявки преди одобрението.", "components.TvDetails.seasonstitle": "Сезони", "i18n.available": "Наличен", "components.Settings.menuGeneralSettings": "<PERSON><PERSON><PERSON>", "components.Settings.manualscanDescription": "Обикновено това ще се изпълнява само веднъж на всеки 24 часа. Overseerr ще проверява наскоро добавения Plex сървър по-агресивно. Ако за първи път конфигурирате Plex, препоръчва се еднократно пълно ръчно сканиране на библиотека!", "components.UserList.user": "Потребител", "i18n.pending": "Очаква се", "i18n.decline": "Откажи", "components.StatusChecker.restartRequired": "Изисква се рестартиране на сървъра", "i18n.movie": "<PERSON>ил<PERSON>", "components.UserList.owner": "Собственик", "components.UserProfile.requestsperdays": "{limit} оставащи", "components.Settings.SettingsLogs.showall": "Показване на всички лог файлове", "components.Settings.is4k": "4К", "components.Settings.SonarrModal.qualityprofile": "Профил качество", "components.TvDetails.recommendations": "Препоръки", "components.UserList.userdeleteerror": "Нещо се обърка при изтриването на потребителя.", "components.UserProfile.UserSettings.UserGeneralSettings.general": "<PERSON><PERSON><PERSON>", "components.TvDetails.tmdbuserscore": "TMDB потребителска оценка", "components.Settings.SettingsJobsCache.process": "Процес", "components.Settings.plexsettingsDescription": "Конфигурирайте настройките за вашия Plex сървър. Overseerr сканира вашите Plex библиотеки, за да определи наличното съдържанието.", "i18n.import": "Импорт", "components.Settings.SettingsMain.applicationTitle": "Заглавие на приложението", "components.StatusBadge.playonplex": "Пусни в Plex", "i18n.open": "Отвори", "components.Settings.port": "Порт", "components.Settings.SonarrModal.default4kserver": "4K сървър по подразбиране", "components.Settings.SettingsJobsCache.sonarr-scan": "<PERSON><PERSON>r сканиране", "pages.serviceunavailable": "Услугата не е достъпна", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "Трябва да предоставите валиден токен за приложение", "components.Settings.librariesRemaining": "Оставащи библиотеки: {count}", "i18n.advanced": "Разширено", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "Трябва да предоставите токен за достъп", "components.Settings.SettingsJobsCache.command": "Команда", "components.Settings.tautulliApiKey": "<PERSON> ключ", "components.UserProfile.ProfileHeader.joindate": "Присъединил се на {joindate}", "components.TvDetails.anime": "Аниме", "i18n.areyousure": "Сигурни ли сте?", "components.Settings.toastPlexRefreshFailure": "Неуспешно извличане на списъка със сървъри на Plex.", "components.Settings.startscan": "Започни сканиране", "components.TvDetails.rtaudiencescore": "Резултат от публиката на Rotten Tomatoes", "components.Settings.SonarrModal.tags": "Етикети", "components.Settings.menuNotifications": "Известия", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "Тип на профила", "components.StatusBadge.seasonepisodenumber": "S{seasonNumber}E{episodeNumber}", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "Трябва да предоставите валиден user ID", "components.Setup.configureservices": "Конфигуриране на услуги", "components.Settings.SettingsUsers.newPlexLoginTip": "Позволете на потребителите на Plex да влизат, без първо да бъдат импортирани", "components.Settings.SettingsMain.originallanguage": "Език в Открийте", "components.Settings.SonarrModal.selectQualityProfile": "Изберете профил за качество", "components.Settings.SonarrModal.animelanguageprofile": "Езиков профил за аниме", "components.Settings.SettingsJobsCache.jobs": "Задания", "components.Settings.SonarrModal.createsonarr": "Добавяне на нов Sonarr сървър", "components.Settings.SettingsJobsCache.jobstarted": "{jobname} за<PERSON>очна.", "components.Settings.SettingsLogs.filterError": "Грешка", "components.Settings.SettingsJobsCache.flushcache": "Прочистване на кеша", "components.Settings.SettingsJobsCache.canceljob": "Отмени задание", "components.TvDetails.network": "{networkCount, plural, one {Мрежа} other {Мрежи}}", "components.Settings.SonarrModal.loadingrootfolders": "Основните папки се зареждат…", "i18n.collection": "Колекция", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "Базовият URL адрес не трябва да завършва с наклонена черта в края", "components.Settings.SonarrModal.server4k": "4K сървър", "components.Settings.SettingsLogs.resumeLogs": "Продължи", "components.UserList.accounttype": "Тип", "components.Settings.webAppUrl": "URL адрес на <WebAppLink>уеб приложението</WebAppLink>", "components.TvDetails.manageseries": "Управление на сериали", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "Настройките за известяване към Discord не успяха да бъдат запазени.", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "Вашият акаунт в момента няма зададена парола. Конфигурирайте парола по-долу, за да разрешите влизане като „локален потребител“, използвайки своя имейл адрес.", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "Нова честота", "components.UserList.created": "Присъединиха", "components.Settings.currentlibrary": "Текуща библиотека: {name}", "i18n.resolved": "Разрешен", "components.TvDetails.Season.somethingwentwrong": "Нещо се обърка при извличане на данни за сезона.", "components.StatusChecker.appUpdatedDescription": "Моля, щракнете върху бутона по-долу, за да презаредите приложението.", "components.UserList.bulkedit": "Групово редактиране", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "Трябва да предоставите валиден потребителски или групов ключ", "components.Settings.SettingsMain.toastApiKeyFailure": "Нещо се обърка при генерирането на нов API ключ.", "components.Settings.SettingsLogs.pauseLogs": "Пауза", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "Лимит за заявка на филм", "components.Settings.SonarrModal.toastSonarrTestFailure": "Неуспешно свързване със Sonarr.", "components.Settings.validationUrl": "Трябва да предоставите валиден URL адрес", "i18n.showingresults": "Показани <strong>{from}</strong> до <strong>{to}</strong> от <strong>{total}</strong> резултата", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "Трябва да предоставите текущата си парола", "components.Settings.SettingsMain.originallanguageTip": "Филтрирайте съдържанието по оригинален език", "components.Settings.SettingsMain.cacheImagesTip": "Кеширане на външни изображения (изисква значително количество дисково пространство)", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "Настройки за известията", "components.Settings.noDefaultServer": "Поне един {serverType} сървър трябва да бъде маркиран като стандартен, за да могат {mediaType} заявките да бъдат обработени.", "components.UserList.email": "<PERSON><PERSON><PERSON><PERSON><PERSON> адрес", "i18n.declined": "Отказан", "components.Settings.settingUpPlexDescription": "За да настроите Plex, можете или да въведете подробностите ръчно, или да изберете сървър, извлечен от <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink>. Натиснете бутона вдясно от падащото меню, за да извлечете списъка с налични сървъри.", "components.Settings.plexlibrariesDescription": "Библиотеките, които Overseerr сканира за заглавия. Настройте и запазете вашите настройки за връзката към Plex, след което щракнете върху бутона по-долу, ако няма изброени библиотеки.", "components.TvDetails.seasonnumber": "Сезон {seasonNumber}", "components.TvDetails.seasons": "{seasonCount, plural, one {# Сезон} other {# Сезони}}", "components.Settings.SonarrModal.validationNameRequired": "Трябва да посочите име на сървъра", "i18n.movies": "Филми", "components.Settings.SonarrModal.loadingprofiles": "Зареждат се профилите за качество…", "i18n.testing": "Тествам…", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "Нещо се обърка при запазването на паролата. Вашата текуща парола правилно ли е въведена?", "components.Settings.SettingsUsers.userSettingsDescription": "Конфигурирайте глобалните потребителски настройки и настройките по подразбиране.", "i18n.noresults": "Няма резултати.", "components.Settings.notificationAgentSettingsDescription": "Конфигурирайте и активирайте агенти за уведомяване.", "components.Settings.plex": "Plex", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "Plex потребител", "components.Settings.SonarrModal.create4ksonarr": "Добавяне на нов 4K Sonarr сървър", "components.Settings.SonarrModal.selectLanguageProfile": "Изберете езиков профил", "components.Settings.SettingsLogs.message": "Съобщение", "components.Settings.SettingsMain.generalsettings": "Общи настройки", "components.Settings.tautulliSettings": "Tautulli настройки", "i18n.test": "Тест", "components.Settings.restartrequiredTooltip": "Overseerr трябва да се рестартира, за да влязат в сила промените в тази настройка", "components.Settings.webhook": "Webhook", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "Изпращайте известия без звук", "components.Settings.validationHostnameRequired": "Трябва да предоставите валидно име на хост или IP адрес", "i18n.partiallyavailable": "Частично наличен", "components.UserList.creating": "Създаване…", "components.StatusChecker.appUpdated": "{applicationTitle} Актуализиран", "i18n.requesting": "Заявяване…", "components.Settings.SettingsMain.validationApplicationUrlTrailingSlash": "URL адресът не трябва да завършва с наклонена черта в края", "components.Settings.default": "По подразбиране", "components.Settings.services": "Услуги", "components.Settings.address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.validationHostnameRequired": "Трябва да предоставите валидно име на хост или IP адрес", "components.Setup.continue": "Продължи", "components.Settings.externalUrl": "Външен URL адрес", "components.Settings.SonarrModal.validationPortRequired": "Трябва да предоставите валиден номер на порт", "i18n.back": "Назад", "components.Settings.mediaTypeSeries": "серии", "components.Settings.SonarrModal.defaultserver": "Сървър по подразбиране", "components.Settings.webpush": "Web Push", "components.StatusBadge.openinarr": "Отвори в {arr}", "components.UserProfile.UserSettings.UserGeneralSettings.user": "Потребител", "pages.returnHome": "Обратно към Начало", "components.Settings.scanning": "Синхронизиране…", "components.Settings.SonarrModal.animeSeriesType": "Тип аниме сериал", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "Отмяна на глобалния лимит", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "Текуща парола", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "URL адресът не трябва да завършва с наклонена черта в края", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "Автоматично заявявайте сериали във вашия <PlexWatchlistSupportLink>Plex Списък за гледане</PlexWatchlistSupportLink>", "i18n.edit": "Редакция", "components.Settings.SettingsMain.apikey": "<PERSON> ключ", "components.Settings.menuPlexSettings": "Plex", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink>Започнете чат</TelegramBotLink>, добавете <GetIdBotLink>@get_id_bot</GetIdBotLink>, и изпълнете команда <code>/my_id</code>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "Токен за достъп", "components.Settings.SettingsLogs.time": "Времево клеймо", "components.UserProfile.UserSettings.unauthorizedDescription": "Нямате права, за да промените настройките на този потребител.", "components.Settings.SonarrModal.testFirstRootFolders": "Тествайте връзката за зареждане на основните папки", "i18n.processing": "В обработка", "components.Settings.SettingsMain.generalsettingsDescription": "Конфигурирайте глобалните настройки и настройките по подразбиране за Overseerr.", "components.UserList.password": "Парола", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "Правата са запазени успешно!", "components.Settings.SonarrModal.testFirstQualityProfiles": "Тествайте връзката, за да заредите профилите за качество", "components.Settings.SettingsJobsCache.jobname": "Име на задание", "i18n.previous": "Предишен", "components.Settings.SettingsMain.toastApiKeySuccess": "Нов API ключ е генериран успешно!", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "Трябва да предоставите нова парола", "components.MovieDetails.physicalrelease": "Физическо издание", "components.Settings.toastPlexRefresh": "Списък със сървъри се извлича от Plex…", "components.Settings.SettingsUsers.userSettings": "Потребителски настройки", "components.Settings.SettingsMain.cacheImages": "Активирайте кеширането на изображения", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "Нова парола", "components.Settings.SonarrModal.syncEnabled": "Активирайте сканирането", "components.UserProfile.UserSettings.menuNotifications": "Известия", "i18n.retrying": "Повторно…", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "Потребителски идентификатор (User ID)", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "Настройките за известяване към Pushover са запазени успешно!", "components.Settings.deleteServer": "Изтриване на сървър {serverType}", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "Потребителски или групов ключ", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "Настройките за известяване към Pushbullet не успяха да бъдат запазени.", "components.TvDetails.showtype": "Тип сериали", "components.UserList.localLoginDisabled": "<strong>Локално влизане</strong> настройката в момента е деактивирана.", "i18n.failed": "Неуспешно", "pages.pagenotfound": "Страницата не е намерена", "components.Settings.toastPlexConnecting": "Опит за свързване с Plex…", "components.Settings.SettingsJobsCache.plex-full-scan": "Пълно сканиране на Plex библиотека", "components.Settings.SonarrModal.selecttags": "Изберете етикети", "i18n.next": "Следващ", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "<FindDiscordIdLink>Номерът</FindDiscordIdLink> асоцийран с вашият Discord потребителски профил", "components.UserProfile.UserSettings.UserNotificationSettings.email": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "Нещо се обърка при запазването на паролата.", "components.Settings.SonarrModal.validationProfileRequired": "Трябва да изберете профил за качество", "components.UserList.role": "Роля", "components.Settings.SonarrModal.externalUrl": "Външен URL адрес", "components.Settings.SonarrModal.loadinglanguageprofiles": "Езиковите профили се зареждат…", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "Изпрати безшумно", "i18n.requested": "Заявен", "components.Settings.validationPortRequired": "Трябва да предоставите валиден номер на порт", "i18n.delete": "Изтрий", "components.TvDetails.viewfullcrew": "Вижте целия екип", "components.Settings.activeProfile": "Активен профил", "components.Settings.SonarrModal.seriesType": "Тип сериали", "components.UserProfile.UserSettings.UserPasswordChange.password": "Парола", "components.Settings.serverLocal": "локален", "components.UserProfile.ProfileHeader.settings": "Редактиране на настройките", "i18n.approved": "Одобрен", "components.UserProfile.unlimited": "Неограничен", "i18n.tvshow": "Сериали", "components.Settings.SettingsJobsCache.download-sync": "Синхронизиране на изтеглянията", "components.StatusBadge.status4k": "4K {status}", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserList.userlist": "Списък с потребители", "components.UserProfile.limit": "{remaining} от {limit}", "components.Settings.SettingsJobsCache.editJobScheduleSelectorSeconds": "Всяка {jobScheduleSeconds, plural, one {секунда} other {{jobScheduleSeconds} секунди}}", "components.Settings.deleteserverconfirm": "Сигурни ли сте, че искате да изтриете този сървър?", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "Език на дисплея", "components.TvDetails.watchtrailer": "Гледайте трейлър", "components.Settings.serverpreset": "Сървър", "components.Settings.SonarrModal.servername": "Име на сървъра", "components.Settings.SonarrModal.languageprofile": "Езиков профил", "components.Settings.menuUsers": "Потребители", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "Автоматично заявявайте филми във вашия <PlexWatchlistSupportLink>Plex Списък за гледане</PlexWatchlistSupportLink>", "components.Settings.validationUrlBaseLeadingSlash": "URL адреса трябва да има водеща наклонена черта", "components.StatusBadge.managemedia": "Управление на {mediaType}", "components.UserList.deleteuser": "Изтриване на потребител", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "Нещо се обърка при запазване на настройките.", "components.TvDetails.TvCast.fullseriescast": "Пълен актьорски състав на сериала", "components.StatusBadge.status": "{status}", "components.Setup.finishing": "Завършва се…", "components.Settings.SettingsJobsCache.imagecachecount": "Изображенията са кеширани", "components.Settings.serverpresetLoad": "Натиснете бутона, за да заредите наличните сървъри", "components.Settings.email": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.TvDetails.status4k": "4K {status}", "components.Settings.SonarrModal.animequalityprofile": "Профил за качество на аниме", "components.Settings.SettingsUsers.localLogin": "Активиране на локално влизане", "components.Settings.menuLogs": "Лог файлове", "components.Settings.sonarrsettings": "Sonarr настройки", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "Показвано име", "components.Settings.validationUrlTrailingSlash": "URL адресът не трябва да завършва с наклонена черта в края", "components.Settings.addsonarr": "Добавяне на Sonarr сървър", "components.Settings.plexlibraries": "Plex Библиотеки", "components.UserList.passwordinfodescription": "Конфигурирайте URL адрес на приложение и активирайте известия по имейл, за да позволите автоматично генериране на парола.", "components.UserList.sortCreated": "Дата на присъединяване", "components.Settings.SettingsMain.applicationurl": "URL адрес на приложението", "components.Settings.SettingsLogs.label": "Етикет", "i18n.loading": "Зареждане…", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "Настройките за известяване към Pushover не успяха да бъдат запазени.", "components.Settings.SettingsLogs.level": "Тежест", "components.UserProfile.movierequests": "Заявки за филми", "components.UserProfile.pastdays": "{type} (преди {days} дни)", "components.Settings.SettingsMain.general": "<PERSON><PERSON><PERSON>", "components.UserProfile.ProfileHeader.userid": "Потребителски идентификатор: {userid}", "components.Settings.SettingsJobsCache.radarr-scan": "Radarr сканиране", "components.UserList.importfromplex": "Импортиране на потребители на Plex", "components.Settings.SonarrModal.port": "Порт", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "Създайте токен от вашите <PushbulletSettingsLink>Настройки на акаунта</PushbulletSettingsLink>", "components.UserList.userdeleted": "Потребителят е изтрит успешно!", "components.Settings.serverSecure": "сигурен", "components.Setup.finish": "Завършете настройката", "components.Settings.experimentalTooltip": "Активирането на тази настройка може да доведе до неочаквано поведение на приложението", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "Не можете да променяте собствените си права.", "components.UserProfile.UserSettings.UserNotificationSettings.sound": "Звук за известяване", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "Базовият URL адрес трябва да има водеща наклонена черта", "components.Settings.serverpresetRefreshing": "Сървърите се получават…", "components.Settings.SonarrModal.testFirstLanguageProfiles": "Тествайте връзката за зареждане на езикови профили", "i18n.request4k": "Заявка в 4K", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname} е отменено.", "components.UserProfile.seriesrequest": "Заявки за сериали", "components.Settings.SettingsLogs.filterDebug": "Дебъг", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "Паролата е твърде кратка; трябва да съдържа минимум 8 знака", "components.Settings.SonarrModal.validationRootFolderRequired": "Трябва да изберете основна папка", "components.Setup.signinMessage": "Започнете, като влезете с вашия Plex акаунт", "components.Settings.manualscan": "Ръчно сканиране на библиотека", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "<FindDiscordIdLink>ID номерът</FindDiscordIdLink> асоцийран с вашият потребителски акаунт", "components.Settings.SettingsMain.partialRequestsEnabled": "Разрешаване на частични заявки за сериали", "components.Settings.SonarrModal.hostname": "Име на хост или IP адрес", "components.Setup.setup": "Настройване", "components.UserProfile.emptywatchlist": "Мултимедията, добавена към вашия <PlexWatchlistSupportLink>списък за гледане в Plex</PlexWatchlistSupportLink>, ще се появи тук.", "components.Settings.enablessl": "Използвай SSL", "components.Settings.SettingsUsers.localLoginTip": "Позволете на потребителите да влизат, като използват своя имейл адрес и парола", "components.Settings.noDefaultNon4kServer": "Ако имате само един сървър {serverType} както за съдържание, което не е 4K, така и за 4K (или ако изтегляте само 4K съдържание), вашият сървър {serverType} трябва <strong>ДА НЕ БЪДЕ</strong> обозначен като 4K сървър.", "components.UserList.nouserstoimport": "Няма Plex потребители за импортиране.", "components.UserProfile.ProfileHeader.profile": "Виж профил", "components.Setup.welcome": "Добре дошли в Overseerr", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "Лимит за заявки на сериали", "components.TitleCard.mediaerror": "{mediaType} не е намерен", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "Вашият 30-<PERSON><PERSON><PERSON><PERSON> <UsersGroupsLink>идентификатор на потребител или група</UsersGroupsLink>", "components.UserProfile.recentlywatched": "Наскоро гледани", "components.UserList.users": "Потребители", "components.Settings.SettingsJobsCache.jobsandcache": "Задания и кеш", "components.Settings.SettingsJobsCache.jobsDescription": "Overseerr изпълнява определени задачи по поддръжката като редовно планирани задачи, но те също могат да бъдат ръчно задействани по-долу. Ръчното изпълнение на задание няма да промени неговия график.", "components.Settings.SonarrModal.animeTags": "Етикети за аниме", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "Коди<PERSON>ай имейлите използвайки <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.SonarrModal.baseUrl": "Базов URL адрес", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "Филтрирайте съдържанието по оригинален език", "components.Settings.toastPlexConnectingSuccess": "Връзката с Plex е установена успешно!", "components.UserProfile.UserSettings.menuGeneralSettings": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.extraData": "Допълнителни данни", "components.UserList.plexuser": "Plex потребител", "components.UserProfile.plexwatchlist": "Plex списък за гледане", "components.TvDetails.streamingproviders": "В момента се излъчва по", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "Всеки {jobScheduleHours, plural, one {час} other {{jobScheduleHours} часа}}", "components.TvDetails.originaltitle": "Оригинално заглавие", "components.Settings.noDefault4kServer": "4K {serverType} сървър трябва да бъде маркиран като стандартен, за да може потребителите да изпращат 4K {mediaType} заявки.", "components.Settings.SettingsUsers.tvRequestLimitLabel": "Глоба<PERSON>ен лимит за заявка на сериали", "components.Settings.SettingsMain.toastSettingsFailure": "Нещо се обърка при запазване на настройките.", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "По подразбиране ({language})", "components.Settings.validationUrlBaseTrailingSlash": "URL адресът не трябва да завършва с наклонена черта в края", "components.Settings.SettingsJobsCache.imagecacheDescription": "Когато е активиран в настройките, Overseerr ще бъде прокси и ще кешира изображения от предварително конфигурирани външни източници. Кешираните изображения се записват във вашата конфигурационна папка. Можете да намерите файловете в <code>{appDataPath}/cache/images</code>.", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "Настройките за известяване чрез Web push са запазени успешно!", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "PGP публичен ключ", "components.TitleCard.cleardata": "Изчистване на данните", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "Нямате права, за да промените паролата на този потребител.", "components.Settings.toastTautulliSettingsFailure": "Нещо се обърка при запазване на настройките на Tautulli.", "pages.internalservererror": "Вътрешна грешка в сървъра", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "<ApplicationRegistrationLink>Регистриране на приложение</ApplicationRegistrationLink> за използване с {applicationTitle}", "components.Settings.SettingsJobsCache.nextexecution": "Следващо изпълнение", "components.UserList.totalrequests": "Заявки", "i18n.close": "Затвори", "components.Settings.SettingsMain.validationApplicationUrl": "Трябва да предоставите валиден URL адрес", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "Общи настройки", "components.TvDetails.cast": "Участват", "i18n.approve": "Одобряване", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "Локален потребител", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "Нещо се обърка при запазване на заданието.", "i18n.usersettings": "Потребителски настройки", "components.Settings.menuServices": "Услуги", "components.Settings.SonarrModal.enableSearch": "Активирайте автоматичното търсене", "i18n.cancel": "Отказ", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "Трябва да потвърдите новата парола", "components.UserList.usercreatedfailedexisting": "Предоставеният имейл адрес вече се използва от друг потребител.", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "Собственик", "components.TitleCard.tvdbid": "Идентификатор за TheTVDB", "components.Settings.serverRemote": "отдалечен", "components.UserProfile.UserSettings.menuChangePass": "Парола", "i18n.experimental": "Експериментален", "components.Settings.SettingsJobsCache.editJobSchedule": "Промяна на задание", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "Трябва да предоставите валиден chat ID", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "Успешно записана паролата!", "components.UserProfile.recentrequests": "Последни заявки", "i18n.unavailable": "Неналичен", "components.Settings.SettingsUsers.defaultPermissions": "Права по подразбиране", "pages.oops": "Опааа", "components.TvDetails.episodeRuntimeMinutes": "{runtime} минути", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "Филтрирайте съдържанието по регионална наличност", "components.Settings.SonarrModal.testFirstTags": "Тествайте връзката за зареждане на тагове", "components.UserProfile.UserSettings.UserPermissions.permissions": "Права", "components.UserList.autogeneratepassword": "Автоматично генериране на парола", "components.Settings.webAppUrlTip": "По избор насочете потребителите към уеб приложението на вашия сървър вместо към „хостваното“ уеб приложение", "components.Settings.SettingsMain.validationApplicationTitle": "Трябва да посочите заглавие на приложението", "components.Settings.ssl": "SSL", "components.Settings.SettingsLogs.copiedLogMessage": "Лог файл е копиран в клипборда.", "components.UserList.importedfromplex": "<strong>{userCount}</strong> Plex {userCount, plural, one {потребител} other {потребители}} импортиран(и) успешно!", "i18n.status": "Статус", "components.Settings.SonarrModal.ssl": "Използвай SSL", "components.TvDetails.originallanguage": "Ориг<PERSON><PERSON><PERSON>ен език", "components.Settings.SettingsJobsCache.download-sync-reset": "Нулиране на синхронизирането на изтеглянията", "components.UserList.usercreatedfailed": "Нещо се обърка при създаването на потребителя.", "components.TvDetails.overview": "Преглед", "components.Settings.plexsettings": "Plex Настройки", "components.Settings.menuJobs": "Задания и кеш", "components.Settings.SettingsUsers.newPlexLogin": "Активиране на ново влизане в Plex", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Настройките за известяване към Discord са запазени успешно!", "i18n.settings": "Настройки", "components.Login.back": "Обратно", "component.BlacklistBlock.blacklistdate": "Дата на добаване в черния списък", "components.Discover.FilterSlideover.status": "Статус", "components.Layout.Sidebar.blacklist": "<PERSON>е<PERSON><PERSON><PERSON> списък", "components.Layout.UserWarnings.emailInvalid": "Невалиден имейл адрес.", "components.Layout.UserWarnings.emailRequired": "Трябва да предоставите имейл адрес.", "components.Blacklist.blacklistNotFoundError": "<strong>{title}</strong> не е в черния списък.", "components.Blacklist.blacklistdate": "дата", "components.DiscoverTvUpcoming.upcomingtv": "Предстоящи сериали", "components.Login.credentialerror": "Въведено неправилно име или парола.", "components.Blacklist.mediaTmdbId": "TMDB идентификатор", "components.Blacklist.mediaType": "Тип", "components.Layout.UserWarnings.passwordRequired": "Необхода е парола.", "components.Login.adminerror": "Трябва да използвате администраторски акаунт при вписване.", "component.BlacklistBlock.blacklistedby": "Добавено от", "component.BlacklistModal.blacklisting": "Добавяне в черния списък", "components.Blacklist.blacklistSettingsDescription": "Управления на медия в черния списък.", "components.Blacklist.blacklistedby": "{date} от {user}", "components.Blacklist.blacklistsettings": "Настройки на черния списък", "components.Blacklist.mediaName": "Заглавие", "components.ManageSlideOver.manageModalRemoveMediaWarning": "* Това ще премахне необратимо този/тази {mediaType} от {arr}, заедно с всички свързани файлове.", "components.Login.noadminerror": "На сървъра не е открит администратор.", "components.Login.validationemailformat": "Изисква се валиден имейл адрес", "components.Login.username": "Потребителско име", "components.Login.validationhostformat": "Изисква се валиден URL адрес", "components.Login.validationHostnameRequired": "Трябва да въведете валидно име на хост или IP адрес", "components.Login.validationUrlBaseTrailingSlash": "Базовият URL адрес не трябва да завършва с наклонена черта", "components.Login.validationhostrequired": "Изисква се {mediaServerName} URL адрес", "components.Login.description": "Тъй като това е първото Ви влизане в {applicationName}, трябва да добавите валиден имейл адрес.", "components.Login.emailtooltip": "Не е необходимо имейл адресът да бъде свързан с вашия {mediaServerName} сървър.", "components.Login.enablessl": "Използвай SSL", "components.Login.hostname": "{mediaServerName} URL", "components.Login.initialsignin": "Свързване", "components.Login.initialsigningin": "Установява се връзка…", "components.Login.invalidurlerror": "Не може да се осъществи връзка със сървъра {mediaServerName}.", "components.Login.loginwithapp": "Влез със {appName}", "components.Login.orsigninwith": "Или влез със", "components.Login.port": "Порт", "components.Login.save": "Добави", "components.Login.servertype": "Тип на сървъра", "components.Login.signinwithjellyfin": "Използвай своя {mediaServerName} акаунт", "components.Login.title": "Добави имейл", "components.Login.urlBase": "Основен URL", "components.Login.validationEmailFormat": "Невалиден имейл адрес", "components.Login.validationEmailRequired": "Трябва да въведете имейл адрес", "components.Login.validationPortRequired": "Трябва да въведете валиден номер на порт", "components.Login.validationUrlBaseLeadingSlash": "Базовият URL адрес трявба да започва със наклонена черта", "components.Login.validationUrlTrailingSlash": "URL адресът не трябва да завършва с наклонена черта", "components.Login.validationservertyperequired": "Моля изберете тип на сървъра", "components.Login.validationusernamerequired": "Изисква се потребителско име", "components.Login.saving": "Добавяне…"}