{"components.Settings.notificationsettings": "Nastavení oznámení", "components.Settings.enablessl": "Použít SSL", "components.Settings.default4k": "Výchozí 4K", "components.Settings.cancelscan": "Zrušit skenování", "components.Settings.activeProfile": "Aktivní profil", "components.Settings.SonarrModal.syncEnabled": "Povolit skenování", "components.Settings.SonarrModal.ssl": "Použít SSL", "components.Settings.SonarrModal.servername": "N<PERSON>zev serveru", "components.Settings.SonarrModal.server4k": "4K server", "components.Settings.SonarrModal.selecttags": "Vybert<PERSON>", "components.Settings.SonarrModal.seasonfolders": "Složky pro série", "components.Settings.SonarrModal.rootfolder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.qualityprofile": "<PERSON>il <PERSON>", "components.Settings.SonarrModal.notagoptions": "<PERSON><PERSON><PERSON><PERSON>.", "components.Settings.SonarrModal.loadingTags": "Načí<PERSON><PERSON><PERSON>…", "components.Settings.SonarrModal.languageprofile": "Jazykový profil", "components.Settings.SonarrModal.externalUrl": "Externí URL", "components.Settings.SonarrModal.defaultserver": "Výchozí server", "components.Settings.SonarrModal.apiKey": "API klíč", "components.Settings.SonarrModal.animeTags": "<PERSON><PERSON>", "components.Settings.SonarrModal.add": "Přidat server", "components.Settings.SettingsUsers.userSettings": "Uživatelské nastavení", "components.Settings.SettingsUsers.defaultPermissions": "Výchozí oprávnění", "components.Settings.SettingsJobsCache.unknownJob": "Neznámá <PERSON>loha", "components.Settings.SettingsJobsCache.sonarr-scan": "<PERSON><PERSON><PERSON> sken", "components.Settings.SettingsJobsCache.runnow": "Spustit nyní", "components.Settings.SettingsJobsCache.radarr-scan": "<PERSON><PERSON> sken", "components.Settings.SettingsJobsCache.jobstarted": "{jobname} zahájeno.", "components.Settings.SettingsJobsCache.jobname": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname} <PERSON><PERSON><PERSON><PERSON>.", "components.Settings.SettingsJobsCache.flushcache": "Vyprázdnit mezipaměť", "components.Settings.SettingsJobsCache.canceljob": "Zrušit <PERSON>", "components.Settings.SettingsJobsCache.cachevsize": "Velikost hodnoty", "components.Settings.SettingsJobsCache.cachename": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.totalrequests": "Celkový počet žádostí", "components.Settings.SettingsAbout.totalmedia": "Celkový počet médií", "components.Settings.SettingsAbout.timezone": "Časové p<PERSON>", "components.Settings.SettingsAbout.supportoverseerr": "Podpoř<PERSON>", "components.Settings.SettingsAbout.overseerrinformation": "<PERSON> <PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.githubdiscussions": "Diskuze na GitHubu", "components.Settings.SettingsAbout.Releases.viewchangelog": "Zobrazit seznam změn", "components.Settings.SettingsAbout.Releases.versionChangelog": "{version} Seznam změn", "components.Settings.SettingsAbout.Releases.currentversion": "Aktuální", "components.Settings.RadarrModal.syncEnabled": "Povolit skenování", "components.Settings.RadarrModal.ssl": "Použít SSL", "components.Settings.RadarrModal.servername": "N<PERSON>zev serveru", "components.Settings.RadarrModal.server4k": "4K server", "components.Settings.RadarrModal.selecttags": "Vybert<PERSON>", "components.Settings.RadarrModal.rootfolder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.qualityprofile": "<PERSON>il <PERSON>", "components.Settings.RadarrModal.notagoptions": "<PERSON><PERSON><PERSON><PERSON>.", "components.Settings.RadarrModal.minimumAvailability": "Minimální dostupnost", "components.Settings.RadarrModal.loadingTags": "Načí<PERSON><PERSON><PERSON>…", "components.Settings.RadarrModal.externalUrl": "Externí URL", "components.Settings.RadarrModal.defaultserver": "Výchozí server", "components.Settings.RadarrModal.apiKey": "API klíč", "components.Settings.RadarrModal.add": "Přidat server", "components.Settings.Notifications.webhookUrl": "Webhook URL", "components.Settings.Notifications.smtpPort": "SMTP Port", "components.Settings.Notifications.smtpHost": "SMTP Host", "components.Settings.Notifications.senderName": "Jméno odesílatele", "components.Settings.Notifications.sendSilently": "<PERSON><PERSON><PERSON>", "components.Settings.Notifications.pgpPassword": "PGP heslo", "components.Settings.Notifications.encryption": "Metoda šifrování", "components.Settings.Notifications.emailsender": "<PERSON><PERSON><PERSON>", "components.Settings.Notifications.chatId": "ID chatu", "components.Settings.Notifications.botUsername": "Jméno bota", "components.Settings.Notifications.authUser": "SMTP uživatelské jméno", "components.Settings.Notifications.authPass": "SMTP Heslo", "components.Settings.Notifications.agentenabled": "Povolit agenta", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "Webhook URL", "components.Settings.Notifications.NotificationsWebhook.customJson": "JSON Payload", "components.Settings.Notifications.NotificationsWebhook.authheader": "Autorizační hlavička", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "Povolit agenta", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "Povolit agenta", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "Webhook URL", "components.Settings.Notifications.NotificationsSlack.agentenabled": "Povolit agenta", "components.Settings.Notifications.NotificationsPushover.agentenabled": "Povolit agenta", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "Povolit agenta", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> token", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "Webhook URL", "components.Settings.Notifications.NotificationsLunaSea.profileName": "Jméno profilu", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "Povolit agenta", "components.Search.searchresults": "Výsledky vyhledávání", "components.ResetPassword.passwordreset": "Obnoven<PERSON> hesla", "components.ResetPassword.email": "E-mailová adresa", "components.ResetPassword.confirmpassword": "Potvrďte he<PERSON>", "components.RequestModal.selectseason": "<PERSON><PERSON><PERSON><PERSON> série", "components.RequestModal.seasonnumber": "{number} Série", "components.RequestModal.edit": "Upravit <PERSON>", "components.RequestModal.cancel": "Zrušit <PERSON>", "components.RequestModal.autoapproval": "<PERSON><PERSON><PERSON>", "components.RequestModal.alreadyrequested": "<PERSON><PERSON> vyžádáno", "components.RequestModal.AdvancedRequester.selecttags": "Vybrat z<PERSON>ky", "components.RequestModal.AdvancedRequester.rootfolder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.requestas": "Zažádat jako", "components.RequestModal.AdvancedRequester.qualityprofile": "<PERSON>il <PERSON>", "components.RequestModal.AdvancedRequester.notagoptions": "<PERSON><PERSON><PERSON><PERSON>.", "components.RequestModal.AdvancedRequester.languageprofile": "Jazykový profil", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.RequestModal.AdvancedRequester.destinationserver": "C<PERSON>lový server", "components.RequestModal.AdvancedRequester.default": "{name} (výchozí)", "components.RequestList.sortModified": "Naposledy změněno", "components.RequestList.sortAdded": "Nejnovější", "components.RequestList.RequestItem.editrequest": "Upravit <PERSON>", "components.RequestList.RequestItem.deleterequest": "Odstranit žádost", "components.RequestList.RequestItem.cancelRequest": "Zrušit <PERSON>", "components.RequestCard.deleterequest": "Odstranit žádost", "components.RequestButton.viewrequest": "Zobrazit žádost", "components.RequestButton.requestmore": "Vyžádat více", "components.RequestButton.declinerequest": "Odmítnout žádost", "components.RequestButton.approverequest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestBlock.server": "C<PERSON>lový server", "components.RequestBlock.rootfolder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestBlock.requestoverrides": "Přepsání požadavku", "components.RequestBlock.profilechanged": "<PERSON>il <PERSON>", "components.RegionSelector.regionServerDefault": "Výchozí ({region})", "components.RegionSelector.regionDefault": "Všechny regiony", "components.PersonDetails.birthdate": "<PERSON><PERSON><PERSON> {birthdate}", "components.PersonDetails.ascharacter": "jako {character}", "components.PermissionEdit.viewrequests": "Zobrazit ž<PERSON>dosti", "components.PermissionEdit.users": "Spravovat uživatele", "components.PermissionEdit.requestTv": "<PERSON><PERSON><PERSON><PERSON>", "components.PermissionEdit.requestMovies": "Vyžádat filmy", "components.PermissionEdit.request4k": "Vyžádat ve 4K", "components.PermissionEdit.managerequests": "Spravovat žádosti", "components.PermissionEdit.autoapproveSeries": "<PERSON><PERSON> sch<PERSON>t se<PERSON>", "components.PermissionEdit.autoapproveMovies": "<PERSON>ky sch<PERSON>ovat filmy", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "Oznámení", "components.UserProfile.UserSettings.UserNotificationSettings.email": "E-mail", "components.UserProfile.UserSettings.UserGeneralSettings.user": "<PERSON>živatel", "components.UserProfile.UserSettings.UserGeneralSettings.role": "Role", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "Vlastník", "components.UserProfile.UserSettings.UserGeneralSettings.general": "Obecné", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "Admin", "components.UserList.users": "Uživatelé", "components.UserList.user": "<PERSON>živatel", "components.UserList.totalrequests": "<PERSON><PERSON><PERSON><PERSON>", "components.UserList.role": "Role", "components.UserList.password": "He<PERSON><PERSON>", "components.UserList.owner": "Vlastník", "components.UserList.creating": "Vytváření…", "components.UserList.created": "Vytvořeno", "components.UserList.create": "Vytvořit", "components.UserList.admin": "Admin", "components.UserList.accounttype": "<PERSON><PERSON>", "components.TvDetails.recommendations": "Doporučení", "components.TvDetails.overview": "<PERSON><PERSON><PERSON><PERSON>", "components.TvDetails.cast": "Obsazení", "components.TvDetails.anime": "Anime", "components.Setup.setup": "Konfigurace", "components.Setup.finishing": "Dokončování…", "components.Setup.continue": "Po<PERSON><PERSON><PERSON><PERSON>", "components.Settings.webhook": "Webhook", "components.Settings.ssl": "SSL", "components.Settings.services": "Služby", "components.Settings.serverpreset": "Server", "components.Settings.serverSecure": "zabezpečené", "components.Settings.serverRemote": "vzdálený", "components.Settings.serverLocal": "místní", "components.Settings.scanning": "Synchronizace…", "components.Settings.port": "Port", "components.Settings.plex": "Plex", "components.Settings.notifications": "Oznámení", "components.Settings.menuUsers": "Uživatelé", "components.Settings.menuServices": "Služby", "components.Settings.menuPlexSettings": "Plex", "components.Settings.menuNotifications": "Oznámení", "components.Settings.menuLogs": "Záznamy", "components.Settings.menuGeneralSettings": "Obecné", "components.Settings.menuAbout": "O aplikaci", "components.Settings.mediaTypeSeries": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.mediaTypeMovie": "film", "components.Settings.is4k": "4K", "components.Settings.email": "E-mail", "components.Settings.default": "Výchozí", "components.Settings.address": "Ad<PERSON>y", "components.Settings.SonarrModal.tags": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.port": "Port", "components.Settings.SettingsUsers.users": "Uživatelé", "components.Settings.SettingsLogs.time": "<PERSON><PERSON><PERSON> z<PERSON>č<PERSON>", "components.Settings.SettingsLogs.resumeLogs": "Po<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.pauseLogs": "<PERSON><PERSON>", "components.Settings.SettingsLogs.message": "Zpráva", "components.Settings.SettingsLogs.logs": "Záznamy", "components.Settings.SettingsLogs.level": "Závažnost", "components.Settings.SettingsLogs.label": "Štítek", "components.Settings.SettingsLogs.filterWarn": "Varování", "components.Settings.SettingsLogs.filterInfo": "Informace", "components.Settings.SettingsLogs.filterError": "Chyba", "components.Settings.SettingsLogs.filterDebug": "Ladění", "components.Settings.SettingsJobsCache.process": "Proces", "components.Settings.SettingsJobsCache.jobtype": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobs": "Úkoly", "components.Settings.SettingsJobsCache.command": "Příkaz", "components.Settings.SettingsJobsCache.cachehits": "Úspěchy", "components.Settings.SettingsJobsCache.cache": "Mezipaměť", "components.Settings.SettingsAbout.version": "Verze", "components.Settings.SettingsAbout.preferredmethod": "Preferované", "components.Settings.SettingsAbout.documentation": "Dokumentace", "components.Settings.SettingsAbout.about": "O aplikaci", "components.Settings.SettingsAbout.Releases.releases": "Verze", "components.Settings.SettingsAbout.Releases.latestversion": "Nejnovější", "components.Settings.RadarrModal.tags": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.port": "Port", "components.Settings.Notifications.encryptionNone": "<PERSON><PERSON><PERSON><PERSON>", "components.Search.search": "Vyhledat", "components.ResetPassword.password": "He<PERSON><PERSON>", "components.RequestModal.season": "Série", "components.RequestModal.QuotaDisplay.season": "série", "components.RequestModal.QuotaDisplay.movie": "film", "components.RequestModal.AdvancedRequester.tags": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.advancedoptions": "Pokroč<PERSON><PERSON>", "components.RequestList.requests": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.requesteddate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.requested": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.modified": "Upraveno", "components.QuotaSelector.unlimited": "<PERSON><PERSON><PERSON><PERSON>", "components.PersonDetails.crewmember": "<PERSON><PERSON><PERSON> profese", "components.PersonDetails.appearsin": "Vystoupení", "components.PermissionEdit.request": "Z<PERSON>ž<PERSON><PERSON><PERSON>", "components.PermissionEdit.autoapprove4kSeries": "Automatické schválení 4K seriálů", "components.NotificationTypeSelector.usermediaapprovedDescription": "Získat upozornění na schválení vašich žádostí o média.", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "Zís<PERSON> upozornění, <PERSON><PERSON><PERSON> ostatní uživatelé zadají nové požadavky na média, k<PERSON><PERSON> jsou automaticky schválena.", "components.NotificationTypeSelector.mediarequestedDescription": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> zažádají o média vyžadující schválení.", "components.MovieDetails.streamingproviders": "Aktuálně streamované na", "pages.serviceunavailable": "Služba není k dispozici", "pages.returnHome": "<PERSON><PERSON><PERSON><PERSON><PERSON> se domů", "pages.pagenotfound": "<PERSON><PERSON><PERSON><PERSON> ne<PERSON>", "pages.oops": "<PERSON><PERSON><PERSON>", "pages.internalservererror": "Interní ch<PERSON>", "pages.errormessagewithcode": "{statusCode} - {error}", "i18n.view": "Zobrazit", "i18n.usersettings": "Uživatelské nastavení", "i18n.unavailable": "Nedostupné", "i18n.tvshows": "<PERSON><PERSON><PERSON><PERSON>", "i18n.tvshow": "Seriál", "i18n.testing": "<PERSON><PERSON><PERSON>…", "i18n.test": "<PERSON><PERSON><PERSON><PERSON>", "i18n.status": "Stav", "i18n.showingresults": "<PERSON><PERSON><PERSON><PERSON>ji <strong>{from}</strong> do <strong>{to}</strong> <strong>{total}</strong> v<PERSON><PERSON><PERSON><PERSON>", "i18n.settings": "Nastavení", "i18n.saving": "Ukládání…", "i18n.save": "Uložit změny", "i18n.retrying": "Opakování…", "i18n.retry": "Opakovat", "i18n.resultsperpage": "Zobrazit {pageSize} výsledků na stránku", "i18n.requesting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>…", "i18n.requested": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.request4k": "Zažádat ve 4K", "i18n.request": "Z<PERSON>ž<PERSON><PERSON><PERSON>", "i18n.processing": "Zpracovává se", "i18n.previous": "Předchozí", "i18n.pending": "Čekající", "i18n.partiallyavailable": "Částečně k dispozici", "i18n.notrequested": "<PERSON><PERSON><PERSON><PERSON>", "i18n.noresults": "Žádné v<PERSON>ky.", "i18n.next": "Dalš<PERSON>", "i18n.movies": "Filmy", "i18n.movie": "Film", "i18n.loading": "Načít<PERSON><PERSON>…", "i18n.failed": "<PERSON><PERSON><PERSON><PERSON>", "i18n.experimental": "Experimentální", "i18n.edit": "<PERSON><PERSON><PERSON><PERSON>", "i18n.delimitedlist": "{a}, {b}", "i18n.deleting": "Odstraňování…", "i18n.delete": "Odstranit", "i18n.declined": "Odmí<PERSON>nut<PERSON>", "i18n.decline": "Odmítnout", "i18n.close": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.canceling": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "i18n.cancel": "Zrušit", "i18n.back": "<PERSON><PERSON><PERSON><PERSON>", "i18n.available": "<PERSON> dispozici", "i18n.areyousure": "Jste si jistý?", "i18n.approved": "Schv<PERSON>len<PERSON>", "i18n.approve": "Schv<PERSON><PERSON>", "i18n.all": "<PERSON><PERSON><PERSON>", "i18n.advanced": "Pokroč<PERSON><PERSON>", "components.UserProfile.unlimited": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.totalrequests": "Celkový počet žádostí", "components.UserProfile.seriesrequest": "Ser<PERSON><PERSON><PERSON>", "components.UserProfile.requestsperdays": "<PERSON>býv<PERSON> {limit}", "components.UserProfile.recentrequests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.menuPermissions": "Oprávnění", "components.UserProfile.UserSettings.menuNotifications": "Oznámení", "components.UserProfile.UserSettings.menuGeneralSettings": "Obecné", "components.UserProfile.UserSettings.menuChangePass": "He<PERSON><PERSON>", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "Vlastní oprávnění nelze upravovat.", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "Oprávnění byla úspěšně uložena!", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "Při ukládání nastavení se něco pokazilo.", "components.UserProfile.UserSettings.UserPermissions.permissions": "Oprávnění", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "<PERSON><PERSON><PERSON> je <PERSON><PERSON><PERSON><PERSON> k<PERSON>; m<PERSON>lo by m<PERSON><PERSON> <PERSON> 8 znaků", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "Musíte zadat nové he<PERSON>lo", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "Musíte zadat své aktuální he<PERSON>lo", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "<PERSON><PERSON> se musí shodovat", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "<PERSON>s<PERSON><PERSON> potvrdit nové heslo", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "Heslo úspěšně uloženo!", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "<PERSON><PERSON>i ukládání hesla se něco pokazilo. <PERSON><PERSON> vaše aktuální heslo zadáno správně?", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "<PERSON><PERSON><PERSON>ání hesla se něco pokazilo.", "components.UserProfile.UserSettings.UserPasswordChange.password": "He<PERSON><PERSON>", "pages.somethingwentwrong": "Něco se pokazilo", "components.PermissionEdit.autoapprove4kMovies": "<PERSON><PERSON><PERSON> s<PERSON>ní 4K filmů", "components.PermissionEdit.autoapprove4k": "Automatické schválení 4K", "components.PermissionEdit.autoapprove": "<PERSON><PERSON><PERSON>", "components.PermissionEdit.advancedrequest": "Pokroč<PERSON><PERSON>", "components.PermissionEdit.admin": "Admin", "components.NotificationTypeSelector.notificationTypes": "<PERSON><PERSON>", "components.NotificationTypeSelector.mediarequested": "Žádost čeká na schválení", "components.NotificationTypeSelector.mediafailedDescription": "Odeslat oznámení, k<PERSON><PERSON> se nepodaří přidat požadavky na média do Radarru nebo Sonarru.", "components.NotificationTypeSelector.mediafailed": "Zpracování požadavku se nezdařilo", "components.NotificationTypeSelector.mediadeclinedDescription": "<PERSON><PERSON><PERSON>, pokud jsou požadavky na média odmítnuty.", "components.NotificationTypeSelector.mediadeclined": "Žádost zamítnuta", "components.NotificationTypeSelector.mediaavailableDescription": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> o média dostupné.", "components.NotificationTypeSelector.mediaavailable": "Ž<PERSON>dost je dostupná", "components.NotificationTypeSelector.mediaapprovedDescription": "Odes<PERSON><PERSON>, <PERSON><PERSON><PERSON> o média ručně schváleny.", "components.NotificationTypeSelector.mediaapproved": "<PERSON><PERSON><PERSON><PERSON>", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "Odes<PERSON><PERSON>, <PERSON><PERSON><PERSON> odešlou nové žádosti o média, k<PERSON><PERSON> jsou <PERSON> schváleny.", "components.NotificationTypeSelector.mediaAutoApproved": "Žádost automaticky schválena", "components.MovieDetails.watchtrailer": "Sledovat upoutávku", "components.MovieDetails.viewfullcrew": "Zobrazit kompletní štáb", "components.MovieDetails.similar": "<PERSON><PERSON><PERSON><PERSON><PERSON> tituly", "components.MovieDetails.showmore": "Zobrazit více", "components.MovieDetails.showless": "Zobrazit méně", "components.MovieDetails.runtime": "{minutes} {minutes, plural, one {minuta} few {minuty} other {minut}}", "components.MovieDetails.revenue": "<PERSON><PERSON><PERSON>", "components.MovieDetails.releasedate": "{releaseCount, plural, one {Datum vydání} other {Data vydání}}", "components.MovieDetails.recommendations": "Doporučení", "components.MovieDetails.play": "Přehrát v {mediaServerName}", "components.MovieDetails.play4k": "Přeh<PERSON><PERSON><PERSON> v {mediaServerName} ve 4K", "components.MovieDetails.overviewunavailable": "<PERSON><PERSON><PERSON><PERSON> není k dispozici.", "components.MovieDetails.overview": "<PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.originaltitle": "Původní název", "components.MovieDetails.originallanguage": "Původní jazyk", "components.MovieDetails.markavailable": "Označit jako <PERSON>", "components.MovieDetails.mark4kavailable": "Označit jako dostupné ve 4K", "components.MovieDetails.cast": "Obsazení", "components.MovieDetails.budget": "Rozpočet", "components.MovieDetails.MovieCast.fullcast": "Kompletní obsazení", "components.MovieDetails.MovieCrew.fullcrew": "Kompletní <PERSON>", "components.MediaSlider.ShowMoreCard.seemore": "Zobrazit více", "components.Login.validationpasswordrequired": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>t he<PERSON>lo", "components.Login.validationemailrequired": "Musíte zadat platnou e-mailovou adresu", "components.Login.signinwithplex": "Použijte svůj účet Plex", "components.Login.signinwithoverseerr": "Použijte svůj účet {applicationTitle}", "components.Login.signinheader": "Pro pokračování se přihlaste", "components.Login.signingin": "Přihlašování…", "components.Login.signin": "Přihlásit se", "components.Login.password": "He<PERSON><PERSON>", "components.Login.loginerror": "Při pokusu o přihlášení se něco pokazilo.", "components.Login.forgotpassword": "Zapomenut<PERSON> he<PERSON>lo?", "components.Login.email": "E-mailová adresa", "components.Layout.VersionStatus.streamstable": "Stabilní verze <PERSON>", "components.Layout.VersionStatus.streamdevelop": "Vývojová verze <PERSON>", "components.Layout.VersionStatus.outofdate": "Z<PERSON><PERSON><PERSON><PERSON>", "components.Layout.UserDropdown.signout": "Odhlásit se", "components.Layout.UserDropdown.settings": "Nastavení", "components.Layout.UserDropdown.myprofile": "Profil", "components.Layout.Sidebar.users": "Uživatelé", "components.Layout.Sidebar.settings": "Nastavení", "components.Layout.Sidebar.requests": "<PERSON><PERSON><PERSON><PERSON>", "components.Layout.Sidebar.dashboard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Layout.SearchInput.searchPlaceholder": "Hledat filmy a seriály", "components.Layout.LanguagePicker.displaylanguage": "Jazyk zobrazení", "components.LanguageSelector.originalLanguageDefault": "Všechny jazyky", "components.LanguageSelector.languageServerDefault": "Výchozí ({language})", "components.DownloadBlock.estimatedtime": "<PERSON><PERSON><PERSON><PERSON> {time}", "components.Discover.upcomingtv": "Nadch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Discover.upcomingmovies": "Nadcházející filmy", "components.Discover.upcoming": "Nadcházející filmy", "components.Discover.trending": "Populární", "components.Discover.recentrequests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Discover.recentlyAdded": "Nedávno přidané", "components.Discover.populartv": "<PERSON>bl<PERSON><PERSON><PERSON>", "components.Discover.DiscoverTvLanguage.languageSeries": "Seriály: {language}", "components.Discover.DiscoverTvGenre.genreSeries": "Seriály: {genre}", "components.Discover.DiscoverNetwork.networkSeries": "Seriály: {network}", "components.Discover.popularmovies": "Oblíbené filmy", "components.Discover.discover": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Discover.TvGenreSlider.tvgenres": "<PERSON><PERSON><PERSON>", "components.Discover.TvGenreList.seriesgenres": "<PERSON><PERSON><PERSON>", "components.Discover.StudioSlider.studios": "St<PERSON><PERSON>", "components.Discover.NetworkSlider.networks": "<PERSON><PERSON>", "components.Discover.MovieGenreSlider.moviegenres": "Žánry filmů", "components.Discover.MovieGenreList.moviegenres": "Žánry filmů", "components.CollectionDetails.numberofmovies": "{count} filmů", "components.AppDataWarning.dockerVolumeMissingDescription": "Připojení svazku <code>{appDataPath}</code> nebylo správně nakonfigurováno. Při zastavení nebo opětovném spuštění kontejneru budou všechna data vymazána.", "components.Discover.DiscoverStudio.studioMovies": "Filmy: {studio}", "components.Discover.DiscoverMovieLanguage.languageMovies": "Filmy: {language}", "components.Discover.DiscoverMovieGenre.genreMovies": "Filmy: {genre}", "components.CollectionDetails.requestcollection4k": "Zažádat o kolekci ve 4K", "components.CollectionDetails.requestcollection": "Zažádat o kolekci", "components.CollectionDetails.overview": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachemisses": "Neúspěchy", "components.NotificationTypeSelector.usermediadeclinedDescription": "Dostat oznámení o odmítnutí vašich požadavků na média.", "components.NotificationTypeSelector.usermediaavailableDescription": "<PERSON><PERSON><PERSON>, j<PERSON><PERSON> budou k dispozici žádosti o média.", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {potvrzení} few {potvrzení} other {potvrzení}} za", "components.Settings.serverpresetRefreshing": "Nač<PERSON><PERSON><PERSON><PERSON> server<PERSON>…", "components.Settings.plexsettings": "Nastavení Plexu", "components.Settings.scan": "Synchronizovat knihovny", "components.Settings.plexlibraries": "Plex knihovny", "components.Settings.notrunning": "<PERSON><PERSON><PERSON>", "components.Settings.radarrsettings": "Nastavení Radarru", "components.Settings.startscan": "Spus<PERSON>t skenování", "components.Settings.serverpresetManualMessage": "Manuální konfigurace", "components.Settings.sonarrsettings": "Nastavení Sonarru", "components.StatusBadge.status": "{status}", "components.IssueDetails.IssueDescription.description": "<PERSON><PERSON>", "components.IssueDetails.comments": "Ko<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "Frekvence", "components.IssueModal.issueOther": "<PERSON><PERSON>", "components.RequestModal.requestadmin": "<PERSON><PERSON> bude sch<PERSON><PERSON><PERSON>.", "components.IssueModal.issueAudio": "Zvuk", "components.IssueModal.issueSubtitles": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueModal.issueVideo": "Video", "components.Layout.Sidebar.issues": "Problé<PERSON>", "components.ManageSlideOver.movie": "film", "components.RequestModal.numberofepisodes": "Počet epizod", "components.IssueDetails.issuepagetitle": "Problé<PERSON>", "components.IssueDetails.leavecomment": "<PERSON><PERSON><PERSON><PERSON>", "components.ManageSlideOver.tvshow": "<PERSON><PERSON><PERSON><PERSON>", "i18n.open": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.issuetype": "<PERSON><PERSON>", "components.IssueList.IssueItem.opened": "O<PERSON>v<PERSON><PERSON>", "components.IssueList.IssueItem.unknownissuetype": "Neznámý", "components.IssueList.issues": "Problé<PERSON>", "components.ManageSlideOver.manageModalRequests": "<PERSON><PERSON><PERSON><PERSON>", "i18n.resolved": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.TvDetails.overviewunavailable": "<PERSON><PERSON><PERSON><PERSON> není k dispozici.", "components.TvDetails.watchtrailer": "Sledovat trailer", "components.UserList.deleteuser": "Odstranit uživatele", "components.UserList.email": "E-mailová adresa", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "Výchozí ({language})", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "Web Push", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "<PERSON><PERSON>", "components.UserProfile.movierequests": "Žádosti o filmy", "components.UserList.localuser": "Místní uživatel", "components.Settings.SettingsLogs.extraData": "Doplňující <PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "Obecná nastavení", "components.IssueModal.CreateIssueModal.toastviewissue": "Zobrazit problém", "components.ManageSlideOver.manageModalNoRequests": "<PERSON><PERSON><PERSON><PERSON>.", "components.IssueList.IssueItem.issuetype": "<PERSON><PERSON>", "components.IssueDetails.problemseason": "Ovlivněná sezóna", "components.IssueDetails.reopenissue": "Znovu otevř<PERSON><PERSON>", "components.NotificationTypeSelector.issuecreated": "<PERSON><PERSON><PERSON><PERSON>", "components.PermissionEdit.viewissues": "Zobrazit problémy", "components.ManageSlideOver.manageModalIssues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.IssueModal.CreateIssueModal.problemepisode": "Ovlivněná epizoda", "components.IssueDetails.IssueComment.delete": "Odstranit komentář", "components.IssueDetails.IssueComment.edit": "Upravit komentář", "components.IssueDetails.allseasons": "Všechny sezóny", "components.IssueDetails.closeissue": "Uzav<PERSON><PERSON><PERSON>", "components.ManageSlideOver.downloadstatus": "Stahování", "components.NotificationTypeSelector.issueresolved": "Problém vyřešen", "components.RequestList.RequestItem.modifieduserdate": "{date} od {user}", "components.RequestList.showallrequests": "Zobrazit všechny žádosti", "components.IssueDetails.IssueDescription.deleteissue": "Odstranit problém", "components.IssueDetails.IssueDescription.edit": "Upravit popis", "components.IssueDetails.allepisodes": "Všechny epizody", "components.IssueDetails.deleteissue": "Odstranit problém", "components.IssueDetails.episode": "Epizoda {episodeNumber}", "components.IssueDetails.lastupdated": "Poslední aktualizace", "components.IssueDetails.unknownissuetype": "Neznámý", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.RequestButton.declinerequest4k": "Odmítnout 4K žádost", "components.ResetPassword.resetpasswordsuccessmessage": "<PERSON><PERSON><PERSON><PERSON> reset<PERSON>!", "components.IssueModal.CreateIssueModal.whatswrong": "Co je špatně?", "components.RequestButton.approverequest4k": "Schválit 4K žádost", "components.RequestButton.viewrequest4k": "Zobrazit 4K požadavek", "components.RequestModal.requestSuccess": "<strong>{title}</strong> by<PERSON> z<PERSON>žád<PERSON>!", "components.Settings.SettingsLogs.logDetails": "Podrobnosti o záznamu", "components.StatusBadge.status4k": "4K {status}", "components.TvDetails.episodeRuntime": "<PERSON><PERSON><PERSON><PERSON> epizody", "components.TvDetails.episodeRuntimeMinutes": "{runtime} minut", "components.TvDetails.originallanguage": "Původní jazyk", "components.TvDetails.originaltitle": "Původní název", "components.UserList.bulkedit": "Hroma<PERSON><PERSON>", "components.IssueDetails.nocomments": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>.", "components.IssueList.IssueItem.issuestatus": "Stav", "components.IssueDetails.problemepisode": "Ovlivněná epizoda", "components.IssueDetails.season": "<PERSON><PERSON><PERSON><PERSON> {seasonNumber}", "components.IssueList.IssueItem.problemepisode": "Ovlivněná epizoda", "components.IssueList.IssueItem.viewissue": "Zobrazit problém", "components.IssueList.sortAdded": "Nejnovější", "components.IssueList.sortModified": "Naposledy změněno", "components.IssueModal.CreateIssueModal.allepisodes": "Všechny epizody", "components.IssueModal.CreateIssueModal.allseasons": "Všechny sezóny", "components.IssueModal.CreateIssueModal.episode": "Epizoda {episodeNumber}", "components.IssueModal.CreateIssueModal.problemseason": "Ovlivněná série", "components.IssueModal.CreateIssueModal.season": "<PERSON><PERSON><PERSON><PERSON> {seasonNumber}", "components.IssueModal.CreateIssueModal.submitissue": "<PERSON><PERSON><PERSON> pro<PERSON>", "components.ManageSlideOver.manageModalTitle": "Spravovat {mediaType}", "components.PermissionEdit.createissues": "Nahlásit problémy", "components.PermissionEdit.manageissues": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.ResetPassword.emailresetlink": "Odkaz na obnovení e-mailu", "components.ResetPassword.validationpasswordmatch": "<PERSON><PERSON> se musí shodovat", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "Musíte zadat platnou adresu URL", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "Obnovit do výchozího nastavení", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "Musíte zadat platnou adresu URL", "components.Settings.SettingsJobsCache.editJobSchedule": "<PERSON><PERSON><PERSON><PERSON>", "components.Setup.configureservices": "Konfigurovat služby", "components.Setup.finish": "Dokončit nastavení", "components.UserList.plexuser": "Plex uživatel", "components.UserList.sortRequests": "Počet žádostí", "components.UserList.userlist": "Seznam uživatelů", "components.UserProfile.ProfileHeader.joindate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> se {joindate}", "components.UserProfile.ProfileHeader.profile": "Zobrazit profil", "components.UserProfile.ProfileHeader.settings": "Upravit nastavení", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "Typ účtu", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "Jazyk zobrazení", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "Místní uživatel", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "Jazyk pro objevování", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "Plex uživatel", "components.UserProfile.UserSettings.UserGeneralSettings.region": "Region pro objevování", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "ID uživatele", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "Nastavení oznámení", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "ID chatu", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "Potvrďte he<PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "Aktuální he<PERSON>lo", "components.IssueDetails.toastissuedeleted": "Problém úspěšně odstraněn!", "components.IssueDetails.toaststatusupdated": "Stav problému úspěšně aktualizován!", "components.ManageSlideOver.markavailable": "Označit jako <PERSON>", "components.IssueDetails.commentplaceholder": "<PERSON>ř<PERSON><PERSON> komentář…", "components.IssueDetails.openedby": "<PERSON><PERSON> {issueId} otevřeno {relativeTime} uživatelem {username}", "components.IssueDetails.openin4karr": "Otev<PERSON><PERSON><PERSON> ve 4K {arr}", "components.IssueDetails.reopenissueandcomment": "Znovu otevřít s komentářem", "components.IssueModal.CreateIssueModal.validationMessageRequired": "Musíte posky<PERSON>ut popis", "components.ManageSlideOver.manageModalClearMedia": "Vyčistit data", "components.ManageSlideOver.alltime": "Za celou dobu", "components.ManageSlideOver.manageModalAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.ManageSlideOver.manageModalClearMediaWarning": "* Tímto <PERSON>v<PERSON>ě odeberete všechna data pro tento {mediaType}, včetně všech požadavků. Pokud tato položka existuje ve vaší knihovně {mediaServerName}, informace o médiích budou znovu vytvořeny během příštího skenování.", "components.ManageSlideOver.manageModalMedia": "Média", "components.ManageSlideOver.manageModalMedia4k": "4K média", "components.ManageSlideOver.markallseasonsavailable": "Označit všechny sezóny jako dostupné", "components.ManageSlideOver.markallseasons4kavailable": "Označit všechny sezóny jako dostupné ve 4K", "components.ManageSlideOver.openarr": "Otevřít v {arr}", "components.ManageSlideOver.openarr4k": "Otev<PERSON><PERSON><PERSON> ve 4K {arr}", "components.ManageSlideOver.opentautulli": "Otevřít v Tautulli", "components.NotificationTypeSelector.adminissuecommentDescription": "Nechte se upozornit, k<PERSON>ž ostatní uživatelé komentují problémy.", "components.NotificationTypeSelector.adminissuereopenedDescription": "Nechte se upozornit, <PERSON><PERSON><PERSON> jsou problémy znovu otevřeny jinými uživateli.", "components.IssueDetails.toaststatusupdatefailed": "Při aktualizaci stavu problému se něco pokazilo.", "components.IssueDetails.IssueComment.postedby": "Zveřejněno {relativeTime} uživatelem {username}", "components.IssueDetails.playonplex": "Přehrá<PERSON> na {mediaServerName}", "components.IssueDetails.play4konplex": "P<PERSON><PERSON><PERSON><PERSON><PERSON> ve 4K na {mediaServerName}", "components.IssueList.IssueItem.openeduserdate": "{date} uživatelem {user}", "components.IssueModal.CreateIssueModal.providedetail": "Uveďte prosím podrobné vysvětlení problému, na který jste narazili.", "components.IssueModal.CreateIssueModal.reportissue": "Nahlásit problém", "components.IssueModal.CreateIssueModal.toastFailedCreate": "Při odesílání problému se něco pokazilo.", "components.IssueDetails.IssueComment.validationComment": "Musíte zadat zprávu", "components.IssueDetails.IssueComment.areyousuredelete": "Opravdu chcete odstranit tento komentář?", "components.IssueDetails.openinarr": "Otevřít v {arr}", "components.IssueDetails.IssueComment.postedbyedited": "Zveřejněno {relativeTime} uživatelem {username} (upraveno)", "components.IssueDetails.toasteditdescriptionsuccess": "Popis problému úspěšně upraven!", "components.IssueDetails.deleteissueconfirm": "Opravdu chcete odstranit tento problém?", "components.IssueDetails.toasteditdescriptionfailed": "<PERSON><PERSON><PERSON>v<PERSON> popisu problému se něco pokazilo.", "components.IssueDetails.toastissuedeletefailed": "Při odstraňování problému se něco pokazilo.", "components.IssueModal.CreateIssueModal.extras": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.IssueList.showallissues": "Zobrazit všechny problémy", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "Zpráva o problému pro <strong>{title}</strong> úspěšně o<PERSON>lána!", "components.ManageSlideOver.mark4kavailable": "Označit jako dostupné ve 4K", "components.NotificationTypeSelector.issuereopened": "Problém znovu otevřen", "components.NotificationTypeSelector.userissuecreatedDescription": "Získejte upozornění, <PERSON><PERSON>ž ostatní uživatelé nahlásí problémy.", "components.PermissionEdit.advancedrequestDescription": "Udělení oprávnění k úpravě pokročilých možností vyžádání médií.", "components.PermissionEdit.request4kMovies": "Vyžádat si filmy 4K", "components.PermissionEdit.usersDescription": "Udělení oprávnění ke správě uživatelů. Uživatelé s tímto oprávněním nemohou upravovat uživatele s oprávněním správce nebo jim toto oprávnění udělit.", "components.RequestModal.selectmovies": "Vyberte film(y)", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "Odeslání oznámení o testu Pushover…", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Nastavení oznámení služby Slack bylo úspěšně uloženo!", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "Testovací oznámení Web push se nepodařilo odeslat.", "components.Settings.Notifications.encryptionDefault": "Použijte protokol STARTTLS, pokud je k dispozici", "components.Settings.Notifications.pgpPrivateKey": "Soukromý klíč PGP", "components.Settings.Notifications.validationEmail": "Musíte zadat platnou e-mailovou adresu", "components.Settings.Notifications.validationPgpPrivateKey": "Musíte zadat platný soukromý klíč PGP", "components.Settings.Notifications.validationUrl": "Musíte zadat platnou adresu URL", "components.Settings.Notifications.validationPgpPassword": "Musíte z<PERSON>t he<PERSON>", "components.Settings.RadarrModal.loadingrootfolders": "Načítání kořenových složek…", "components.Settings.RadarrModal.testFirstQualityProfiles": "Test připojení pro načtení profilů k<PERSON>ity", "components.Settings.RadarrModal.validationRootFolderRequired": "Je třeba vybrat kořenovou složku", "components.Settings.SettingsAbout.Releases.releasedataMissing": "Údaje o vydání jsou momentálně nedostupné.", "components.Settings.SettingsAbout.gettingsupport": "Získání podpory", "components.Settings.SettingsJobsCache.download-sync-reset": "Resetování synchronizace stažení", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "Při ukládání <PERSON> se něco pokazilo.", "components.Settings.SonarrModal.testFirstQualityProfiles": "Test připojení pro načtení profilů k<PERSON>ity", "components.Settings.notificationAgentSettingsDescription": "Konfigurace a povolení agentů pro oznámení.", "components.Settings.settingUpPlexDescription": "Chcete-li nastavit službu Plex, můž<PERSON> buď zadat údaje ručně, nebo vybrat server získaný z adresy <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink>. Stisknutím tlačítka vpravo od rozevírací nabídky načtete seznam dostupných serverů.", "components.Settings.toastPlexRefreshFailure": "Nepodařilo se načíst seznam serverů Plex.", "components.TvDetails.play4k": "Přeh<PERSON><PERSON><PERSON> v {mediaServerName} ve 4K", "components.TvDetails.play": "Přehrát v {mediaServerName}", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "Filtrování obsahu podle regionální dostupnosti", "components.Settings.Notifications.NotificationsGotify.agentenabled": "Povolení agenta", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "Nastavení oznámení Gotify se nepodařilo uložit.", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Nastavení oznámení Gotify úspěšně uloženo!", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "Testovací oznámení Gotify se nepodařilo odeslat.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Odeslání oznámení o testu Gotify…", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Oznámení o testu Gotify odesláno!", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "Adresa URL nesmí končit koncovým lomítkem", "components.Settings.Notifications.NotificationsGotify.validationTypes": "Musíte vybrat alespoň jeden typ oznámení", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "Oznámení o testu LunaSea odesláno!", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "Musíte zadat platnou adresu URL", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Testovací oznámení Pushbullet odesláno!", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "Musíte zadat přís<PERSON>pový token", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "Musíte vybrat alespoň jeden typ oznámení", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "Odeslání test<PERSON><PERSON>ho <PERSON>…", "components.Settings.RadarrModal.validationApplicationUrl": "Musíte zadat platnou adresu URL", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "Adresa URL nesmí končit koncovým lomítkem", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "<PERSON><PERSON><PERSON>ce úsp<PERSON>šně upravena!", "components.Settings.SonarrModal.baseUrl": "Základní adresa URL", "components.Settings.SonarrModal.editsonarr": "Upravit server Sonarr", "components.Settings.SonarrModal.enableSearch": "Povolení automatického vyhledávání", "components.Settings.SonarrModal.edit4ksonarr": "Upravit 4K Sonarr Server", "components.Settings.SonarrModal.testFirstLanguageProfiles": "Testovací připojení pro načtení jazykových profilů", "components.Settings.SonarrModal.testFirstRootFolders": "Test připojení pro načtení kořenových složek", "components.Settings.SonarrModal.validationApiKeyRequired": "Musíte zadat klíč API", "components.Settings.SonarrModal.validationApplicationUrl": "Musíte zadat platnou adresu URL", "components.Settings.deleteserverconfirm": "Opravdu chcete tento server odstranit?", "components.Settings.menuJobs": "Práce a mezipaměť", "components.TvDetails.similar": "Podobné série", "components.TvDetails.streamingproviders": "V současné době streamuje na", "components.UserList.nouserstoimport": "Neexistují <PERSON>dní už<PERSON>lé systému Plex, kter<PERSON> by by<PERSON> mo<PERSON>t.", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "Nastavení úspěšně uloženo!", "components.Settings.SettingsJobsCache.nextexecution": "<PERSON><PERSON><PERSON> s<PERSON>", "components.TvDetails.nextAirDate": "<PERSON><PERSON><PERSON> datum vysílán<PERSON>", "components.TvDetails.viewfullcrew": "Zobrazit celé obsazení", "components.UserList.edituser": "Úprava oprávnění už<PERSON>", "components.NotificationTypeSelector.issuecreatedDescription": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>u <PERSON>š<PERSON>y problémy.", "components.NotificationTypeSelector.issuereopenedDescription": "Odesílat <PERSON>ornění, k<PERSON><PERSON> se problémy znovu otevřou.", "components.RequestModal.pending4krequest": "Čeká na vyřízení žádosti 4K", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "Nastavení webových push oznámení se nepodařilo ulož<PERSON>.", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "Odeslání testovacího oznámení webhooku…", "components.Settings.Notifications.toastEmailTestSending": "Odeslání e-mailového oznámení o testu…", "components.Settings.Notifications.validationChatIdRequired": "Musíte předložit platné chatovací <PERSON>", "components.Settings.RadarrModal.hostname": "Název hostitele nebo IP adresa", "components.Settings.RadarrModal.loadingprofiles": "Načítán<PERSON> profil<PERSON>…", "components.Settings.RadarrModal.selectRootFolder": "Vyberte kořenovou složku", "components.Settings.SettingsAbout.runningDevelop": "Používá<PERSON> vě<PERSON><PERSON> <code>develop</code>, kter<PERSON> je doporučena pouze těm, kte<PERSON><PERSON> se podílejí na vývoji nebo pomáhají s testováním.", "components.Settings.webpush": "Web Push", "components.RequestList.RequestItem.failedretry": "Při opakovaném pokusu o zadání požadavku se něco pokazilo.", "components.Settings.RadarrModal.enableSearch": "Povolení automatického vyhledávání", "components.RequestModal.requestCancel": "<PERSON><PERSON><PERSON><PERSON> o <strong>{title}</strong> <PERSON><PERSON><PERSON><PERSON>.", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "<PERSON><PERSON><PERSON> <UsersGroupsLink>identifik<PERSON>tor už<PERSON>le nebo skupiny</UsersGroupsLink>", "components.ManageSlideOver.pastdays": "Posledních {days, number} {days, plural, one {den} few {dny} other {dní}}", "components.ManageSlideOver.playedby": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.NotificationTypeSelector.adminissueresolvedDescription": "<PERSON>echte se upozornit, <PERSON><PERSON><PERSON> jsou problémy vyřešeny jinými uživateli.", "components.NotificationTypeSelector.issuecomment": "Komentář k problému", "components.NotificationTypeSelector.issuecommentDescription": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> problémy obdrží nové komentáře.", "components.NotificationTypeSelector.userissuecommentDescription": "Získejte upozornění na nové komentáře k problémům, které jste nahlásili.", "components.NotificationTypeSelector.userissuereopenedDescription": "Dostávejte upozornění, <PERSON><PERSON><PERSON>u vá<PERSON>é problémy znovu otevřeny.", "components.NotificationTypeSelector.userissueresolvedDescription": "Dostávejte oznámení o vyřešení problémů, kter<PERSON> j<PERSON> na<PERSON>.", "components.NotificationTypeSelector.usermediafailedDescription": "Získejte upozornění, k<PERSON>ž se nepodaří přidat požadavky na média do Radarru nebo Sonarru.", "components.NotificationTypeSelector.usermediarequestedDescription": "Získejte upozornění, k<PERSON>ž ostatní uživatelé zadají nové požadavky na média, které vyžadují schválení.", "components.PermissionEdit.adminDescription": "Plný přístup správce. Obchází všechny ostatní kontroly oprávnění.", "components.PermissionEdit.autoapprove4kMoviesDescription": "Automatické schvalování žádostí o filmy 4K.", "components.PermissionEdit.autoapprove4kSeriesDescription": "Udělit automatické schválení žádostí o sérii 4K.", "components.PermissionEdit.autoapproveSeriesDescription": "Udělit automatické schválení pro žádosti jiné než řady 4K.", "components.PermissionEdit.createissuesDescription": "Udělit povolení k podávání zpráv o mediálních problémech.", "components.PermissionEdit.manageissuesDescription": "Udělit povolení ke správě mediálních záležitostí.", "components.PermissionEdit.managerequestsDescription": "Udělit povolení ke správě žádostí médií. Všechny žádosti podané uživatelem s tímto oprávněním budou automaticky schváleny.", "components.PermissionEdit.request4kDescription": "Udělování povolení k předkládání žádostí o média 4K.", "components.PermissionEdit.request4kMoviesDescription": "Udělit povolení k podávání žádostí o filmy 4K.", "components.PermissionEdit.request4kTv": "Vyžádat si sérii 4K", "components.PermissionEdit.requestMoviesDescription": "Udělit povolení k předkládání žádostí o filmy jiné než 4K.", "components.PermissionEdit.requestTvDescription": "Udělit povolení k předkládání žádostí pro jiné série než 4K.", "components.PermissionEdit.viewissuesDescription": "Udělit oprávnění k zobrazení problémů s médii nahlášených jinými už<PERSON>.", "components.PermissionEdit.viewrequestsDescription": "Udělit oprávnění k zobrazení požadavků na média zadaných jinými uživateli.", "components.PersonDetails.alsoknownas": "Známý také jako: {names}", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{movies} na {quotaDays} {days}</quotaUnits>", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "Zbývá málo žádostí na sezónu", "components.ResetPassword.gobacklogin": "Zpět na stránku pro přihlášení", "components.ResetPassword.resetpassword": "Obnoven<PERSON> hesla", "components.ResetPassword.validationemailrequired": "Musíte zadat platnou e-mailovou adresu", "components.ResetPassword.validationpasswordminchars": "<PERSON><PERSON><PERSON> je <PERSON><PERSON><PERSON><PERSON> k<PERSON>; m<PERSON>lo by m<PERSON><PERSON> <PERSON> 8 znaků", "components.ResetPassword.validationpasswordrequired": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>t he<PERSON>lo", "components.Settings.Notifications.NotificationsGotify.url": "Adresa URL serveru", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "Musíte zadat token aplikace", "components.Settings.Notifications.botAPI": "Autorizační <PERSON>", "components.Settings.RadarrModal.validationHostnameRequired": "Musíte zadat platný název hostitele nebo IP adresu", "components.Settings.Notifications.NotificationsSlack.validationTypes": "Musíte vybrat alespoň jeden typ oznámení", "components.Settings.Notifications.enableMentions": "<PERSON><PERSON><PERSON>", "components.Settings.Notifications.validationSmtpPortRequired": "Musíte zadat platné číslo portu", "components.Settings.SettingsLogs.copyToClipboard": "Kopírování do schránky", "components.Settings.SonarrModal.testFirstTags": "Testovací připojení k načítání značek", "components.Settings.SonarrModal.toastSonarrTestFailure": "Nepodařilo se připojit k systému Sonarr.", "components.Settings.SonarrModal.toastSonarrTestSuccess": "Připojení Sonarr úspěšně navázáno!", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "Základní adresa URL musí mít na začátku lomítko", "components.Settings.manualscanDescription": "Obvykle se provádí pouze jednou za 24 hodin. <PERSON><PERSON><PERSON><PERSON> bude kontrolovat nedávno přidané položky vašeho serveru Plex agresivněji. Pokud Plex konfigurujete poprvé, doporučujeme provést jednorázovou úplnou ruční kontrolu knihovny!", "components.Settings.urlBase": "Základní adresa URL", "components.Settings.tautulliSettingsDescription": "Volitelně nakonfigurujte nastavení serveru <PERSON>lli. Jellyseerr načte data historie sledování pro vaše média Plex z Tautulli.", "components.Settings.toastPlexConnecting": "Pokus o připojení k systému Plex…", "components.Settings.validationApiKey": "Musíte zadat klíč API", "components.Settings.validationHostnameRequired": "Musíte zadat platný název hostitele nebo IP adresu", "components.Settings.validationPortRequired": "Musíte zadat platné číslo portu", "components.Settings.validationUrl": "Musíte zadat platnou adresu URL", "components.Settings.validationUrlBaseLeadingSlash": "Základ URL musí mít na začátku lomítko", "components.TvDetails.showtype": "Typ série", "components.UserList.importfrommediaserver": "Import uživatelů systému {mediaServerName}", "components.UserList.importfromplex": "Import uživatelů systému Plex", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Nastavení oznámení Discordu úspěšně uloženo!", "components.RequestButton.requestmore4k": "Vyžádat si více informací v rozlišení 4K", "components.RequestModal.QuotaDisplay.quotaLink": "Souhrn limitů va<PERSON><PERSON> požadavků si můžete prohlédnout na stránce vašeho <ProfileLink>profilu</ProfileLink>.", "components.RequestModal.QuotaDisplay.requiredquota": "Abyste mohli za<PERSON>t o tento seriál, musíte mít alespoň <strong>{seasons}</strong> {seasons, plural, one {zbývající žádost o sezónu} few {zbývající žádosti o sezónu} other {zbývajících žádostí o sezónu}}.", "components.RequestModal.requestfrom": "Žádost od {username} čeká na schválení.", "components.RequestModal.requesterror": "Při odesílání <PERSON>dos<PERSON> se něco pokazilo.", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "<PERSON><PERSON>še adresa URL <LunaSeaLink>notification webhook</LunaSeaLink> pro uživatele nebo zařízení", "components.Settings.Notifications.toastEmailTestSuccess": "E-mailové ozná<PERSON>í o testu o<PERSON>láno!", "components.Settings.RadarrModal.baseUrl": "Základní adresa URL", "components.Settings.RadarrModal.default4kserver": "Výchozí server 4K", "components.Settings.RadarrModal.edit4kradarr": "Upravit 4K Radarr Server", "components.Settings.SettingsAbout.uptodate": "Aktuální", "components.Settings.SonarrModal.validationProfileRequired": "Je třeba vybrat profil k<PERSON>ity", "components.Settings.currentlibrary": "Současná knihovna: {name}", "components.IssueDetails.closeissueandcomment": "Uzavřít s komentářem", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "Nastavení oznámení Telegramu se nepodařilo uložit.", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "Musíte zadat platné ID uživatele", "components.RequestModal.pendingapproval": "Vaše žádost čeká na schválení.", "components.Settings.Notifications.NotificationsPushover.accessToken": "Token API aplikace", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "Nastavení oznámení webhooku se nepodařilo uložit.", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "Nastavení oznámení Webhook bylo úspěšně uloženo!", "components.Settings.Notifications.botAvatarUrl": "Adresa URL avatara bota", "components.Settings.Notifications.discordsettingssaved": "Nastavení oznámení Discordu úspěšně uloženo!", "components.Settings.Notifications.webhookUrlTip": "Vytvoření <DiscordWebhookLink>webhook integrace</DiscordWebhookLink> na serveru", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "Nemáte oprávnění měnit heslo to<PERSON>o <PERSON>.", "components.Settings.Notifications.encryptionOpportunisticTls": "Vždy používejte protokol STARTTLS", "components.Settings.RadarrModal.toastRadarrTestSuccess": "Spojení Radarr úspěšně navázáno!", "components.Settings.SettingsJobsCache.cacheksize": "Velikost klíče", "components.Settings.SettingsUsers.newPlexLoginTip": "Povolení přihlášení už<PERSON>lů systému {mediaServerName} bez předchozího importu", "components.Settings.SonarrModal.validationPortRequired": "Musíte zadat platné číslo portu", "components.UserList.userfail": "Při ukládání uživatelských oprávnění se něco pokazilo.", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "Odesílání oznámení bez zvuku", "components.RequestBlock.languageprofile": "Jazykový profil", "components.RequestModal.QuotaDisplay.quotaLinkUser": "<PERSON><PERSON><PERSON> limitů požadavků tohoto uživatele můžete zobrazit na jeho <ProfileLink>profilov<PERSON> str<PERSON></ProfileLink>.", "components.Settings.Notifications.NotificationsGotify.token": "Token a<PERSON>lik<PERSON>", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "Testovací oznámení LunaSea se nepodařilo o<PERSON>lat.", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "Musíte zadat platnou adresu URL", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "Označení kanálu", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "Nastavení oznámení Pushbullet se nepodařilo ulož<PERSON>.", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "Musíte vybrat alespoň jeden typ oznámení", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Nastavení oznámení Pushover úspěšně uloženo!", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "Odeslání oznámení o testu ve službě Slack…", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "Oznámení o testu na Slacku odesláno!", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "Nastavení webových oznámení push bylo úspěšně uloženo!", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "Nápověda k proměnné <PERSON>", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "Testovací oznámení Webhook odesláno!", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "Musíte zadat platné užitečné zatížení JSON", "components.Settings.Notifications.allowselfsigned": "Povolení certifikátů s vlastním podpisem", "components.Settings.Notifications.discordsettingsfailed": "Nastavení oznámení Discordu se nepodařilo ulož<PERSON>.", "components.Settings.Notifications.encryptionImplicitTls": "Použití implicitního protokolu TLS", "components.Settings.Notifications.toastDiscordTestSending": "Odeslání oznámení o testu Discord…", "components.Settings.Notifications.toastDiscordTestSuccess": "Oznámení o testu Discordu odesláno!", "components.Settings.Notifications.toastEmailTestFailed": "E-mail<PERSON><PERSON> test<PERSON>í oznámení se nepodařilo o<PERSON>.", "components.Settings.Notifications.validationBotAPIRequired": "Musíte zadat autorizační token bota", "components.Settings.Notifications.validationTypes": "Musíte vybrat alespoň jeden typ oznámení", "components.Settings.RadarrModal.announced": "Oznámeno", "components.Settings.RadarrModal.create4kradarr": "Přidání nového serveru 4K Radarr", "components.Settings.RadarrModal.selectQualityProfile": "<PERSON><PERSON><PERSON><PERSON> profil k<PERSON>", "components.Settings.RadarrModal.testFirstRootFolders": "Test připojení pro načtení kořenových složek", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "Základ URL nesmí končit koncovým lomítkem", "components.Settings.RadarrModal.validationNameRequired": "Je třeba zadat název serveru", "components.Settings.SettingsAbout.appDataPath": "<PERSON><PERSON><PERSON><PERSON> dat", "components.Settings.SettingsAbout.helppaycoffee": "Pomozte zaplatit kávu", "components.Settings.SettingsAbout.outofdate": "Zast<PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachekeys": "Klíče celkem", "components.Settings.SettingsJobsCache.download-sync": "Synchronizovat stažení", "components.Settings.SettingsJobsCache.jobsandcache": "Práce a mezipaměť", "components.Settings.SettingsJobsCache.plex-full-scan": "Kompletní skenování knihovny Plex", "components.Settings.SettingsLogs.copiedLogMessage": "Zkopírování zprávy protokolu do schránky.", "components.Settings.SettingsUsers.defaultPermissionsTip": "Počáteční oprávnění přidělená novým uživatelům", "components.Settings.SettingsUsers.tvRequestLimitLabel": "Globální limit požadavků na sérii", "components.Settings.SettingsUsers.userSettingsDescription": "Konfigurace globálních a výchozích uživatelských nastavení.", "components.Settings.SonarrModal.animequalityprofile": "Profil kvality anime", "components.Settings.SonarrModal.create4ksonarr": "Přidání nového serveru 4K Sonarr", "components.Settings.SonarrModal.hostname": "Název hostitele nebo IP adresa", "components.Settings.SonarrModal.loadinglanguageprofiles": "Načít<PERSON><PERSON> j<PERSON> profilů…", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "Adresa URL nesmí končit koncovým lomítkem", "components.Settings.addradarr": "Přidání serveru <PERSON>", "components.Settings.addsonarr": "Adding a Radarr server", "components.Settings.copied": "Zkopírování klíče API do schránky.", "components.Settings.externalUrl": "Externí adresa URL", "components.Settings.hostname": "Název hostitele nebo IP adresa", "components.Settings.manualscan": "Manuální skenování knihovny", "components.Settings.plexlibrariesDescription": "Knihovny ve kterých Jellyseerr vyhledává tituly. Nastavte a uložte nastavení připojení k Plex serveru a poté klikněte na tlačítko níže, pokud nejsou v seznamu uvedeny žádné knihovny.", "components.Settings.serverpresetLoad": "Stisknutím tlačítka načtete dostupné servery", "components.Settings.toastTautulliSettingsFailure": "Při ukládání nastavení Tautulli se něco pokazilo.", "components.Settings.webAppUrl": "<WebAppLink><PERSON><PERSON> aplikace</WebAppLink> Ad<PERSON>a URL", "components.Settings.validationUrlTrailingSlash": "Adresa URL nesmí končit koncovým lomítkem", "components.Settings.webAppUrlTip": "Volitelné přesměrování uživatelů na webovou aplikaci na vašem serveru namísto hostované webové aplikace", "components.Setup.welcome": "Vítejte v Jellyseerr", "components.Setup.signinMessage": "Skenování bude probíhat na pozadí. Mezitím můžete pokračovat v procesu nastavení", "components.TvDetails.TvCrew.fullseriescrew": "Posádka celé série", "components.UserList.autogeneratepassword": "<PERSON><PERSON><PERSON> he<PERSON>", "components.UserList.autogeneratepasswordTip": "Zaslání hesla vygenerovaného serverem uživateli e-mailem", "components.UserList.deleteconfirm": "Opravdu chcete tohoto uživatele odstranit? Všechny údaje o jeho žádosti budou trvale odstraněny.", "components.UserList.localLoginDisabled": "Nastavení <strong>Povolit místní přihlášení</strong> je v současné době zakázáno.", "components.UserList.userssaved": "Uživatelská oprávnění byla úspěšně uložena!", "components.UserList.validationEmail": "<PERSON><PERSON><PERSON> adresa je povinná", "components.UserProfile.ProfileHeader.userid": "ID uživatele: {userid}", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "Limit požadavků na filmy", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "Limit požadavků na sérii", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "Při ukládání nastavení se něco pokazilo.", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "Nastavení e-mailových oznámení bylo úspěšně uloženo!", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "Musíte předložit platné chatovací <PERSON>", "components.UserProfile.limit": "{remaining} z {limit}", "components.UserProfile.recentlywatched": "<PERSON><PERSON><PERSON><PERSON>", "i18n.import": "Import", "components.PermissionEdit.autoapproveDescription": "Udělit automatické schválení všem žádostem o média jiná než 4K.", "components.PermissionEdit.autoapprove4kDescription": "Udělit automatické schválení všem žádostem o média 4K.", "components.PermissionEdit.request4kTvDescription": "Udělit povolení k předkládání žádostí o seriály 4K.", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "Testovací oznámení Pushbullet se nepodařilo o<PERSON>lat.", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "Nastavení oznámení Pushover se nepodařilo uložit.", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "Testovací oznámení služby Slack se nepodařilo o<PERSON>.", "components.Settings.Notifications.botApiTip": "<CreateBotLink>Vytvoření bota</CreateBotLink> pro použití s <PERSON><PERSON><PERSON>rr", "components.Settings.Notifications.pgpPrivateKeyTip": "Podepisování <PERSON>ých e-mailových zpráv pomocí <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.sendSilentlyTip": "Odesílání oznámení bez zvuku", "components.Settings.Notifications.telegramsettingssaved": "Nastavení oznámení Telegramu úspěšně uloženo!", "components.Settings.RadarrModal.validationPortRequired": "Musíte zadat platné číslo portu", "components.Settings.SettingsLogs.showall": "Zobrazit všechny protokoly", "components.Settings.SettingsUsers.newPlexLogin": "Povolení nového přihlášení k systému {mediaServerName}", "components.Settings.SonarrModal.animerootfolder": "Kořenová složka Anime", "components.Settings.SonarrModal.loadingrootfolders": "Načítání kořenových složek…", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "Token API aplikace", "components.Settings.RadarrModal.editradarr": "Upravit server Radarr", "components.Settings.RadarrModal.createradarr": "Přidání nového serveru Radarr", "components.Settings.RadarrModal.inCinemas": "V kinech", "components.Settings.RadarrModal.released": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.selectMinimumAvailability": "Vyberte minimální dostupnost", "components.Settings.SettingsAbout.Releases.viewongithub": "Zobrazit na GitHubu", "components.Settings.librariesRemaining": "Zbývající knihovny: {count}", "components.Settings.noDefault4kServer": "Server 4K {serverType} musí být ozna<PERSON>en jako v<PERSON>, aby u<PERSON><PERSON><PERSON> mohli odes<PERSON> p<PERSON>adavky 4K {mediaType}.", "components.Settings.noDefaultNon4kServer": "Pokud máte pouze jeden server {serverType} pro obsah jiný než 4K i 4K (nebo pokud stahujete pouze obsah 4K), v<PERSON>š server {serverType} by <strong>neměl</strong> b<PERSON>t označen jako server 4K.", "components.Settings.noDefaultServer": "<PERSON><PERSON> moh<PERSON> být zpracovány požadavky typu {mediaType}, musí být alespoň jeden server typu {serverType} označen jako výchozí.", "components.Settings.plexsettingsDescription": "Nastavte připojení k Plex serveru. <PERSON><PERSON><PERSON>rr prohledá knihovny Plex serveru aby zjistil dostupnost obsahu.", "components.Settings.toastPlexRefresh": "Získání seznamu serverů z aplikace Plex…", "components.Settings.toastPlexRefreshSuccess": "Seznam serverů Plex úspěšně načten!", "components.UserList.passwordinfodescription": "Nakonfigurujte adresu URL aplikace a povolte e-mailová <PERSON>, která umožní automatic<PERSON>é generov<PERSON> he<PERSON>.", "components.UserList.sortCreated": "Datum <PERSON>", "components.UserList.sortDisplayName": "Zobrazované <PERSON>", "components.UserList.usercreatedfailed": "Při vytváření uživatele se něco pokazilo.", "components.UserList.usercreatedfailedexisting": "Zadaná e-mailová adresa je již používána jiným uživatelem.", "components.UserList.userdeleteerror": "Při odstraňování uživatele se něco pokazilo.", "components.RequestModal.requestApproved": "<PERSON><PERSON><PERSON><PERSON> o <strong>{title}</strong> schv<PERSON><PERSON>!", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Nastavení oznámení Pushbullet úspěšně uloženo!", "components.RequestModal.approve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.errorediting": "Při úpravě požadavku se něco pokazilo.", "components.Settings.Notifications.NotificationsPushover.validationTypes": "Musíte vybrat alespoň jeden typ oznámení", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "Vytvoření integrace <WebhookLink>Příchozí webový háček</WebhookLink>", "components.RequestModal.requestedited": "Požadavek na <strong>{title}</strong> úspěšně upraven!", "components.NotificationTypeSelector.issueresolvedDescription": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> jsou problémy vyřeš<PERSON>y.", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "Musíte zadat platný klíč uživatele nebo skupiny", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "Web push testovací oznámení odesláno!", "components.PermissionEdit.autoapproveMoviesDescription": "Automatic<PERSON>é s<PERSON>valování žádostí o filmy jiné než 4K.", "components.PermissionEdit.requestDescription": "Udělit povolení k předkládání žádostí o média jiná než 4K.", "components.RequestModal.AdvancedRequester.animenote": "* Tento seriál je anime.", "components.Settings.Notifications.NotificationsPushover.userToken": "<PERSON><PERSON><PERSON><PERSON> už<PERSON>le nebo skupiny", "components.RequestCard.failedretry": "Při opakovaném pokusu o zadání požadavku se něco pokazilo.", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "Vyžaduje se pouze v případě, že nepoužíváte profil <code>default</code>", "components.RequestCard.mediaerror": "{mediaType} Nenalezeno", "components.RequestList.RequestItem.mediaerror": "{mediaType} Nenalezeno", "components.RequestModal.QuotaDisplay.allowedRequests": "Můž<PERSON> p<PERSON>t o <strong>{limit}</strong> {type} každé <strong>{days}</strong> dny.", "components.RequestModal.SearchByNameModal.notvdbiddescription": "<PERSON><PERSON> s<PERSON><PERSON> jsme nemohli automaticky spárovat. Níže prosím vyberte správnou shodu.", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "Základ URL musí mít na začátku lomítko", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Plex Nedávno přidané skenování", "components.Settings.SettingsLogs.logsDescription": "Tyto protokoly můžete také zobrazit přímo prostřednictvím <code>stdout</code> nebo v <code>{appDataPath}/logs/overseerr.log</code>.", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "Nastavení e-mailových oznámení se nepodařilo ulož<PERSON>.", "components.Settings.SonarrModal.loadingprofiles": "Načítání kvalitních profilů…", "components.Settings.SonarrModal.selectRootFolder": "Vyberte kořenovou složku", "components.ResetPassword.requestresetlinksuccessmessage": "Na zadanou e-mailovou adresu bude zaslán odkaz pro obnovení hesla, pokud je spojena s platným uživatelem.", "components.RequestModal.pendingrequest": "Čekající <PERSON>", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "Nastavení oznámení LunaSea úspěšně uloženo!", "components.Settings.SonarrModal.default4kserver": "Výchozí server 4K", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "<PERSON><PERSON><PERSON> <UsersGroupsLink>identifik<PERSON>tor už<PERSON>le nebo skupiny</UsersGroupsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "Nastavení oznámení Pushover úspěšně uloženo!", "components.RequestModal.requestcancelled": "<PERSON><PERSON><PERSON><PERSON> o <strong>{title}</strong> <PERSON><PERSON><PERSON><PERSON>.", "components.Settings.SettingsUsers.toastSettingsFailure": "Při ukládání nastavení se něco pokazilo.", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "Testovací oznámení webhooku se nepodařilo odeslat.", "components.Settings.Notifications.botUsernameTip": "Umožněte uživatelům také zahájit chat s vaším botem a nakonfigurovat si vlastní oznámení", "components.Settings.Notifications.emailsettingssaved": "Nastavení e-mailových oznámení bylo úspěšně uloženo!", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Oznámení o testu Pushover odesláno!", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "Nastavení oznámení služby Slack se nepodařilo <PERSON>.", "components.Settings.toastPlexConnectingSuccess": "Připojení k systému Plex úspěšně navázáno!", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "Odeslání oznámení o testu LunaSea…", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "Vytvořte token ze svého <PushbulletSettingsLink>Nastavení <PERSON></PushbulletSettingsLink>", "components.Settings.Notifications.encryptionTip": "Ve většině případů používá implicitní TLS port 465 a STARTTLS port 587", "components.Settings.Notifications.toastDiscordTestFailed": "Oznámení o testu Discord se nepodařilo o<PERSON>.", "components.Settings.SettingsUsers.movieRequestLimitLabel": "Globální limit požadavků na filmy", "components.Settings.validationUrlBaseTrailingSlash": "Základ URL nesmí končit koncovým lomítkem", "components.TvDetails.firstAirDate": "Datum prvního vysílání", "components.Settings.RadarrModal.validationApiKeyRequired": "Musíte zadat klíč API", "components.Settings.toastPlexConnectingFailure": "Nepodařilo se připojit k systému Plex.", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "Nastavení oznámení LunaSea se nepodařilo ul<PERSON>ž<PERSON>.", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicationRegistrationLink>Registrace aplikace</ApplicationRegistrationLink> pro použití s aplikac<PERSON>", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "Musíte zadat platný token aplikace", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "Testovací oznámení <PERSON> se nepodařilo o<PERSON>lat.", "components.Settings.SonarrModal.animelanguageprofile": "<PERSON><PERSON> <PERSON><PERSON>", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "JSON payload ú<PERSON><PERSON>šn<PERSON> resetován!", "components.Settings.SonarrModal.selectQualityProfile": "<PERSON><PERSON><PERSON><PERSON> profil k<PERSON>", "components.Settings.tautulliApiKey": "Klíč API", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "Aby bylo mo<PERSON><PERSON><PERSON> př<PERSON><PERSON><PERSON><PERSON> webová oznámení push, musí být služ<PERSON> Jellyseerr obsluhována prostřednictvím protokolu HTTPS.", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "Musíte zvolit minimální dostupnost", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "Základní adresa URL nesmí končit koncovým lomítkem", "components.Settings.SonarrModal.validationRootFolderRequired": "Je třeba vybrat kořenovou složku", "components.UserList.createlocaluser": "Vytvořit místního <PERSON>", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "Odesílání webového testovacího oznámení push…", "components.Settings.Notifications.chatIdTip": "Spusťte chat s botem, přidejte <GetIdBotLink>@get_id_bot</GetIdBotLink> a zadejte příkaz <code>/my_id</code>", "components.Settings.Notifications.emailsettingsfailed": "Nastavení e-mailových oznámení se nepodařilo ulož<PERSON>.", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "Musíte vybrat alespoň jeden typ oznámení", "components.Settings.SettingsAbout.betawarning": "Jedná se o software BETA. Funkce mohou být nefunkční a/nebo nestabilní. Jakékoli problémy prosím nahlaste na GitHubu!", "components.Settings.SonarrModal.createsonarr": "Přidání nového serveru Sonarr", "components.Settings.Notifications.pgpPasswordTip": "Podepisování <PERSON>ých e-mailových zpráv pomocí <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.SonarrModal.selectLanguageProfile": "<PERSON><PERSON><PERSON><PERSON><PERSON> profilu", "components.Settings.Notifications.telegramsettingsfailed": "Nastavení oznámení Telegramu se nepodařilo uložit.", "components.Settings.RadarrModal.toastRadarrTestFailure": "Nepodařilo se připojit k Radarru.", "components.Settings.SettingsUsers.localLogin": "Povolení místního přihlášení", "components.Settings.SettingsUsers.localLoginTip": "Umožnit uživatelům přihlašovat se pomocí e-mailové adresy a hesla namísto protokolu Plex OAuth", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "Nastavení oznámení Discordu se nepodařilo ulož<PERSON>.", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "Nastavení oznámení Pushbullet úspěšně uloženo!", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "Musíte zadat přís<PERSON>pový token", "components.Settings.SettingsJobsCache.cacheDescription": "Jellyseerr ukládá do mezipaměti požadavky na externí koncové body API, aby optimalizoval výkon a zamezil zbytečným voláním API.", "components.Settings.SonarrModal.validationHostnameRequired": "Musíte zadat platný název hostitele nebo IP adresu", "components.Settings.Notifications.toastTelegramTestFailed": "Testovací oznámení Telegramu se nepodařilo odeslat.", "components.Settings.Notifications.toastTelegramTestSending": "Odeslání testovacího oznámení Telegramu…", "components.Settings.SettingsJobsCache.jobsDescription": "Jellyseerr provád<PERSON> urč<PERSON> úlohy ú<PERSON> jako p<PERSON> naplán<PERSON><PERSON>, ale lze je také spustit ručně níže. Ruční spuštění úlohy nezmění její plán.", "components.Settings.SettingsUsers.toastSettingsSuccess": "Uživatelská nastavení byla úspěšně uložena!", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "Musíte zadat platný token aplikace", "components.Settings.Notifications.toastTelegramTestSuccess": "Oznámení o testu Telegramu odesláno!", "components.TvDetails.TvCast.fullseriescast": "Kompletní obsazení série", "components.UserList.importfromplexerror": "Při importu uživatelů systému Plex se něco pokazilo.", "components.Settings.Notifications.validationSmtpHostRequired": "Musíte zadat platný název hostitele nebo IP adresu", "components.Settings.RadarrModal.validationProfileRequired": "Je třeba vybrat profil k<PERSON>ity", "components.Settings.serviceSettingsDescription": "Níže nakonfigurujte server(y) {serverType}. Můžete připojit více serverů {serverType}, ale pouze dva z nich mohou být označeny jako výchozí (jeden ne-4K a jeden 4K). Správci mohou před schválením změnit server používaný ke zpracování nových požadavků.", "components.Settings.RadarrModal.testFirstTags": "Testovací připojení k načítání značek", "components.UserList.validationpasswordminchars": "<PERSON><PERSON><PERSON> je <PERSON><PERSON><PERSON><PERSON> k<PERSON>; m<PERSON>lo by m<PERSON><PERSON> <PERSON> 8 znaků", "components.UserProfile.pastdays": "{type} (pos<PERSON>n<PERSON>ch {days} dnů)", "components.Settings.SettingsJobsCache.cacheflushed": "Mezipaměť {cachename} by<PERSON> <PERSON>.", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "Musíte zadat platné ID uživatele", "components.Settings.SonarrModal.validationLanguageProfileRequired": "Je třeba vybrat jazykový profil", "components.Settings.SonarrModal.validationNameRequired": "Je třeba zadat název serveru", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "Zobrazované <PERSON>", "components.Settings.tautulliSettings": "<PERSON><PERSON><PERSON>", "components.Settings.toastTautulliSettingsSuccess": "Nastavení Tautulli úspěšně uloženo!", "components.UserProfile.UserSettings.unauthorizedDescription": "Nemáte oprávnění měnit nastavení tohoto u<PERSON>.", "components.UserList.newplexsigninenabled": "Nastavení <strong>Povolit nové přihlášení do systému Plex</strong> je v současné době povoleno. Uživatelé systému Plex s přístupem do knihovny nemusí být pro přihlášení importováni.", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "Musíte zadat platný klíč uživatele nebo skupiny", "components.UserList.usercreatedsuccess": "Uživatel úspěšně vytvořen!", "components.UserList.userdeleted": "Uživatel úspěšně smazán!", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "<FindDiscordIdLink>vícemístné identifikační <PERSON></FindDiscordIdLink> spojené s vaším uživatelským účtem Discord", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "Uživatelské ID Discordu", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "Přepsání globálního limitu", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "Musíte zadat platné ID uživatele služby Discord", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "Veřejný klíč PGP", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "Šifrování e-mailových zpráv pomocí <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "Nastavení webových oznámení push bylo úspěšně uloženo!", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "<FindDiscordIdLink>vícemístné identifikační č<PERSON>lo</FindDiscordIdLink> spojené s vaším uživatelským účtem", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> token", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "<PERSON><PERSON><PERSON><PERSON> už<PERSON>le nebo skupiny", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink>Začněte chat</TelegramBotLink>, přidejte <GetIdBotLink>@get_id_bot</GetIdBotLink> a zadejte příkaz <code>/my_id</code>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "Nastavení oznámení Pushbullet se nepodařilo ulož<PERSON>.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "Nastavení oznámení Pushover se nepodařilo uložit.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "<ApplicationRegistrationLink>Registrace aplikace</ApplicationRegistrationLink> pro použití s {applicationTitle}", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "Nastavení oznámení Telegramu úspěšně uloženo!", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "Nastavení webových push oznámení se nepodařilo ulož<PERSON>.", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "Tento uživatelský účet v současné době nemá nastavené heslo. Níže nastavte heslo, aby se tento účet mohl přihlašovat jako \"místní uživatel.\"", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "Váš účet v současné době nemá nastavené heslo. Níže nastavte heslo, abyste se mohli přihlásit jako \"místní uživatel\" pomocí své e-mailové adresy.", "i18n.importing": "Importování…", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {Epizoda} other {Epizody}}", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}}", "components.TvDetails.productioncountries": "{countryCount, plural, one {Zem<PERSON>} other {Země}} produkce", "components.TvDetails.network": "{networkCount, plural, one {<PERSON><PERSON>} other {<PERSON><PERSON>}}", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {sezóna} few {sezóny} other {sezón}}", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> {playCount, plural, one {přeh<PERSON><PERSON><PERSON>} few {p<PERSON>eh<PERSON><PERSON><PERSON>} other {přeh<PERSON>án<PERSON>}}", "components.MovieDetails.productioncountries": "{countryCount, plural, one {Zem<PERSON>} other {Země}} produkce", "components.MovieDetails.studio": "{studioCount, plural, one {Studio} other {Studia}}", "components.QuotaSelector.days": "{count, plural, one {den} few {dny} other {dní}}", "components.TvDetails.seasons": "{seasonCount, plural, one {# sezóna} few {# sezóny} other {# sezón}}", "components.RequestCard.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}}", "components.RequestModal.QuotaDisplay.requiredquotaUser": "Aby mohl tento uživatel zažádat o tento seriál, musí mít alespoň <strong>{seasons}</strong> {seasons, plural, one {zbývající žádost o sezónu} few {zbývající žádosti o sezónu} other {zbývajících žádostí o sezónu}}.", "components.RequestModal.requestmovies4k": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o {count} {count, plural, one {film} few {filmy} other {filmů}} ve 4K", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "{jobScheduleHours, plural, one {Každou hodinu} few {Každé {jobScheduleHours} hodiny} other {<PERSON><PERSON><PERSON><PERSON>ch {jobScheduleHours} hodin}}", "components.RequestBlock.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}}", "components.RequestButton.approve4krequests": "Schv<PERSON>lit {requestCount, plural, one {4K žádost} few {{requestCount} 4K žádosti} other {{requestCount} 4K žádostí}}", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}}", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "<PERSON>to u<PERSON> mů<PERSON>e p<PERSON>t o <strong>{limit}</strong> {type} každ<PERSON>ch <strong>{days}</strong> dní.", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {film} few {filmy} other {filmů}}", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {<PERSON><PERSON><PERSON><PERSON>} other {<strong>#</strong>}} {type} {remaining, plural, one {zbývaj<PERSON><PERSON><PERSON>} few {zb<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>} other {zbývajících žádostí}}", "components.RequestModal.requestseasons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o {seasonCount} {seasonCount, plural, one {sezónu} few {sezóny} other {sezón}}", "components.RequestModal.requestseasons4k": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o {seasonCount} {seasonCount, plural, one {sezónu} few {sezóny} other {sezón}} ve 4K", "components.UserList.importedfromplex": "Úspěšně {userCount, plural, one {importován} few {importováni} other {importováno}} <strong>{userCount}</strong> {userCount, plural, one {uživatel} few {uživatelé} other {uživatelů}} Plex!", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "Filtrovat obsah podle původního jazyka", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "Vytvořte token z <PushbulletSettingsLink>Nastavení <PERSON></PushbulletSettingsLink>", "components.QuotaSelector.movies": "{count, plural, one {film} few {filmy} other {filmů}}", "components.QuotaSelector.seasons": "{seasonCount, plural, one {sezóna} few {sezóny} other {sezón}}", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{seasons} za {quotaDays} {days}</quotaUnits>", "components.RequestButton.approverequests": "Schv<PERSON>lit {requestCount, plural, one {žádost} few {{requestCount} <PERSON><PERSON><PERSON><PERSON>} other {{requestCount} žádostí}}", "components.RequestButton.decline4krequests": "Odmítnout {requestCount, plural, one {4K žádost} few {{requestCount} 4K žádosti} other {{requestCount} 4K žádostí}}", "components.RequestButton.declinerequests": "Odmítnout {requestCount, plural, one {žádost} few {{requestCount} <PERSON><PERSON><PERSON><PERSON>} other {{requestCount} žádost<PERSON>}}", "components.RequestModal.requestmovies": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o {count} {count, plural, one {film} few {filmy} other {filmů}}", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "{jobScheduleMinutes, plural, one {Každou minutu} few {Každé {jobScheduleMinutes} minuty} other {<PERSON><PERSON><PERSON><PERSON>ch {jobScheduleMinutes} minut}}", "components.MovieDetails.digitalrelease": "Digitální vydání", "components.Discover.DiscoverWatchlist.discoverwatchlist": "Váš seznam ke zhlédnutí Plex", "components.MovieDetails.physicalrelease": "Fyzické vydání", "components.MovieDetails.managemovie": "Spravovat film", "components.AirDateBadge.airsrelative": "Vysílá se {relativeTime}", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Žádosti o filmy", "components.MovieDetails.rtaudiencescore": "Skóre publika Rotten Tomatoes", "components.MovieDetails.rtcriticsscore": "Rotten Tomatoes Tomatometer", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "Žádosti o seriály", "components.AirDateBadge.airedrelative": "Vysíláno {relativeTime}", "components.Layout.UserDropdown.requests": "<PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.reportissue": "Nahlásit problém", "components.StatusChecker.restartRequired": "Je vyžadován restart serveru", "components.PermissionEdit.viewrecent": "Zobrazit naposledy přidané", "components.StatusChecker.appUpdated": "{applicationTitle} Aktualizováno", "components.StatusChecker.appUpdatedDescription": "Chcete-li aplikaci znovu načíst, klikněte na tlačítko níže.", "components.StatusChecker.restartRequiredDescription": "Chcete-li použít aktualizovaná nastavení, restartujte server.", "components.RequestCard.tmdbid": "TMDB ID", "components.RequestList.RequestItem.tmdbid": "TMDB ID", "components.TitleCard.tmdbid": "TMDB ID", "components.TitleCard.tvdbid": "TheTVDB ID", "components.RequestCard.tvdbid": "TheTVDB ID", "components.RequestList.RequestItem.tvdbid": "TheTVDB ID", "components.PermissionEdit.autorequestMovies": "Automatické vyžádání filmů", "components.PermissionEdit.autorequestMoviesDescription": "Udělte oprávnění k automatickému odesílání žádostí o filmy v jiném rozlišení než 4K prostřednictvím Plex Watchlistu.", "components.PermissionEdit.viewrecentDescription": "Udělte oprávnění k zobrazení seznamu nedávno přidaných médií.", "components.Settings.restartrequiredTooltip": "Aby se projevily změny tohoto nastavení, mus<PERSON> b<PERSON><PERSON>", "components.StatusChecker.reloadApp": "<PERSON>nov<PERSON> načíst {applicationTitle}", "components.TitleCard.cleardata": "Vyčistit data", "components.TitleCard.mediaerror": "{mediaType} Nenalezeno", "components.PermissionEdit.autorequestDescription": "Udělte oprávnění k automatickému odesílání žádostí o média jiná než 4K prostřednictvím seznamu Plex Watchlist.", "components.PermissionEdit.autorequest": "Automatická <PERSON>", "components.PermissionEdit.autorequestSeries": "Automaticky vyžádat seriál", "components.PermissionEdit.viewwatchlists": "Zobrazit Plex Watchilisty", "components.Settings.advancedTooltip": "Nesprávná konfigurace tohoto nastavení může vést k narušení funkčnosti", "components.Settings.deleteServer": "Smazat server {serverType}", "components.Settings.experimentalTooltip": "Povolení tohoto nastavení může vést k neočekávanému chování aplikace", "components.Settings.SettingsLogs.viewdetails": "Zobrazit podrobnosti", "components.PermissionEdit.autorequestSeriesDescription": "Udělte oprávnění k automatickému odesílání žádostí o seriály jiné než 4K prostřednictvím Plex Watchlist.", "components.RequestBlock.approve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestBlock.decline": "Odmítnout požadavek", "components.RequestBlock.lastmodifiedby": "Naposledy změněno od", "components.RequestBlock.requestdate": "Datum p<PERSON>žadav<PERSON>", "components.RequestBlock.requestedby": "Vyžádáno od", "components.RequestCard.approverequest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestCard.cancelrequest": "Zrušit <PERSON>", "components.RequestCard.declinerequest": "Odmítnout požadavek", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "Plex Watchlist synchronizace", "components.StatusBadge.managemedia": "Spravovat {mediaType}", "components.StatusBadge.openinarr": "Otevřít v {arr}", "components.StatusBadge.playonplex": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cez {mediaServerName}", "components.TvDetails.manageseries": "Spravovat sérii", "components.RequestBlock.delete": "Smazat požadavek", "components.RequestBlock.edit": "Upravit požadavek", "components.RequestCard.editrequest": "Upravit požadavek", "components.RequestModal.SearchByNameModal.nomatches": "Pro tuto sérii jsme ne<PERSON> shodu.", "components.RequestModal.requestcollection4ktitle": "Vyžádejte si kolekci ve 4K", "components.RequestModal.requestcollectiontitle": "Vyžádat k<PERSON>", "components.RequestModal.requestmovie4ktitle": "Vyžádejte si film ve 4K", "components.RequestModal.requestmovietitle": "Vyžádat si film", "components.RequestModal.requestseries4ktitle": "Žádost o sérii ve 4K", "components.RequestModal.requestseriestitle": "Vyžádat sérii", "components.TvDetails.episodeCount": "{episodeCount, plural, one {# epizoda} few {# epizody} other {# epizod}}", "components.NotificationTypeSelector.mediaautorequested": "Žádost byla automaticky odeslána", "components.NotificationTypeSelector.mediaautorequestedDescription": "Nechte se upozornit, k<PERSON><PERSON> jsou nové žádosti o média automaticky odeslány pro položky na vašem Seznamu ke zhlédnutí Plex.", "components.PermissionEdit.viewwatchlistsDescription": "Udělte oprávnění k zobrazení Plex Watchlistu ostatních uživatelů.", "components.TvDetails.Season.somethingwentwrong": "Při načítání údajů o sezóně se něco pokazilo.", "i18n.restartRequired": "Je vyžadován restart", "components.Discover.plexwatchlist": "Váš seznam ke zhlédnutí Plex", "components.MovieDetails.theatricalrelease": "Uvedení v kinech", "components.Discover.DiscoverWatchlist.watchlist": "Seznam ke zhlédnutí Plex", "components.TvDetails.reportissue": "Nahlásit problém", "components.MovieDetails.tmdbuserscore": "Uživatelské skóre TMDB", "components.TvDetails.seasonstitle": "Sezóny", "components.TvDetails.seasonnumber": "<PERSON><PERSON><PERSON><PERSON> {seasonNumber}", "components.TvDetails.rtcriticsscore": "Rotten Tomatoes Tomatometer", "components.TvDetails.rtaudiencescore": "Rotten Tomatoes Audience Skóre", "components.TvDetails.tmdbuserscore": "Uživatelské skóre TMDB", "components.TvDetails.status4k": "4K {status}", "components.Discover.emptywatchlist": "Zde se objeví média přidaná do vašeho <PlexWatchlistSupportLink>seznamu ke zhlédnutí Plex</PlexWatchlistSupportLink>.", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "Automaticky vyžádat filmy na vašem <PlexWatchlistSupportLink>Plex Watchlistu</PlexWatchlistSupportLink>", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "Automaticky vyžádejte sérii na vašem <PlexWatchlistSupportLink>Plex Watchlistu</PlexWatchlistSupportLink>", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "Automaticky vyžádat sérii", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "Automaticky vyžádat filmy", "components.UserProfile.plexwatchlist": "Plex Watchlist", "components.UserProfile.emptywatchlist": "Zde se zobrazí média přidaná do vašeho <PlexWatchlistSupportLink>Plex Watchlistu</PlexWatchlistSupportLink>.", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "Aktuální frekvence", "components.Discover.DiscoverMovieKeyword.keywordMovies": "Filmy: {keywordTitle}", "components.Discover.networks": "<PERSON><PERSON>", "components.Discover.moviegenres": "Žánry filmů", "components.Discover.tmdbmoviegenre": "Žánr filmu TMDB", "components.Discover.tmdbnetwork": "Stanice TMDB", "components.Discover.CreateSlider.providetmdbsearch": "Zadejte vyhledávací dotaz", "components.Discover.CreateSlider.starttyping": "Začínáte psát pro hledání.", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {# aktivní filtr} few {# aktivní filtry} other {# aktivních filtrů}}", "components.Discover.FilterSlideover.streamingservices": "Streamovací služby", "components.Discover.CreateSlider.addsuccess": "Vytvořen nový posuvník a uloženo nastavení přizpůsobení objevování.", "components.Discover.createnewslider": "Vytvořit nový posuvník", "components.Discover.customizediscover": "Přizpůsobit objevování", "components.Discover.CreateSlider.validationDatarequired": "Mus<PERSON><PERSON> zadat hodnotu dat.", "components.Discover.CreateSlider.validationTitlerequired": "Musíte z<PERSON> n<PERSON>zev.", "components.Discover.DiscoverSliderEdit.deletefail": "Nepodařilo se odstranit posuvník.", "components.Discover.DiscoverSliderEdit.deletesuccess": "Posuvník úspěšně odstraněn.", "components.Discover.DiscoverSliderEdit.enable": "Přepnout viditelnost", "components.Discover.DiscoverSliderEdit.remove": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.DiscoverTvKeyword.keywordSeries": "<PERSON>i<PERSON><PERSON>: {keywordTitle}", "components.Discover.PlexWatchlistSlider.emptywatchlist": "Zde se objeví média přidaná do vašeho <PlexWatchlistSupportLink>seznamu ke shlédnutí Plex</PlexWatchlistSupportLink>.", "components.Discover.stopediting": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.studios": "St<PERSON><PERSON>", "components.Discover.tmdbmoviekeyword": "Klíčové slovo filmu TMDB", "components.Discover.tmdbtvkeyword": "Klíčové slovo seriálu TMDB", "components.Discover.tvgenres": "<PERSON><PERSON><PERSON>", "components.Discover.updatefailed": "Při aktualizaci nastavení přizpůsobení objevování se něco pokazilo.", "components.Discover.tmdbtvgenre": "Žánr seriálu TMDB", "components.Discover.updatesuccess": "Nastavení přizpůsobení objevování aktualizováno.", "components.Discover.DiscoverMovies.discovermovies": "Filmy", "components.Discover.DiscoverMovies.sortPopularityAsc": "Oblíbenost vzestupně", "components.Discover.DiscoverMovies.sortPopularityDesc": "Oblíbenost sestupně", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "Datum vydání vzestupně", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "<PERSON>tum v<PERSON> sest<PERSON>", "components.Discover.DiscoverMovies.sortTitleAsc": "Název (A-Z) vzestupně", "components.Discover.DiscoverMovies.sortTitleDesc": "N<PERSON><PERSON>v (A-Z) sestupně", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "Hodnocení TMDB vzestupně", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "Hodnocení TMDB sestupně", "components.Discover.DiscoverTv.discovertv": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "Datum prvního vysílání vzestupně", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {# aktivní filtr} few {# aktivní filtry} other {# aktivních filtrů}}", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "Datum prvního vysílání sestupně", "components.Discover.DiscoverTv.sortPopularityAsc": "Oblíbenost vzestupně", "components.Discover.DiscoverTv.sortPopularityDesc": "Oblíbenost sestupně", "components.Discover.DiscoverTv.sortTitleAsc": "Název (A-Z) vzestupně", "components.Discover.DiscoverTv.sortTitleDesc": "N<PERSON><PERSON>v (A-Z) sestupně", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "Hodnocení TMDB vzestupně", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "Hodnocení TMDB sestupně", "components.Discover.FilterSlideover.clearfilters": "Vymazat aktivní filtry", "components.Discover.FilterSlideover.filters": "Filtry", "components.Discover.FilterSlideover.firstAirDate": "Datum prvního vysílání", "components.Discover.FilterSlideover.from": "Od", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {# aktivní filtr} few {# aktivní filtry} other {# aktivních filtrů}}", "components.Discover.FilterSlideover.genres": "<PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.keywords": "<PERSON><PERSON><PERSON><PERSON><PERSON> slova", "components.Discover.FilterSlideover.originalLanguage": "Původní jazyk", "components.Discover.FilterSlideover.ratingText": "Hodn<PERSON><PERSON><PERSON> mezi {minValue} a {maxValue}", "components.Discover.FilterSlideover.releaseDate": "<PERSON><PERSON>", "components.Discover.FilterSlideover.runtime": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.runtimeText": "{minValue}-{maxValue} minut délky", "components.Discover.FilterSlideover.studio": "Studio", "components.Discover.FilterSlideover.tmdbuserscore": "Uživatelské hodnocení TMDB", "components.Discover.FilterSlideover.to": "Do", "components.Discover.PlexWatchlistSlider.plexwatchlist": "Váš seznam ke zhlédnutí Plex", "components.DownloadBlock.formattedTitle": "{title}: <PERSON><PERSON><PERSON><PERSON> {seasonNumber} E<PERSON><PERSON>da {episodeNumber}", "components.Discover.RecentlyAddedSlider.recentlyAdded": "Nedávno přidané", "components.Discover.resetfailed": "Při obnovení nastavení přizpůsobení objevování se něco pokazilo.", "components.Discover.resetsuccess": "Nastavení přizpůsobení objevování úspěšně obnoveno.", "components.Discover.resettodefault": "Obnovit na výchozí", "components.Discover.resetwarning": "Obnovit všechny posuvníky na výchozí hodnoty. Toto také odstraní všechny vlastní posuvníky!", "components.Discover.tmdbsearch": "Hledání TMDB", "components.Discover.tmdbstudio": "Studio TMDB", "components.Layout.Sidebar.browsemovies": "Filmy", "components.Layout.Sidebar.browsetv": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.CreateSlider.addSlider": "Přidat posuvník", "components.Discover.CreateSlider.addcustomslider": "Vytvořit vlastní posuvník", "components.Discover.CreateSlider.addfail": "Nepodařilo se vytvořit nový posuvník.", "components.Discover.CreateSlider.editSlider": "Upravit posuvník", "components.Discover.CreateSlider.editfail": "Nepodařilo se upravit posuvník.", "components.Discover.CreateSlider.editsuccess": "Upraven posuvník a uloženo nastavení přizpůsobení objevování.", "components.Discover.CreateSlider.needresults": "Musíte mít alespoň 1 výsledek.", "components.Discover.CreateSlider.nooptions": "Žádné v<PERSON>ky.", "components.Discover.CreateSlider.providetmdbgenreid": "Zadejte ID žánru TMDB", "components.Discover.CreateSlider.providetmdbkeywordid": "Zadejte ID klíčového slova TMDB", "components.Discover.CreateSlider.providetmdbnetwork": "Zadejte ID stanice TMDB", "components.Discover.CreateSlider.providetmdbstudio": "Zadejte ID studia TMDB", "components.Discover.CreateSlider.searchGenres": "Hledat žánry…", "components.Discover.CreateSlider.searchKeywords": "Hledat klíčová slova…", "components.Discover.CreateSlider.searchStudios": "Hledat studia…", "components.Discover.CreateSlider.slidernameplaceholder": "Název posuvníku", "components.Settings.SettingsMain.toastApiKeySuccess": "Nový klíč API byl úspěšně vygenerován!", "components.TvDetails.Season.noepisodes": "Seznam epizod není k dispozici.", "components.Settings.SettingsJobsCache.imagecachesize": "Celková velikost mezipaměti", "components.Settings.SettingsMain.validationApplicationUrl": "Musíte zadat platnou adresu URL", "components.Selector.starttyping": "Začínáte psát a hledáte.", "components.Settings.SettingsMain.validationApplicationUrlTrailingSlash": "Adresa URL nesmí končit koncovým lomítkem", "components.Selector.searchStudios": "Hledat studia…", "components.RequestCard.unknowntitle": "Neznámý název", "components.RequestList.RequestItem.unknowntitle": "Neznámý název", "components.Selector.searchGenres": "<PERSON><PERSON><PERSON><PERSON>…", "components.Settings.SettingsJobsCache.editJobScheduleSelectorSeconds": "Každou {jobScheduleSeconds, plural, one {sekundu} other {{jobScheduleSeconds} sekund}}", "components.Settings.SettingsMain.apikey": "API klíč", "components.Settings.SettingsMain.applicationTitle": "Název aplikace", "components.Settings.SettingsMain.applicationurl": "Adresa URL aplikace", "components.Settings.SettingsMain.cacheImages": "Povolení ukládání obrázků do mezipaměti", "components.Settings.SettingsMain.cacheImagesTip": "Ukládání obrázků z externích zdrojů do mezipaměti (vyžaduje značné množství místa na disku)", "components.Settings.SettingsMain.general": "Obecné", "components.Settings.SettingsMain.generalsettings": "Obecná nastavení", "components.Settings.SettingsMain.generalsettingsDescription": "Konfigurace globálních a výchozích nastavení pro Jellyseerr.", "components.Settings.SettingsMain.hideAvailable": "<PERSON><PERSON><PERSON><PERSON><PERSON> dos<PERSON> média", "components.Settings.SettingsMain.locale": "Jazyk zobrazení", "components.Settings.SettingsMain.originallanguage": "Objevte jazyk", "components.Settings.SettingsMain.originallanguageTip": "Filtrování obsahu podle původního jazyka", "components.Settings.SettingsMain.partialRequestsEnabled": "Povolení požadavků na částečné série", "components.Settings.SettingsJobsCache.imagecachecount": "Obrázky v mezipaměti", "components.Settings.SettingsJobsCache.imagecache": "Vyrovnávací paměť obrázků", "components.Settings.SettingsJobsCache.imagecacheDescription": "Pokud je tato funkce povolena v nastavení, bude služba Jellyseerr proxy serverem a ukládat do mezipaměti obrázky z předem nakonfigurovaných externích zdrojů. Cached images are saved into your config folder. You can find the files in <code>{appDataPath}/cache/images</code>.", "components.Settings.SettingsMain.validationApplicationTitle": "Musíte uvést název žádosti", "components.Settings.SettingsMain.toastApiKeyFailure": "Při generování nového klíče API se něco pokazilo.", "components.Settings.SettingsMain.toastSettingsFailure": "Při ukládání nastavení se něco pokazilo.", "components.Settings.SettingsMain.toastSettingsSuccess": "Nastavení úspěšně uloženo!", "components.Settings.SettingsJobsCache.image-cache-cleanup": "Čištění mezipa<PERSON>ěti obrázků", "components.StatusBadge.seasonepisodenumber": "S{seasonNumber}E{episodeNumber}", "components.Selector.searchKeywords": "<PERSON><PERSON><PERSON><PERSON>ová slova pro vyhledávání…", "components.Selector.showless": "Zobrazit méně", "components.Selector.showmore": "Zobrazit více", "components.Selector.nooptions": "Žádné v<PERSON>ky.", "components.Discover.FilterSlideover.voteCount": "Počet hlasů mezi {minValue} a {maxValue}", "components.Settings.RadarrModal.tagRequestsInfo": "Automatické p<PERSON>í do<PERSON>točné značky s ID uživatele a zobrazovaným jménem žadatele", "components.Settings.SonarrModal.tagRequests": "Požadavky na štítek", "i18n.collection": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.tagRequests": "Požadavky na štítek", "components.Settings.SettingsJobsCache.availability-sync": "Synchronizace dostupnosti médií", "components.Discover.FilterSlideover.tmdbuservotecount": "Počet hlasů uživatelů TMDB", "components.Discover.tmdbmoviestreamingservices": "Filmové streamovací služby TMDB", "components.Settings.SonarrModal.tagRequestsInfo": "Automatické p<PERSON>í do<PERSON>točné značky s ID uživatele a zobrazovaným jménem žadatele", "components.Discover.tmdbtvstreamingservices": "Televizní streamovací služby TMDB", "components.Settings.Notifications.NotificationsPushover.sound": "Zvuk upozornění", "components.MovieDetails.imdbuserscore": "Uživatelské skóre IMDB", "components.UserProfile.UserSettings.UserNotificationSettings.sound": "Zvuk upozornění", "components.Settings.SonarrModal.animeSeriesType": "Typ anime série", "components.Settings.Notifications.NotificationsPushover.deviceDefault": "Výchozí zařízení", "components.UserProfile.UserSettings.UserNotificationSettings.deviceDefault": "Výchozí zařízení", "components.Settings.SonarrModal.seriesType": "Typ série", "components.Login.servertype": "Typ serveru", "components.Login.validationEmailFormat": "Neplatný email", "components.Login.validationUrlBaseLeadingSlash": "Základ URL adresy musí obsahovat lomítko", "components.Login.validationEmailRequired": "Musíte posky<PERSON>ut email", "components.Login.validationPortRequired": "Musíte poskytnout platné číslo portu", "components.Login.back": "<PERSON><PERSON><PERSON><PERSON><PERSON> se", "components.Login.validationUrlBaseTrailingSlash": "Základ URL adresy nesmí končit lomítkem", "components.Setup.back": "<PERSON><PERSON><PERSON><PERSON><PERSON> se", "components.Login.validationusernamerequired": "Uživatelské jméno je povinné", "components.Setup.configemby": "<PERSON><PERSON><PERSON><PERSON>", "components.PermissionEdit.blacklistedItemsDescription": "Ud<PERSON>lit oprávnění přidávat média na černou listinu.", "components.Login.signinwithjellyfin": "<PERSON><PERSON><PERSON><PERSON><PERSON> {mediaServerName} účet", "components.Settings.Notifications.userEmailRequired": "Vyžadovat email uživatelů", "components.TitleCard.addToWatchList": "Přidat na seznam sledování", "components.TvDetails.removefromwatchlist": "Odstranit ze seznamu sledování", "components.Login.credentialerror": "Nesprávné <PERSON>é j<PERSON>no nebo he<PERSON>lo.", "components.Setup.servertype": "Zvolte typ serveru", "components.Settings.manualscanDescriptionJellyfin": "Ob<PERSON>kle se provád<PERSON> pouze jednou za 24 hodin. Je<PERSON><PERSON>rr bude kontrolovat nedávno přidané položky vašeho {mediaServerName} serveru agresivněji. Pokud nastavujete Jellyseerr poprvé, doporučujeme provést jednorázovou úplnou ruční kontrolu knihoven!", "components.ManageSlideOver.manageModalRemoveMediaWarning": "* Toto nenávratně odstraní tento {mediaType} z {arr}, včetně v<PERSON><PERSON>ů.", "components.Blacklist.blacklistdate": "datum", "components.Blacklist.mediaName": "Jméno", "components.MovieDetails.watchlistDeleted": "<strong>{title}</strong> úspěšně odstraněno ze seznamu sledování!", "components.Settings.Notifications.validationWebhookRoleId": "<PERSON><PERSON><PERSON><PERSON> posky<PERSON><PERSON>ut platné <PERSON> Discord role", "components.Blacklist.blacklistedby": "{date} uživatelem {user}", "components.Layout.UserWarnings.passwordRequired": "<PERSON><PERSON><PERSON> je povinné.", "components.Login.validationHostnameRequired": "Musíte poskytnout platné hostitelské jméno nebo IP adresu", "components.Selector.searchStatus": "Vyberte status…", "components.TvDetails.watchlistSuccess": "<strong>{title}</strong> úspěšně přidáno na seznam sledování!", "components.Blacklist.blacklistNotFoundError": "<strong>{title}</strong> není na černé list<PERSON>.", "components.TitleCard.watchlistDeleted": "<strong>{title}</strong> úspěšně odstraněno ze seznamu sledování!", "component.BlacklistBlock.blacklistdate": "Datum přidání na černou listinu", "component.BlacklistBlock.blacklistedby": "Přidáno na černou listinu uživatelem", "components.Blacklist.blacklistSettingsDescription": "Spravovat média na černé listině.", "components.Blacklist.mediaTmdbId": "tmdb ID", "components.Blacklist.mediaType": "<PERSON><PERSON>", "components.Blacklist.blacklistsettings": "Nastavení černé <PERSON>", "components.Discover.FilterSlideover.status": "Status", "components.Layout.Sidebar.blacklist": "Černá listina", "components.Layout.UserWarnings.emailInvalid": "<PERSON><PERSON><PERSON> adresa je neplatná.", "components.Layout.UserWarnings.emailRequired": "<PERSON><PERSON><PERSON> adresa je povinná.", "components.Login.emailtooltip": "Adresa <PERSON> být asociována s <PERSON>š<PERSON> {mediaServerName} instancí.", "components.Login.enablessl": "Používat SSL", "components.Login.initialsignin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Login.initialsigningin": "Přip<PERSON>jo<PERSON><PERSON>…", "components.Login.invalidurlerror": "N<PERSON>ze se připojit k {mediaServerName} serveru.", "components.Login.port": "Port", "components.Login.save": "<PERSON><PERSON><PERSON><PERSON>", "components.Login.saving": "Přidá<PERSON>…", "components.Login.username": "Uživatelské jméno", "components.Login.validationemailformat": "<PERSON> po<PERSON><PERSON><PERSON> platný email", "components.Login.validationhostformat": "Je požadována platná URL", "components.Login.validationhostrequired": "<PERSON> p<PERSON> {mediaServerName} URL", "components.Login.validationservertyperequired": "Prosím zvolte typ serveru", "components.Login.hostname": "{mediaServerName} URL", "components.Login.urlBase": "Základ URL adresy", "components.Login.validationUrlTrailingSlash": "URL adresa nesmí končit lomítkem", "components.ManageSlideOver.removearr": "Odstranit z {arr}", "components.ManageSlideOver.removearr4k": "Odstranit z 4K {arr}", "components.MovieDetails.addtowatchlist": "Přidat na seznam sledování", "components.MovieDetails.downloadstatus": "<PERSON><PERSON>", "components.MovieDetails.openradarr": "Otevřít film v Radarr", "components.MovieDetails.openradarr4k": "Otevřít film ve 4K Radarr", "components.MovieDetails.removefromwatchlist": "Odstranit ze seznamu sledování", "components.MovieDetails.watchlistError": "Něco se pokazilo. Zkuste to znovu.", "components.MovieDetails.watchlistSuccess": "<strong>{title}</strong> úspěšně přidáno na seznam sledování!", "components.PermissionEdit.blacklistedItems": "Přidat média na černou listinu.", "components.PermissionEdit.manageblacklist": "Spravovat černou listinu", "components.PermissionEdit.manageblacklistDescription": "<PERSON><PERSON><PERSON><PERSON> oprávnění spravovat černou listinu.", "components.PermissionEdit.viewblacklistedItems": "Zobrazit černou listinu.", "components.PermissionEdit.viewblacklistedItemsDescription": "Udělit oprávnění zobrazit černou listinu.", "components.RequestList.RequestItem.profileName": "Profil", "components.RequestList.RequestItem.removearr": "Odstranit z {arr}", "components.RequestList.sortDirection": "Přepnout směr <PERSON>", "components.Selector.canceled": "<PERSON><PERSON><PERSON><PERSON>", "components.Selector.inProduction": "V produkci", "components.Settings.SettingsAbout.supportjellyseerr": "Podpo<PERSON><PERSON>", "components.Settings.SettingsJobsCache.jellyfin-recently-added-scan": "Sken nedávno přidaných na Jellyfin", "components.Settings.SettingsJobsCache.jellyfin-full-scan": "Kompletní sken knihoven Jellyfin", "components.Settings.SettingsJobsCache.plex-refresh-token": "Obnovení Plex tokenu", "components.Settings.SettingsMain.discoverRegionTip": "Filtrovat obsah podle dostupnosti v regionu", "components.Settings.SettingsMain.streamingRegion": "Streamovací region", "components.Settings.SettingsMain.streamingRegionTip": "Zobrazit streamovací služby podle dostupnosti v regionu", "components.Settings.SettingsMain.discoverRegion": "Region objevování", "components.Settings.apiKey": "API klíč", "components.Settings.invalidurlerror": "N<PERSON>ze se připojit k {mediaServerName} serveru.", "components.Settings.jellyfinForgotPasswordUrl": "URL pro zapomenuté heslo", "components.Settings.jellyfinSettings": "Nastavení {mediaServerName}", "components.Settings.jellyfinSettingsSuccess": "{mediaServerName} nastavení úspěšně uloženo!", "components.Settings.jellyfinSyncFailedGenericError": "Něco se pokazilo při synchronizaci knihoven", "components.Settings.jellyfinSyncFailedNoLibrariesFound": "Nebyly nalezeny žádné knihovny", "components.Settings.jellyfinlibraries": "{mediaServerName} knihovny", "components.Settings.jellyfinsettings": "Nastavení {mediaServerName}", "components.Settings.manualscanJellyfin": "Manuální skenování knihovny", "components.Settings.menuJellyfinSettings": "{mediaServerName}", "components.Settings.jellyfinlibrariesDescription": "<PERSON><PERSON><PERSON><PERSON>, ve k<PERSON>ch {mediaServerName} hledá tituly. Pokud nejsou v seznamu žádné knihovny, klikněte na tlačítko níže.", "components.Settings.jellyfinsettingsDescription": "Konfigurujte nastavení svého {mediaServerName} serveru. {mediaServerName} sken<PERSON><PERSON> {mediaServerName} knihovny pro zjištění dostupnosti obsahu.", "components.Settings.save": "Uložit změny", "components.Settings.saving": "Ukládání…", "components.Settings.syncJellyfin": "Synchronizovat knihovny", "components.Settings.syncing": "Synchronizace", "components.Settings.tip": "Tip", "components.Setup.configjellyfin": "<PERSON><PERSON><PERSON><PERSON>", "components.Setup.configplex": "Nastavit <PERSON>", "components.Setup.configuremediaserver": "Nastavit média server", "components.Setup.signin": "Přihlásit se", "components.Setup.signinWithEmby": "Zadejte údaje k Emby", "components.Setup.signinWithJellyfin": "Zadejte údaje k <PERSON>llyfin", "components.Setup.signinWithPlex": "Zadejte údaje k Plex", "components.Setup.subtitle": "Začněte výběrem média serveru", "components.StatusBadge.seasonnumber": "S{seasonNumber}", "components.TitleCard.watchlistError": "Něco se pokazilo. Zkuste to znovu.", "components.TvDetails.addtowatchlist": "Přidat na seznam sledování", "components.TitleCard.watchlistCancel": "se<PERSON><PERSON> sledování pro <strong>{title}</strong> <PERSON><PERSON><PERSON><PERSON>.", "components.TitleCard.watchlistSuccess": "<strong>{title}</strong> úspěšně přidáno na seznam sledování!", "components.TvDetails.watchlistDeleted": "<strong>{title}</strong> úspěšně odstraněno ze seznamu sledování!", "components.TvDetails.watchlistError": "Něco se pokazilo. Zkuste to znovu.", "components.UserList.importfromJellyfin": "Import uživatelů z {mediaServerName}", "components.UserList.importfromJellyfinerror": "Něco se pokazilo během importování uživate<PERSON>ů z {mediaServerName}.", "components.UserList.mediaServerUser": "{mediaServerName} uživatel", "components.UserList.noJellyfinuserstoimport": "{mediaServerName} nemá žádné uživatele k importování.", "components.UserList.username": "Uživatelské jméno", "components.UserList.validationUsername": "Musíte poskytnout uživatelské jméno", "components.UserProfile.UserSettings.UserGeneralSettings.discoverRegion": "Region pro objevování", "components.UserProfile.UserSettings.UserGeneralSettings.discoverRegionTip": "Filtrovat obsah podle dostupnosti v regionu", "components.UserProfile.UserSettings.UserGeneralSettings.email": "E-mail", "components.UserProfile.UserSettings.UserGeneralSettings.mediaServerUser": "{mediaServerName} uživatel", "components.UserProfile.UserSettings.UserGeneralSettings.save": "Uložit změny", "components.UserProfile.UserSettings.UserGeneralSettings.saving": "Ukládání…", "components.UserProfile.UserSettings.UserGeneralSettings.streamingRegion": "Streamovací region", "components.UserProfile.UserSettings.UserGeneralSettings.streamingRegionTip": "Zobrazit streamovací služby podle dostupnosti v regionu", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailureEmail": "Tento email už je použ<PERSON>!", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailureEmailEmpty": "Jiný uživatel už používá toto uživatelské jméno. Musíte si nastavit email", "components.UserProfile.UserSettings.UserGeneralSettings.validationemailformat": "<PERSON> po<PERSON><PERSON><PERSON> platný email", "components.UserProfile.UserSettings.UserGeneralSettings.validationemailrequired": "<PERSON><PERSON><PERSON> adresa je povinná", "components.UserProfile.localWatchlist": "Seznam sledování už<PERSON>le {username}", "i18n.addToBlacklist": "Přidat na černou listinu", "i18n.blacklistError": "Něco se pokazilo. Zkuste to znovu.", "i18n.blacklist": "Černá listina", "i18n.blacklistDuplicateError": "<strong>{title}</strong> už se nachází na černé listině.", "i18n.blacklistSuccess": "<strong>{title}</strong> by<PERSON> přidáno na černou listinu.", "i18n.blacklisted": "<PERSON> <PERSON><PERSON><PERSON> list<PERSON>", "i18n.removeFromBlacklistSuccess": "<strong>{title}</strong> by<PERSON> odstraněno z černé listiny.", "i18n.removefromBlacklist": "Odstranit z černé listiny", "components.Login.title": "Přidat email", "components.Login.adminerror": "Musíte se přihlásit administrátorským účtem.", "components.Login.description": "<PERSON>že je toto Vaše první přihlášení do {applicationName}, musíte přidat platnou emailovou adresu.", "components.Settings.jellyfinSettingsFailure": "Něco se pokazilo při ukládání nastavení {mediaServerName}.", "components.Settings.timeout": "Časový limit", "components.Selector.ended": "Ukončeno", "components.Selector.returningSeries": "Vracející se seriál", "components.Settings.SettingsJobsCache.usersavatars": "Uživatelské avatary", "i18n.specials": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}