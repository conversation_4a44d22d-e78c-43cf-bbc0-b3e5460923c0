{"components.RequestModal.requestseasons": "<PERSON><PERSON><PERSON><PERSON>i {seasonCount} {seasonCount, plural, one {Season} other {Seasons}}", "components.Layout.Sidebar.dashboard": "Pronađi novo", "pages.returnHome": "Povratak na glavnu stranicu", "pages.oops": "Ups", "i18n.unavailable": "<PERSON><PERSON>", "i18n.tvshows": "Serije", "i18n.processing": "Obradjuje se", "i18n.pending": "Na čekanju", "i18n.partiallyavailable": "Delimično <PERSON>", "i18n.movies": "Filmovi", "i18n.deleting": "Brisanje u toku…", "i18n.delete": "<PERSON><PERSON><PERSON><PERSON>", "i18n.declined": "Odbijeno", "i18n.decline": "<PERSON><PERSON><PERSON>j", "i18n.cancel": "<PERSON><PERSON><PERSON><PERSON>", "i18n.available": "Do<PERSON><PERSON><PERSON>", "i18n.approved": "Odobreno", "i18n.approve": "<PERSON><PERSON><PERSON>", "components.UserList.userlist": "Lista korisnika", "components.UserList.userdeleteerror": "Nešto nije u redu prilikom brisanje korisnika.", "components.UserList.userdeleted": "Korisnik izbrisan!", "components.UserList.user": "<PERSON><PERSON><PERSON>", "components.UserList.totalrequests": "<PERSON><PERSON><PERSON><PERSON>", "components.UserList.role": "<PERSON><PERSON><PERSON>", "components.UserList.plexuser": "Plex korisnik", "components.UserList.deleteuser": "Izbriši korisnika", "components.UserList.deleteconfirm": "Da li ste sigurni da želite da izbrišete ovog korisnika? Svi njegovi zahtevi će biti trajno izbrisani.", "components.UserList.created": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserList.admin": "Administrator", "components.TvDetails.similar": "Slične serije", "components.TvDetails.showtype": "<PERSON><PERSON> serije", "components.TvDetails.recommendations": "<PERSON><PERSON><PERSON>", "components.TvDetails.overviewunavailable": "<PERSON><PERSON> nije dos<PERSON>.", "components.TvDetails.overview": "Pregled", "components.TvDetails.originallanguage": "<PERSON>ni jezik", "components.TvDetails.network": "{networkCount, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}}", "components.TvDetails.cast": "<PERSON><PERSON><PERSON>", "components.TvDetails.anime": "Anime", "components.TvDetails.TvCast.fullseriescast": "Svi glumci serije", "components.Setup.welcome": "Dobrodošli u Jellyseerr", "components.Setup.signinMessage": "Započni tako što ćete se prijaviti sa svojim Plex nalogom", "components.Setup.finishing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>…", "components.Setup.finish": "Završite podešavanja", "components.Setup.continue": "<PERSON><PERSON><PERSON>", "components.Setup.configureservices": "<PERSON>n<PERSON><PERSON><PERSON><PERSON><PERSON> servise", "components.Settings.validationPortRequired": "Morate dodati važeći broj porta", "components.Settings.validationHostnameRequired": "Morate dodati važeći hostname ili IP adresu", "components.Settings.startscan": "Pokreni s<PERSON>", "components.Settings.ssl": "SSL", "components.Settings.sonarrsettings": "<PERSON><PERSON><PERSON>", "components.Settings.radarrsettings": "<PERSON><PERSON>", "components.Settings.port": "Port", "components.Settings.plexsettingsDescription": "Konfigurišite podešavanja za Vaš Plex server. <PERSON><PERSON><PERSON>rr skenira vaše Plex biblioteke da utvrdi šta je dostupno od sadržaja.", "components.Settings.plexsettings": "Plex podešavanja", "components.Settings.plexlibrariesDescription": "Je<PERSON><PERSON>rr skenira sadržaj za imena. Podesite informacije o Plex konekciji, a zatim kliknite dugme ispod ako nije navedena nijedna biblioteka.", "components.Settings.plexlibraries": "Plex biblioteke", "components.Settings.notificationsettings": "Podešavanje notifikacija", "components.Settings.menuServices": "Servisi", "components.Settings.menuPlexSettings": "Plex", "components.Settings.menuNotifications": "Notifikacije", "components.Settings.menuLogs": "Logovi", "components.Settings.menuJobs": "Poslovi & Keš", "components.Settings.menuGeneralSettings": "Opš<PERSON>", "components.Settings.menuAbout": "O nama", "components.Settings.manualscanDescription": "Normalno, ovo će biti pokrenutno na svakih 24 sata. <PERSON><PERSON><PERSON>rr će proveriti nedavno dodati Plex sadržaj češće. Ako je ovo prvi put da se Plex konfiguriše, jedan ručni scan sadržaja je preporučen!", "components.Settings.manualscan": "Ručno skeniranje sadržaja", "components.Settings.librariesRemaining": "<PERSON><PERSON><PERSON><PERSON><PERSON> koji se obradjuje: {count}", "components.Settings.hostname": "Hostname ili IP adresa", "components.Settings.deleteserverconfirm": "Da li ste sigurni da želite da izbrišete ovaj server?", "components.Settings.default4k": "Podrazumevano 4K", "components.Settings.default": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.currentlibrary": "Trenutna biblioteka: {name}", "components.Settings.copied": "Kopiran API ključ.", "components.Settings.cancelscan": "Otkaži skeniranje", "components.Settings.addsonarr": "<PERSON><PERSON><PERSON> server", "components.Settings.address": "<PERSON><PERSON><PERSON>", "components.Settings.addradarr": "Dodaj Radarr server", "components.Settings.activeProfile": "Aktivni profil", "components.Settings.SonarrModal.validationRootFolderRequired": "Morate odabrati root folder", "components.Settings.SonarrModal.validationProfileRequired": "Morate odabrati profil kvaliteta", "components.Settings.SonarrModal.validationPortRequired": "Morate popuniti važeći broj porta", "components.Settings.SonarrModal.validationNameRequired": "Morate popuniti ime servera", "components.Settings.SonarrModal.validationHostnameRequired": "Morate dodati važeći hostname ili IP adresu", "components.Settings.SonarrModal.validationApiKeyRequired": "Morate dodati API ključ", "components.Settings.SonarrModal.testFirstRootFolders": "Testirajte konekciju da učitate root foldere", "components.Settings.SonarrModal.testFirstQualityProfiles": "Testirajte konekciju da učitate profile kvaliteta", "components.Settings.SonarrModal.ssl": "Koristi SSL", "components.Settings.SonarrModal.servername": "<PERSON>v servera", "components.Settings.SonarrModal.server4k": "4K Server", "components.Settings.SonarrModal.selectRootFolder": "Odaberi root folder", "components.Settings.SonarrModal.selectQualityProfile": "Odaberi profil kvaliteta", "components.Settings.SonarrModal.seasonfolders": "<PERSON><PERSON><PERSON>i", "components.Settings.SonarrModal.rootfolder": "Root folder", "components.Settings.SonarrModal.qualityprofile": "<PERSON>il <PERSON>", "components.Settings.SonarrModal.port": "Port", "components.Settings.SonarrModal.loadingrootfolders": "Učitavanje root foldera…", "components.Settings.SonarrModal.loadingprofiles": "Učitavanje profila kvaliteta…", "components.Settings.SonarrModal.hostname": "Hostname ili IP adresa", "components.Settings.SonarrModal.editsonarr": "Izmeni Sonarr server", "components.Settings.SonarrModal.defaultserver": "Podrazumevani server", "components.Settings.SonarrModal.createsonarr": "Do<PERSON>j novi Sonarr server", "components.Settings.SonarrModal.baseUrl": "Osnovni URL", "components.Settings.SonarrModal.apiKey": "API Ključ", "components.Settings.SonarrModal.animerootfolder": "Root folder za anime", "components.Settings.SonarrModal.animequalityprofile": "Kvalitet profila za anime", "components.Settings.SonarrModal.add": "Dodaj server", "components.Settings.SettingsAbout.version": "Verzija", "components.Settings.SettingsAbout.totalrequests": "U<PERSON><PERSON><PERSON> zahteva", "components.Settings.SettingsAbout.totalmedia": "Ukupno medija", "components.Settings.SettingsAbout.overseerrinformation": "<PERSON> <PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.githubdiscussions": "GitHub rasprave", "components.Settings.SettingsAbout.gettingsupport": "<PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.validationRootFolderRequired": "Morate odabrati root folder", "components.Settings.RadarrModal.validationProfileRequired": "Morate odabrati profil kvaliteta", "components.Settings.RadarrModal.validationPortRequired": "Morate da navedete važeći broj porta", "components.Settings.RadarrModal.validationNameRequired": "Morate popuniti ime servera", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "Morate odabrati <PERSON> dostupnost", "components.Settings.RadarrModal.validationHostnameRequired": "Morate dodati hostname ili IP adresu", "components.Settings.RadarrModal.validationApiKeyRequired": "Morate dodati API ključ", "components.Settings.RadarrModal.toastRadarrTestSuccess": "Konekcija sa Radarr serverom uspešno uspostavljena!", "components.Settings.RadarrModal.toastRadarrTestFailure": "Neuspešna konekcija ka Radarr.", "components.Settings.RadarrModal.testFirstRootFolders": "Testirajte konekciju da učitate root foldere", "components.Settings.RadarrModal.testFirstQualityProfiles": "Testirajte konekciju da učitate profile kvaliteta", "components.Settings.notrunning": "Ne radi", "components.Settings.RadarrModal.ssl": "Koristi SSL", "components.Settings.RadarrModal.servername": "<PERSON>v servera", "components.Settings.RadarrModal.server4k": "4K Server", "components.Settings.RadarrModal.selectRootFolder": "Odaberi root folder", "components.Settings.RadarrModal.selectQualityProfile": "Odaberi profil kvaliteta", "components.Settings.RadarrModal.selectMinimumAvailability": "Odaberi minimalnu dostupnost", "components.Settings.RadarrModal.rootfolder": "Root folder", "components.Settings.RadarrModal.qualityprofile": "<PERSON>il <PERSON>", "components.Settings.RadarrModal.port": "Port", "components.Settings.RadarrModal.minimumAvailability": "Minimalna dostupnost", "components.Settings.RadarrModal.loadingrootfolders": "Učitavanje root foldera…", "components.Settings.RadarrModal.loadingprofiles": "Učitavanje nivoa kvaliteta…", "components.Settings.RadarrModal.hostname": "Hostname ili IP adresa", "components.Settings.RadarrModal.editradarr": "Izmeni Radarr server", "components.Settings.RadarrModal.defaultserver": "Podrazumevani server", "components.Settings.RadarrModal.createradarr": "Dodaj novi Radarr server", "components.Settings.RadarrModal.baseUrl": "Osnovni URL", "components.Settings.RadarrModal.apiKey": "API Ključ", "components.Settings.RadarrModal.add": "<PERSON><PERSON><PERSON>", "components.Settings.Notifications.webhookUrl": "Webhook adresa", "components.Settings.Notifications.validationSmtpPortRequired": "Morate da navedete važeći broj porta", "components.Settings.Notifications.validationSmtpHostRequired": "Morate da navedete važeće ime hosta ili IP adresu", "components.Settings.Notifications.smtpPort": "SMTP Port", "components.Settings.Notifications.smtpHost": "SMTP Host", "components.Settings.Notifications.emailsettingssaved": "Email notifikacija uspešno sačuvana!", "components.Settings.Notifications.emailsettingsfailed": "Email notifika<PERSON> nije us<PERSON>š<PERSON> sačuvana.", "components.Settings.Notifications.emailsender": "Adresa pošiljaoca", "components.Settings.Notifications.discordsettingssaved": "Discord notifikacija uspešno sačuvana!", "components.Settings.Notifications.discordsettingsfailed": "Discord notifikacija za podešavanje nije uspešno sačuvana.", "components.Settings.Notifications.authUser": "SMTP Korisničko ime", "components.Settings.Notifications.authPass": "SMTP lozinka", "components.Settings.Notifications.agentenabled": "Omogući agenta", "components.Search.searchresults": "<PERSON><PERSON><PERSON><PERSON> pretrage", "components.RequestModal.selectseason": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>(e)", "components.RequestModal.seasonnumber": "Sezona {number}", "components.RequestModal.season": "Sezona", "components.RequestModal.requestfrom": "Trenutno postoji zahtev na čekanju od {username}.", "components.RequestModal.requestadmin": "<PERSON><PERSON><PERSON> zahtev će odmah biti prihvaćen.", "components.RequestModal.requestSuccess": "<PERSON><PERSON><PERSON> zahtev za <strong>{title}</strong> !", "components.RequestModal.requestCancel": "<PERSON><PERSON><PERSON><PERSON> za <strong>{title}</strong> ot<PERSON><PERSON>.", "components.RequestModal.pendingrequest": "Zahtev na čekanju", "components.RequestModal.numberofepisodes": "# broj epizoda", "components.RequestModal.cancel": "Otkaži zahtev", "components.RequestList.requests": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {Sezona} other {Sezona}}", "components.RequestCard.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {Sezone}}", "components.RequestBlock.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {Sezone}}", "components.PersonDetails.ascharacter": "kao {character}", "components.PersonDetails.appearsin": "Pojavljivanja", "components.MovieDetails.studio": "{studioCount, plural, one {<PERSON><PERSON><PERSON><PERSON><PERSON> kuća} other {Izdava<PERSON><PERSON> kuće}}", "components.MovieDetails.similar": "Slični naslovi", "components.MovieDetails.runtime": "{minutes} minuta", "components.MovieDetails.revenue": "Prihod", "components.MovieDetails.releasedate": "{releaseCount, plural, one {<PERSON><PERSON>} other {<PERSON><PERSON><PERSON> i<PERSON>}}", "components.MovieDetails.recommendations": "<PERSON><PERSON><PERSON>", "components.MovieDetails.overviewunavailable": "<PERSON><PERSON> nije dos<PERSON>.", "components.MovieDetails.overview": "Pregled", "components.MovieDetails.originallanguage": "<PERSON>ni jezik", "components.MovieDetails.cast": "<PERSON><PERSON><PERSON>", "components.MovieDetails.budget": "Budžet", "components.MovieDetails.MovieCast.fullcast": "Kompletna glumačka postava", "components.Layout.UserDropdown.signout": "<PERSON><PERSON><PERSON><PERSON>", "components.Layout.Sidebar.users": "<PERSON><PERSON><PERSON><PERSON>", "components.Layout.Sidebar.settings": "Podešavanja", "components.Layout.Sidebar.requests": "<PERSON><PERSON><PERSON><PERSON>", "components.Layout.SearchInput.searchPlaceholder": "Pretraži filmove i serije", "components.Discover.upcomingmovies": "Predstojeći filmovi", "components.Discover.upcoming": "Predstojeći filmovi", "components.Discover.trending": "Popular<PERSON>", "components.Discover.recentrequests": "<PERSON><PERSON><PERSON>", "components.Discover.recentlyAdded": "<PERSON><PERSON><PERSON>", "components.Discover.populartv": "Popularne serije", "components.Discover.popularmovies": "Popularni filmovi", "pages.errormessagewithcode": "{statusCode} - {error}", "components.StatusBadge.status": "{status}", "components.UserProfile.movierequests": "Zahtev za film", "i18n.advanced": "Napredno", "i18n.back": "<PERSON><PERSON>", "components.Discover.discover": "Pronađi novo", "components.MovieDetails.showless": "Prika<PERSON><PERSON> manje", "components.Settings.SettingsLogs.logs": "Logovi", "components.MovieDetails.watchtrailer": "<PERSON><PERSON><PERSON> na<PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "Notifikacije", "components.UserList.create": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.process": "Proces", "components.Settings.menuUsers": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.plex": "Plex", "components.UserProfile.UserSettings.menuGeneralSettings": "Opšte", "i18n.requested": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Discover.NetworkSlider.networks": "Mreže", "components.Discover.StudioSlider.studios": "<PERSON><PERSON><PERSON>", "i18n.failed": "Neuspešno", "components.CollectionDetails.overview": "Pregled", "i18n.loading": "U<PERSON>ita<PERSON><PERSON>…", "components.UserList.creating": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "components.UserList.accounttype": "Tip", "i18n.tvshow": "Serije", "components.Layout.UserDropdown.myprofile": "Profil", "components.Layout.UserDropdown.settings": "Podešavanja", "components.Login.password": "Lozinka", "components.MovieDetails.showmore": "Prikaži više", "components.MovieDetails.viewfullcrew": "Prikažu celu postavu", "components.PersonDetails.crewmember": "<PERSON><PERSON><PERSON>", "components.PermissionEdit.request": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.tags": "Oznake", "components.Settings.RadarrModal.tags": "Oznake", "components.Settings.SettingsJobsCache.jobtype": "Tip", "components.Settings.SettingsLogs.pauseLogs": "<PERSON><PERSON>", "components.Settings.email": "Email", "components.Settings.serverLocal": "lokalni", "components.Settings.serverRemote": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.serverpreset": "Server", "components.Settings.services": "Servisi", "components.UserList.users": "<PERSON><PERSON><PERSON><PERSON>", "i18n.all": "Sve", "components.QuotaSelector.unlimited": "Neo<PERSON><PERSON><PERSON><PERSON>", "components.RequestCard.deleterequest": "<PERSON><PERSON><PERSON><PERSON> zahtev", "components.RequestList.RequestItem.modified": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.QuotaDisplay.movie": "film", "components.Settings.SettingsAbout.preferredmethod": "Poželjno", "components.Settings.SettingsJobsCache.cache": "<PERSON><PERSON>", "components.Settings.SettingsLogs.filterInfo": "Info", "components.Settings.SonarrModal.tags": "Oznake", "components.Settings.mediaTypeMovie": "film", "components.UserList.owner": "Vlasnik", "components.UserList.password": "Lozinka", "i18n.request": "<PERSON><PERSON><PERSON><PERSON>", "i18n.requesting": "<PERSON><PERSON><PERSON><PERSON> se…", "i18n.saving": "<PERSON><PERSON><PERSON><PERSON>…", "i18n.testing": "Testiran<PERSON>…", "components.ManageSlideOver.movie": "film", "components.Settings.SettingsAbout.Releases.currentversion": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.Releases.latestversion": "Najnovije", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "Administrator", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "Vlasnik", "components.UserProfile.UserSettings.UserGeneralSettings.general": "Opšte", "components.UserProfile.UserSettings.UserPermissions.permissions": "Dozvole", "components.PermissionEdit.admin": "Administrator", "components.UserProfile.UserSettings.UserPasswordChange.password": "Lozinka", "components.ResetPassword.password": "Lozinka", "components.Search.search": "Pretraga", "components.MovieDetails.originaltitle": "<PERSON>ni naslov", "components.RequestList.RequestItem.requested": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.advancedoptions": "Napredno", "components.RequestModal.QuotaDisplay.season": "sezona", "components.Settings.scanning": "Sinhronizacija u toku…", "components.UserProfile.UserSettings.UserGeneralSettings.user": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.Releases.releases": "Izdanja", "components.Settings.SettingsJobsCache.jobs": "<PERSON><PERSON><PERSON>i", "components.Settings.SettingsLogs.filterDebug": "Otklanjanje grešaka", "components.Settings.SettingsLogs.filterError": "Greška", "components.Settings.SettingsLogs.filterWarn": "Upozorenje", "components.Settings.SettingsLogs.message": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.time": "Vremenska oznaka", "components.Settings.SettingsAbout.about": "O nama", "components.Settings.SettingsUsers.users": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.notifications": "Notifikacije", "components.Setup.setup": "Podesiti", "components.UserProfile.UserSettings.menuChangePass": "Lozinka", "components.UserProfile.UserSettings.menuNotifications": "Notifikacije", "components.UserProfile.UserSettings.menuPermissions": "Dozvole", "i18n.close": "Zatvori", "i18n.canceling": "Otkazivanje…", "i18n.movie": "Film", "i18n.retry": "Pokušaj ponovo", "i18n.edit": "Izmena", "i18n.settings": "Podešavanja", "i18n.next": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.view": "Pogled", "components.RequestList.RequestItem.deleterequest": "<PERSON><PERSON><PERSON><PERSON> zahtev", "components.RequestModal.selectmovies": "Selekt<PERSON>j film(ove)", "components.Settings.SettingsAbout.documentation": "Dokumentacija", "i18n.experimental": "Eksperimentalno", "components.PermissionEdit.autoapprove": "Automatsko odobrenje", "components.UserProfile.UserSettings.UserGeneralSettings.role": "<PERSON><PERSON><PERSON>", "components.UserProfile.unlimited": "Neo<PERSON><PERSON><PERSON><PERSON>", "i18n.previous": "<PERSON><PERSON><PERSON><PERSON>", "components.UserList.sortCreated": "Datum p<PERSON>", "components.Discover.upcomingtv": "Predstojeće serije", "components.IssueDetails.IssueComment.areyousuredelete": "Da li ste sigurni da želite da obrišete ovaj komentar?", "components.IssueDetails.IssueComment.delete": "Obriši komentar", "components.IssueDetails.IssueComment.edit": "Izmeni komentar", "components.IssueDetails.IssueComment.validationComment": "<PERSON>rate uneti poruku", "components.Layout.Sidebar.issues": "<PERSON>i", "components.PermissionEdit.request4k": "Zahtevajte 4K", "components.Settings.SettingsJobsCache.nextexecution": "Sledeće izvršenje", "components.Settings.Notifications.senderName": "<PERSON><PERSON>", "components.DownloadBlock.estimatedtime": "<PERSON><PERSON><PERSON><PERSON><PERSON> {time}", "components.RequestModal.AdvancedRequester.requestas": "<PERSON><PERSON><PERSON><PERSON><PERSON> kao", "i18n.retrying": "Ponovni poku<PERSON>…", "components.Discover.MovieGenreSlider.moviegenres": "Žanrovi filmova", "components.Discover.TvGenreSlider.tvgenres": "Žanrovi serija", "components.Discover.MovieGenreList.moviegenres": "Žanrovi filmova", "components.Discover.TvGenreList.seriesgenres": "Žanrovi serija", "components.RegionSelector.regionDefault": "Svi regioni", "components.Settings.SonarrModal.externalUrl": "Eksterni URL", "components.Discover.DiscoverMovieLanguage.languageMovies": "{language} filmova", "components.CollectionDetails.numberofmovies": "{count} filmova", "components.RequestList.sortAdded": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.DiscoverMovieGenre.genreMovies": "{genre} filmovi", "components.IssueDetails.comments": "Komentari", "components.IssueDetails.commentplaceholder": "<PERSON><PERSON><PERSON><PERSON> komentar…", "components.IssueDetails.issuetype": "Tip", "components.IssueDetails.deleteissue": "<PERSON><PERSON><PERSON><PERSON> problem", "components.IssueDetails.episode": "Epizoda {episodeNumber}", "components.IssueDetails.lastupdated": "Poslednja izmena", "components.IssueDetails.nocomments": "<PERSON><PERSON> k<PERSON>.", "components.IssueDetails.openin4karr": "Otvori u 4K {arr}", "components.IssueDetails.openinarr": "O<PERSON><PERSON>i u {arr}", "components.IssueDetails.playonplex": "Pusti na {mediaServerName}u", "components.IssueList.IssueItem.issuetype": "Tip", "components.IssueDetails.season": "Sezona {seasonNumber}", "components.IssueList.IssueItem.opened": "Otvoren", "components.IssueList.IssueItem.unknownissuetype": "Nepoznat", "components.IssueModal.issueOther": "Ostalo", "components.IssueModal.issueVideo": "Video", "components.Login.signin": "Prijavite se", "components.Login.signingin": "Prijavljivanje…", "components.Login.forgotpassword": "<PERSON><PERSON><PERSON><PERSON> ste lozinku?", "components.ManageSlideOver.manageModalRequests": "<PERSON><PERSON><PERSON><PERSON>", "components.ManageSlideOver.tvshow": "serije", "components.MediaSlider.ShowMoreCard.seemore": "<PERSON><PERSON><PERSON> vi<PERSON>e", "components.RequestBlock.profilechanged": "<PERSON>il <PERSON>", "components.PermissionEdit.autoapproveMovies": "Automatsko odobrenje filmova", "components.PermissionEdit.autoapproveSeries": "Automatsko odobrenje serija", "components.RequestButton.requestmore": "Zatraži još", "components.RequestButton.viewrequest": "<PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.rootfolder": "Root folder", "components.Settings.RadarrModal.externalUrl": "Eksterni URL", "components.Settings.SettingsJobsCache.cacheksize": "Veličina ključa", "components.Settings.serverpresetManualMessage": "Ručna konfiguracija", "components.UserList.sortRequests": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "Lokalni korisnik", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "Standardna podešavanja", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "Plex korisnik", "components.PermissionEdit.autoapprove4k": "Automatsko odobrenje 4K", "components.IssueModal.issueSubtitles": "Titl", "components.PermissionEdit.managerequests": "Upravljajte zahtevima", "components.RequestBlock.rootfolder": "Root folder", "components.RequestButton.declinerequest": "Odbijte zahtev", "components.RequestModal.AdvancedRequester.destinationserver": "Odredišni server", "components.RequestList.RequestItem.requesteddate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "<PERSON><PERSON><PERSON><PERSON> token", "components.Settings.SettingsJobsCache.jobname": "<PERSON><PERSON> posla", "components.Settings.SonarrModal.languageprofile": "<PERSON><PERSON><PERSON> profila", "components.Settings.is4k": "4K", "components.Settings.mediaTypeSeries": "serije", "components.Settings.serverSecure": "bezbedno", "components.UserList.bulkedit": "Grupno uređivanje", "components.UserList.localuser": "Lokalni korisnik", "components.UserProfile.ProfileHeader.settings": "Izmena podešavanja", "i18n.status": "Status", "components.IssueDetails.IssueDescription.edit": "Izmeni opis", "components.IssueList.IssueItem.issuestatus": "Status", "components.Settings.SettingsJobsCache.jobstarted": "{jobname} je <PERSON><PERSON><PERSON><PERSON><PERSON>.", "components.UserProfile.ProfileHeader.profile": "<PERSON><PERSON><PERSON> profil", "components.IssueDetails.allepisodes": "Sve epizode", "components.IssueDetails.IssueDescription.deleteissue": "Izbriši problem", "components.IssueDetails.IssueDescription.description": "Opis", "components.IssueDetails.allseasons": "<PERSON><PERSON> sezone", "components.IssueDetails.unknownissuetype": "Nepoznat", "components.Settings.SonarrModal.syncEnabled": "Omogući skeniranje", "components.ResetPassword.confirmpassword": "Potvrdi lozinku", "components.Settings.SettingsJobsCache.cachename": "<PERSON><PERSON>", "components.IssueModal.CreateIssueModal.extras": "<PERSON><PERSON><PERSON>", "components.RequestModal.autoapproval": "Automatsko odobrenje", "components.Settings.Notifications.encryptionNone": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.runnow": "<PERSON><PERSON><PERSON><PERSON> sada", "components.IssueDetails.play4konplex": "Pusti u 4K na {mediaServerName}u", "components.IssueList.issues": "<PERSON>i", "components.IssueModal.issueAudio": "Audio", "components.PermissionEdit.viewrequests": "<PERSON><PERSON><PERSON>", "components.RequestButton.approverequest": "<PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.languageprofile": "<PERSON><PERSON><PERSON> profila", "components.Settings.SettingsAbout.timezone": "Vremenska zona", "components.Settings.serverpresetRefreshing": "<PERSON>uz<PERSON><PERSON> servera…", "components.Settings.RadarrModal.announced": "Najavljeno", "components.Settings.RadarrModal.syncEnabled": "Omogući skeniranje", "components.Settings.SettingsJobsCache.cachehits": "Pogodaka", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "Frekvencija", "components.Settings.SettingsAbout.Releases.viewchangelog": "Prikaži dnevnik promena", "components.Settings.SettingsJobsCache.cachekeys": "Ukup<PERSON> ključeva", "components.Settings.SettingsJobsCache.canceljob": "Otkaži posao", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname} je ot<PERSON><PERSON>.", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "ID korisnika", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "Podešavanje notifikacija", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "Potvrdi lozinku", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "Nova lozinka", "components.UserProfile.recentrequests": "<PERSON><PERSON><PERSON>", "i18n.open": "Otvori", "i18n.resolved": "<PERSON><PERSON><PERSON>", "components.RequestBlock.server": "Odredišni server", "components.PermissionEdit.advancedrequest": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.leavecomment": "Komentar", "components.PermissionEdit.users": "Upravljajte korisnica", "components.Settings.SettingsAbout.supportoverseerr": "<PERSON><PERSON><PERSON>", "components.TvDetails.watchtrailer": "<PERSON><PERSON><PERSON> na<PERSON>", "components.CollectionDetails.requestcollection": "Zahtevaj kolekciju", "components.StatusBadge.status4k": "4K {status}", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "Omogući agenta", "components.IssueDetails.issuepagetitle": "Problem", "components.Login.email": "<PERSON><PERSON> ad<PERSON>", "components.Settings.SettingsLogs.resumeLogs": "<PERSON><PERSON><PERSON>", "components.NotificationTypeSelector.mediaapproved": "Zahtev je odobren", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "Webhook adresa", "components.ManageSlideOver.manageModalAdvanced": "Napredno", "components.ManageSlideOver.manageModalMedia": "<PERSON><PERSON><PERSON>", "components.Settings.Notifications.NotificationsWebhook.customJson": "JSON zadržaj", "components.Settings.SettingsLogs.label": "Labela", "components.Settings.SettingsLogs.level": "Ozbiljnost", "components.Settings.SettingsAbout.Releases.versionChangelog": "{version} Dnevnik promena", "i18n.import": "Uvoz", "i18n.importing": "Uvoz…", "components.Settings.SettingsJobsCache.command": "<PERSON><PERSON><PERSON>", "components.NotificationTypeSelector.mediaavailable": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.webhook": "Webhook", "components.Settings.Notifications.NotificationsSlack.agentenabled": "Omogući agenta", "components.Settings.Notifications.chatId": "Chat-ID", "components.ManageSlideOver.downloadstatus": "Preuziman<PERSON>", "components.Settings.Notifications.NotificationsWebhook.authheader": "Zaglavlje autorizacije", "i18n.test": "Test", "components.RequestList.sortModified": "Poslednja izmena", "components.Settings.RadarrModal.released": "U bioskopima od", "components.UserProfile.UserSettings.UserNotificationSettings.email": "Email", "components.Settings.SettingsJobsCache.cachemisses": "Gosp<PERSON>đ<PERSON>", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "Webhook adresa", "components.Settings.Notifications.NotificationsPushover.agentenabled": "Omogući agenta", "components.RegionSelector.regionServerDefault": "Podrazumevano ({region})", "components.UserList.email": "<PERSON><PERSON> ad<PERSON>", "components.Discover.DiscoverStudio.studioMovies": "{network} film", "components.Discover.DiscoverNetwork.networkSeries": "{network} serija", "components.Settings.SettingsJobsCache.flushcache": "Obriši keš", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "Otkrijte jezik", "components.UserProfile.UserSettings.UserGeneralSettings.region": "Otkrijte region", "components.NotificationTypeSelector.mediadeclined": "Zahtev odbijen", "components.RequestModal.AdvancedRequester.default": "{name} (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "components.RequestModal.AdvancedRequester.qualityprofile": "<PERSON>il <PERSON>", "components.ResetPassword.email": "<PERSON><PERSON> ad<PERSON>", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "Omogući agenta", "components.Settings.SettingsJobsCache.download-sync": "Download sinhronizacija", "components.Settings.Notifications.sendSilently": "Pošalji tiho", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "<PERSON>ip naloga", "components.UserProfile.requestsperdays": "{limit} preostalo", "components.PersonDetails.birthdate": "<PERSON><PERSON><PERSON><PERSON> {birthdate}", "components.Settings.SettingsUsers.defaultPermissions": "Podrazumevane do<PERSON>", "components.Discover.DiscoverTvLanguage.languageSeries": "Serije na jeziku {language}", "components.Discover.DiscoverTvGenre.genreSeries": "{genre} <PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.RequestModal.alreadyrequested": "Već je traženo", "components.ResetPassword.passwordreset": "Reset<PERSON><PERSON><PERSON>", "components.Settings.SettingsUsers.userSettings": "Korisnička podešavanja", "pages.serviceunavailable": "<PERSON><PERSON> nije <PERSON>", "components.Settings.SettingsJobsCache.unknownJob": "Nepoznat posao", "components.Settings.SettingsJobsCache.radarr-scan": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.sonarr-scan": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.extraData": "Dodatni podaci", "components.Settings.enablessl": "Koristi SSL", "components.TvDetails.episodeRuntimeMinutes": "{runtime} minuta", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "Chat-ID", "i18n.delimitedlist": "{a}, {b}", "i18n.usersettings": "Korisnička podešavanja", "components.UserProfile.ProfileHeader.joindate": "<PERSON><PERSON><PERSON><PERSON><PERSON> se {joindate}", "components.Settings.Notifications.botUsername": "Bot korisničko ime", "components.Settings.Notifications.pgpPassword": "PGP lozinka", "components.Settings.SettingsLogs.logDetails": "Detalji dnevnika", "components.Settings.scan": "Sinhronizacija biblioteka", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "Pošalji tiho", "i18n.notrequested": "<PERSON><PERSON>", "components.Settings.Notifications.validationEmail": "Morate uneti validnu email adresu", "components.UserList.usercreatedfailedexisting": "Email adresa je već iskorišćena od strane drugog korisnika.", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "Šaljem LunaSea probno obaveštenje…", "components.CollectionDetails.requestcollection4k": "Zahtevaj kolekciju u 4K", "components.Login.signinheader": "Uloguj se za nastavak", "components.Login.signinwithoverseerr": "Iskoristi nalog od {applicationTitle}", "components.Settings.SettingsAbout.helppaycoffee": "Pomozi i plati kafu", "components.Settings.SettingsJobsCache.editJobSchedule": "Modifikuj posao", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "Posao us<PERSON>šno izmenjen!", "i18n.save": "Sačuvaj izmene", "components.PermissionEdit.autoapprove4kMovies": "Automatski odobri 4K filmove", "components.PermissionEdit.autoapprove4kMoviesDescription": "Dozvoli automatsko odobravanje zahteva za 4K filmove.", "components.PermissionEdit.autoapprove4kSeries": "Automatski odobri 4K serije", "components.ResetPassword.resetpassword": "Resetuj svoju lozinku", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Šaljem Gotify probno obaveštenje…", "components.Settings.RadarrModal.create4kradarr": "Dodaj novi 4K Radarr server", "components.ManageSlideOver.alltime": "Sve vreme", "components.Settings.validationUrl": "Morate uneti validnu URL adresu", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "P<PERSON><PERSON><PERSON> jezik", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "Prikaži ime", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "Morate potvrditi novu lozinku", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "Do<PERSON><PERSON> je do greške prilikom čuvanja posla.", "components.Login.signinwithplex": "Koristi tvoj Plex nalog", "components.Login.validationemailrequired": "<PERSON><PERSON><PERSON> uneti validnu email adresu", "components.Login.validationpasswordrequired": "<PERSON><PERSON><PERSON> lo<PERSON>", "components.Settings.toastPlexConnectingFailure": "Neuspelo povezivanje na Plex.", "components.RequestBlock.languageprofile": "Jezički profil", "components.ResetPassword.resetpasswordsuccessmessage": "Lozinka uspešno resetovana!", "components.ResetPassword.validationemailrequired": "<PERSON><PERSON><PERSON> uneti validnu email adresu", "components.ResetPassword.validationpasswordminchars": "Lozinka je previše kratka; mora imati najmanje 8 znakova", "components.ResetPassword.validationpasswordrequired": "<PERSON><PERSON><PERSON> lo<PERSON>", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "Šaljem Pushbullet probno obaveštenje…", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Pushbullet probno obaveštenje poslato!", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "Šaljem Pushover probno obaveštenje…", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "Šaljem Slack probno obaveštenje…", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "Omogući agenta", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "Morate uneti validnu URL adresu", "components.Settings.Notifications.encryptionOpportunisticTls": "Uvek koristi STARTTLS", "components.Settings.RadarrModal.edit4kradarr": "Izmeni 4K Radarr server", "components.Settings.SettingsAbout.outofdate": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobsandcache": "Poslovi i keš", "components.Settings.SettingsUsers.toastSettingsSuccess": "Podešavanja korisnika uspešno sačuvana!", "components.Settings.SonarrModal.animelanguageprofile": "Jezički profil za anime", "components.Settings.SonarrModal.animeTags": "<PERSON>ime oz<PERSON>ke", "components.Settings.SonarrModal.enableSearch": "Omogući automatsku pretragu", "components.Settings.SonarrModal.loadingTags": "Oznake se učitavaju…", "components.Settings.SonarrModal.loadinglanguageprofiles": "Učitavanje jezičkih profila…", "components.Settings.SonarrModal.selecttags": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.selectLanguageProfile": "Izaberi jezički profil", "components.Settings.SonarrModal.toastSonarrTestFailure": "Neuspešno povezivanje na Sonarr.", "components.Settings.SonarrModal.toastSonarrTestSuccess": "Povezivanje na Sonarr uspešno!", "components.Settings.SonarrModal.validationApplicationUrl": "Mo<PERSON>š uneti validnu URL adresu", "components.Settings.SonarrModal.validationLanguageProfileRequired": "<PERSON><PERSON><PERSON> izabrati jezički profil", "components.Settings.externalUrl": "Spoljni URL", "components.Settings.tautulliSettings": "<PERSON><PERSON>lli podešavanja", "components.Settings.tautulliApiKey": "API ključ", "components.Settings.toastTautulliSettingsFailure": "Nešto nije u redu prilikom čuvanja Tautulli podešavanja.", "components.Settings.tautulliSettingsDescription": "Opciono konfiguriši podešavanja za tvoj Tautulli server. Jellyseerr će učitavati istoriju gledanja Plex sadržaja iz Tautulli-ja.", "components.Settings.toastPlexConnectingSuccess": "Povezivanje sa Plex-om us<PERSON>šno!", "components.Settings.toastPlexRefresh": "Učitavam listu servera sa Plex-a…", "components.Settings.toastPlexRefreshFailure": "Neuspešno učitavanje liste servara sa Plexa.", "components.Settings.toastPlexRefreshSuccess": "Lista Plex servera uspešno preuzeta!", "components.TvDetails.nextAirDate": "Sledeći datum prikazivanja", "components.UserList.nouserstoimport": "Nema Plex korisnika za unos.", "components.UserList.createlocaluser": "Kreiraj lokalnog korisnika", "components.UserList.autogeneratepassword": "Automatski generiši šifru", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "Podašavanja us<PERSON>š<PERSON> sačuvana!", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "Enkriptuj email por<PERSON> k<PERSON> <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "Pošalji notifikacije bez zvuka", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "Morate uneti trenutnu lozinku", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "Morate uneti novu lozinku", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "Lozinka je previše kratka; mora imati najmanje 8 znakova", "components.UserProfile.totalrequests": "<PERSON><PERSON><PERSON> bro<PERSON>", "i18n.noresults": "<PERSON><PERSON> rezultata.", "i18n.request4k": "Zahtevaj u 4K", "i18n.resultsperpage": "Prikaži {pageSize} rezultata po strani", "i18n.showingresults": "P<PERSON><PERSON><PERSON>m <strong>{from}</strong> do <strong>{to}</strong> od <strong>{total}</strong> rezultata", "components.Settings.RadarrModal.validationApplicationUrl": "Morate uneti validnu URL adresu", "components.Settings.SettingsAbout.Releases.viewongithub": "Pogledaj na GitHub", "components.Settings.SonarrModal.edit4ksonarr": "Izmeni 4K Sonarr server", "components.UserList.validationEmail": "Morate uneti validnu email adresu", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "Lozinka us<PERSON>š<PERSON> sačuvana!", "components.Settings.toastPlexConnecting": "Pokušavam povezivanje na Plex…", "pages.somethingwentwrong": "Nešto je pogre<PERSON>", "pages.internalservererror": "Interna greška servera", "pages.pagenotfound": "Stranica nije pronađena", "components.ManageSlideOver.mark4kavailable": "Obeleži kao dostupno u 4K", "components.Login.loginerror": "Dogodila se greška prilikom prijave.", "components.Settings.SonarrModal.create4ksonarr": "Dodaj novi 4K Sonarr server", "components.PermissionEdit.autoapprove4kSeriesDescription": "Dozvoli automatsko odobravanje zahteva za 4K serije.", "components.ResetPassword.gobacklogin": "Vrati se na stranu za prijavu", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "Svaki {jobScheduleHours, plural, one {hour} other {{jobScheduleHours} hours}}", "components.Settings.SettingsAbout.uptodate": "Najsvežiji", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "Morate da navedete važeći JSON korisni teret", "components.Settings.Notifications.validationChatIdRequired": "Morate da navedete važeći ID za ćaskanje", "components.StatusBadge.playonplex": "Igrajte na {mediaServerName}-u", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "Morate da obezbedite pristupni token", "components.UserList.userssaved": "Korisničke dozvole su uspešno sačuvane!", "components.Discover.DiscoverMovieKeyword.keywordMovies": "{keywordTitle} Филмови", "components.Discover.CreateSlider.searchGenres": "Претражи жанрове…", "components.Discover.CreateSlider.addsuccess": "Креирај нови клизач и сачувана подешавања прилагођавања открића.", "components.Discover.CreateSlider.editfail": "Неуспешно измењивање клизача.", "components.Discover.CreateSlider.editSlider": "Измени клизач", "components.Discover.CreateSlider.providetmdbnetwork": "Наведите TMDB ID мреже", "components.Discover.CreateSlider.editsuccess": "Измењен клизач и сачувана подешавања прилагођавања открића.", "components.Discover.CreateSlider.providetmdbsearch": "Наведите упит за претрагу", "components.Discover.CreateSlider.providetmdbkeywordid": "Наведите ID кључне речи TMDB", "components.Discover.CreateSlider.providetmdbstudio": "Наведите TMDB Студио ID", "components.Discover.CreateSlider.validationDatarequired": "Морате да наведете вредност података.", "components.Discover.CreateSlider.searchStudios": "Претражи студије…", "components.Discover.DiscoverMovies.discovermovies": "Филмови", "components.Discover.CreateSlider.searchKeywords": "Претражи кључне речи…", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "Датум изласка опадајући", "components.Discover.CreateSlider.providetmdbgenreid": "Наведите ID TMDB жанра", "components.Discover.CreateSlider.addSlider": "Дод<PERSON><PERSON> клизач", "components.AirDateBadge.airedrelative": "Емитовано {relativeTime}", "components.Discover.CreateSlider.addcustomslider": "Направи прилагођени клизач", "components.Discover.CreateSlider.addfail": "Неуспешно креирање новог клизача.", "components.AirDateBadge.airsrelative": "Емитује се {relativeTime}", "components.Discover.CreateSlider.needresults": "Морате имати најмање 1 резултат.", "components.AppDataWarning.dockerVolumeMissingDescription": "<code>{appDataPath}</code> монтирање фолдера није правилно конфигурисано. Сви подаци ће бити избрисани када се контејнер заустави или рестартује.", "components.Discover.CreateSlider.nooptions": "Нема резултата.", "components.Discover.CreateSlider.slidernameplaceholder": "Име клизача", "components.Discover.CreateSlider.starttyping": "Почните да куцате за претрагу.", "components.Discover.DiscoverMovies.sortTitleDesc": "На<PERSON><PERSON><PERSON> (Z-A) опадајуће", "components.Discover.CreateSlider.validationTitlerequired": "Морати дати наслов.", "components.Discover.DiscoverMovies.sortTitleAsc": "<PERSON><PERSON><PERSON><PERSON><PERSON> (А-З) растуће", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {# Active Filter} остали {# Active Filters}}", "components.Discover.DiscoverMovies.sortPopularityAsc": "Популарност растућа", "components.Discover.DiscoverMovies.sortPopularityDesc": "Популарност опадајућа", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "Датум изласка растући"}