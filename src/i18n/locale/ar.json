{"components.AppDataWarning.dockerVolumeMissingDescription": "<code>{appDataPath}</code>مجلد التثبيت لم يتم إعداده بشكل صحيح سيتم تنظيف جميع البيانات عند إيقاف او إعادة تشغيل حاوي التطبيق.", "components.NotificationTypeSelector.mediarequestedDescription": "إرسال تنبيهات في حالة وصول طلبات جديدة تتطلب الموافقة.", "components.NotificationTypeSelector.notificationTypes": "أنواع التنبيهات", "components.NotificationTypeSelector.usermediaapprovedDescription": "تلقي تنبيهات عندما يتم الموافقة على طلبات المحتوى.", "components.NotificationTypeSelector.usermediadeclinedDescription": "تلقي تنبيهات عندما يتم رفض الطلبات.", "components.PermissionEdit.createissuesDescription": "إعطاء صلاحية للإبلاغ عن مشاكل.", "components.PermissionEdit.managerequestsDescription": "إعطاء صلاحية بإدارة الطلبات. كل الطلبات المقدمة من قبل مستخدم يحمل هذا الإذن سيتم الموافقة عليها بشكل تلقائي.", "components.PermissionEdit.autoapproveDescription": "موافقة تلقائية لكل طلبات المحتوى باستثناء جودة الفور كي.", "components.PermissionEdit.request4kMoviesDescription": "إعطاء صلاحيّة بطلب أفلام بجودة فور كي.", "components.PermissionEdit.requestDescription": "إعطاء اصلاحية بطلب محتوى ليس بجودة فور كي.", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "تم إستهلاك الحد الأقصى المسموح لك بطلب المواسم", "components.RequestModal.AdvancedRequester.selecttags": "إختار علامات التعريف", "components.RequestModal.AdvancedRequester.tags": "علامات التعريف", "components.RequestModal.QuotaDisplay.quotaLink": "يمكنك الإطلاع على ملخص الحد الأقصى لطلباتك في <ProfileLink>صفحة ملف التعريف</ProfileLink>.", "components.RequestModal.QuotaDisplay.requiredquota": "تحتاج على الأقل ان تملك <strong>{seasons}</strong> {seasons, plural, one {طلب موسم} other {طلبات مواسم}} متبقية/متبقّي من حدك الأقصى لتتمكن من طلب هذا المسلسل.", "components.Discover.NetworkSlider.networks": "شبكات البث", "components.Discover.upcomingmovies": "<PERSON><PERSON><PERSON><PERSON><PERSON> قادمة", "components.Discover.upcomingtv": "مسلسلات قادمة", "components.IssueDetails.season": "موسم {seasonNumber}", "components.IssueModal.issueVideo": "الفديو", "components.ManageSlideOver.manageModalMedia": "المحتوى", "components.Login.loginerror": "حد<PERSON> خطأ ما أثناء محاولة تسجيل الدخول.", "components.ManageSlideOver.manageModalNoRequests": "لا يوجد طلبات.", "components.Layout.VersionStatus.streamstable": "نسخة مستقرة من \"أوفرسيرر\"", "components.Login.forgotpassword": "نسيت كلمة السر؟", "components.Login.signingin": "جاري تسجيل الدخول…", "components.Login.signinheader": "سجل دخول للمتابعة", "components.Layout.VersionStatus.streamdevelop": "نسخة التطوير من \"أوفرسيرر\"", "components.Login.password": "كلمة السر", "components.Login.signin": "تسجيل دخول", "components.Login.signinwithplex": "إستخدم حساب بليكس", "components.Login.signinwithoverseerr": "إستخدم حسابك في {applicationTitle}", "components.ManageSlideOver.manageModalClearMedia": "محو البيانات", "components.Login.validationemailrequired": "يجب عليك تزويد بريد إلكتروني صحيح", "components.Login.validationpasswordrequired": "يجب عليك إدخال كلمة سر", "components.ManageSlideOver.alltime": "جميع الأوقات", "components.ManageSlideOver.downloadstatus": "التنزيلات", "components.ManageSlideOver.manageModalAdvanced": "متقدم", "components.ManageSlideOver.manageModalClearMediaWarning": "* سيتم حذف جميع البيانات بشكل نهائي لـ {mediaType},متضمنا جميع الطلبات.إذا كان هذا المحتوى متوفر في مكتبة {mediaServerName}، سيتم إعادة تفاصيل المحتوى في عملية الفحص القادمة.", "components.ManageSlideOver.manageModalRequests": "الطلبات", "components.ManageSlideOver.manageModalTitle": "إدارة {mediaType}", "components.ManageSlideOver.manageModalIssues": "المشاكل المفتوحة", "components.MovieDetails.budget": "الميزانية", "components.ManageSlideOver.manageModalMedia4k": "محتوى جودة الفور كي", "components.ManageSlideOver.playedby": "تم تشغيله بواسطة", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> {playCount, plural, one {تشغيل} other {تشغيل}}", "components.ManageSlideOver.tvshow": "مسلسل", "components.MediaSlider.ShowMoreCard.seemore": "رؤية المزيد", "components.MovieDetails.MovieCast.fullcast": "طاقم التمثيل", "components.MovieDetails.MovieCrew.fullcrew": "طاقم العمل", "components.MovieDetails.cast": "الطاقم", "components.NotificationTypeSelector.mediarequested": "الطلب معلق للموافقة", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "إرسال تنبيهات عند طلب المستخدمين لمحتوى تمت الموافقة عليه بشكل تلقائي.", "components.NotificationTypeSelector.mediadeclinedDescription": "إرسال تنبيهات في حالة رفض الطلبات.", "components.CollectionDetails.numberofmovies": "{count} <PERSON><PERSON><PERSON>ام", "components.CollectionDetails.overview": "نظرة عامة", "components.CollectionDetails.requestcollection": "طلب تجميعة", "components.CollectionDetails.requestcollection4k": "طلب تجميعة بجودة فور كي", "components.Discover.DiscoverMovieGenre.genreMovies": "{genre} أفلام", "components.Discover.DiscoverMovieLanguage.languageMovies": "{language} أفلام", "components.Discover.DiscoverNetwork.networkSeries": "{network} مسلسلات", "components.Discover.DiscoverStudio.studioMovies": "{studio} أفلام", "components.Discover.DiscoverTvGenre.genreSeries": "{genre} مسلسلات", "components.Discover.DiscoverTvLanguage.languageSeries": "{language} مسلسلات", "components.Discover.MovieGenreList.moviegenres": "أنواع الأفلام", "components.Discover.MovieGenreSlider.moviegenres": "أنواع الأفلام", "components.Discover.StudioSlider.studios": "إستديوهات الإنتاج", "components.Discover.TvGenreList.seriesgenres": "أنواع المسلسلات", "components.Discover.TvGenreSlider.tvgenres": "أنواع المسلسلات", "components.Discover.discover": "إكتشف", "components.Discover.popularmovies": "أفلام ذات شعبية", "components.Discover.populartv": "مسلسلات ذات شعبية", "components.Discover.recentlyAdded": "أضيف مؤخرا", "components.Discover.recentrequests": "الطلبات الحديثة", "components.Discover.trending": "الأكثر شيوعاً", "components.Discover.upcoming": "<PERSON><PERSON><PERSON><PERSON><PERSON> قادمة", "components.DownloadBlock.estimatedtime": "الزمن المتوقّع {time}", "components.IssueDetails.IssueComment.areyousuredelete": "هل أنت متأكد من حذف هذا التعليق ؟", "components.IssueDetails.IssueComment.delete": "<PERSON><PERSON><PERSON> التعليق", "components.IssueDetails.IssueComment.edit": "تعديل التعليق", "components.IssueDetails.IssueComment.postedby": "علّق {relativeTime} بواسطة {username}", "components.IssueDetails.IssueComment.postedbyedited": "علّق {relativeTime} بواسطة {username} (تم تعديله)", "components.IssueDetails.IssueComment.validationComment": "يجب عليك كتابه رسالة", "components.IssueDetails.IssueDescription.deleteissue": "م<PERSON><PERSON> المشك<PERSON>ة", "components.IssueDetails.IssueDescription.description": "الوصف", "components.IssueDetails.IssueDescription.edit": "تعديل الوصف", "components.IssueDetails.allepisodes": "جميع الحلقات", "components.IssueDetails.allseasons": "جميع المواسم", "components.IssueDetails.closeissue": "إغلاق المشكلة", "components.IssueDetails.closeissueandcomment": "إغلاق مع كتابة تعليق", "components.IssueDetails.commentplaceholder": "إضافة تعليق…", "components.IssueDetails.comments": "تعليقات", "components.IssueDetails.deleteissue": "<PERSON><PERSON><PERSON> المشكلة", "components.IssueDetails.deleteissueconfirm": "هل أنت متأكد من حذف هذه المشكلة ؟", "components.IssueDetails.episode": "حلقة {episodeNumber}", "components.IssueDetails.issuepagetitle": "مشكلة", "components.IssueDetails.issuetype": "النوع", "components.IssueDetails.lastupdated": "أخر تحديث", "components.IssueDetails.leavecomment": "تعليق", "components.IssueDetails.nocomments": "لا تعليقات.", "components.IssueDetails.openedby": "#{issueId} تم فتح {relativeTime} من قبل {username}", "components.IssueDetails.openin4karr": "فتح بجودة فور كي في {arr}", "components.IssueDetails.openinarr": "فتح في {arr}", "components.IssueDetails.play4konplex": "تشغيل بجودة فور كي في بليكس", "components.IssueDetails.playonplex": "تشغيل في بليكس", "components.IssueDetails.problemepisode": "الحلقة المعنيّة", "components.IssueDetails.problemseason": "الموسم المعني", "components.IssueDetails.reopenissue": "إعادة فتح المشكلة", "components.IssueDetails.reopenissueandcomment": "إعادة فتح مع ترك تعليق", "components.IssueDetails.toasteditdescriptionfailed": "حد<PERSON> خطأ ما أثناء تعديل وصف المشكلة.", "components.IssueDetails.toasteditdescriptionsuccess": "تم تعديل وصف المشكلة بنجاح!", "components.IssueDetails.toaststatusupdated": "تم تعديل حالة المشكلة بنجاح!", "components.IssueDetails.toaststatusupdatefailed": "حد<PERSON> خطأ ما أثناء تحديث حالة المشكلة.", "components.IssueDetails.unknownissuetype": "غير معروف", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {حلقة} other {حلقات}}", "components.IssueList.IssueItem.issuestatus": "الحالة", "components.IssueList.IssueItem.issuetype": "النوع", "components.IssueList.IssueItem.opened": "مفتوحة", "components.IssueDetails.toastissuedeleted": "تم حذف المشكلة بنجاح!", "components.IssueDetails.toastissuedeletefailed": "حد<PERSON> خطأ أثناء حذف المشكلة.", "components.IssueList.IssueItem.openeduserdate": "{date} بواسطة {user}", "components.IssueList.IssueItem.problemepisode": "الحلقة المعنيّة", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {موسم} other {مواسم}}", "components.IssueList.IssueItem.unknownissuetype": "غير معروف", "components.IssueList.IssueItem.viewissue": "الإطلاع على المشكلة", "components.IssueList.issues": "المشاكل", "components.IssueList.showallissues": "رؤية جميع المشاكل", "components.IssueList.sortAdded": "الأحدث", "components.IssueList.sortModified": "أخر تعديل", "components.IssueModal.CreateIssueModal.allepisodes": "جميع الحلقات", "components.IssueModal.CreateIssueModal.allseasons": "جميع المواسم", "components.IssueModal.CreateIssueModal.episode": "حلقة {episodeNumber}", "components.IssueModal.CreateIssueModal.extras": "الإضافات", "components.IssueModal.CreateIssueModal.problemepisode": "الحلقة المعنيّة", "components.IssueModal.CreateIssueModal.problemseason": "الموسم المعني", "components.IssueModal.CreateIssueModal.providedetail": "رجاءا قم بتزويد شرح مفصل للمشكلة التي صادفتها.", "components.IssueModal.CreateIssueModal.reportissue": "الإبلاغ عن مشكلةْ", "components.IssueModal.CreateIssueModal.season": "موسم {seasonNumber}", "components.IssueModal.CreateIssueModal.submitissue": "تأكيد المشكلة", "components.IssueModal.CreateIssueModal.toastFailedCreate": "حد<PERSON> خطأ ما أثناء تأكيد المشكلة.", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "تقرير المشكلة عن <strong>{title}</strong> تم تأكيده بنجاح!", "components.IssueModal.CreateIssueModal.toastviewissue": "رؤية المشكلة", "components.IssueModal.CreateIssueModal.validationMessageRequired": "يجب عليك ذكر وصف", "components.IssueModal.CreateIssueModal.whatswrong": "ماهي المشكلة؟", "components.IssueModal.issueAudio": "الصوت", "components.IssueModal.issueOther": "أ<PERSON><PERSON><PERSON>", "components.IssueModal.issueSubtitles": "الترجمة", "components.LanguageSelector.languageServerDefault": "الإفتراضي ({language})", "components.LanguageSelector.originalLanguageDefault": "كل اللغات", "components.Layout.LanguagePicker.displaylanguage": "لغة العرض", "components.Layout.SearchInput.searchPlaceholder": "بحث عن فيلم أو مسلسل", "components.Layout.Sidebar.dashboard": "إكتشف", "components.Layout.Sidebar.issues": "المشاكل", "components.Layout.Sidebar.requests": "الطلبات", "components.Layout.Sidebar.settings": "الإعدادات", "components.Layout.Sidebar.users": "المستخدمين", "components.Layout.UserDropdown.myprofile": "ملف التعريف", "components.Layout.UserDropdown.settings": "الإعدادات", "components.Layout.UserDropdown.signout": "تسجيل خروج", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {تأكيد} other {تأكيد}} behind", "components.Layout.VersionStatus.outofdate": "غير محدّث", "components.Login.email": "عنوان البريد الإلكتروني", "components.ManageSlideOver.markavailable": "الإشارة بتوفر هذا المحتوى", "components.ManageSlideOver.movie": "فيلْم", "components.ManageSlideOver.openarr": "فتح في {arr}", "components.ManageSlideOver.mark4kavailable": "الإشارة بتوفّره بجودة فور كي", "components.ManageSlideOver.markallseasons4kavailable": "الإشارة بأن كل المواسم متوفرة بجودة فور كي", "components.ManageSlideOver.markallseasonsavailable": "الإشارة بتوفر جميع المواسم", "components.ManageSlideOver.openarr4k": "فتح بجودة فور كي في {arr}", "components.ManageSlideOver.opentautulli": "فتح في تطبيق تاتولي (Tautulli)", "components.ManageSlideOver.pastdays": "مضى من الأيام {days, number}", "components.MovieDetails.mark4kavailable": "الإشارة بأنه متوفر بجودة فور كي", "components.MovieDetails.markavailable": "الإشارة بتوفر هذا المحتوى", "components.MovieDetails.originallanguage": "اللغة الأصلية", "components.MovieDetails.originaltitle": "الإسم الأصلي", "components.MovieDetails.overviewunavailable": "النظرة العامة غير متوفرة.", "components.MovieDetails.productioncountries": "إنتاج {countryCount, plural, one {الدولة} other {الدول}}", "components.MovieDetails.recommendations": "توصيات", "components.MovieDetails.releasedate": "{releaseCount, plural, one {تاريخ الاصدار other {تواريخ الاصدار}}", "components.MovieDetails.revenue": "العائد / الربح", "components.MovieDetails.runtime": "{minutes} دقيقة", "components.MovieDetails.showless": "رؤية تفاصيل أقل", "components.MovieDetails.showmore": "رؤية المزيد", "components.MovieDetails.similar": "عناوين مشابهه", "components.MovieDetails.overview": "نظرة عامة", "components.MovieDetails.streamingproviders": "يعرض حاليا على", "components.MovieDetails.studio": "{studioCount, plural, one {إستديو الإنتاج} other {إستديوهات الإنتاج}}", "components.MovieDetails.viewfullcrew": "رؤية طاقم العمل", "components.MovieDetails.watchtrailer": "<PERSON><PERSON><PERSON><PERSON> الإعلان", "components.NotificationTypeSelector.adminissuecommentDescription": "الحصول على تنبيهات عند تعليق المستخدمين على المشاكل.", "components.NotificationTypeSelector.adminissuereopenedDescription": "الحصول على تنبيهات عند إعادة فتح المشاكل من قبل المستخدمين.", "components.NotificationTypeSelector.adminissueresolvedDescription": "الحصول على تنبيهات في حالة حل المشاكل من قبل المستخدمين.", "components.NotificationTypeSelector.issuecomment": "تعليق على المشكلة", "components.NotificationTypeSelector.issuecommentDescription": "إرسال تنبيهات في حالة وجود تعليقات جديدة على المشاكل.", "components.NotificationTypeSelector.issuecreated": "تم التبليغ عن المشكلة", "components.NotificationTypeSelector.issuecreatedDescription": "إرسال تنبيهات في حالة التبليغ عن مشاكل.", "components.NotificationTypeSelector.issuereopened": "تم إعادة فتح المشكلة", "components.NotificationTypeSelector.issuereopenedDescription": "إرسال تنبيهات عند إعادة فتح المشاكل.", "components.NotificationTypeSelector.issueresolved": "تم حل المشكلة", "components.NotificationTypeSelector.issueresolvedDescription": "إرسال تنبيهات عند حل المشاكل.", "components.NotificationTypeSelector.mediaAutoApproved": "تم الموافقة على الطلب بشكل تلقائي", "components.NotificationTypeSelector.mediaapproved": "تمت الموافقة على الطلب", "components.NotificationTypeSelector.mediaapprovedDescription": "إرسال تنبيهات عند الموافقة على الطلبات بشكل يدوي.", "components.NotificationTypeSelector.mediaavailable": "الطلب متاح", "components.NotificationTypeSelector.mediaavailableDescription": "إرسال تنبيهات عندما يتوفر المحتوى المطلوب.", "components.NotificationTypeSelector.mediadeclined": "تم رفض الطلب", "components.NotificationTypeSelector.mediafailed": "فشل إنشاء الطلب", "components.NotificationTypeSelector.mediafailedDescription": "تلقي تنبيهات في حالة فشل إرسال الطلبات الى رادار أو سونار.", "components.NotificationTypeSelector.userissuecommentDescription": "تلقي تنبيهات في حالة وصول تعليقات جديدة على مشاكل تم فتحها بواسطتك.", "components.NotificationTypeSelector.userissuecreatedDescription": "تلقي تنبيهات في حالة قيام المستخدمين بالتبليغ عن مشاكل.", "components.NotificationTypeSelector.userissuereopenedDescription": "تلقي تنبيهات عندما يتم إعادة فتح مشكلة تم الإبلاغ عنها بواسطتك.", "components.NotificationTypeSelector.userissueresolvedDescription": "تلقي تنبيهات في حالة الإشارة بحل مشكلة تم الابلاغ عنها بواسطتك.", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "تلقي تنبيهات في حالة الموافقة بشكل تلقائي على طلب محتوى جديد من قبل المستخدمين.", "components.NotificationTypeSelector.usermediaavailableDescription": "تلقي تنبيهات عندما تصبح الطلبات متوفرة.", "components.NotificationTypeSelector.usermediafailedDescription": "تلقي تنبيهات في حالة فشل إرسال الطلبات الى رادار أو سونار.", "components.NotificationTypeSelector.usermediarequestedDescription": "تلقي تنبيهات عندما يتم طلب محتوى جديد يتطلب الموافقة عليه.", "components.PermissionEdit.admin": "مسؤول", "components.PermissionEdit.adminDescription": "ولوج إداري كامل لا يتطلب أي أذونات.", "components.PermissionEdit.advancedrequest": "طلبات متقدمة", "components.PermissionEdit.advancedrequestDescription": "إعطاء صلاحية تعديل الخيارات المتقدمة لطلب المحتوى.", "components.PermissionEdit.autoapprove": "موافقة تلقائية", "components.PermissionEdit.autoapprove4k": "موافقة تلقائية لجودة الفور كي", "components.PermissionEdit.autoapprove4kDescription": "إعطاء موافقة تلقائية لكل طلبات المحتوى بجودة الفور كي.", "components.PermissionEdit.autoapprove4kMovies": "موافقة تلقائية للأفلام بجودة فور كي", "components.PermissionEdit.autoapprove4kMoviesDescription": "إعطاء موافقة تلقائية لكل طلبات الأفلام بجودة فور كي.", "components.PermissionEdit.autoapprove4kSeries": "موافقة تلقائية للمسلسلات بجودة فور كي", "components.PermissionEdit.autoapprove4kSeriesDescription": "موافقة تلقائية لطلبات المسلسلات بجودة فور كي.", "components.PermissionEdit.autoapproveMovies": "موافقة تلقائية للأفلام", "components.PermissionEdit.autoapproveMoviesDescription": "موافقة تلقائية لكل طلبات الأفلام باستثناء جودة الفور كي.", "components.PermissionEdit.autoapproveSeries": "موافقة تلقائية للمسلسلات", "components.PermissionEdit.autoapproveSeriesDescription": "موافقة تلقائية لكل طلبات المسلسلات باستثناء جودة الفور كي.", "components.PermissionEdit.createissues": "الإبلاغ عن وجود مشاكل", "components.PermissionEdit.manageissues": "إدارة المشاكل", "components.PermissionEdit.manageissuesDescription": "إعطاء صلاحية بإدارة المشاكل.", "components.PermissionEdit.managerequests": "إدارة الطلبات", "components.PermissionEdit.request": "ط<PERSON><PERSON>", "components.PermissionEdit.request4k": "طلب بجودة فور كيّ", "components.PermissionEdit.request4kDescription": "اعطاء صلاحية بطلب محتوى بجودة فور كي.", "components.PermissionEdit.request4kMovies": "طلب أفلام بجودة فور كي", "components.PermissionEdit.request4kTv": "طلب مسلسلات بجودة فور كي", "components.PermissionEdit.request4kTvDescription": "إعطاء صلاحية بطلب مسلسلات بجودة فور كي.", "components.PermissionEdit.requestMovies": "<PERSON><PERSON><PERSON> أفلام", "components.PermissionEdit.requestMoviesDescription": "إعطاء صلاحية بطلب أفلام ليست بجودة فور كي.", "components.PermissionEdit.requestTv": "طلب مسلسلات", "components.PermissionEdit.requestTvDescription": "إعطاء إذن بطلب مسلسلات ليست بجودة ٤ك.", "components.PermissionEdit.users": "إدارة المستخدمين", "components.PermissionEdit.usersDescription": "إعطاء الإذن بإدارة المستخدمين. الذين يملكون هذه الصلاحية لا يستطيعون تعديل أي مستخدم مسؤول أو إعطاء صلاحية مسوؤل.", "components.PermissionEdit.viewissues": "الإطلاع على المشاكل", "components.PermissionEdit.viewissuesDescription": "إعطاء صلاحية برؤية كل المشاكل المبلغ عنها من قبل المستخدمين.", "components.PermissionEdit.viewrequests": "الإطلاع على الطلبات", "components.PermissionEdit.viewrequestsDescription": "إعطاء صلاحية بالإطلاع على جميع الطلبات المقدمة من قبل المستخدمين.", "components.PersonDetails.alsoknownas": "أيضا يُعرف بإسم: {names}", "components.PersonDetails.appearsin": "الظهور", "components.PersonDetails.ascharacter": "{character} شارك بِدوْر", "components.PersonDetails.birthdate": "ولد في {birthdate}", "components.PersonDetails.crewmember": "عضو", "components.PersonDetails.lifespan": "{birthdate} - {deathdate}", "components.QuotaSelector.days": "{count, plural, one {يوم} other {أيام}}", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{movies} كل {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.movies": "{count, plural, one {فيلم} other {أفلام}}", "components.QuotaSelector.seasons": "{count, plural, one {موسم} other {مواسم}}", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{seasons} كل {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.unlimited": "<PERSON>ير محدود", "components.RegionSelector.regionDefault": "كل المناطق", "components.RegionSelector.regionServerDefault": "الإفتراضي ({region})", "components.RequestBlock.profilechanged": "ملف تعريف الجودة", "components.RequestBlock.requestoverrides": "تغيير اعدادات الطلب الافتراضية", "components.RequestBlock.rootfolder": "مجلّد الحفظ", "components.RequestBlock.seasons": "{seasonCount, plural, one {موسم} other {مواسم}}", "components.RequestBlock.server": "وجهه السيرفر", "components.RequestButton.approve4krequests": "الموافقة على {requestCount, plural, one {طلب فور كي} other {{requestCount} طلبات فور كي}}", "components.RequestButton.approverequest": "الموافقة على الطلب", "components.RequestButton.approverequest4k": "الموافقة على طلب فور كي", "components.RequestButton.approverequests": "الموافقة على {requestCount, plural, one {طلب} other {{requestCount} طلبات}}", "components.RequestButton.decline4krequests": "رفض {requestCount, plural, one {طلب الفور كي} other {{requestCount} طلبات الفور كي}}", "components.RequestButton.declinerequest": "<PERSON><PERSON><PERSON> الطلب", "components.RequestButton.declinerequest4k": "رف<PERSON> طلب الفور كي", "components.RequestButton.declinerequests": "رفض {requestCount, plural, one {طلب} other {{requestCount} طلبات}}", "components.RequestButton.requestmore": "<PERSON><PERSON><PERSON> المزيد", "components.RequestButton.requestmore4k": "طل<PERSON> المزيد بجودة فور كي", "components.RequestButton.viewrequest": "رؤية تفاصيل الطلب", "components.RequestButton.viewrequest4k": "رؤية تفاصيل طلب الفور كي", "components.RequestCard.deleterequest": "<PERSON><PERSON><PERSON> الطلب", "components.RequestCard.failedretry": "حد<PERSON> خطأ ما أثناء محاولة إعادة الطلب.", "components.RequestCard.mediaerror": "{mediaType} غير موجود", "components.RequestCard.seasons": "{seasonCount, plural, one {موسم} other {مواسم}}", "components.RequestList.RequestItem.cancelRequest": "إلغاء الطلب", "components.RequestList.RequestItem.deleterequest": "<PERSON><PERSON><PERSON> الطلب", "components.RequestList.RequestItem.editrequest": "تعديل الطلب", "components.RequestList.RequestItem.failedretry": "حد<PERSON> خطأ ما أثناء محاولة إعادة الطلب.", "components.RequestList.RequestItem.mediaerror": "{mediaType} غير موجود", "components.RequestList.RequestItem.modified": "تم تعديله", "components.RequestList.RequestItem.modifieduserdate": "{date} من <PERSON> {user}", "components.RequestList.RequestItem.requested": "تم طلبه", "components.RequestList.RequestItem.requesteddate": "تم طلبه", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {موسم} other {مواسم}}", "components.RequestList.requests": "الطلبات", "components.RequestList.showallrequests": "رؤية جميع الطلبات", "components.RequestList.sortAdded": "الأحدث", "components.RequestList.sortModified": "أخر تعديل", "components.RequestModal.AdvancedRequester.advancedoptions": "متقدم", "components.RequestModal.AdvancedRequester.animenote": "*هذا المحتوى بتصنيف \" إنمي \".", "components.RequestModal.AdvancedRequester.default": "{name} (إفترا<PERSON>ي)", "components.RequestModal.AdvancedRequester.destinationserver": "وجهه السيرفر", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})'", "components.RequestModal.AdvancedRequester.languageprofile": "ملف تعريف اللغة", "components.RequestModal.AdvancedRequester.notagoptions": "لا يو<PERSON>د علامات تعريف.", "components.RequestModal.AdvancedRequester.qualityprofile": "ملف تعريف الجودة", "components.RequestModal.AdvancedRequester.requestas": "تقديم الطلب بإسم", "components.RequestModal.AdvancedRequester.rootfolder": "م<PERSON><PERSON><PERSON> الحفظ", "components.RequestModal.QuotaDisplay.allowedRequests": "مسموح لك بطلب <strong>{limit}</strong> {type} كل <strong>{days}</strong> أيام.", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "هذا المستخدم مسموح له بطلب <strong>{limit}</strong> {type} كل <strong>{days}</strong> أيام.", "components.RequestModal.QuotaDisplay.movie": "فِيلم", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {فيلم} other {أفلام}}", "components.RequestModal.QuotaDisplay.quotaLinkUser": "يمكنك الإطلاع على ملخص الحد الاقصى لطلبات هذا المستخدم في <ProfileLink>صفحة ملف التعريف</ProfileLink>.", "components.RequestModal.QuotaDisplay.requestsremaining": "{متبقي, plural, =0 {No} other {<strong>#</strong>}} {type} {متبقي, plural, one {طلب} other {طلبات}} متبقية", "components.RequestModal.QuotaDisplay.season": "موسم", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {موسم} other {مواسم}}", "components.RequestModal.QuotaDisplay.requiredquotaUser": "هذا المستخدم يحتاج أن يملك على الاقل <strong>{seasons}</strong> {seasons, plural, one {طلب موسم} other {طلبات مواسم}} متبقية/متبقي ليتمكن من طلب هذا المسلسل.", "components.RequestModal.SearchByNameModal.notvdbiddescription": "لم نتمكن من مطابقة هذا المسلسل بشكل تلقائي. الرجاء مطابقة المسلسل الصحيح من القائمة بالإسفل.", "components.RequestModal.alreadyrequested": "تم طلبه مُسبقاً", "components.RequestModal.approve": "الموافقة على الطلب", "components.RequestModal.autoapproval": "موافقة تلقائية", "components.RequestModal.cancel": "إلغاء الطلب", "components.RequestModal.edit": "تعديل الطلب", "components.RequestModal.errorediting": "حد<PERSON> خطأ ما أثناء محاولة تعديل الطلب.", "components.RequestModal.numberofepisodes": "# حلقات", "components.RequestModal.pending4krequest": "طلب فور كي مُعلّق", "components.RequestModal.pendingapproval": "طلبك معلق بانتظار الموافقة.", "components.RequestModal.pendingrequest": "طلب مُعلّق", "components.ResetPassword.validationpasswordmatch": "يجب مطابقة كلمة السر مع تأكيدها", "components.RequestModal.requestmovies": "طلب {count} {count, plural, one {فيلم} other {أفلام}}", "components.ResetPassword.email": "عنوان البريد الإلكتروني", "components.RequestModal.requestseasons4k": "طلب {seasonCount} {seasonCount, plural, one {موسم} other {مواسم}} بجودة فور كي", "components.ResetPassword.emailresetlink": "رابط إستعادة البريد الإلكتروني", "components.ResetPassword.passwordreset": "إعادة تعيين كلمة السر", "components.ResetPassword.password": "كلمة السر", "components.ResetPassword.validationemailrequired": "يجب عليك تزويد بريد إلكتروني صحيح", "components.RequestModal.requestApproved": "طلبك لـ <strong>{title}</strong> تمت الموافقة عليه!", "components.RequestModal.requestCancel": "طلبك لـ <strong>{title}</strong> تم إلغاءه.", "components.RequestModal.requestSuccess": "<strong>{title}</strong> تم الطلب بنجاح!", "components.RequestModal.requestadmin": "هذا الطلب سيتم الموافقة عليه بشكل تلقائي.", "components.RequestModal.requestcancelled": "الطلب لـ <strong>{title}</strong> تم إلغاءه.", "components.RequestModal.requestedited": "الطلب لـ <strong>{title}</strong> تم تعديله بنجاح!", "components.RequestModal.requesterror": "حد<PERSON> خطأ ما أثناء تأكيد الطلب.", "components.RequestModal.requestfrom": "{username} طلب هذا المستخدم معلق بانتظار الموافقة.", "components.RequestModal.requestmovies4k": "طلب {count} {count, plural, one {فيلم} other {أفلام}} بجودة فور كي", "components.RequestModal.requestseasons": "طلب {seasonCount} {seasonCount, plural, one {موسم} other {مواسم}}", "components.RequestModal.season": "موسم", "components.RequestModal.seasonnumber": "موسم {number}", "components.RequestModal.selectmovies": "إختيار فيلم /أفلام", "components.RequestModal.selectseason": "إختيار موسم/مواسم", "components.ResetPassword.confirmpassword": "تأكيد كلمة السر", "components.ResetPassword.gobacklogin": "العودة لصفحة تسجيل الدخول", "components.ResetPassword.requestresetlinksuccessmessage": "رابط إستعادة كلمة السر سيتم إرساله للبريد الإلكتروني المذكور إذا كان مرتبطا بمستخدم صحيح.", "components.ResetPassword.resetpassword": "إعادة تعيين كلمة السر", "components.ResetPassword.resetpasswordsuccessmessage": "تم تغيير كلمة السر بنجاح!", "components.ResetPassword.validationpasswordminchars": "كلمة السر قصيرة جدا؛ الحد الأدنى ثمانية خانات", "components.ResetPassword.validationpasswordrequired": "يجب عليك كتابة كلمة سر", "components.Search.search": "ب<PERSON><PERSON>", "components.Search.searchresults": "نتائج البحث", "components.Settings.Notifications.NotificationsGotify.agentenabled": "تفعيل الخدمة", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "فشل حفظ إعدادات تنبيه خدمه قونتفاي.", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "تم حفظ اعدادات خدمة تنبيه قونتفاي بنجاح!", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "فشل إرسال تنبيه تجريبي لخدمة قونتفاي.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "إرسال تنبيه تجريبي لقونتفاي…", "components.Settings.Notifications.NotificationsGotify.token": "مف<PERSON><PERSON><PERSON> التطبيق (Token)", "components.Settings.Notifications.NotificationsGotify.url": "عنوان السيرفر", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "يجب عليك كتابة مفتاح التطبيق", "components.Settings.Notifications.NotificationsGotify.validationTypes": "يجب عليك إختيار نوع تنبيه واح<PERSON> على الأقل", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "تم إرسال تنبيه تجريبي لقونتفاي!", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "يجب عليك كتابة رابط صحيح", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "تم حفظ اعدادات تنبيه لوناسي بنجاح!", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "الرابط يجب أن لا ينتهي بعلامة السلاش /", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "تفعيل الخدمة", "components.Settings.Notifications.NotificationsLunaSea.profileName": "إسم ملف التعريف", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "مطلوب فقط في حالة عدم إستخدام ملف التعريف الإفتراضي <code>default</code>", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "فشل في حفظ اعدادات تنبيه تطبيق لونا سي.", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "فشل في ارسال التنبيه التجريبي الى لوناسي.", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "جاري إرسال تنبيه تجريبي الى لوناسي…", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "تم ارسال التنبيه!", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "يجب عليك اختيار نوع تنبيه وا<PERSON><PERSON> على الاقل", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "يجب عليك تزويد رابط صحيح", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "رابط webhook", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "رابط المستخدم أو الجهاز <LunaSeaLink>notification webhook URL</LunaSeaLink>", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "مف<PERSON><PERSON><PERSON> الدخول <PERSON>", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "جاري ارسال التنبيه…", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "فشل إرسال تنبيه تجريبي Pushbullet.", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "تم إرسال تنبيه إختبار Pushbullet!", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "يج<PERSON> إختيار نوع تنبيه واحد على الأقل", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "تم إرسال تنبيه إختبار Pushover!", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "يجب ذكر application Token صحيح", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "يجب ذكر Access Token", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "الثلاثين خانة الخاصة بـ <UsersGroupsLink>بمعرف المستخدم او المجموعة </UsersGroupsLink>", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "اعادة تعيين", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "تم ارسال تنبيه تجريبي بنجاح!", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "فشل حفظ إعدادات تنبيه webkook.", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "تم حفظ الاعدادات!", "components.Settings.Notifications.agentenabled": "تفعيل الخدمة", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "عمل مفتاح من <PushbulletSettingsLink>اعدادات الحساب</PushbulletSettingsLink>", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "تفعيل الخدمة", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "علامة القناة", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "فشل حفظ إعدادات تنبيه Pushbullet.", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "تم حفظ إعدادات تنبيه pushb6 بنجاح!", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "جاري الإرسال…", "components.Settings.Notifications.NotificationsPushover.accessToken": "مف<PERSON><PERSON><PERSON> التطبيق API", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicationRegistrationLink>تسجيل تطبيق</ApplicationRegistrationLink> لاستخدامه مع اوفرسيرر", "components.Settings.Notifications.NotificationsPushover.agentenabled": "تفعيل الخدمة", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "فشل حفظ إعدادات تنبيه Pushover.", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "تم حفظ إعدادات تنبيه Pushover!", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "فشل إرسال تنبيه تجريبي.", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "جاري الإرسال…", "components.Settings.Notifications.NotificationsPushover.userToken": "مفتاح مستخدم أو مجموعة", "components.Settings.Notifications.NotificationsPushover.validationTypes": "يجب عليك اختيار نوع تنبيه وا<PERSON><PERSON> على الاقل", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "يجب ذكر مفتاح مستخدم أو مجموعة صحيح", "components.Settings.Notifications.NotificationsSlack.agentenabled": "تفعيل الخدمة", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "فشل حفظ إعدادات تنبيه Slack.", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "تم حفظ الاعدادات بنجاح!", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "فشل ارسال تنبيه تجريبي.", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "جاري ارسال تنبيه Slack تجريبي…", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "تم ارسال تنبيه تجريبي!", "components.Settings.Notifications.NotificationsSlack.validationTypes": "يجب عليك اختيار نوع تنبيه", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "الرجاء تزويد رابط صحيح", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "رابط webhook", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "إنشاء <WebhookLink>Incoming Webhook</WebhookLink> integration", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "تفعيل الخدمة", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "لاستقبال تنبيهات بواسطة push , اوفرسيرر يجب ان يكون يعمل من خلال HTTPS.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "فشل ارسال التنبيه.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "تم الارسال!", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "فشل حفظ إعدادات تنبيه web push.", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "تم حفظ الاعدادات بنجاح!", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "تفعيل الخدمة", "components.Settings.Notifications.NotificationsWebhook.authheader": "صلا<PERSON><PERSON><PERSON><PERSON> Header", "components.Settings.Notifications.NotificationsWebhook.customJson": "JSON Payload جيسون", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "تم اعادة تعيين JSON payload بنجاح!", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "\"Template Variable Help\"", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "فشل ارسال تنبيه تجريبي.", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "جاري ارسال تنبيه webhook تجريبي…", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "يجب عليك كتابة رابط صحيح", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "يجب عليك اختيار نوع تنبيه وا<PERSON><PERSON> على الاقل", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "يجب عليك كتابة رابط صحيح", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "رابط \"Webhook URL", "components.Settings.Notifications.allowselfsigned": "السماح بالشهادة الشخصية الموقعة", "components.Settings.Notifications.authPass": "SMTP كلمة سر", "components.Settings.Notifications.authUser": "اسم مستخدم SMPT", "components.Settings.Notifications.botAPI": "مفتاح تصريح البوت token", "components.Settings.Notifications.botApiTip": "<CreateBotLink>انشاء بوت</CreateBotLink> لاستخدامه مع اوفرسيرر", "components.Settings.Notifications.botAvatarUrl": "رابط صورة البوت", "components.Settings.Notifications.botUsername": "اسم البوت", "components.Settings.Notifications.botUsernameTip": "اسمح للمستخدمين ببدء محادثة مع بوتك الخاص وضبط اعدادات التنبيه الخاصة بهم", "components.Settings.Notifications.chatId": "عنوان المحادثة", "components.Settings.Notifications.discordsettingssaved": "تم حفظ الاعدادات بنجاح!", "components.Settings.Notifications.emailsettingssaved": "تم حفظ اعدادات تنبيه الايميل بنجاح!", "components.Settings.Notifications.encryptionDefault": "استخدم STARTTLS اذا كان متاحا", "components.Settings.Notifications.chatIdTip": "ابدأ محادثة مع البوت، أضف<GetIdBotLink>@get_id_bot</GetIdBotLink>, and و أعط <code>/my_id</code> الأمر", "components.Settings.Notifications.discordsettingsfailed": "فشل حفظ اعدادات تنبيه الديسكورد.", "components.Settings.Notifications.emailsender": "عنوان المرسل", "components.Settings.Notifications.emailsettingsfailed": "فشل حفظ اعدادات تنبيه الايميل.", "components.Settings.Notifications.enableMentions": "تفعيل التخصيص \"Mention\"", "components.Settings.Notifications.encryption": "وضع التشفير", "components.Settings.Notifications.encryptionOpportunisticTls": "استخدم STARTTLS دائما", "components.Settings.Notifications.encryptionTip": "في معظم الحالات، يستخدم Implicit TLS منفذ 465 و STARTTLS يستخدم منفذ 587", "components.Settings.Notifications.pgpPasswordTip": "سجل رسائل الايميل المشفرة باستخدام<OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.sendSilently": "أرسل بشكل صامت", "components.Settings.Notifications.senderName": "اسم المرسل", "components.Settings.Notifications.pgpPassword": "كلمة سر PGP", "components.Settings.Notifications.pgpPrivateKey": "رمز PGP الخاص", "components.Settings.Notifications.sendSilentlyTip": "أرسل التنبيهات بدون صوت", "components.Settings.Notifications.pgpPrivateKeyTip": "سجل رسائل الايميل المشفرة باستخدام<OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.smtpPort": "من<PERSON><PERSON> SMTP", "components.Settings.Notifications.smtpHost": "مضيف SMPT", "components.Settings.Notifications.telegramsettingsfailed": "فشل حفظ اعدادات تنبيه التليقرام.", "components.Settings.Notifications.telegramsettingssaved": "تم حفظ إعدادات التليقرام بنجاح!", "components.Settings.Notifications.toastDiscordTestFailed": "فشل ارسال تنبيه الديسكورد التجريبي.", "components.Settings.Notifications.toastTelegramTestFailed": "فشل ارسال التنبيه التجريبي للتليقرام.", "components.Settings.Notifications.toastTelegramTestSuccess": "تم ارسال التنبيه!", "components.Settings.Notifications.toastDiscordTestSending": "جاري ارسال تنبيه تجريبي للديسكورد…", "components.Settings.Notifications.toastDiscordTestSuccess": "تم ارسال التنبيه!", "components.Settings.Notifications.toastEmailTestFailed": "فشل ارسال تنبيه تجريبي للإيميل.", "components.Settings.Notifications.toastEmailTestSending": "جاري ارسال تنبيه تجريبي للايميل…", "components.Settings.Notifications.toastEmailTestSuccess": "تم ارسال التنبيه!", "components.Settings.Notifications.toastTelegramTestSending": "جاري ارسال تنبيه تجريبي للتليقرام…", "components.Settings.Notifications.validationBotAPIRequired": "يجب عليك ذكر مفتاح تصريح البوت \"Token\"", "components.Settings.Notifications.validationChatIdRequired": "يجب عليك ذكر عنوان دردشة صحيح", "components.Settings.Notifications.validationPgpPassword": "يجب عليك كتابة كلمة سر PGP", "components.Settings.Notifications.validationEmail": "يجب عليك كتابة عنوان بريد صحيح", "components.Settings.Notifications.validationSmtpHostRequired": "يجب ذكر عنوان مضيف أو أي بي صحيح", "components.Settings.Notifications.validationTypes": "يجب اختيار نوع تنبيه واحد على الأقل", "components.Settings.Notifications.webhookUrl": "رابط WEbhook", "components.Settings.Notifications.validationPgpPrivateKey": "يجب عليك كتابة المفتاح الخاص الصحيح ب PGP", "components.Settings.RadarrModal.add": "إضافة سيرفر", "components.Settings.Notifications.validationSmtpPortRequired": "يجب ذكر رقم منفذ صحيح", "components.Settings.Notifications.validationUrl": "يجب ذكر عنوان URL صحيح", "components.Settings.Notifications.webhookUrlTip": "إنشاء <DiscordWebhookLink>webhook integration</DiscordWebhookLink> للسيرفر الخاص بك", "components.Settings.RadarrModal.announced": "تم الإعلان عنه", "components.Settings.RadarrModal.apiKey": "مفتاح API", "components.Settings.Notifications.encryptionImplicitTls": "استخدم Implicit TLS", "components.Settings.Notifications.encryptionNone": "<PERSON>ي<PERSON> محدد", "components.Settings.RadarrModal.defaultserver": "السيرفر الافتراضي", "components.Settings.RadarrModal.enableSearch": "تفعيل البحث التلقائي", "components.Settings.RadarrModal.externalUrl": "عنوان الارتباط الخارجي", "components.Settings.RadarrModal.qualityprofile": "ملف تعريف الجودة", "components.Settings.RadarrModal.loadingrootfolders": "جاري تحميل مجلدات الحفظ…", "components.Settings.RadarrModal.selectRootFolder": "<PERSON>خ<PERSON><PERSON><PERSON> مج<PERSON>د الحفظ", "components.Settings.RadarrModal.baseUrl": "عنوان الارتباط \"URL \"", "components.Settings.RadarrModal.create4kradarr": "إضافة سيرفر رادار جديد \"خاص بالفور كي\"", "components.Settings.RadarrModal.createradarr": "إضافة سيرفر رادار جديد", "components.Settings.RadarrModal.default4kserver": "سيرفر الفور كي الافتراضي", "components.Settings.RadarrModal.edit4kradarr": "تعديل سيرفر رادار الخاص بالفور كي", "components.Settings.RadarrModal.editradarr": "تعديل سيرفر رادار", "components.Settings.RadarrModal.hostname": "عنوان المضيف أو رقم الأي بي", "components.Settings.RadarrModal.inCinemas": "متوفر بالسينما", "components.Settings.RadarrModal.loadingTags": "جاري تحميل علامات التعريف…", "components.Settings.RadarrModal.loadingprofiles": "جاري تحميل ملفات تعريف الجودة…", "components.Settings.RadarrModal.notagoptions": "لا يو<PERSON>د علامات تعريف.", "components.Settings.RadarrModal.port": "المنفذ", "components.Settings.RadarrModal.released": "تم إصداره", "components.Settings.RadarrModal.minimumAvailability": "الحالة الأدنى للتوفر", "components.Settings.RadarrModal.rootfolder": "م<PERSON><PERSON><PERSON> الحفظ", "components.Settings.RadarrModal.selectMinimumAvailability": "إختار الحالة الأدنى للتوفر", "components.Settings.RadarrModal.selectQualityProfile": "إختار ملف تعريف الجودة", "components.Settings.RadarrModal.selecttags": "إختار علامات التعريف", "components.Settings.RadarrModal.server4k": "سيرفر جودة الفور كي", "components.Settings.RadarrModal.servername": "اسم السيرفر", "components.Settings.RadarrModal.ssl": "استخدم SSL", "components.Settings.RadarrModal.validationProfileRequired": "يجب اختيار ملف تعريف للجودة", "components.Settings.RadarrModal.validationRootFolderRequired": "يج<PERSON> اختيار مج<PERSON>د الحفظ", "components.Settings.SettingsAbout.Releases.currentversion": "الحالي", "components.Settings.SettingsAbout.Releases.latestversion": "الأحدث", "components.Settings.SettingsAbout.Releases.releasedataMissing": "بيانات الاصدار غير متوفرة حاليا.", "components.Settings.SettingsAbout.Releases.releases": "إصدارات", "components.Settings.RadarrModal.syncEnabled": "تفعيل البحث", "components.Settings.RadarrModal.tags": "علامات التعريف", "components.Settings.RadarrModal.testFirstQualityProfiles": "اختبار الاتصال لتحميل ملفات تعريف الجودة", "components.Settings.RadarrModal.testFirstRootFolders": "اختبار الاتصال لتحميل مجلدات الحفظ", "components.Settings.RadarrModal.testFirstTags": "اختبار الاتصال لتحميل العلامات", "components.Settings.RadarrModal.toastRadarrTestFailure": "فشل الاتصال برادار.", "components.Settings.RadarrModal.toastRadarrTestSuccess": "تم انشاء الاتصال برادار بنجاح!", "components.Settings.RadarrModal.validationApiKeyRequired": "يجب عليك كتابة مفتاح API صحيح", "components.Settings.RadarrModal.validationApplicationUrl": "يجب عليك كتابة عنوان ارتباط صحيح", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "عنوان الارتباط يجب لا ينتهي بعلامة السلاش /", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "يجب لا ينتهي عنوان الارتباط الاساسي بعلامة السلاش", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "عنوان الارتباط الاساسي يجب ان يحتوي على علامة سلاش توضيحية \"غالبا بمنتصف كلمتين او بالبداية\"", "components.Settings.RadarrModal.validationHostnameRequired": "يجب كتابة عنوان مضيف أو رقم اي بي صحيح", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "يج<PERSON> اختيار المستوى الادنى للتوفر", "components.Settings.RadarrModal.validationNameRequired": "يج<PERSON> اختيار اسم سيرفر", "components.Settings.RadarrModal.validationPortRequired": "يجب كتابة رقم منفذ صحيح", "components.Settings.SettingsAbout.Releases.versionChangelog": "{version} سجل التحسينات", "components.Settings.SettingsAbout.Releases.viewchangelog": "الاطلاع على سجل التحسينات", "components.Settings.SettingsAbout.Releases.viewongithub": "رؤية المحتوى على GitHub", "components.Settings.SettingsAbout.about": "حو<PERSON>", "components.Settings.SettingsAbout.appDataPath": "مكان تخزين البيانات", "components.Settings.SettingsAbout.betawarning": "هذا اصدار تجريبي. بعض الخصائص المتقدمة غير مستقرة أو قد لا تعمل. الرجاء الابلاغ عن اي مشاكل على GitHub!", "components.Settings.SettingsAbout.documentation": "الوثائق", "components.Settings.SettingsAbout.gettingsupport": "الحصول على دعم", "components.Settings.SettingsAbout.githubdiscussions": "نقاشا<PERSON> GitHub", "components.Settings.SettingsAbout.helppaycoffee": "ساعدني بدفع حساب القهوة", "components.Settings.SettingsAbout.outofdate": "<PERSON>ي<PERSON> محدث", "components.Settings.SettingsAbout.overseerrinformation": "حول اوفرسيرر", "components.Settings.SettingsAbout.preferredmethod": "مُفضّل", "components.Settings.SettingsJobsCache.cache": "الملفات المؤقتة", "components.Settings.SettingsAbout.totalmedia": "المجموع الكلي للمحتوى", "components.Settings.SettingsAbout.totalrequests": "مجموع الطلبات", "components.Settings.SettingsAbout.version": "النسخة", "components.Settings.SettingsAbout.uptodate": "مُحدّث", "components.Settings.SettingsJobsCache.cacheDescription": "أوفرسيرر يخزن ملفات مؤقته متعلقه بالمفاتيح الخارجية API لتحسين الاداء وتفادي ارسال طلبات اتصال متكررة.", "components.Settings.SettingsJobsCache.cacheflushed": "تم محو الملف المخزّن.", "components.Settings.SettingsJobsCache.cachekeys": "مجموع المفاتيح", "components.Settings.SettingsJobsCache.canceljob": "الغاء الطلب", "components.Settings.SettingsJobsCache.cachename": "اسم ملفات التخزين", "components.Settings.SettingsJobsCache.cachevsize": "الحجم الكلي", "components.Settings.SettingsAbout.runningDevelop": "انت تستخدم نسخة تجريبية <code>develop</code> branch of Jellyseerr, النسخة مستحسنه فقط للمساهمين بتطوير التطبيق او المساعدين باختبار النسخ التجريبية.", "components.Settings.SettingsAbout.supportoverseerr": "إدعم أوفرسيرر", "components.Settings.SettingsAbout.timezone": "المنطقة الزمنية", "components.Settings.SettingsJobsCache.cachehits": "Hits،", "components.Settings.SettingsJobsCache.cacheksize": "حجم المفاتيح", "components.Settings.SettingsJobsCache.cachemisses": "مفقودات", "components.Settings.SettingsUsers.newPlexLoginTip": "السماح لمستخدمين بليكس بتسجيل الدخول دون الحاجة لدمجهم في أوفرسيرر", "components.Settings.SonarrModal.animeTags": "علامات تعريف الإنمي", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "كل {jobScheduleMinutes, plural, one {دقيقة} other {{jobScheduleMinutes} دقائق}}", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "حد<PERSON> خطأ ما اثناء حفظ المهمة.", "components.Settings.SettingsJobsCache.jobsDescription": "أوفرسيرر يعمل على جدولة بعض مهام الصيانة بشكل دوري ولكن بالإمكان عمل هذه المهام بشكل يدوي من القائمة بالإسفل. عمل اي مهمة بشكل يدوي لن يأثر على جدولتها التلقائية.", "components.Settings.SettingsLogs.message": "الرسائل", "components.Settings.SettingsJobsCache.unknownJob": "مهمة غير معروفه", "components.Settings.SettingsLogs.resumeLogs": "استئناف", "components.Settings.SettingsLogs.showall": "إظهار كل السجلات", "components.Settings.SettingsLogs.logs": "السجلات", "components.Settings.SonarrModal.baseUrl": "عنوان الارتباط URL BASE", "components.Settings.SonarrModal.create4ksonarr": "إضافة سيرفر سونارر لجودة الفور كي", "components.Settings.SonarrModal.loadinglanguageprofiles": "جاري تحميل ملفات تعريف اللغة…", "components.Settings.SonarrModal.loadingTags": "جاري تحميل علامات التعريف…", "components.Settings.SonarrModal.loadingprofiles": "جاري تحميل ملفات تعريف الجودة…", "components.Settings.SettingsJobsCache.command": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.download-sync": "تحميل الدمج", "components.Settings.SettingsJobsCache.download-sync-reset": "تحميل إعادة تعيين الدمج", "components.Settings.SettingsJobsCache.editJobSchedule": "تعديل المهمة", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "مّعدل جديد", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "كل {jobScheduleHours, plural, one {ساعة} other {{jobScheduleHours} ساعات}}", "components.Settings.SettingsJobsCache.flushcache": "محو الملفات المؤقتة", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "تم تعديل المهمة بنجاح!", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname} تم إلغاء هذه المهمة.", "components.Settings.SettingsJobsCache.jobname": "إسم المهمة", "components.Settings.SettingsJobsCache.jobs": "المهام", "components.Settings.SettingsJobsCache.jobsandcache": "المهام والملفات المؤقته", "components.Settings.SettingsJobsCache.jobstarted": "{jobname} بد<PERSON> عمل هذه المهمة.", "components.Settings.SettingsJobsCache.jobtype": "النوع", "components.Settings.SettingsJobsCache.nextexecution": "وقت التنفيذ التالي", "components.Settings.SettingsJobsCache.plex-full-scan": "الفحص الكلي لمكتبة بليكس", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "فحص المحتويات المضافة مؤخرا على بليكس", "components.Settings.SettingsJobsCache.process": "تنفيذ", "components.Settings.SettingsJobsCache.radarr-scan": "فحص رادار", "components.Settings.SettingsJobsCache.runnow": "تنفيذ \" الآن \"", "components.Settings.SettingsJobsCache.sonarr-scan": "فحص سونار", "components.Settings.SettingsLogs.copiedLogMessage": "نسخ رسائل السجل.", "components.Settings.SettingsLogs.copyToClipboard": "نسخ", "components.Settings.SettingsLogs.extraData": "بيانات إضافية", "components.Settings.SettingsLogs.filterDebug": "متابعة عميقة \" Debug \"", "components.Settings.SettingsLogs.filterError": "خطأ", "components.Settings.SettingsLogs.filterInfo": "معلومات", "components.Settings.SettingsLogs.filterWarn": "تحذير", "components.Settings.SettingsLogs.label": "ملصق", "components.Settings.SettingsLogs.level": "الحدة", "components.Settings.SettingsLogs.logDetails": "تفاصيل السجلات", "components.Settings.SettingsLogs.logsDescription": "تستطيع الإطلاع على هذه السجلات ايضا من خلال <code>stdout</code>, أو في <code>{appDataPath}/logs/overseerr.log</code>.", "components.Settings.SettingsLogs.pauseLogs": "ايقاف مؤقت", "components.Settings.SettingsLogs.time": "الختم الزمني", "components.Settings.SettingsUsers.defaultPermissions": "الاذونات الافتراضية", "components.Settings.SettingsUsers.defaultPermissionsTip": "الإذونات الأساسية للمستخدمين الجدد", "components.Settings.SettingsUsers.localLogin": "تفعيل تسجيل الدخول محليا", "components.Settings.SettingsUsers.localLoginTip": "السماح للمستخدمين بتسجيل الدخول بإستخدام البريد الإلكتروني وكلمة السر بدل التصادق مع بليكس", "components.Settings.SettingsUsers.movieRequestLimitLabel": "ال<PERSON><PERSON> الأع<PERSON>ى لطلبات الأفلام ( تعيين عام )", "components.Settings.SettingsUsers.newPlexLogin": "تفعيل تسجيل الدخول لمستخدمين بليكس الجدد", "components.Settings.SettingsUsers.toastSettingsFailure": "حد<PERSON> خطأ ما أثناء حفظ الإعدادات.", "components.Settings.SettingsUsers.toastSettingsSuccess": "تم حفظ إعدادات المستخدم بنجاح!", "components.Settings.SettingsUsers.tvRequestLimitLabel": "ال<PERSON><PERSON> الأعلى لطلبات المسلسلات \"تعيين عام\"", "components.Settings.SettingsUsers.userSettings": "إعدادات المستخدم", "components.Settings.SettingsUsers.userSettingsDescription": "<PERSON><PERSON><PERSON> إعدادت المستخدم العامة والإفتراضية.", "components.Settings.SettingsUsers.users": "المستخدمين", "components.Settings.SonarrModal.add": "إضافة سيرفر", "components.Settings.SonarrModal.animelanguageprofile": "ملف تعريف اللغة للإنمي", "components.Settings.SonarrModal.animequalityprofile": "ملف تعريف الجودة للإنمي", "components.Settings.SonarrModal.animerootfolder": "م<PERSON><PERSON><PERSON> حفظ الإنمي", "components.Settings.SonarrModal.apiKey": "مفتاح API", "components.Settings.SonarrModal.createsonarr": "إضافة سيرفر سونار جديد", "components.Settings.SonarrModal.default4kserver": "سيرفر الفور كي الافتراضي", "components.Settings.SonarrModal.defaultserver": "السيرفر الافتراضي", "components.Settings.SonarrModal.edit4ksonarr": "تعديل سيرفر سونارر لجودة الفور كي", "components.Settings.SonarrModal.editsonarr": "تعديل سيرفر سونار", "components.Settings.SonarrModal.enableSearch": "تفعيل البحث التلقائي", "components.Settings.SonarrModal.externalUrl": "عنوان الارتباط الخارجي", "components.Settings.SonarrModal.hostname": "إسم المضيف أو رقم الأي بي", "components.Settings.SonarrModal.languageprofile": "ملف تعريف اللغة", "components.Settings.SonarrModal.loadingrootfolders": "جاري تحميل مجلدات الحفظ…", "components.Settings.SonarrModal.notagoptions": "لا يو<PERSON>د علامات تعريف.", "components.Settings.SonarrModal.port": "المنفذ", "components.Settings.SonarrModal.qualityprofile": "ملفات تعريف الجودة", "components.Settings.SonarrModal.rootfolder": "م<PERSON><PERSON><PERSON> الحفظ", "components.Settings.SonarrModal.seasonfolders": "مجلدات المواسم", "components.Settings.SonarrModal.selectLanguageProfile": "إختار ملف تعريف اللغة", "components.Settings.SonarrModal.selectQualityProfile": "إختار ملف تعريف الجودة", "components.Settings.SonarrModal.selectRootFolder": "<PERSON>خ<PERSON><PERSON><PERSON> مج<PERSON>د الحفظ", "components.Settings.SonarrModal.selecttags": "إختار علامات التعريف", "components.Settings.menuGeneralSettings": "عام", "components.Settings.manualscanDescription": "عادةً٫ سيتم عمل هذا الفحص مرة واحدة كل 24 ساعة. أوفرسيرر سيفحص المحتوى المضاف مؤخرا في مكتبة بليكس بشكل مكثف. إذا كانت هذه المرة الأولى التي تقوم بها بإعداد بليكس يستحسن أن تقوم بعمل فحص يدوي كامل لمرة واحدة!", "components.Settings.menuAbout": "حو<PERSON>", "components.Settings.plex": "بليكس", "components.Settings.plexlibraries": "مكتبات بليكس", "components.Settings.noDefaultServer": "على الأقل {serverType} سيرفر واحد يجب أن يكون معدا كإفتراضي لإتاحة تنفيذ طلبات الـ {mediaType}.", "components.Settings.plexsettings": "إعدادات بليكس", "components.Settings.serviceSettingsDescription": "قم بإعداد {serverType} بالإسفل.تستطيع الاتصال بأكثر من سيرفر {serverType} ,ولكن إثنان فقط يمكن إعدادهما كإفتراضيين (واحد لجودة الفور كي والأخر لغير جودة الفور كي). أصحاب الصلاحيات الإدارية بإمكانهم تجاوز السيرفر المستخدم قبل تأكيدهم لأي طلب محتوى جديد.", "components.Settings.serverpresetManualMessage": "<PERSON><PERSON><PERSON> يدوي", "components.Settings.sonarrsettings": "إعدادات سونار", "components.Settings.tautulliSettingsDescription": "بشكل إختياري أضبط إعدادات سيرفرك الخاص بـ Tautulli.أوفرسيرر سيقوم بجلب بيانات سجل المشاهدة لمحتوى بليكس من Tautu<PERSON>.", "components.Settings.webhook": "ويب هوك Webhook", "components.Settings.webpush": "ويب بوش Web Push", "components.Settings.SonarrModal.server4k": "سيرفر جودة الفور كي", "components.Settings.SonarrModal.servername": "إسم السيرفر", "components.Settings.SonarrModal.ssl": "إستخدم SSL", "components.Settings.SonarrModal.syncEnabled": "تفعيل الفحص", "components.Settings.SonarrModal.tags": "علامات التعريف", "components.Settings.SonarrModal.testFirstLanguageProfiles": "إختبار الإتصال لتحميل ملفات تعريف اللغة", "components.Settings.SonarrModal.testFirstQualityProfiles": "إختبار الإتصال لتحميل ملفات تعريف الجودة", "components.Settings.SonarrModal.testFirstRootFolders": "إختبار الإتصال لتحميل مجلدات الحفظ", "components.Settings.SonarrModal.testFirstTags": "إختبار الإتصال لتحميل العلامات", "components.Settings.SonarrModal.toastSonarrTestFailure": "فشل الإتصال بـ سونار.", "components.Settings.SonarrModal.toastSonarrTestSuccess": "تم الإتصال بـ سونار بنجاح!", "components.Settings.SonarrModal.validationApiKeyRequired": "يجب ذكر مفتاح API", "components.Settings.SonarrModal.validationApplicationUrl": "يجب ذكر عنوان إرتباط صحيح", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "أساس عنوان الإرتباط يجب أن يحتوي على علامة السلاش", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "أساس عنوان الإرتباط يجب أن لا يُختتم بعلامة السلاش", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "عنوان الإرتباط يجب أن لا يخُتتم بعلامة السلاش", "components.Settings.SonarrModal.validationHostnameRequired": "يجب كتابة عنوان مضيف أو رقم أي بي صحيح", "components.Settings.SonarrModal.validationLanguageProfileRequired": "يج<PERSON> إختيار ملف تعريف جودة", "components.Settings.SonarrModal.validationNameRequired": "يجب كتابة إسم السيرفر", "components.Settings.SonarrModal.validationPortRequired": "يجب ذكر رقم منفذ صحيح", "components.Settings.SonarrModal.validationProfileRequired": "يج<PERSON> إختيار ملف تعريف جودة", "components.Settings.SonarrModal.validationRootFolderRequired": "يج<PERSON> إختيار مج<PERSON>د حفظ", "components.Settings.activeProfile": "ملف تعريف نشِط", "components.Settings.addradarr": "إضافة سيرفر رادار", "components.Settings.address": "العناوين", "components.Settings.addsonarr": "إضافة سيرفر سونار", "components.Settings.cancelscan": "إلغاء الفحص", "components.Settings.copied": "نسخ مفتاح الـ API.", "components.Settings.currentlibrary": "المكتبة الحالية: {name}", "components.Settings.default": "الإفتراضي", "components.Settings.default4k": "فور كي الإفتراضي", "components.Settings.deleteserverconfirm": "هل أنت متأكد من حذف هذا السيرفر؟", "components.Settings.email": "البريد الإلكترونيّ", "components.Settings.enablessl": "إستخدم SSL", "components.Settings.externalUrl": "عنوان الإرتباط الخارجي", "components.Settings.hostname": "عنوان المضيف أو رقم الأي بي", "components.Settings.is4k": "فور كي", "components.Settings.librariesRemaining": "المكتبات المتبقية: {count}", "components.Settings.manualscan": "فحص يدوي للمكتبة", "components.Settings.mediaTypeMovie": "فِيلم", "components.Settings.mediaTypeSeries": "مسلسل", "components.Settings.menuJobs": "المهام والملفات المؤقتة", "components.Settings.menuLogs": "السجلات", "components.Settings.menuNotifications": "التنبيهات", "components.Settings.menuPlexSettings": "بليكس", "components.Settings.menuServices": "الخدمات", "components.Settings.menuUsers": "المستخدمين", "components.Settings.noDefault4kServer": "سيرفر {serverType} مخصص للفور كي يجب أن يكون معدا كإفتراضي لتمكين المستخدمين من طلب محتويات الفور كي لـ {mediaType}.", "components.Settings.noDefaultNon4kServer": "إذا لم يكن لديك سوى سيرفر واحد {serverType} مخصص لكل المحتوى الفور كي وغير الفور كي (أو أنك لا تقوم بتحميل سوى محتوى الفور كي), سيرفرك {serverType} يجب <strong>أن لا</strong> يكون معدا بشكل مخصص لمحتوى الفور كي.", "components.Settings.notificationAgentSettingsDescription": "ضبط وتفعيل خدمات التنبيهات.", "components.Settings.notifications": "التنبيهات", "components.Settings.notificationsettings": "اعدادات التنبيهات", "components.Settings.notrunning": "لا يعمل", "components.Settings.plexlibrariesDescription": "المكتبات التي سيقوم أوفرسيرر بفحصها. قم بإعداد وحفظ إعدادات الإتصال ببليكس ثم قم بالضغط على الزر بالإسفل اذا لم تظهر مكتبات بليكس بالقائمة.", "components.Settings.plexsettingsDescription": "ضبط إعدادات سيرفر بليكس. أوفرسيرر سيفحص مكتبات بليكس للتأكد من توفر المحتوى من عدمه.", "components.Settings.port": "المنفذ", "components.Settings.radarrsettings": "إعدادات رادار", "components.Settings.scan": "دم<PERSON> المكتبات", "components.Settings.scanning": "جاري الدمج…", "components.Settings.serverLocal": "م<PERSON><PERSON>ي", "components.Settings.serverRemote": "<PERSON><PERSON> بعد", "components.Settings.serverSecure": "<PERSON><PERSON><PERSON>", "components.Settings.serverpreset": "سيرفر", "components.Settings.serverpresetLoad": "اضغط على الزر لتحميل السيرفرات المتاحة", "components.Settings.serverpresetRefreshing": "جاري جلب السيرفرات…", "components.Settings.services": "الخدمات", "components.Settings.settingUpPlexDescription": "لإعداد بليكس٫ تستطيع كتابة التفاصيل بشكل يدوي أو إختيار السيرفر جلب السيرفر منretrieved from <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink>. إضغط على الزر يمين القائمة المنسدلة لجلب قائمة السيرفرات المتاحة.", "components.Settings.ssl": "إتصال SSL", "components.Settings.startscan": "<PERSON><PERSON><PERSON><PERSON> الفحص", "components.Settings.tautulliApiKey": "مفتاح API", "components.Settings.tautulliSettings": "إعداد<PERSON><PERSON>", "components.Settings.toastPlexConnecting": "جاري محاولة الإتصال ببليكس…", "components.Settings.toastPlexConnectingFailure": "فشل الإتصال ببليكس.", "components.Settings.toastPlexConnectingSuccess": "تم الإتصال ببليكس بنجاح!", "components.Settings.toastPlexRefresh": "جاري جلب قائمة السيرفرات من بليكس…", "components.Settings.toastPlexRefreshFailure": "فشل جلب قائمة السيرفرات.", "components.Settings.toastPlexRefreshSuccess": "تم جلب قائمة السيرفرات من بليكس بنجاح!", "components.Settings.toastTautulliSettingsFailure": "حد<PERSON> خطأ ما أثناء حفظ إعدادا<PERSON>.", "components.Settings.toastTautulliSettingsSuccess": "تم حفظ إعدادات Tau<PERSON>lli بنجاح !", "components.Settings.urlBase": "عنوان الإرتباط الأساسي", "components.Settings.validationApiKey": "يجب كتابة مفتاح API", "components.Settings.validationHostnameRequired": "يجب كتابة إسم مضيف أو رقم أي بي صحيح", "components.Settings.validationPortRequired": "يجب كتابة رقم منفذ صحيح", "components.Settings.validationUrl": "يجب كتابة عنوان ارتباط صحيح", "components.Settings.validationUrlBaseLeadingSlash": "عنوان الارتباط الأساسي يجب أن يحتوي على علامة السلاش", "components.Settings.validationUrlBaseTrailingSlash": "عنوان الإرتباط الأساسي يجب أن لا ينتهي بعلامة السلاش", "components.Settings.validationUrlTrailingSlash": "عنوان الإرتباط يجب أن لا ينتهي بعلامة السلاش", "components.Settings.webAppUrl": "<WebAppLink>Web App</WebAppLink> عنوان الارتباط", "components.Settings.webAppUrlTip": "بشكل إختياري توجيه المستخدمين لصفحة ويب التطبيق في سيرفرك بدل صفحة ويب التطبيق المستضاف", "components.Setup.configureservices": "إعداد الخدمات", "components.Setup.continue": "إستمرار", "components.Setup.finish": "إنهاء الإعداد", "components.Setup.finishing": "جاري الإنهاء…", "components.UserList.userdeleted": "تم حذف المستخدم بنجاح!", "components.UserList.users": "المستخدمين", "components.TvDetails.seasons": "{seasonCount, plural, one {# موسم} other {# مواسم}}", "components.TvDetails.showtype": "نوُع المسلسل", "components.UserList.accounttype": "نوع العضويّة", "components.UserList.admin": "مسؤول", "components.TvDetails.cast": "الطاقم", "components.TvDetails.streamingproviders": "يعرض حاليا على", "components.UserList.deleteuser": "<PERSON>ذ<PERSON> المستخدم", "components.UserList.importfromplex": "جلب مستخدمين بليكس", "components.UserList.localuser": "مستخدم محلي", "components.UserList.newplexsigninenabled": "<strong>خاصية تفعيل تسجيل دخول مستخدم جديد من بليكس</strong>مفعلة حاليا. مستخدمين بليكس الذين لديهم صلاحية الإطلاع على مكتبة بليكس لا يحتاجون لان يتم جلبهم ليتمكنوا من تسجيل الدخول.", "components.UserProfile.ProfileHeader.settings": "تعديل الإعدادات", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "نوع الحساب", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "مسؤول", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "لغة العرض", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "تصفية المحتوى بحسب توفره بالمنطقة", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "يجب ذكر رقم مستخدم ديسكورد صحيح", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "تم حفظ إعدادات تنيه الديسكورد بنجاح!", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "إعدادات التنبيه", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "فشل حفظ إعدادات تنبيه Pushbullet.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "<ApplicationRegistrationLink>تسجيل تطبيق application</ApplicationRegistrationLink> للإستخدام مع {applicationTitle}", "i18n.approve": "موافقة", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "يجب ذكر مفتاح عام PGP صحيح", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "فشل حفظ إعدادات تنبيه web Push.", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "كلمة سر جديد", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "لا تستطيع تعديل صلاحياتك المُعطاة.", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "حساب هذا المستخدم بدون كلمة سر حاليا. قم بإعداد كلمة سر بالإسفل لإتاحة هذا الحساب من تسجيل الدخول \"كمستخدم محلي.\"", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "تم حفظ كلمة السر بنجاح!", "components.UserProfile.UserSettings.unauthorizedDescription": "لا تملك الصلاحية لتعديل إعدادات هذا المستخدم.", "components.UserProfile.totalrequests": "مجموع الطلبات", "i18n.deleting": "جاري الحذف…", "i18n.delete": "<PERSON><PERSON><PERSON>", "i18n.requesting": "جاري الطلب…", "pages.somethingwentwrong": "حد<PERSON> خطأ ما", "components.Setup.setup": "الإعداد", "components.Setup.signinMessage": "إبد<PERSON> من خلال تسجيل دخولك بحساب بليكس", "components.Setup.welcome": "مرحبا بك في أوفرسيرر", "components.StatusBadge.status": "{status}", "components.StatusBadge.status4k": "فور كي {status}", "components.TvDetails.TvCast.fullseriescast": "طاقم عمل المسلسل", "components.TvDetails.TvCrew.fullseriescrew": "طاقم فريق المسلسل", "components.TvDetails.anime": "إنمي", "components.TvDetails.episodeRuntime": "مدة عرض الحلقة", "components.TvDetails.episodeRuntimeMinutes": "{runtime} دقيقة", "components.TvDetails.firstAirDate": "تاريخ العرض الأول", "components.TvDetails.network": "{networkCount, plural, one {شبكة البث} other {شبكات البث}}", "components.TvDetails.nextAirDate": "تاريخ العرض التالي", "components.TvDetails.originallanguage": "اللغة الأصلية", "components.TvDetails.originaltitle": "العنوان الأصلي", "components.TvDetails.overview": "نظرة عامة", "components.TvDetails.overviewunavailable": "النظرة العامة غير متاحة.", "components.TvDetails.productioncountries": "إنتاج {countryCount, plural, one {الدولة} other {الدول}}", "components.TvDetails.recommendations": "التوصيات", "components.TvDetails.similar": "مسلسلات مشابهه", "components.TvDetails.viewfullcrew": "رؤية طاقم العمل", "components.TvDetails.watchtrailer": "<PERSON><PERSON><PERSON><PERSON> الإعلان", "components.UserList.autogeneratepassword": "إنشاء كلمة سر بشكل تلقائي", "components.UserList.autogeneratepasswordTip": "أرسل كلمة السر المنشئة الى إيميل المستخدم", "components.UserList.bulkedit": "تعديل شامل ( للم<PERSON><PERSON><PERSON> )", "components.UserList.create": "إنشاء", "components.UserList.created": "تاريخ الإنضمامْ", "components.UserList.createlocaluser": "إنشاء مستخدم محلي", "components.UserList.creating": "جاري الإنشاء…", "components.UserList.deleteconfirm": "هل أنت متأكد من حذف هذا المستخدم؟ كل الطلبات التي تم إنشاءها من خلاله سيتم حذفها بشكل نهائي.", "components.UserList.edituser": "تعديل صلاحيات المستخدم", "components.UserList.email": "عنوان البريد الإلكتروني", "components.UserList.importedfromplex": "<strong>{userCount}</strong> Plex {userCount, plural, one {مستخدم} other {مستخدمين}} تم جلبهم بنجاح!", "components.UserList.importfromplexerror": "حدث خطأ ما أثناء محاولة جلب المستخدمين.", "components.UserList.localLoginDisabled": "<strong>خاصية تفعيل تسجيل الدخول المحلي</strong> معطلة حاليا.", "components.UserList.nouserstoimport": "لا يوجد مستخدمين بليكس ليتم جلبهم.", "components.UserList.owner": "المالك", "components.UserList.password": "كلمة السر", "components.UserList.passwordinfodescription": "ضبط عنوان إرتباط التطبيق وتفعيل تنبيهات الإيميل للسماح بإنشاء كلمات سر تلقائية.", "components.UserList.plexuser": "مستخدم بليكس", "components.UserList.role": "صلاحيّات الحساب", "components.UserList.sortCreated": "تاريخ الإنضمام", "components.UserList.sortDisplayName": "إسم العرض", "components.UserList.sortRequests": "<PERSON><PERSON><PERSON> الطلبات", "components.UserList.totalrequests": "الطلبات", "components.UserList.user": "مُستخدم/عضو", "components.UserList.usercreatedfailed": "حدث خطأ ما أثناء إنشاء المستخدم.", "components.UserList.usercreatedfailedexisting": "عنوان البريد الإلكتروني المزوّد مستعمل من قبل مستخدم أخر.", "components.UserList.usercreatedsuccess": "تم إنشاء المستخدم بنجاح!", "components.UserList.userdeleteerror": "حد<PERSON> خطأ ما أثناء حذف المستخدم.", "components.UserList.userfail": "حد<PERSON> خطأ ما أثناء حفظ صلاحيات المستخدم.", "components.UserList.userlist": "قائمة المستخدمين", "components.UserList.userssaved": "تم حفظ صلاحيات المستخدم بنجاح!", "components.UserList.validationEmail": "يجب عليك ذكر عنوان بريد إلكتروني صحيح", "components.UserList.validationpasswordminchars": "كلمة السر قصيرة جدا؛ الحد الأدنى 8 خانات", "components.UserProfile.ProfileHeader.joindate": "منضم {joindate}", "components.UserProfile.ProfileHeader.profile": "رؤية ملف التعريف", "components.UserProfile.ProfileHeader.userid": "رقم المستخدم: {userid}", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "رقم ديسكورد المستخدم", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "<FindDiscordIdLink>رقم متعدد الخانات</FindDiscordIdLink> مرتبط بحساب عضوية الديسكورد", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "إسم العرض", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "تجاوز الحد العام", "components.UserProfile.UserSettings.UserGeneralSettings.general": "عام", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "الإعدادات العامة", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "الإفتراضي ({language})", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "مستخدم محلي", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "ال<PERSON><PERSON> الأق<PERSON>ى لطلبات الأفلام", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "لغة العرض الخاصة بـ إكتشف محتوى جديد", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "تصفية المحتوى بحسب اللغة الأصلية", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "المالك", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "مستخدم بليكس", "components.UserProfile.UserSettings.UserGeneralSettings.region": "المنطقة لـ إكتشف محتوى جديد", "components.UserProfile.UserSettings.UserGeneralSettings.role": "صلاحيّات الحساب", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "ال<PERSON><PERSON> الأق<PERSON>ى لطلبات المسلسلات", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "حد<PERSON> خطأ ما أثناء حفظ الإعدادات.", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "تم حفظ الإعدادات بنجاح!", "components.UserProfile.UserSettings.UserGeneralSettings.user": "مستخدم", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "رقم عضوية المستخدم", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "ة <FindDiscordIdLink>رقم متعدد الخانات</FindDiscordIdLink> مرتبط بحساب المستخدم", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "فشل حفظ إعدادات تنبيه الديسكورد.", "components.UserProfile.UserSettings.UserNotificationSettings.email": "البريد الإلكترونيّ", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "فشل حفظ إعدادات تنبيه الإيميل.", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "تم حفظ إعدادات تنبيه الإيميل بنجاح!", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "التنبيهات", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "مف<PERSON><PERSON><PERSON> PGP العام", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "رسائل إيميل مشفرة باستخدام <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "مف<PERSON><PERSON><PERSON> الدخول <PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "إنشاء Token من <PushbulletSettingsLink>إعدادات الحساب </PushbulletSettingsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "تم حفظ إعدادات تنبيه Pushbullet بنجاح!", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "مفتاح التطبيق API Token", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "مستخدم أو مفتاح مجموعة", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "الثلاثين خانة الخاصةبـ <UsersGroupsLink>بمعرف المستخدم أو المجموعة </UsersGroupsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "فشل حفظ إعدادات تنبيه Pushover.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "تم حفظ إعدادات تنبيه Pushover بنجاح!", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "رقم عضوية المحادثة", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink>إبد<PERSON> محادثة مع</TelegramBotLink>, قم بإضافة <GetIdBotLink>@get_id_bot</GetIdBotLink>, قم بإرسال الأمر <code>/my_id</code>", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "فشل حفظ إعدادات تنبيه التليقرام.", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "تم حفظ إعدادات تنبيه التليقرام بنجاح!", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "يجب ذكر رقم عضوية مستخدم صحيحة", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "أرسل بصمت", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "ارسال التنبيهات بدون صوت", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "يجب ذكر Access Token", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "يجب ذكر Access Token صحيح", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "يجب ذكر مفتاح مستخدم او مجموعة صحيح", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "يجب ذكر رقم هوية محادثة صحيحة", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "ويب بوش Web Push", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "تم حفظ إعدادات تنبيه Web Push بنجاح!", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "تأكيد كلمة السر", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "كلمة السر الحالية", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "حسابك حاليا بدون كلمة سر. قم بإعداد كلمة سر بالأسفل لإتاحة تسجيل الدخول كـ\"مستخدم محلي\" بإستخدام البريد الإلكتروني.", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "ليس لديك الصلاحية لتعديل كلمة سر هذا المستخدم.", "components.UserProfile.UserSettings.UserPasswordChange.password": "كلمة السر", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "حد<PERSON> خطأ ما أثناء حفظ كلمة السر.", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "حد<PERSON> خطأ ما أثناء حفظ كلمة السر. هل كان إدخالك لكلمة السر صحيحا؟", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "يجب تأكيد كلمة السر الجديدة", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "كلمات السر المُدخله يجب أن تتطابق", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "يجب كتابة كلمة السر الحالية", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "يجب كتابة كلمة سر جديدة", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "كلمة السر قصيرة جدا؛ الحد الأدنى ثمانية خانات", "components.UserProfile.UserSettings.UserPermissions.permissions": "الصلاحيات", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "حد<PERSON> خطأ ما أثناء حفظ الإعدادات.", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "تم حفظ إعدادات الصلاحيات بنجاح!", "components.UserProfile.UserSettings.menuChangePass": "كلمة السر", "components.UserProfile.UserSettings.menuGeneralSettings": "عام", "components.UserProfile.UserSettings.menuNotifications": "التنبيهات", "components.UserProfile.UserSettings.menuPermissions": "الصلاحيات", "components.UserProfile.limit": "{remaining} من {limit}", "components.UserProfile.movierequests": "طلبات الأفلام", "components.UserProfile.pastdays": "{type} (كل {days} أيام/يوم)", "components.UserProfile.recentlywatched": "تم مشاهدتها موخرا", "components.UserProfile.recentrequests": "الطلبات الحديثة", "components.UserProfile.requestsperdays": "{limit} متبقي", "components.UserProfile.seriesrequest": "طلبات المسلسلات", "components.UserProfile.unlimited": "<PERSON>ير محدود", "i18n.advanced": "متقدم", "i18n.all": "الكل", "i18n.approved": "تمت الموافقة عليه", "i18n.areyousure": "هل أنت متأكد؟", "i18n.available": "متوفر", "i18n.back": "عودة", "i18n.cancel": "إلغاء", "i18n.canceling": "جاري الإلغاء…", "i18n.close": "إغلاق", "i18n.decline": "<PERSON><PERSON><PERSON>", "i18n.declined": "تم رفضه", "i18n.delimitedlist": "{a}, {b}", "i18n.edit": "تعديل", "i18n.experimental": "تحت التجربة", "i18n.failed": "فشل", "i18n.import": "إستيراد/جلب", "i18n.importing": "جاري الإستيراد…", "i18n.loading": "جاري التحميل…", "i18n.movie": "فيلِم", "i18n.movies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.next": "التالي", "i18n.noresults": "لا يوجد نتائج.", "i18n.notrequested": "لم يتم طلبه", "i18n.open": "مفتوح", "i18n.partiallyavailable": "متوفر بشكل جزئي", "i18n.pending": "معلّق", "i18n.previous": "السابق", "i18n.processing": "قيد التنفيذ", "i18n.request": "ط<PERSON><PERSON>", "i18n.request4k": "طلب بجودة فور كي", "i18n.requested": "تم طلبه", "i18n.resolved": "تم الحل", "i18n.resultsperpage": "عرض {pageSize} نتائج بالصفحة الواحدة", "i18n.retry": "إعادة محاولة", "i18n.retrying": "جاري إعادة المحاولة…", "i18n.save": "حفظ التغييرات", "i18n.saving": "جاري الحفظ…", "i18n.settings": "إعدادات", "i18n.showingresults": "عرض <strong>{from}</strong> الى <strong>{to}</strong> من <strong>{total}</strong> نتائج", "i18n.status": "الحالة", "i18n.test": "إختبار", "i18n.testing": "جاري الإختبار…", "i18n.tvshow": "مسلسلْ", "i18n.tvshows": "مسلسلات", "i18n.unavailable": "<PERSON>ير متاح", "i18n.usersettings": "إعدادات المستخدم", "i18n.view": "<PERSON><PERSON><PERSON>", "pages.errormessagewithcode": "{statusCode} - {error}", "pages.internalservererror": "خطأ سيرفر داخلي", "pages.oops": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pages.pagenotfound": "الصفحة غير متاحة", "pages.returnHome": "عودة للقائمة الرئيسية", "pages.serviceunavailable": "الخدمة غير متاحة", "components.RequestBlock.languageprofile": "لغة ملف التعريف", "components.Discover.FilterSlideover.tmdbuservotecount": "عدد المقيّمين", "components.Discover.FilterSlideover.voteCount": "عدد المقيمين بين {minValue} و {maxValue}", "components.Discover.PlexWatchlistSlider.plexwatchlist": "قائمتك الخاصة برغبات بليكس", "components.Discover.resetfailed": "حدث خطأ ما خلال إعادة ضبط إعدادت (إكتشف) المخصّصة.", "components.Discover.resetwarning": "إعادة ضبط شرائط المحتوى لوضعها الإفتراضي. سيؤدي هذا أيضًا إلى حذف أي أشرطة محتوى مخصصة!", "components.Discover.stopediting": "إلغاء التعديل", "components.Discover.tmdbmoviekeyword": "كلمات بحث رئيسية للأفلام من (TMDB)", "components.Discover.studios": "إستديوهات الإنتاج", "components.Discover.updatesuccess": "تم تحديث إعدادات التخصيص لصفحة إكتشف.", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "طلبات الأفلام", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "طلبات المسلسلات", "components.MovieDetails.digitalrelease": "الإصدار الرقمي", "components.MovieDetails.reportissue": "قم بالإبلاغ عن مشكلة", "components.MovieDetails.rtaudiencescore": "تقييم الجمهور من موقع (Rotten Tomatoes)", "components.MovieDetails.rtcriticsscore": "تقييم النقاد من موقع Rotten Tomatoes", "components.RequestCard.declinerequest": "<PERSON><PERSON><PERSON> الطلب", "components.RequestList.RequestItem.unknowntitle": "عنوان غير معروف", "components.RequestModal.requestseriestitle": "طل<PERSON> مسلسل", "components.Selector.searchStudios": "بحث بالإستديوهات…", "components.Selector.showmore": "إظهار المزيد", "components.Selector.showless": "رؤية تفاصيل أقل", "components.Settings.SettingsJobsCache.availability-sync": "دمج توفر المحتويات", "components.Settings.SettingsMain.applicationTitle": "إسم التطبيق", "components.StatusBadge.managemedia": "إدارة {mediaType}", "i18n.restartRequired": "يتطلب إعادة التشغيل", "components.TvDetails.rtcriticsscore": "تقييم النقاد من موقع Rotten Tomatoes", "components.Settings.RadarrModal.tagRequests": "رموز تعريف الطلبات", "components.Settings.RadarrModal.tagRequestsInfo": "بشكل تلقائي إضافة رمز تعريف يحتوي على معرّف المستخدم وإسم العرض الخاص بمقدّم الطلب", "components.Settings.SettingsMain.apikey": "مفتاح API", "components.Settings.SettingsMain.validationApplicationTitle": "يجب تقديم عنوان للتطبيق", "components.Settings.SettingsMain.validationApplicationUrlTrailingSlash": "الرابط يجب لا أن ينتهي بعلامة سلاش", "components.Settings.SonarrModal.tagRequests": "علامات تعريف الطلبات", "components.Settings.SonarrModal.tagRequestsInfo": "بشكل تلقائي إضافة علامة تعريف مع معرّف المستخدم وإسم العرض الخاص بمقدّم الطلب", "components.Settings.restartrequiredTooltip": "يجب إعادة تشغيل أوفرسيرر لتفعيل هذه التغييرات", "components.TitleCard.tvdbid": "معّرف موقع TVDB", "components.TvDetails.manageseries": "إدارة المسلسل", "components.TvDetails.tmdbuserscore": "تقييم المستخدمين من موقع TMDB", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "تقديم تلقائي لطلبات المسلسلات", "i18n.collection": "تجميعة", "components.RequestBlock.approve": "الموافقة على الطلب", "components.RequestBlock.requestedby": "تم الطلب من قبل", "components.Settings.SettingsMain.generalsettingsDescription": "ضبط الإعدادات العامة والإفتراضية بأوفرسيرر.", "components.StatusChecker.appUpdated": "{applicationTitle} تم التحديث", "components.TitleCard.cleardata": "محو البيانات", "components.TitleCard.mediaerror": "{mediaType} غير موجود", "components.Discover.DiscoverWatchlist.discoverwatchlist": "قائمة الرغبات (watchlist) الخاصة ببليكس", "components.RequestList.RequestItem.tvdbid": "المعرّف الخاص بموقع TVDB", "components.Discover.DiscoverWatchlist.watchlist": "قائمة رغبات بليكس", "components.Settings.experimentalTooltip": "تفعيل هذا الخيار قد يؤدي الى نتائج غير متوقعة بأداء التطبيق", "components.Discover.tmdbtvstreamingservices": "شبكات بث المسلسلات من TMDB", "components.Discover.tmdbmoviestreamingservices": "شبكات بث الأفلام من (TMDB)", "components.Layout.UserDropdown.requests": "الطلبات", "components.MovieDetails.physicalrelease": "الإصدار الفعلي لأقراص البلوري", "components.NotificationTypeSelector.mediaautorequestedDescription": "إستقبال تنبيهات عندما يتم تقديم طلبات بشكل تلقائي من رغبات بليكس.", "components.PermissionEdit.autorequestSeries": "طلبات تلقائية للمسلسلات", "components.PermissionEdit.viewrecentDescription": "إعطاء الإذن برؤية قائمة المُضاف مؤخراً.", "components.PermissionEdit.viewwatchlists": "رؤية قائمة رغبات بليكس", "components.RequestBlock.lastmodifiedby": "أخر تعديل بواسطة", "components.RequestBlock.requestdate": "تاريخ الطلب", "components.RequestCard.approverequest": "الموافقة على الطلب", "components.RequestCard.cancelrequest": "إلغاء الطلب", "components.RequestCard.editrequest": "تعديل الطلب", "components.RequestCard.tvdbid": "المعرّف الخاص بموقع TVDB", "components.Selector.searchKeywords": "بحث بالكلمات الرئيسية أو الدّالة…", "components.Selector.searchGenres": "إختيار نوع المحتوى…", "components.Selector.starttyping": "قم بالكتابة لبدء البحث.", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "المُعدّل الحالي", "components.Settings.SettingsJobsCache.editJobScheduleSelectorSeconds": "كل {jobScheduleSeconds, plural, one {ثانية} other {{jobScheduleSeconds} ثواني}}", "components.Settings.SettingsJobsCache.image-cache-cleanup": "تنظيف تخزين الصور المؤقت", "components.Settings.SettingsJobsCache.imagecache": "تخزين الصور المؤقت", "components.Settings.SettingsJobsCache.imagecachecount": "الصور والملتقطات المُخزّنة", "components.Settings.SettingsJobsCache.imagecachesize": "المجموع الكُلي لحجم الملتقطات", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "دمج رغبات بليكس", "components.Settings.SettingsLogs.viewdetails": "رؤية التفاصيل", "components.Settings.SettingsMain.applicationurl": "رابط التطبيق", "components.Settings.SettingsMain.originallanguage": "اللغة الخاصة بصفحة إكتشف", "components.Settings.deleteServer": "حذف {serverType} سيرفر", "components.StatusBadge.openinarr": "فتح في {arr}", "components.StatusChecker.restartRequired": "مطلوب إعادة تشغيل السيرفر", "components.StatusBadge.playonplex": "تشغيل في بليكس", "components.StatusChecker.restartRequiredDescription": "الرجاء إعادة تشغيل السيرفر لتطبيق الإعدادات المُحدثة.", "components.TitleCard.tmdbid": "معرّف موقع TMDB", "components.TvDetails.episodeCount": "{episodeCount, plural, one {# حلقة} other {# حلقات}}", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "تقديم طلبات تلقائي للأفلام", "components.UserProfile.emptywatchlist": "المحتوى المضاف إلى <PlexWatchlistSupportLink>رغبات بليكس </PlexWatchlistSupportLink> سيظهر هنا.", "components.AirDateBadge.airsrelative": "تُعرضْ {relativeTime}", "components.PermissionEdit.autorequestMovies": "طلبات تلقائية للأفلام", "components.StatusChecker.reloadApp": "إعادة تحميل {applicationTitle}", "components.TvDetails.seasonstitle": "مواسم", "components.TvDetails.status4k": "فور كي {status}", "components.Discover.createnewslider": "إنشاء شريط محتوى جديد", "components.Discover.customizediscover": "تخصيص صفحة (إكتشف)", "components.Discover.moviegenres": "أنواع الأفلام", "components.Discover.networks": "شبكات البث", "components.Discover.resettodefault": "إعادة ضبط للوضع الإفتراضي", "components.Discover.resetsuccess": "تم إعادة ضبط إعدادات إكتشف المخصصة لوضعها الإفتراضي.", "components.Discover.tmdbstudio": "إستديوهات من TMDB", "components.Discover.tmdbnetwork": "شبكات البث من (TMDB)", "components.Discover.tmdbsearch": "بث في TMDB", "components.Discover.tvgenres": "أنواع المسلسلات", "components.Discover.updatefailed": "حدث خطأ ما أثناء تحديث الإعدادات المخصصة لصفحة إكتشف.", "components.DownloadBlock.formattedTitle": "{title}: م<PERSON><PERSON><PERSON> {seasonNumber} حلقة {episodeNumber}", "components.TvDetails.reportissue": "الإبلاغ عن مشكلةْ", "components.TvDetails.rtaudiencescore": "تقييم الجمهور من موقع Rotten Tomatoes", "components.Discover.DiscoverMovies.discovermovies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Discover.DiscoverMovies.sortPopularityAsc": "الشعبية تصاعديا", "components.Discover.DiscoverMovies.sortPopularityDesc": "الشعبية تنازلياً", "components.Discover.DiscoverMovies.sortTitleAsc": "العناوين بت<PERSON><PERSON><PERSON><PERSON> أبجدي (بحس<PERSON> الأحرف الإنجليزية)", "components.Discover.DiscoverMovies.sortTitleDesc": "العناوين بتنازل أبجدي (بحس<PERSON> الأحرف الإنجليزية)", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "تقييم TMDB بترتيب تصاعدي", "components.Discover.DiscoverSliderEdit.deletesuccess": "تم حذف شريط المحتوى.", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "تقييم TMDB بترتيب تنازلي", "components.Discover.DiscoverSliderEdit.deletefail": "فشل حذف شريط المحتوى.", "components.Discover.DiscoverSliderEdit.enable": "التبديل للرؤية", "components.Discover.DiscoverSliderEdit.remove": "حَذف", "components.Discover.DiscoverTv.discovertv": "مُسلسل", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "تاريخ البث بترتيب تصاعدي", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {# الفلتر المفعل} other {# الفلاتر المفعلة}}", "components.Discover.DiscoverTv.sortPopularityAsc": "الشعبية بترتيب تصاعدي", "components.Discover.DiscoverTv.sortPopularityDesc": "الشعبية بترتيب تنازلي", "components.Discover.DiscoverTvKeyword.keywordSeries": "{keywordTitle} مسلسل", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {# الفلتر المفعل} other {# الفلاتر المفعلة}}", "components.Discover.FilterSlideover.clearfilters": "محو الفلاتر المفعلة", "components.Discover.FilterSlideover.filters": "فلاتر", "components.Discover.FilterSlideover.from": "من", "components.Discover.FilterSlideover.originalLanguage": "اللغة الأصلية", "components.Discover.FilterSlideover.runtime": "مُدّة العرض", "components.Discover.FilterSlideover.tmdbuserscore": "تقييم المستخدمين من (TMDB)", "components.Discover.FilterSlideover.to": "إ<PERSON><PERSON>", "components.Discover.FilterSlideover.studio": "إستديوهات", "components.Discover.RecentlyAddedSlider.recentlyAdded": "أُضيفَ مؤخراً", "components.Discover.tmdbmoviegenre": "نوع الأفلام من (TMDB)", "components.Discover.tmdbtvgenre": "أنواع المسلسلات من TMDB", "components.Discover.plexwatchlist": "قائمتك الخاصة برغبات بليكس", "components.MovieDetails.theatricalrelease": "الإصدار السينمائي (العرض بالسينما)", "components.MovieDetails.tmdbuserscore": "تقييم مستخدمين TMDB", "components.NotificationTypeSelector.mediaautorequested": "تم تقديم الطلب بشكل تلقائي", "components.PermissionEdit.autorequest": "طلب تلقائي", "components.PermissionEdit.autorequestSeriesDescription": "إعطاء الإذن بتقديم طلبات تلقائية للمسلسلات بغير جودة الفور كي من خلال رغبات بليكس.", "components.PermissionEdit.viewrecent": "رؤية ماتم إضافته مؤخراً", "components.PermissionEdit.viewwatchlistsDescription": "إعطاء الإذن برؤية رغبات بليكس الخاصة بكل المستخدمين الأخرين.", "components.RequestBlock.decline": "<PERSON><PERSON><PERSON> الطلب", "components.RequestBlock.edit": "تعديل الطلب", "components.RequestCard.tmdbid": "المعرّف الخاص بموقع TMDB", "components.RequestList.RequestItem.tmdbid": "المعرّف الخاص بموقع TMDB", "components.RequestModal.requestseries4ktitle": "طلب مسلسل بجودة فور كي", "components.Settings.SettingsMain.cacheImages": "تفعيل تخزين الملتقطات والصور", "components.Settings.SettingsMain.generalsettings": "إعدادات عامة", "components.Settings.SettingsMain.locale": "لغة العرض", "components.Settings.SettingsMain.toastSettingsSuccess": "تم حفظ الإعدادات!", "components.StatusChecker.appUpdatedDescription": "الرجاء النقر على الزر بالإسفل لإعادة تحميل الصفحة.", "components.AirDateBadge.airedrelative": "عُرضت {relativeTime}", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {# الفلتر المُفعّل } other {# الفلاتر المفعلة}}", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "تاريخ الإصدار تصاعدياً", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "تاريخ الإصدار تنازلياً", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "تاريخ البث بترتيب تنازلي", "components.Discover.DiscoverTv.sortTitleAsc": "العنوانين بترتيب ابجدي تصاعدي", "components.Discover.DiscoverTv.sortTitleDesc": "العناوين بترتيب أبجدي تنازلي", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "تقييم TMDB بترتيب تصاعدي", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "تقييم TMDB بترتيب تنازلي", "components.Discover.FilterSlideover.firstAirDate": "تاريخ البث", "components.Discover.FilterSlideover.keywords": "كلمات رئيسية أو دالّة", "components.Discover.FilterSlideover.ratingText": "التقييم بين {minValue} و {maxValue}", "components.Discover.FilterSlideover.releaseDate": "تاريخ الإصدار", "components.MovieDetails.managemovie": "إدارة الفيلم", "components.Layout.Sidebar.browsemovies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Layout.Sidebar.browsetv": "مسلسلاتْ", "components.PermissionEdit.autorequestDescription": "إعطاء الإذن بتقديم طلبات تلقائية للمحتويات (بغير جودة الفور كي) من خلال رغبات بليكس.", "components.PermissionEdit.autorequestMoviesDescription": "إعطاء الإذن بتقديم طلبات أفلام تلقائية بغير جودة الفور كي من خلال رغبات بليكس.", "components.RequestCard.unknowntitle": "عنوان غير معروف", "components.RequestModal.SearchByNameModal.nomatches": "لم يتم العثور على عنوان مطابق لهذا المسلسل.", "components.RequestModal.requestcollection4ktitle": "طلب تجميعة بجودة فور كي (تجمعية هي الأفلام بأجزاء متعددة أو مرتبطة)", "components.RequestModal.requestmovie4ktitle": "طلب فيلم بجودة فور كي", "components.RequestModal.requestmovietitle": "طلب فيلم", "components.Selector.nooptions": "لا يوجد نتائج.", "components.RequestModal.requestcollectiontitle": "طلب تجميعة (التجميعة هي حزمة أفلام مرتبطة ببعضها)", "components.Settings.SettingsJobsCache.imagecacheDescription": "عند التفعيل بالإعدادات، أوفرسيرر سيقوم بحفظ الملتقطات والصور. يتم تخزين هذه الملتقطات في مجلد الضبط الخاص بك. تستطيع إيجاد هذه الملفات في <code>{appDataPath}/cache/images</code>.", "components.Settings.SettingsMain.cacheImagesTip": "الملتقطات من المصادر الخارجية (تتطلب حجم هائل متوفر في مساحة التخزين الخاصة بجهازك)", "components.Settings.SettingsMain.general": "عام", "components.Settings.SettingsMain.hideAvailable": "إخفاء المحتوى المتوفّر", "components.Settings.SettingsMain.originallanguageTip": "فلترة المحتوى باللغة الأصلية", "components.Settings.SettingsMain.partialRequestsEnabled": "السماح بالطلبات الجزئية للمسلسلات (بمعنى مسلسل لم ينتهي عرض الموسم بالكامل)", "components.Settings.SettingsMain.toastApiKeyFailure": "حدث خطأ ما أثناء إنشاء مفتاح API جديد.", "components.Settings.SettingsMain.toastSettingsFailure": "حد<PERSON> خطأ ما أثناء حفظ الإعدادات.", "components.Settings.SettingsMain.toastApiKeySuccess": "تم إنشاء مفتاح API جديد بنجاح!", "components.Settings.SettingsMain.validationApplicationUrl": "يجب تقديم رابط صحيح", "components.Settings.advancedTooltip": "ضبط هذا الإعداد بشكل غير صحيح قد يؤدي الى عدم عمل بعض الخصائص", "components.StatusBadge.seasonepisodenumber": "م{seasonNumber}ح{episodeNumber}", "components.TvDetails.Season.somethingwentwrong": "حد<PERSON> خطأ ما أثناء محاولة جلب بيانات الموسم.", "components.TvDetails.Season.noepisodes": "قائمة الحلقات غير متوفرة.", "components.TvDetails.seasonnumber": "موسم {seasonNumber}", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "بشكل تلقائي تقديم طلبات الأفلام <PlexWatchlistSupportLink>من رغبات بليكس </PlexWatchlistSupportLink>", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "تقديم الطلبات بشكل تلقائي للمسلسلات<PlexWatchlistSupportLink>من رغبات بليكس </PlexWatchlistSupportLink>", "components.Discover.FilterSlideover.genres": "أنواع المحتوى", "components.Discover.FilterSlideover.runtimeText": "{minValue}-{maxValue} مدة العرض بالدقائق", "components.RequestBlock.delete": "<PERSON><PERSON><PERSON> الطلب", "components.UserProfile.plexwatchlist": "رغبات بليكس", "components.Discover.emptywatchlist": "المحتوى المُضاف إلى<PlexWatchlistSupportLink>رغبات بليكس </PlexWatchlistSupportLink> سيظهر هنا.", "components.Discover.tmdbtvkeyword": "كلمات بحث رئيسية عن المسلسلات من TMDB", "components.Discover.FilterSlideover.streamingservices": "شبكات البثّ", "components.Discover.PlexWatchlistSlider.emptywatchlist": "المحتوى المُضاف إلى <PlexWatchlistSupportLink>رغبات بليكس </PlexWatchlistSupportLink> سيظهر هنا.", "components.Discover.CreateSlider.addcustomslider": "إنشاء شريط محتوى مخصّص", "components.Discover.CreateSlider.addSlider": "إضافة شريط محتوى", "components.Discover.CreateSlider.addfail": "فشل إنشاء شريط محتوى جديد.", "components.Discover.CreateSlider.editSlider": "تعديل شريط المحتوى", "components.Discover.CreateSlider.editfail": "فشل تعديل شريط المحتوى.", "components.Discover.CreateSlider.editsuccess": "تم تعديل الشريط وتم حفظ إعدادات التخصيص لصفحة (إكتشف).", "components.Discover.CreateSlider.addsuccess": "تم إنشاء شريط جديد و تم حفظ إعدادات التخصيص لصفحة (إكتشف).", "components.Discover.CreateSlider.needresults": "يجب أن يكون لديك نتيجة واحدة على الأقل.", "components.Discover.CreateSlider.nooptions": "لا يوجد نتائج.", "components.Discover.CreateSlider.providetmdbgenreid": "قم بتوفير معرّف نوع المحتوى من (TMDB)", "components.Discover.CreateSlider.providetmdbnetwork": "قم بتوفير معرّف شبكة البث من (TMDB)", "components.Discover.CreateSlider.providetmdbsearch": "قم بتوفير بحث إستعلام", "components.Discover.CreateSlider.providetmdbstudio": "قم بتوفير معرّف الإستديو من (TMDB)", "components.Discover.CreateSlider.searchGenres": "بحث بنوع المحتوى…", "components.Discover.CreateSlider.searchKeywords": "بحث بكلمات رئيسية…", "components.Discover.CreateSlider.searchStudios": "بحث عن الإستديوهات…", "components.Discover.CreateSlider.slidernameplaceholder": "إسم شريط المحتوى", "components.Discover.CreateSlider.starttyping": "قم بالكتابة لبدء البحث.", "components.Discover.CreateSlider.validationDatarequired": "يجب تقديم بيانات.", "components.Discover.CreateSlider.validationTitlerequired": "يجب تقديم عنوان.", "components.Discover.DiscoverMovieKeyword.keywordMovies": "{keywordTitle} الأفلام", "components.Discover.CreateSlider.providetmdbkeywordid": "قم بتوفير معرّف كلمات بحث رئيسية من (TMDB)", "components.MovieDetails.imdbuserscore": "تقييم مستخدمين IMDB", "components.Settings.Notifications.NotificationsPushover.sound": "صوت التنبيه", "components.UserProfile.UserSettings.UserNotificationSettings.deviceDefault": "الجهاز الإفتراضي", "components.Settings.SonarrModal.animeSeriesType": "نوع مسلسل الإنمي", "components.Settings.SonarrModal.seriesType": "نوع المسلسل", "components.UserProfile.UserSettings.UserNotificationSettings.sound": "صوت التنبيه", "components.Settings.Notifications.NotificationsPushover.deviceDefault": "الجهاز الإفتراضي", "component.BlacklistBlock.blacklistedby": "مدرج في القائمة السوداء من قبل", "component.BlacklistBlock.blacklistdate": "تاريخ الإدراج في القائمة السوداء", "component.BlacklistModal.blacklisting": "القائمة السوداء", "components.Blacklist.blacklistNotFoundError": "<strong>{title}</strong> غير مدرج في القائمة السوداء.", "components.Blacklist.blacklistSettingsDescription": "إدارة الوسائط المدرجة في القائمة السوداء.", "components.Blacklist.mediaType": "النوع", "components.Blacklist.mediaName": "الاسم", "components.Blacklist.blacklistdate": "التاريخ", "components.Blacklist.blacklistsettings": "إعدادات القائمة السوداء", "components.Blacklist.blacklistedby": "{date} بواسطة {user}"}