---
title: Discord
description: Configure Discord notifications.
sidebar_position: 3
---

# Discord

The Discord notification agent enables you to post notifications to a channel in a server you manage.

:::info
Users can optionally opt-in to being mentioned in Discord notifications by configuring their [Discord user ID](https://support.discord.com/hc/en-us/articles/206346498-Where-can-I-find-my-User-Server-Message-ID-) in their user settings.
:::

## Configuration

### Webhook URL

You can find the webhook URL in the Discord application, at **Server Settings &rarr; Integrations &rarr; Webhooks**.

### Notification Role ID (optional)

If a role ID is specified, it will be included in the webhook message. See [Discord role ID](https://support.discord.com/hc/en-us/articles/206346498-Where-can-I-find-my-User-Server-Message-ID).

### Bot Username (optional)

If you would like to override the name you configured for your bot in Discord, you may set this value to whatever you like!

### Bot Avatar URL (optional)

Similar to the bot username, you can override the avatar for your bot.
