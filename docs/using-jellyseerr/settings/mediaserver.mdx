---
title: Mediaserver Settings
description: Configure your Jellyfin, Emby, or Plex server settings.
sidebar_position: 3
---

# Media Server

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

<Tabs groupId="media-server-type" queryString>
    <TabItem value="jellyfin" label="Jellyfin">
:::info
To set up Jelly<PERSON>, make sure you log in using an account with administrative privileges.

The email address can be any email address and is only used for notifications, password resets, and local sign-in.
It is **not** tied to your Jellyfin account.
:::

### Jellyfin Libraries

In this section, simply select the libraries you would like Jellyseerr to scan. Je<PERSON><PERSON><PERSON> will periodically check the selected libraries for available content to update the media status that is displayed to users.

If you do not see your Jellyfin library listed, verify your Jellyfin settings are correct and click the Sync Libraries button.

### Manual Library Scan

<PERSON> will perform a full scan of your Jellyfin libraries once every 24 hours (recently added items are fetched more frequently). If this is your first time configuring <PERSON><PERSON><PERSON>, a one-time full manual library scan is recommended!

### Jellyfin Settings

This section is where you configure the connection to your Jellyfin server.

    <Tabs groupId="versions" queryString>
        <TabItem value="latest" label="Latest">

#### Internal URL

The internal URL is the URL that Jellyseerr will use to communicate with your Jellyfin server. This URL should be accessible from the machine running Jellyseerr.

In most cases, this will be the hostname or IP address of the machine running Jellyfin, followed by the port number Jellyfin is running on (usually 8096).

:::note
When running Jellyseerr in a docker container with a bridged network (default), the container's network will be separate from the host network. Therefore, you cannot use `localhost` or `127.0.0.1` as the internal URL as it will resolve to the container itself.
:::
:::tip
If you are running Jellyfin in a docker container, you can put both Jellyseerr and Jellyfin on the same docker network by using a custom [docker network](https://docs.docker.com/reference/cli/docker/network/). This will allow you to use the container name as the internal URL.
:::

#### External URL

The external URL is the URL that your users will use to access Jellyfin. This URL is used to generate links in `Play on Jellyfin` buttons, Jellyfin avatars and other places where users need to access Jellyfin directly.

In most cases, the external URL will be different from the internal URL. This is especially true if you are connecting to Jellyfin using docker container names or local IP addresses.

#### Forgot Password URL

The forgot password URL is the URL that users will be directed to when they click the "Forgot Password" button on the login page. This URL should be accessible from the machine running Jellyseerr.

By default, this field is empty and the "Forgot Password" button on the login page will redirect to the Jellyfin internal URL with the path `/web/index.html#!/forgotpassword`.

You can customize this URL to point to a custom password reset page if you have one.

        </TabItem>
        <TabItem value="develop" label="develop">

#### Hostname or IP Address

If you have Jellyseerr installed on the same network as Jellyfin, you can set this to the local IP address of your Jellyfin server. Otherwise, this should be set to a valid hostname (e.g., jellyfin.myawesomeserver.com).

In most cases, this will be the hostname or IP address of the machine running Jellyfin.

:::note
When running Jellyseerr in a docker container with a bridged network (default), the container's network will be separate from the host network. Therefore, you cannot use `localhost` or `127.0.0.1` as the internal URL as it will resolve to the container itself.
:::
:::tip
If you are running Jellyfin in a docker container, you can put both Jellyseerr and Jellyfin on the same docker network by using a custom [docker network](https://docs.docker.com/reference/cli/docker/network/). This will allow you to use the container name as the internal URL.
:::

#### Port

This value should be set to the port that your Jellyfin server listens on. The default port that Jellyfin uses is 8096, but you may need to set this to 443 or some other value if your Jellyfin server is hosted on a VPS or a different machine and is behind a reverse proxy.

#### Use SSL

Enable this setting to connect to Jellyfin via HTTPS rather than HTTP. Note that self-signed certificates are **not** officially supported.

#### External URL

The external URL is the URL that your users will use to access Jellyfin. This URL is used to generate links in `Play on Jellyfin` buttons, Jellyfin avatars and other places where users need to access Jellyfin directly.

In most cases, the external URL will be different from the internal URL. This is especially true if you are connecting to Jellyfin using docker container names or local IP addresses.

#### Forgot Password URL

The forgot password URL is the URL that users will be directed to when they click the "Forgot Password" button on the login page. This URL should be accessible from the machine running Jellyseerr.

By default, this field is empty and the "Forgot Password" button on the login page will redirect to the Jellyfin internal URL with the path `/web/index.html#!/forgotpassword`.

You can customize this URL to point to a custom password reset page if you have one.

            </TabItem>
        </Tabs>
    </TabItem>

   <TabItem value="emby" label="Emby">
:::info
To set up Emby, make sure you log in using an account with administrative privileges.

The email address can be any email address and is only used for notifications, password resets, and local sign-in.
It is **not** tied to your Emby account.
:::

### Emby Libraries

In this section, simply select the libraries you would like Jellyseerr to scan. Jellyseerr will periodically check the selected libraries for available content to update the media status that is displayed to users.

If you do not see your Emby library listed, verify your Emby settings are correct and click the Sync Libraries button.

### Manual Library Scan

Jellyseerr will perform a full scan of your Emby libraries once every 24 hours (recently added items are fetched more frequently). If this is your first time configuring Emby, a one-time full manual library scan is recommended!

### Emby Settings

This section is where you configure the connection to your Emby server.

        <Tabs groupId="versions" queryString>
            <TabItem value="latest" label="Latest">

#### Internal URL

The internal URL is the URL that Jellyseerr will use to communicate with your Emby server. This URL should be accessible from the machine running Jellyseerr.

In most cases, this will be the hostname or IP address of the machine running Emby, followed by the port number Emby is running on (usually 8096).

:::note
When running Jellyseerr in a docker container with a bridged network (default), the container's network will be separate from the host network. Therefore, you cannot use `localhost` or `127.0.0.1` as the internal URL as it will resolve to the container itself.
:::
:::tip
If you are running Emby in a docker container, you can put both Jellyseerr and Emby on the same docker network by using a custom [docker network](https://docs.docker.com/reference/cli/docker/network/). This will allow you to use the container name as the internal URL.
:::

#### External URL

The external URL is the URL that your users will use to access Emby. This URL is used to generate links in `Play on Emby` buttons, Emby avatars and other places where users need to access Emby directly.

In most cases, the external URL will be different from the internal URL. This is especially true if you are connecting to Emby using docker container names or local IP addresses.

#### Forgot Password URL

The forgot password URL is the URL that users will be directed to when they click the "Forgot Password" button on the login page. This URL should be accessible from the machine running Jellyseerr.

By default, this field is empty and the "Forgot Password" button on the login page will redirect to the Emby internal URL with the path `/web/index.html#!/forgotpassword.html`.

You can customize this URL to point to a custom password reset page if you have one.

            </TabItem>
            <TabItem value="develop" label="develop">

#### Hostname or IP Address

If you have Jellyseerr installed on the same network as Emby, you can set this to the local IP address of your Emby server. Otherwise, this should be set to a valid hostname (e.g., jellyfin.myawesomeserver.com).

In most cases, this will be the hostname or IP address of the machine running Emby.

:::note
When running Jellyseerr in a docker container with a bridged network (default), the container's network will be separate from the host network. Therefore, you cannot use `localhost` or `127.0.0.1` as the internal URL as it will resolve to the container itself.
:::
:::tip
If you are running Emby in a docker container, you can put both Jellyseerr and Emby on the same docker network by using a custom [docker network](https://docs.docker.com/reference/cli/docker/network/). This will allow you to use the container name as the internal URL.
:::

#### Port

This value should be set to the port that your Emby server listens on. The default port that Emby uses is 8096, but you may need to set this to 443 or some other value if your Emby server is hosted on a VPS or a different machine and is behind a reverse proxy.

#### Use SSL

Enable this setting to connect to Emby via HTTPS rather than HTTP. Note that self-signed certificates are **not** officially supported.

#### External URL

The external URL is the URL that your users will use to access Emby. This URL is used to generate links in `Play on Emby` buttons, Emby avatars and other places where users need to access Emby directly.

In most cases, the external URL will be different from the internal URL. This is especially true if you are connecting to Emby using docker container names or local IP addresses.

#### Forgot Password URL

The forgot password URL is the URL that users will be directed to when they click the "Forgot Password" button on the login page. This URL should be accessible from the machine running Jellyseerr.

By default, this field is empty and the "Forgot Password" button on the login page will redirect to the Emby internal URL with the path `/web/index.html#!/startup/forgotpassword.html`.

You can customize this URL to point to a custom password reset page if you have one.

            </TabItem>
        </Tabs>
    </TabItem>

    <TabItem value="plex" label="Plex">

### Plex Settings

:::info
To set up Plex, you can either enter your details manually or select a server retrieved from [plex.tv](https://plex.tv/). Press the button to the right of the "Server" dropdown to retrieve available servers.

Depending on your setup/configuration, you may need to enter your Plex server details manually in order to establish a connection from Jellyseerr.
:::

#### Hostname or IP Address

If you have Jellyseerr installed on the same network as Plex, you can set this to the local IP address of your Plex server. Otherwise, this should be set to a valid hostname (e.g., `plex.myawesomeserver.com`).

#### Port

This value should be set to the port that your Plex server listens on. The default port that Plex uses is `32400`, but you may need to set this to `443` or some other value if your Plex server is hosted on a VPS or cloud provider.

#### Use SSL

Enable this setting to connect to Plex via HTTPS rather than HTTP. Note that self-signed certificates are _not_ supported.

#### Web App URL (optional)

The **Play on Plex** buttons on media pages link to items on your Plex server. By default, these links use the [Plex Web App](https://support.plex.tv/articles/200288666-opening-plex-web-app/) hosted from plex.tv, but you can provide the URL to the web app on your Plex server and we'll use that instead!

Note that you will need to enter the full path to the web app (e.g., `https://plex.myawesomeserver.com/web`).

### Plex Libraries

In this section, simply select the libraries you would like Jellyseerr to scan. Jellyseerr will periodically check the selected libraries for available content to update the media status that is displayed to users.

If you do not see your Plex libraries listed, verify your Plex settings are correct and click the **Sync Libraries** button.

### Manual Library Scan

Jellyseerr will perform a full scan of your Plex libraries once every 24 hours (recently added items are fetched more frequently). If this is your first time configuring Plex, a one-time full manual library scan is recommended!

</TabItem>

</Tabs>
